"""
向量嵌入服务
支持多种嵌入模型，包括OpenAI、BGE等
"""
import logging
from typing import List, Optional
import numpy as np
from abc import ABC, abstractmethod

from rag_action.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class BaseEmbeddingService(ABC):
    """嵌入服务基类"""
    
    @abstractmethod
    def embed_text(self, text: str) -> List[float]:
        """对单个文本进行嵌入"""
        pass
    
    @abstractmethod
    def embed_texts(self, texts: List[str]) -> List[List[float]]:
        """对多个文本进行批量嵌入"""
        pass
    
    @abstractmethod
    def get_embedding_dim(self) -> int:
        """获取嵌入向量维度"""
        pass


class OpenAIEmbeddingService(BaseEmbeddingService):
    """OpenAI嵌入服务 - 使用LangChain接口"""

    def __init__(self):
        try:
            from langchain_openai import OpenAIEmbeddings

            # 使用LangChain的OpenAI嵌入接口
            self.embeddings = OpenAIEmbeddings(
                openai_api_key=settings.embedding.openai_api_key,
                openai_api_base=settings.embedding.base_url,
                model=settings.embedding.model_name,
                chunk_size=1000,  # LangChain批处理大小
                max_retries=3,    # 重试次数
                request_timeout=30  # 请求超时
            )
            self.model_name = settings.embedding.model_name
            logger.info(f"初始化LangChain OpenAI嵌入服务，模型: {self.model_name}")
        except Exception as e:
            logger.error(f"初始化LangChain OpenAI嵌入服务失败: {e}")
            raise
    
    def embed_text(self, text: str) -> List[float]:
        """对单个文本进行嵌入 - 使用LangChain接口"""
        try:
            # 截断过长的文本
            if len(text) > settings.embedding.max_tokens * 4:  # 粗略估算token数
                text = text[:settings.embedding.max_tokens * 4]

            # 使用LangChain的embed_query方法
            embedding = self.embeddings.embed_query(text)
            return embedding
        except Exception as e:
            logger.error(f"LangChain OpenAI文本嵌入失败: {e}")
            raise
    
    def embed_texts(self, texts: List[str]) -> List[List[float]]:
        """对多个文本进行批量嵌入 - 使用LangChain接口"""
        try:
            # 截断过长的文本
            processed_texts = []
            for text in texts:
                if len(text) > settings.embedding.max_tokens * 4:
                    text = text[:settings.embedding.max_tokens * 4]
                processed_texts.append(text)
            
            # 使用LangChain的embed_documents方法进行批量嵌入
            embeddings = self.embeddings.embed_documents(processed_texts)
            return embeddings
        except Exception as e:
            logger.error(f"LangChain OpenAI批量文本嵌入失败: {e}")
            raise
    
    def get_embedding_dim(self) -> int:
        """获取嵌入向量维度"""
        # text-embedding-3-small: 1536维
        # text-embedding-3-large: 3072维
        if "large" in self.model_name:
            return 3072
        return 1536


class BGEEmbeddingService(BaseEmbeddingService):
    """BGE嵌入服务"""
    
    def __init__(self):
        try:
            from sentence_transformers import SentenceTransformer
            self.model = SentenceTransformer(
                settings.bge.model_name,
                device=settings.bge.device
            )
            logger.info(f"初始化BGE嵌入服务，模型: {settings.bge.model_name}")
        except Exception as e:
            logger.error(f"初始化BGE嵌入服务失败: {e}")
            raise
    
    def embed_text(self, text: str) -> List[float]:
        """对单个文本进行嵌入"""
        try:
            embedding = self.model.encode(
                text,
                normalize_embeddings=settings.bge.normalize_embeddings
            )
            return embedding.tolist()
        except Exception as e:
            logger.error(f"BGE文本嵌入失败: {e}")
            raise
    
    def embed_texts(self, texts: List[str]) -> List[List[float]]:
        """对多个文本进行批量嵌入"""
        try:
            embeddings = self.model.encode(
                texts,
                normalize_embeddings=settings.bge.normalize_embeddings,
                batch_size=32
            )
            return embeddings.tolist()
        except Exception as e:
            logger.error(f"BGE批量文本嵌入失败: {e}")
            raise
    
    def get_embedding_dim(self) -> int:
        """获取嵌入向量维度"""
        return self.model.get_sentence_embedding_dimension()


class EmbeddingServiceFactory:
    """嵌入服务工厂"""
    
    @staticmethod
    def create_service() -> BaseEmbeddingService:
        """根据配置创建嵌入服务"""
        model_type = settings.embedding.model.lower()
        
        if model_type == "openai":
            return OpenAIEmbeddingService()
        elif model_type == "bge":
            return BGEEmbeddingService()
        else:
            raise ValueError(f"不支持的嵌入模型类型: {model_type}")


# 全局嵌入服务实例
def get_embedding_service() -> BaseEmbeddingService:
    """获取嵌入服务实例"""
    if not hasattr(get_embedding_service, '_instance'):
        get_embedding_service._instance = EmbeddingServiceFactory.create_service()
    return get_embedding_service._instance
