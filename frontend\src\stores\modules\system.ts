import { defineStore } from 'pinia'
import { systemApi } from '@/api'
import type { SystemHealth, SystemStats } from '@/types'

interface SystemState {
  // 系统健康状态
  health: SystemHealth | null
  
  // 系统统计信息
  stats: SystemStats | null
  
  // 服务状态
  services: Record<string, {
    status: 'loaded' | 'loading' | 'error'
    load_time?: number
    error?: string
  }>
  
  // 系统指标
  metrics: {
    cpu_usage: number
    memory_usage: number
    disk_usage: number
    api_response_times: Record<string, {
      avg: number
      min: number
      max: number
      count: number
    }>
    database_connections: {
      mysql: { active: number; idle: number }
      milvus: { active: number; idle: number }
    }
  } | null
  
  // 系统日志
  logs: Array<{
    timestamp: string
    level: string
    message: string
    module?: string
    function?: string
    line?: number
  }>
  
  // 备份列表
  backups: Array<{
    backup_id: string
    created_at: string
    size: number
    type: 'mysql' | 'milvus' | 'full'
    status: 'completed' | 'failed' | 'in_progress'
  }>
  
  // 加载状态
  loading: {
    health: boolean
    stats: boolean
    services: boolean
    metrics: boolean
    logs: boolean
    backups: boolean
  }
  
  // 自动刷新配置
  autoRefresh: {
    enabled: boolean
    interval: number // 秒
    timers: Record<string, NodeJS.Timeout>
  }
}

export const useSystemStore = defineStore('system', {
  state: (): SystemState => ({
    health: null,
    stats: null,
    services: {},
    metrics: null,
    logs: [],
    backups: [],
    loading: {
      health: false,
      stats: false,
      services: false,
      metrics: false,
      logs: false,
      backups: false
    },
    autoRefresh: {
      enabled: false,
      interval: 30,
      timers: {}
    }
  }),

  getters: {
    // 系统整体状态
    systemStatus: (state) => {
      if (!state.health) return 'unknown'
      return state.health.status
    },
    
    // 是否健康
    isHealthy: (state) => state.health?.status === 'healthy',
    
    // 服务加载状态统计
    serviceStats: (state) => {
      const services = Object.values(state.services)
      return {
        total: services.length,
        loaded: services.filter(s => s.status === 'loaded').length,
        loading: services.filter(s => s.status === 'loading').length,
        error: services.filter(s => s.status === 'error').length
      }
    },
    
    // 最近的错误日志
    recentErrors: (state) => {
      return state.logs
        .filter(log => log.level === 'ERROR')
        .slice(0, 10)
    },
    
    // 系统资源使用率
    resourceUsage: (state) => {
      if (!state.metrics) return null
      
      return {
        cpu: state.metrics.cpu_usage,
        memory: state.metrics.memory_usage,
        disk: state.metrics.disk_usage
      }
    }
  },

  actions: {
    // 获取系统健康状态
    async fetchHealth() {
      this.loading.health = true
      try {
        this.health = await systemApi.getHealth()
      } catch (error) {
        console.error('获取系统健康状态失败:', error)
      } finally {
        this.loading.health = false
      }
    },

    // 获取系统统计信息
    async fetchStats() {
      this.loading.stats = true
      try {
        this.stats = await systemApi.getStats()
      } catch (error) {
        console.error('获取系统统计信息失败:', error)
      } finally {
        this.loading.stats = false
      }
    },

    // 获取服务状态
    async fetchServiceStatus() {
      this.loading.services = true
      try {
        const response = await systemApi.getServiceStatus()
        this.services = response.services
      } catch (error) {
        console.error('获取服务状态失败:', error)
      } finally {
        this.loading.services = false
      }
    },

    // 获取系统指标
    async fetchMetrics() {
      this.loading.metrics = true
      try {
        this.metrics = await systemApi.getMetrics()
      } catch (error) {
        console.error('获取系统指标失败:', error)
      } finally {
        this.loading.metrics = false
      }
    },

    // 获取系统日志
    async fetchLogs(params?: {
      level?: 'DEBUG' | 'INFO' | 'WARNING' | 'ERROR'
      limit?: number
      offset?: number
      start_time?: string
      end_time?: string
    }) {
      this.loading.logs = true
      try {
        const response = await systemApi.getLogs(params)
        this.logs = response.logs
      } catch (error) {
        console.error('获取系统日志失败:', error)
      } finally {
        this.loading.logs = false
      }
    },

    // 获取备份列表
    async fetchBackups() {
      this.loading.backups = true
      try {
        const response = await systemApi.getBackups()
        this.backups = response.backups
      } catch (error) {
        console.error('获取备份列表失败:', error)
      } finally {
        this.loading.backups = false
      }
    },

    // 创建数据库备份
    async createBackup() {
      try {
        const result = await systemApi.backupDatabase()
        ElMessage.success('备份创建成功')
        
        // 刷新备份列表
        await this.fetchBackups()
        
        return result
      } catch (error) {
        console.error('创建备份失败:', error)
        throw error
      }
    },

    // 恢复数据库
    async restoreDatabase(backupId: string) {
      try {
        const result = await systemApi.restoreDatabase(backupId)
        ElMessage.success('数据库恢复成功')
        return result
      } catch (error) {
        console.error('恢复数据库失败:', error)
        throw error
      }
    },

    // 重启服务
    async restartService(serviceName: string) {
      try {
        await systemApi.restartService(serviceName)
        ElMessage.success(`服务 ${serviceName} 重启成功`)
        
        // 刷新服务状态
        await this.fetchServiceStatus()
      } catch (error) {
        console.error('重启服务失败:', error)
        throw error
      }
    },

    // 清理缓存
    async clearCache() {
      try {
        const result = await systemApi.clearCache()
        ElMessage.success(`缓存清理成功，清理了 ${result.cleared_items} 项`)
        return result
      } catch (error) {
        console.error('清理缓存失败:', error)
        throw error
      }
    },

    // 启用自动刷新
    enableAutoRefresh(interval?: number) {
      if (interval) {
        this.autoRefresh.interval = interval
      }
      
      this.autoRefresh.enabled = true
      
      // 设置定时器
      this.autoRefresh.timers.health = setInterval(() => {
        this.fetchHealth()
      }, this.autoRefresh.interval * 1000)
      
      this.autoRefresh.timers.stats = setInterval(() => {
        this.fetchStats()
      }, this.autoRefresh.interval * 1000)
      
      this.autoRefresh.timers.metrics = setInterval(() => {
        this.fetchMetrics()
      }, this.autoRefresh.interval * 1000)
    },

    // 禁用自动刷新
    disableAutoRefresh() {
      this.autoRefresh.enabled = false
      
      // 清除所有定时器
      Object.values(this.autoRefresh.timers).forEach(timer => {
        if (timer) clearInterval(timer)
      })
      
      this.autoRefresh.timers = {}
    },

    // 刷新所有数据
    async refreshAll() {
      const promises = [
        this.fetchHealth(),
        this.fetchStats(),
        this.fetchServiceStatus(),
        this.fetchMetrics()
      ]
      
      await Promise.allSettled(promises)
    },

    // 重置状态
    reset() {
      this.health = null
      this.stats = null
      this.services = {}
      this.metrics = null
      this.logs = []
      this.backups = []
      
      // 禁用自动刷新
      this.disableAutoRefresh()
    }
  }
})
