import MySQLdb
from MySQLdb import Error

try:
    conn = MySQLdb.connect(
        host='localhost',
        user='root',
        password='root',
        database='example_db'
    )
    # 在正常企业开发中，直接用MySQLdb（mysqlclient）进行原生SQL操作的情况较少，更多会使用ORM框架（如SQLAlchemy、Django ORM等）来管理数据库连接和操作，提高开发效率和安全性。
    # 但底层原理类似，下面是原生用法示例，企业中偶尔用于脚本或特殊场景。
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM game_scenes")
    results = cursor.fetchall()
    print(results)
except Error as e:
    print(f"数据库错误: {e}")
    
except Exception as e:
    print(f"其他错误: {e}")
    
finally:
    # 判断'cursor'是否在局部变量中，关闭游标。游标用于执行SQL语句和获取结果。
    if 'cursor' in locals():
        cursor.close()
    # 判断'conn'是否在局部变量中，关闭数据库连接。conn是数据库连接对象，负责与数据库的通信。
    if 'conn' in locals():
        conn.close()