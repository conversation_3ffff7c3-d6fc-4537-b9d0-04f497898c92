"""
01 - LangGraph 时光旅行案例
展示重放（Replay）和分叉（Fork）功能
"""

"""
LangGraph 的重放（Replay）和分叉（Fork）功能允许开发者在对话流程中回溯到任意历史节点，重新执行或探索不同的对话路径。

【重放（Replay）】
- 作用：可以将对话流程回滚到某个历史状态，并从该点重新执行后续步骤。这对于调试、修正模型输出或分析对话流程非常有用。
- 应用场景：
    1. 对话调试：开发者可以定位到某一步骤，修改输入或参数，观察不同的模型响应。
    2. 错误修正：用户发现某一步模型输出不理想，可以回到该步重试或调整，避免从头开始。
    3. 结果复现：方便复现历史对话，便于分析和归档。

【分叉（Fork）】
- 作用：允许从任意历史节点创建一个新的分支，探索不同的对话走向。每个分支互不影响，便于多路径实验。
- 应用场景：
    1. 多方案探索：在同一初始对话基础上，尝试不同的提示词、参数或策略，比较效果。
    2. A/B 测试：对比不同模型或配置在同一上下文下的表现。
    3. 用户自定义：允许用户在对话历史中“回到过去”，并基于该点提出新的问题，形成新的对话分支。

【总结】
LangGraph 的重放和分叉功能极大提升了对话流程的可控性和灵活性，适用于调试、实验、产品化等多种场景，是构建复杂对话系统的重要能力。
"""

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph, MessagesState, START, END
from langgraph.checkpoint.memory import MemorySaver
from typing import Dict, Any, List
import json
import time
from datetime import datetime

# 创建 LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0.7
)

# 扩展状态类型
class TimeTravelState(MessagesState):
    step: int
    timestamp: float
    branch_id: str
    metadata: Dict[str, Any]

def chat_node(state: TimeTravelState) -> TimeTravelState:
    """聊天节点 - 记录执行步骤"""
    messages = state["messages"]
    step = state.get("step", 0) + 1
    branch_id = state.get("branch_id", "main")
    
    print(f"�� 执行步骤 {step} (分支: {branch_id})")
    
    response = llm.invoke([
        HumanMessage(content=f"""
当前是第 {step} 步对话，分支: {branch_id}

用户消息: {messages[-1].content if messages else ""}

请提供回应，并在回应中说明这是第几步和当前分支。
""")
    ])
    
    return {
        "messages": [response],  # 返回消息列表
        "step": step,
        "timestamp": time.time(),
        "branch_id": branch_id,
        "metadata": {
            "execution_time": datetime.now().isoformat(),
            "step_number": step,
            "branch": branch_id
        }
    }

def create_workflow():
    """创建工作流"""
    workflow = StateGraph(TimeTravelState)
    workflow.add_node("chat", chat_node)
    # 这行代码的意思是设置工作流的入口节点为 "chat"，即工作流从 "chat" 节点开始执行
    workflow.set_entry_point("chat")
    workflow.add_edge("chat", END)
    return workflow

def basic_time_travel_demo():
    """基础时光旅行演示"""
    print("=== 基础时光旅行演示 ===")
    print("展示重放和分叉的基本功能")
    print()
    
    # 创建检查点保存器
    checkpointer = MemorySaver()
    
    # 编译工作流
    workflow = create_workflow()
    app = workflow.compile(checkpointer=checkpointer)
    
    # 初始对话序列
    conversation_steps = [
        "你好，我叫小明",
        "我喜欢编程",
        "我想学习Python",
        "请推荐一些学习资源"
    ]
    
    thread_id = "time_travel_demo"
    
    print("1️⃣ 执行初始对话序列...")
    print("-" * 50)
    
    for i, message in enumerate(conversation_steps):
        result = app.invoke(
            {
                "messages": [HumanMessage(content=message)],
                "step": i,
                "branch_id": "main",
                "metadata": {}
            },
            config={"configurable": {"thread_id": thread_id}}
        )
        print(f"步骤 {i+1}: {result['messages'][-1].content}")
        print(f"时间戳: {datetime.fromtimestamp(result['timestamp']).strftime('%H:%M:%S')}")
        print()
    
    print("2️⃣ 查看历史检查点...")
    print("-" * 50)
    
    # 简化检查点查看
    try:
        checkpoints = app.get_state(config={"configurable": {"thread_id": thread_id}})
        print(f"总共有 {len(checkpoints)} 个检查点")
        print("检查点信息: 已保存执行状态")
    except Exception as e:
        print(f"获取检查点时出错: {e}")
    
    print("\n3️⃣ 重放到第2步...")
    print("-" * 50)
    
    # 重放到第2步（索引为1）
    try:
        replay_result = app.invoke(
            {
                "messages": [HumanMessage(content="重新开始，我想学习Java")],
                "step": 1,
                "branch_id": "replay_branch",
                "metadata": {"replay_from": 1}
            },
            config={
                "configurable": {"thread_id": f"{thread_id}_replay"},
                "metadata": {"replay_from": 1}
            }
        )
        print(f"重放结果: {replay_result['messages'][-1].content}")
    except Exception as e:
        print(f"重放时出错: {e}")
    
    print("\n4️⃣ 分叉执行...")
    print("-" * 50)
    
    # 从第3步分叉
    try:
        fork_result = app.invoke(
            {
                "messages": [HumanMessage(content="假设我想学习机器学习")],
                "step": 2,
                "branch_id": "ml_branch",
                "metadata": {"fork_from": 2, "branch": "ml_learning"}
            },
            config={
                "configurable": {"thread_id": f"{thread_id}_fork"},
                "metadata": {"fork_from": 2, "branch": "ml_learning"}
            }
        )
        print(f"分叉结果: {fork_result['messages'][-1].content}")
    except Exception as e:
        print(f"分叉时出错: {e}")

def advanced_branching_demo():
    """高级分支演示"""
    print("\n=== 高级分支演示 ===")
    print("展示多分支并行执行和分支合并")
    print()
    
    checkpointer = MemorySaver()
    workflow = create_workflow()
    app = workflow.compile(checkpointer=checkpointer)
    
    # 定义多个学习路径
    learning_paths = {
        "frontend": {
            "name": "前端开发",
            "steps": ["学习HTML", "学习CSS", "学习JavaScript", "学习React"]
        },
        "backend": {
            "name": "后端开发", 
            "steps": ["学习Python", "学习Django", "学习数据库", "学习API设计"]
        },
        "data": {
            "name": "数据科学",
            "steps": ["学习Python", "学习Pandas", "学习机器学习", "学习深度学习"]
        }
    }
    
    print("🚀 创建多个学习分支...")
    print("-" * 50)
    
    branch_results = {}
    
    for branch_id, path_info in learning_paths.items():
        print(f"\n📚 分支: {path_info['name']} ({branch_id})")
        thread_id = f"branch_{branch_id}"
        
        branch_messages = []
        for i, step in enumerate(path_info["steps"]):
            result = app.invoke(
                {
                    "messages": [HumanMessage(content=step)],
                    "step": i,
                    "branch_id": branch_id,
                    "metadata": {"path": path_info['name']}
                },
                config={"configurable": {"thread_id": thread_id}}
            )
            # 修复：正确访问消息内容
            branch_messages.append(result['messages'][-1].content)
            print(f"  步骤 {i+1}: {step}")
        
        branch_results[branch_id] = {
            "name": path_info['name'],
            "messages": branch_messages,
            "thread_id": thread_id
        }
    
    print("\n🔄 分支合并示例...")
    print("-" * 50)
    
    # 从不同分支合并信息
    merge_prompt = """
基于所有分支的学习内容，给我一个完整的学习路径建议。
请考虑：
1. 不同路径的优缺点
2. 学习顺序的合理性
3. 技能之间的关联性
4. 就业前景分析
"""
    
    # 收集所有分支的信息
    all_branches_info = []
    for branch_id, branch_data in branch_results.items():
        all_branches_info.append(f"分支 {branch_data['name']}: {len(branch_data['messages'])} 个步骤")
    
    merge_result = app.invoke(
        {
            "messages": [HumanMessage(content=f"{merge_prompt}\n\n分支信息: {'; '.join(all_branches_info)}")],
            "step": 0,
            "branch_id": "merged_branch",
            "metadata": {"merge_from": list(branch_results.keys())}
        },
        config={"configurable": {"thread_id": "merged_analysis"}}
    )
    # 修复：正确访问消息内容
    print(f"合并分析结果:\n{merge_result['messages'][-1].content}")

def error_recovery_demo():
    """错误恢复演示"""
    print("\n=== 错误恢复演示 ===")
    print("展示如何从错误点重放和修复")
    print()
    
    checkpointer = MemorySaver()
    workflow = create_workflow()
    app = workflow.compile(checkpointer=checkpointer)
    
    thread_id = "error_recovery_demo"
    
    print("1️⃣ 执行可能出错的对话...")
    print("-" * 50)
    
    # 模拟正常对话
    normal_steps = [
        "你好，我想学习编程",
        "我没有任何基础",
        "请给我一个学习计划"
    ]
    
    for i, message in enumerate(normal_steps):
        result = app.invoke(
            {
                "messages": [HumanMessage(content=message)],
                "step": i,
                "branch_id": "normal",
                "metadata": {}
            },
            config={"configurable": {"thread_id": thread_id}}
        )
        # 修复：正确访问消息内容
        print(f"步骤 {i+1}: {result['messages'][-1].content}")
    
    print("\n2️⃣ 模拟错误情况...")
    print("-" * 50)
    
    # 模拟错误（用户提供错误信息）
    error_message = "我想学习量子计算，请给我一个详细的计划"
    
    try:
        error_result = app.invoke(
            {
                "messages": [HumanMessage(content=error_message)],
                "step": 3,
                "branch_id": "error_branch",
                "metadata": {"error_type": "unrealistic_request"}
            },
            config={"configurable": {"thread_id": thread_id}}
        )
        # 修复：正确访问消息内容
        print(f"错误步骤: {error_result['messages'][-1].content}")
    except Exception as e:
        print(f"发生错误: {e}")
    
    print("\n3️⃣ 从错误点重放...")
    print("-" * 50)
    
    # 从第3步重放，提供更合理的要求
    recovery_message = "我想学习Python基础，请给我一个适合初学者的计划"
    
    recovery_result = app.invoke(
        {
            "messages": [HumanMessage(content=recovery_message)],
            "step": 3,
            "branch_id": "recovery_branch",
            "metadata": {"recovery_from": 3, "error_fixed": True}
        },
        config={
            "configurable": {"thread_id": f"{thread_id}_recovery"},
            "metadata": {"recovery_from": 3}
        }
    )
    # 修复：正确访问消息内容
    print(f"恢复结果: {recovery_result['messages'][-1].content}")

def state_comparison_demo():
    """状态比较演示"""
    print("\n=== 状态比较演示 ===")
    print("展示不同分支状态的比较")
    print()
    
    checkpointer = MemorySaver()
    workflow = create_workflow()
    app = workflow.compile(checkpointer=checkpointer)
    
    # 创建两个不同的分支
    branches = {
        "conservative": ["保守投资", "低风险", "稳定收益"],
        "aggressive": ["激进投资", "高风险", "高收益"]
    }
    
    print("📊 创建不同投资策略分支...")
    print("-" * 50)
    
    branch_states = {}
    
    for branch_name, steps in branches.items():
        print(f"\n💰 分支: {branch_name}")
        thread_id = f"investment_{branch_name}"
        
        for i, step in enumerate(steps):
            result = app.invoke(
                {
                    "messages": [HumanMessage(content=step)],
                    "step": i,
                    "branch_id": branch_name,
                    "metadata": {"strategy": branch_name}
                },
                config={"configurable": {"thread_id": thread_id}}
            )
            print(f"  步骤 {i+1}: {result['messages'][-1].content}")
        
        # 保存分支状态
        try:
            branch_states[branch_name] = app.get_state(
                config={"configurable": {"thread_id": thread_id}}
            )
        except Exception as e:
            print(f"  获取分支状态时出错: {e}")
            branch_states[branch_name] = []
    
    print("\n📈 比较不同分支...")
    print("-" * 50)
    
    for branch_name, states in branch_states.items():
        print(f"\n分支 {branch_name}:")
        print(f"  步骤数: {len(states)}")
        print(f"  状态类型: {type(states).__name__}")
        
        # 简化：只显示基本信息，避免复杂的状态访问
        if states:
            print(f"  有保存的状态")
        else:
            print(f"  无保存的状态")
    
    print("\n🔄 创建比较分析...")
    print("-" * 50)
    
    comparison_prompt = """
基于两个不同的投资策略分支，请提供一个综合的投资建议。
考虑：
1. 两种策略的优缺点
2. 适合的投资者类型
3. 风险收益平衡
4. 实际应用建议
"""
    
    try:
        comparison_result = app.invoke(
            {
                "messages": [HumanMessage(content=comparison_prompt)],
                "step": 0,
                "branch_id": "comparison",
                "metadata": {"compare_branches": list(branches.keys())}
            },
            config={"configurable": {"thread_id": "comparison_analysis"}}
        )
        print(f"比较分析结果:\n{comparison_result['messages'][-1].content}")
    except Exception as e:
        print(f"创建比较分析时出错: {e}")

def main():
    """主函数"""
    basic_time_travel_demo()
    advanced_branching_demo()
    error_recovery_demo()
    state_comparison_demo()

if __name__ == "__main__":
    main()