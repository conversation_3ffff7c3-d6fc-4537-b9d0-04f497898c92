"""
数据库连接和初始化模块
管理MySQL和Milvus数据库的连接和初始化
"""
import logging
from typing import Generator
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from pymilvus import connections, Collection, utility
import pymysql

from rag_action.core.config import get_settings
from rag_action.models.database import Base, MilvusSchema

logger = logging.getLogger(__name__)
settings = get_settings()

# MySQL数据库引擎
engine = create_engine(
    settings.mysql.database_url,
    echo=settings.mysql.echo,
    pool_pre_ping=True,
    pool_recycle=300
)

# 会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db() -> Generator[Session, None, None]:
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.mysql_initialized = False
        self.milvus_initialized = False
    
    def init_mysql(self) -> bool:
        """初始化MySQL数据库"""
        try:
            # 检查数据库是否存在，不存在则创建
            self._create_database_if_not_exists()
            
            # 创建所有表
            Base.metadata.create_all(bind=engine)
            
            # 测试连接
            with engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            
            self.mysql_initialized = True
            logger.info("MySQL数据库初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"MySQL数据库初始化失败: {e}")
            return False
    
    def _create_database_if_not_exists(self):
        """创建数据库（如果不存在）"""
        try:
            # 连接到MySQL服务器（不指定数据库）
            server_url = f"mysql+pymysql://{settings.mysql.user}:{settings.mysql.password}@{settings.mysql.host}:{settings.mysql.port}/?charset={settings.mysql.charset}"
            server_engine = create_engine(server_url)
            
            with server_engine.connect() as conn:
                # 检查数据库是否存在
                result = conn.execute(text(f"SHOW DATABASES LIKE '{settings.mysql.database}'"))
                if not result.fetchone():
                    # 创建数据库
                    conn.execute(text(f"CREATE DATABASE {settings.mysql.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
                    logger.info(f"创建数据库: {settings.mysql.database}")
                    
        except Exception as e:
            logger.error(f"创建数据库失败: {e}")
            raise
    
    def init_milvus(self) -> bool:
        """初始化Milvus向量数据库"""
        try:
            # 连接到Milvus
            connections.connect(
                alias="default",
                host=settings.milvus.host,
                port=settings.milvus.port
            )
            
            # 检查集合是否存在
            collection_name = settings.milvus.collection_name
            if utility.has_collection(collection_name):
                logger.info(f"Milvus集合 '{collection_name}' 已存在")
                collection = Collection(collection_name)
            else:
                # 创建集合
                schema = MilvusSchema.get_collection_schema()
                collection = Collection(
                    name=collection_name,
                    schema=schema,
                    using='default'
                )
                logger.info(f"创建Milvus集合: {collection_name}")
            
            # 检查并创建索引
            try:
                # 检查embedding字段是否有索引
                embedding_has_index = False
                try:
                    indexes = collection.indexes
                    for index in indexes:
                        if index.field_name == "embedding":
                            embedding_has_index = True
                            break
                except:
                    embedding_has_index = False

                if not embedding_has_index:
                    index_params = MilvusSchema.get_index_params()
                    collection.create_index(
                        field_name="embedding",
                        index_params=index_params,
                        index_name="embedding_hnsw_index"
                    )
                    logger.info("创建Milvus embedding索引")
                else:
                    logger.info("Milvus embedding索引已存在")
            except Exception as index_error:
                logger.warning(f"索引检查/创建失败: {index_error}")
                # 继续执行，不因为索引问题阻止启动
            
            # 加载集合到内存
            collection.load()
            
            self.milvus_initialized = True
            logger.info("Milvus向量数据库初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"Milvus向量数据库初始化失败: {e}")
            return False
    
    def init_all(self) -> bool:
        """初始化所有数据库"""
        mysql_ok = self.init_mysql()
        milvus_ok = self.init_milvus()
        
        if mysql_ok and milvus_ok:
            logger.info("所有数据库初始化成功")
            return True
        else:
            logger.error("数据库初始化失败")
            return False
    
    def check_mysql_health(self) -> bool:
        """检查MySQL健康状态"""
        try:
            with engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            return True
        except Exception as e:
            logger.error(f"MySQL健康检查失败: {e}")
            return False
    
    def check_milvus_health(self) -> bool:
        """检查Milvus健康状态"""
        try:
            # 确保连接存在
            if not connections.has_connection("default"):
                connections.connect(
                    alias="default",
                    host=settings.milvus.host,
                    port=settings.milvus.port
                )

            # 检查集合是否存在
            if not utility.has_collection(settings.milvus.collection_name):
                return False

            collection = Collection(settings.milvus.collection_name)
            collection.num_entities  # 获取实体数量来测试连接
            return True
        except Exception as e:
            logger.error(f"Milvus健康检查失败: {e}")
            return False
    
    def get_mysql_stats(self) -> dict:
        """获取MySQL统计信息"""
        try:
            with SessionLocal() as db:
                # 获取各表的记录数
                from rag_action.models.database import Note, Tag, NoteChunk
                
                notes_count = db.query(Note).count()
                tags_count = db.query(Tag).count()
                chunks_count = db.query(NoteChunk).count()
                
                return {
                    "notes_count": notes_count,
                    "tags_count": tags_count,
                    "chunks_count": chunks_count
                }
        except Exception as e:
            logger.error(f"获取MySQL统计信息失败: {e}")
            return {}
    
    def get_milvus_stats(self) -> dict:
        """获取Milvus统计信息"""
        try:
            # 确保连接存在
            if not connections.has_connection("default"):
                connections.connect(
                    alias="default",
                    host=settings.milvus.host,
                    port=settings.milvus.port
                )

            if not utility.has_collection(settings.milvus.collection_name):
                return {
                    "collection_name": settings.milvus.collection_name,
                    "num_entities": 0,
                    "is_loaded": False
                }

            collection = Collection(settings.milvus.collection_name)
            return {
                "collection_name": settings.milvus.collection_name,
                "num_entities": collection.num_entities,
                "is_loaded": True  # 简化处理，避免版本兼容问题
            }
        except Exception as e:
            logger.error(f"获取Milvus统计信息失败: {e}")
            return {}


# 全局数据库管理器实例
db_manager = DatabaseManager()
