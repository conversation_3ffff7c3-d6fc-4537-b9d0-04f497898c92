#!/usr/bin/env python
# coding: utf-8

# # Ollama DeepSeek-R1:7B Agent 实战：小红书爆款文案生成助手
# 
# 本文件将指导您如何使用 Ollama 的 DeepSeek-R1:7B 模型构建一个能够生成小红书爆款文案的智能 Agent。
# 基于原有的 rednote.py，但使用本地部署的 Ollama 模型。

import os
import json
import re
import time
import random
from langchain_community.llms import Ollama
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain.tools import tool

# mock 工具函数（如有需要可移到单独文件）
def mock_search_web(query: str) -> str:
    """模拟网页搜索工具，返回预设的搜索结果。"""
    print(f"[Tool Call] 模拟搜索网页：{query}")
    time.sleep(1) # 模拟网络延迟
    if "小红书美妆趋势" in query:
        return "近期小红书美妆流行'多巴胺穿搭'、'早C晚A'护肤理念、'伪素颜'妆容，热门关键词有#氛围感、#抗老、#屏障修复。"
    elif "保湿面膜" in query:
        return "小红书保湿面膜热门话题：沙漠干皮救星、熬夜急救面膜、水光肌养成。用户痛点：卡粉、泛红、紧绷感。"
    elif "深海蓝藻保湿面膜" in query:
        return "关于深海蓝藻保湿面膜的用户评价：普遍反馈补水效果好，吸收快，对敏感肌友好。有用户提到价格略高，但效果值得。"
    else:
        return f"未找到关于 '{query}' 的特定信息，但市场反馈通常关注产品成分、功效和用户体验。"

def mock_query_product_database(product_name: str) -> str:
    """模拟查询产品数据库，返回预设的产品信息。"""
    print(f"[Tool Call] 模拟查询产品数据库：{product_name}")
    time.sleep(0.5) # 模拟数据库查询延迟
    if "深海蓝藻保湿面膜" in product_name:
        return "深海蓝藻保湿面膜：核心成分为深海蓝藻提取物，富含多糖和氨基酸，能深层补水、修护肌肤屏障、舒缓敏感泛红。质地清爽不粘腻，适合所有肤质，尤其适合干燥、敏感肌。规格：25ml*5片。"
    elif "美白精华" in product_name:
        return "美白精华：核心成分是烟酰胺和VC衍生物，主要功效是提亮肤色、淡化痘印、改善暗沉。质地轻薄易吸收，适合需要均匀肤色的人群。"
    else:
        return f"产品数据库中未找到关于 '{product_name}' 的详细信息。"

def mock_generate_emoji(context: str) -> list:
    """模拟生成表情符号，根据上下文提供常用表情。"""
    print(f"[Tool Call] 模拟生成表情符号，上下文：{context}")
    time.sleep(0.2) # 模拟生成延迟
    if "补水" in context or "水润" in context or "保湿" in context:
        return ["💦", "💧", "🌊", "✨"]
    elif "惊喜" in context or "哇塞" in context or "爱了" in context:
        return ["💖", "😍", "🤩", "💯"]
    elif "熬夜" in context or "疲惫" in context:
        return ["😭", "😮‍💨", "😴", "💡"]
    elif "好物" in context or "推荐" in context:
        return ["✅", "👍", "⭐", "🛍️"]
    else:
        return random.sample(["✨", "🔥", "💖", "💯", "🎉", "👍", "🤩", "💧", "🌿"], k=min(5, len(context.split())))

# 简化的系统提示词
SYSTEM_PROMPT = """
你是一个资深的小红书爆款文案专家。

请按照以下格式输出JSON：
{
  "title": "吸引人的标题，包含表情符号",
  "body": "文案正文，分段清晰，语言活泼真诚",
  "hashtags": ["#标签1", "#标签2", "#标签3", "#标签4", "#标签5"],
  "emojis": ["表情1", "表情2", "表情3", "表情4", "表情5"]
}

要求：
1. 标题要吸引人，包含相关表情符号
2. 正文要分段，语言活泼真诚，富有感染力
3. 至少5个相关标签，要热门且相关
4. 至少5个表情符号，要与内容匹配
5. 确保JSON格式正确，可以被解析
"""

def get_ollama_client():
    """获取 Ollama 客户端，使用 DeepSeek-R1:7B 模型"""
    # 检查 Ollama 服务是否可用
    base_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    model_name = os.getenv("OLLAMA_MODEL", "deepseek-r1:7b")
    
    try:
        client = Ollama(
            base_url=base_url,
            model=model_name,
            temperature=0.7,
            # 对于本地模型，可能需要调整一些参数
            num_ctx=4096,  # 上下文窗口大小
        )
        return client
    except Exception as e:
        raise ValueError(f"无法连接到 Ollama 服务: {e}")

client = get_ollama_client()

@tool
def search_web_tool(query: str) -> str:
    """搜索互联网上的实时信息。"""
    return mock_search_web(query)

@tool
def query_product_database_tool(product_name: str) -> str:
    """查询内部产品数据库。"""
    return mock_query_product_database(product_name)

@tool
def generate_emoji_tool(context: str) -> str:
    """根据内容生成表情符号。"""
    emojis = mock_generate_emoji(context)
    return " ".join(emojis)

# 简化的生成函数
def generate_rednote_simple(product_name: str, tone_style: str = "活泼甜美") -> str:
    """使用简化的方法生成小红书爆款文案（适合推理模型）"""
    client = get_ollama_client()
    
    # 创建增强提示词
    prompt = f"""
{SYSTEM_PROMPT}

请为产品「{product_name}」生成一篇{tone_style}风格的小红书笔记。

产品信息：
- 产品名称：{product_name}
- 文案风格：{tone_style}

请直接输出JSON格式的文案。
"""
    
    response = client.invoke(prompt)
    result = response.strip()
    
    # 多种方式尝试解析JSON
    json_patterns = [
        r'```json\s*(\{.*?\})\s*```',  # 代码块中的JSON
        r'```\s*(\{.*?\})\s*```',      # 代码块中的内容
        r'(\{.*\})',                   # 直接的JSON
    ]
    
    for pattern in json_patterns:
        match = re.search(pattern, result, re.DOTALL)
        if match:
            try:
                json_content = match.group(1)
                parsed_json = json.loads(json_content)
                return json.dumps(parsed_json, ensure_ascii=False, indent=2)
            except json.JSONDecodeError:
                continue
    
    # 如果所有模式都失败，尝试直接解析
    try:
        parsed_json = json.loads(result)
        return json.dumps(parsed_json, ensure_ascii=False, indent=2)
    except json.JSONDecodeError:
        return result

def format_rednote_for_markdown(json_string: str) -> str:
    """
    将 JSON 格式的小红书文案转换为 Markdown 格式。
    """
    try:
        data = json.loads(json_string)
    except json.JSONDecodeError as e:
        return f"错误：无法解析 JSON 字符串 - {e}\n原始字符串：\n{json_string}"
    
    title = data.get("title", "无标题")
    body = data.get("body", "")
    hashtags = data.get("hashtags", [])
    
    markdown_output = f"## {title}\n\n{body}\n\n"
    if hashtags:
        hashtag_string = " ".join(hashtags)
        markdown_output += f"{hashtag_string}\n"
    
    return markdown_output.strip()

def test_ollama_connection():
    """测试 Ollama 连接是否正常"""
    try:
        client = get_ollama_client()
        # 简单的测试查询
        test_response = client.invoke("你好，请简单回复一下")
        print(f"✅ Ollama 连接测试成功！模型回复: {test_response[:50]}...")
        return True
    except Exception as e:
        print(f"❌ Ollama 连接测试失败: {e}")
        print("请确保：")
        print("1. Ollama 服务已启动 (ollama serve)")
        print("2. DeepSeek-R1:7B 模型已下载 (ollama pull deepseek-r1:7b)")
        print("3. 服务地址正确 (默认: http://localhost:11434)")
        return False

if __name__ == "__main__":
    # 首先测试连接
    if not test_ollama_connection():
        exit(1)
    
    print("\n" + "="*50)
    print("开始生成小红书文案...")
    print("="*50)
    
    # 示例：生成两种风格的文案
    product_name_1 = "深海蓝藻保湿面膜"
    tone_style_1 = "活泼甜美"
    result_1 = generate_rednote_simple(product_name_1, tone_style_1)
    print("\n--- 生成的文案 1 ---")
    print(result_1)
    print("\n--- Markdown 格式 ---")
    print(format_rednote_for_markdown(result_1))

    print("\n" + "-"*50)
    
    product_name_2 = "美白精华"
    tone_style_2 = "知性温柔"
    result_2 = generate_rednote_simple(product_name_2, tone_style_2)
    print("\n--- 生成的文案 2 ---")
    print(result_2)
    print("\n--- Markdown 格式 ---")
    print(format_rednote_for_markdown(result_2)) 