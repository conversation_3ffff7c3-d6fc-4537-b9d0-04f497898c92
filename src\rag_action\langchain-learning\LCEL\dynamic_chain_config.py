
"""
应用场景：
1. 智能问答系统：根据用户需求动态组合不同的分析、总结、推荐等链，实现灵活的多步推理。
2. 企业知识库：可根据业务变化，随时添加或移除新的处理链，无需重启服务。
3. 多模型协作：支持在运行时切换或组合不同模型链，满足多样化任务。
4. 个性化定制：允许用户自定义链的组合和流程，提升系统适应性和扩展性。

好处：
- 灵活性高：无需修改主程序即可增删链，适应业务快速变化。
- 易于扩展：新功能链可随时集成，支持插件式开发。
- 降低维护成本：链的配置和组合可通过配置文件或接口动态管理，便于维护和升级。
- 支持多场景复用：同一套链组件可按需组合，适配不同业务流程。
"""

from langchain_core.output_parsers import StrOutputParser
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableParallel, RunnablePassthrough
from typing import Dict, Any, List, Callable
import json

# 创建 LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0
)

class DynamicChainBuilder:
    """动态链构建器"""
    
    def __init__(self):
        self.chains = {}
        self.configs = {}
    
    def add_chain(self, name: str, prompt_template: str, **kwargs):
        """动态添加链"""
        chain = ChatPromptTemplate.from_template(prompt_template) | llm | StrOutputParser()
        self.chains[name] = chain
        self.configs[name] = kwargs
        print(f"✅ 已添加链: {name}")
    
    def remove_chain(self, name: str):
        """移除链"""
        if name in self.chains:
            del self.chains[name]
            del self.configs[name]
            print(f"❌ 已移除链: {name}")
        else:
            print(f"⚠️ 链 {name} 不存在")
    
    def build_parallel_chain(self, chain_names: List[str] = None):
        """构建并行链"""
        if chain_names is None:
            chain_names = list(self.chains.keys())
        
        if not chain_names:
            raise ValueError("没有可用的链")
        
        # 动态构建并行链
        parallel_chains = {}
        for name in chain_names:
            if name in self.chains:
                parallel_chains[name] = self.chains[name]
        
        return RunnableParallel(parallel_chains)
    
    def build_sequential_chain(self, chain_names: List[str]):
        """构建顺序链"""
        if not chain_names:
            raise ValueError("链名称列表不能为空")
        
        # 动态构建顺序链
        chain = self.chains[chain_names[0]]
        for name in chain_names[1:]:
            if name in self.chains:
                chain = chain | self.chains[name]
        
        return chain
    
    def get_chain_info(self):
        """获取链信息"""
        return {
            "available_chains": list(self.chains.keys()),
            "configs": self.configs
        }

def basic_dynamic_example():
    """基础动态链示例"""
    
    print("=== 基础动态链示例 ===")
    
    builder = DynamicChainBuilder()
    
    # 动态添加链
    builder.add_chain(
        "analyze", 
        "请分析主题：{topic}",
        description="分析主题"
    )
    
    builder.add_chain(
        "summarize", 
        "请总结：{topic}",
        description="总结主题"
    )
    
    builder.add_chain(
        "explain", 
        "请详细解释：{topic}",
        description="详细解释"
    )
    
    # 构建并行链
    parallel_chain = builder.build_parallel_chain()
    result = parallel_chain.invoke({"topic": "人工智能"})
    
    print("并行链结果:")
    for key, value in result.items():
        print(f"  {key}: {value[:100]}...")
    
    # 构建顺序链
    sequential_chain = builder.build_sequential_chain(["analyze", "summarize"])
    result = sequential_chain.invoke({"topic": "机器学习"})
    print(f"\n顺序链结果: {result[:100]}...")

def runtime_config_example():
    """运行时配置示例"""
    
    print("\n=== 运行时配置示例 ===")
    
    builder = DynamicChainBuilder()
    
    # 修复：使用正确的参数名 prompt_template
    configs = [
        {
            "name": "tech_analysis",
            "prompt_template": "请从技术角度分析：{topic}",
            "description": "技术分析"
        },
        {
            "name": "business_analysis", 
            "prompt_template": "请从商业角度分析：{topic}",
            "description": "商业分析"
        },
        {
            "name": "future_prediction",
            "prompt_template": "请预测 {topic} 的未来发展",
            "description": "未来预测"
        }
    ]
    
    # 动态添加配置
    for config in configs:
        builder.add_chain(**config)
    
    # 运行时选择链
    selected_chains = ["tech_analysis", "business_analysis"]
    chain = builder.build_parallel_chain(selected_chains)
    
    result = chain.invoke({"topic": "区块链技术"})
    print("选择的链结果:")
    for key, value in result.items():
        print(f"  {key}: {value[:100]}...")

def conditional_chain_building():
    """条件链构建示例"""
    
    print("\n=== 条件链构建示例 ===")
    
    builder = DynamicChainBuilder()
    
    # 添加不同类型的链
    builder.add_chain("simple", "请简单介绍：{topic}")
    builder.add_chain("detailed", "请详细介绍：{topic}")
    builder.add_chain("technical", "请从技术角度分析：{topic}")
    builder.add_chain("business", "请从商业角度分析：{topic}")
    
    def build_chain_by_topic(topic: str, complexity: str = "simple"):
        """根据主题和复杂度构建链"""
        
        if complexity == "simple":
            return builder.build_parallel_chain(["simple"])
        elif complexity == "detailed":
            return builder.build_parallel_chain(["detailed"])
        elif complexity == "technical":
            return builder.build_parallel_chain(["technical"])
        elif complexity == "comprehensive":
            return builder.build_parallel_chain(["detailed", "technical", "business"])
        else:
            return builder.build_parallel_chain(["simple"])
    
    # 测试不同复杂度
    complexities = ["simple", "detailed", "comprehensive"]
    topic = "人工智能"
    
    for complexity in complexities:
        print(f"\n复杂度: {complexity}")
        chain = build_chain_by_topic(topic, complexity)
        result = chain.invoke({"topic": topic})
        
        if isinstance(result, dict):
            for key, value in result.items():
                print(f"  {key}: {value[:80]}...")
        else:
            print(f"  结果: {result[:100]}...")

def dynamic_chain_modification():
    """动态链修改示例"""
    
    print("\n=== 动态链修改示例 ===")
    
    builder = DynamicChainBuilder()
    
    # 初始链
    builder.add_chain("initial", "请分析：{topic}")
    
    # 构建初始链
    chain = builder.build_parallel_chain()
    result = chain.invoke({"topic": "机器学习"})
    print("初始结果:", result["initial"][:100] + "...")
    
    # 动态添加新链
    builder.add_chain("enhanced", "请深入分析：{topic}")
    builder.add_chain("summary", "请总结：{topic}")
    
    # 重新构建链
    new_chain = builder.build_parallel_chain()
    result = new_chain.invoke({"topic": "深度学习"})
    print("\n增强后结果:")
    for key, value in result.items():
        print(f"  {key}: {value[:80]}...")
    
    # 移除链
    builder.remove_chain("initial")
    final_chain = builder.build_parallel_chain()
    result = final_chain.invoke({"topic": "神经网络"})
    print("\n移除后结果:")
    for key, value in result.items():
        print(f"  {key}: {value[:80]}...")

def config_from_json():
    """从 JSON 配置动态构建链"""
    
    print("\n=== 从 JSON 配置动态构建链 ===")
    
    # 修复：使用正确的参数名 prompt_template
    json_config = '''
    {
        "chains": [
            {
                "name": "analysis",
                "prompt_template": "请分析 {topic} 的优势和劣势",
                "description": "优势劣势分析"
            },
            {
                "name": "comparison", 
                "prompt_template": "请比较 {topic} 与其他技术的差异",
                "description": "技术比较"
            },
            {
                "name": "recommendation",
                "prompt_template": "请为 {topic} 提供建议",
                "description": "建议推荐"
            }
        ],
        "default_combination": ["analysis", "recommendation"]
    }
    '''
    
    config = json.loads(json_config)
    builder = DynamicChainBuilder()
    
    # 从配置动态添加链
    for chain_config in config["chains"]:
        builder.add_chain(**chain_config)
    
    # 使用默认组合
    default_chain = builder.build_parallel_chain(config["default_combination"])
    result = default_chain.invoke({"topic": "云计算"})
    
    print("默认组合结果:")
    for key, value in result.items():
        print(f"  {key}: {value[:80]}...")
    
    # 使用所有链
    all_chain = builder.build_parallel_chain()
    result = all_chain.invoke({"topic": "大数据"})
    
    print("\n所有链结果:")
    for key, value in result.items():
        print(f"  {key}: {value[:80]}...")

def main():
    """主函数"""
    basic_dynamic_example()
    runtime_config_example()
    conditional_chain_building()
    dynamic_chain_modification()
    config_from_json()

if __name__ == "__main__":
    main() 