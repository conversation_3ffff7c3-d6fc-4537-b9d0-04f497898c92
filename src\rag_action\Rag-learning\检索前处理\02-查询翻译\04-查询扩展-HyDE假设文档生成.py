"""
详细流程说明：

1. 用户输入一个关于游戏的问题（如“黑神话悟空中的主角有哪些主要技能？”）。
2. 系统首先通过HyDE（Hypothetical Document Embeddings）思路，利用大模型根据用户问题生成一段与问题相关的假设文档（即“假设答案”）。
3. 将生成的假设文档与原始向量数据库中的文档进行向量化检索，找到与假设文档最相关的真实资料片段。
4. 将检索到的相关文档片段与用户原始问题一起，拼接成最终的prompt，输入给大模型进行问答生成。
5. 大模型基于真实资料和用户问题，输出最终的中文回答。

核心流程分为三步：
- HyDE假设文档生成：用大模型根据问题生成一段“假设内容”，以此作为检索线索。
- 检索：用假设文档在向量数据库中查找最相关的真实资料。
- 回答生成：将检索到的资料和原始问题输入大模型，生成最终答案。

这样做的好处是：即使原始资料中没有直接覆盖用户问题的表述，HyDE生成的假设文档可以作为“桥梁”，提升检索的相关性和召回率，最终让大模型基于更相关的内容给出高质量回答。
"""

from langchain.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_openai import ChatOpenAI
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_community.document_loaders import TextLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import Chroma
# 加载文档并构建向量数据库
loader = TextLoader("C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/黑悟空wiki.txt", encoding='utf-8')
data = loader.load()
text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=0)
splits = text_splitter.split_documents(data)

embed_model = HuggingFaceEmbeddings(model_name="BAAI/bge-small-zh") 
vectordb = Chroma.from_documents(documents=splits, embedding= embed_model)
# HyDE文档生成模板
# 1. 构建HyDE假设文档生成的Prompt模板
template = """请根据下方用户问题，撰写一段与该问题高度相关的游戏内容（可虚构但要合理），用于后续检索相关资料：
问题：{question}
内容："""
prompt_hyde = ChatPromptTemplate.from_template(template)

# 2. 初始化大模型（LLM）用于HyDE假设文档生成和最终问答
llm = ChatOpenAI(
    model="gpt-4o-mini",
    temperature=0,
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",  # 请替换为你的API Key
    base_url="https://api.zhizengzeng.com/v1"
)

# 3. 构建HyDE假设文档生成链：输入问题，输出一段假设内容
generate_docs_for_retrieval = (
    prompt_hyde
    | llm
    | StrOutputParser()
)

# 4. 用户输入示例问题
question = "黑神话悟空中的主角有哪些主要技能？"

# 5. 步骤一：生成HyDE假设文档
print("\n=== 步骤一：HyDE假设文档生成 ===")
generated_doc = generate_docs_for_retrieval.invoke({"question": question})
print(generated_doc)

# 6. 步骤二：用假设文档进行向量检索，查找最相关的真实资料
print("\n=== 步骤二：用假设文档检索相关资料 ===")
retriever = vectordb.as_retriever()
# 这里将假设文档作为检索线索，查找与之最相关的真实文档
retrieved_docs = retriever.invoke(generated_doc)
if not isinstance(retrieved_docs, list):
    retrieved_docs = [retrieved_docs]
for i, doc in enumerate(retrieved_docs, 1):
    print(f"\n文档 {i}:")
    print(doc.page_content)

# 7. 步骤三：将检索到的资料与原始问题拼接，输入大模型生成最终答案
print("\n=== 步骤三：基于资料和问题生成最终答案 ===")
# 整理检索到的内容
context = "\n\n".join([doc.page_content for doc in retrieved_docs])
# 构建最终问答的Prompt模板
answer_template = """请根据下方提供的真实资料内容，结合用户问题，给出准确、简明的中文回答。
资料内容：
{context}
用户问题：{question}
请直接给出答案："""
answer_prompt = ChatPromptTemplate.from_template(answer_template)
# 创建最终的问答链
final_rag_chain = (
    answer_prompt
    | llm
    | StrOutputParser()
)
# 获取最终答案
final_answer = final_rag_chain.invoke({"context": context, "question": question})
print(final_answer)
print("\n=== 最终答案 ===")
print(final_answer)
