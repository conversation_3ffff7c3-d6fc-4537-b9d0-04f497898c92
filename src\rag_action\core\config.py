"""
配置管理模块
使用Pydantic加载YAML配置文件，提供全局配置访问
"""
import os
import yaml
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from functools import lru_cache


class AppConfig(BaseModel):
    """应用配置"""
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = True
    title: str = "AI笔记系统"
    description: str = "基于RAG的智能笔记管理系统"
    version: str = "1.0.0"


class MySQLConfig(BaseModel):
    """MySQL数据库配置"""
    host: str = "localhost"
    port: int = 3306
    user: str = "root"
    password: str
    database: str = "ai_notes"
    charset: str = "utf8mb4"
    echo: bool = False

    @property
    def database_url(self) -> str:
        """生成数据库连接URL"""
        return f"mysql+pymysql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}?charset={self.charset}"


class MilvusConfig(BaseModel):
    """Milvus向量数据库配置"""
    host: str = "localhost"
    port: int = 19530
    collection_name: str = "note_embeddings"
    embedding_dim: int = 1536
    index_type: str = "HNSW"
    metric_type: str = "COSINE"

    # BM25全文检索配置
    enable_bm25: bool = True
    bm25_language: str = "chinese"  # 支持chinese, english, standard
    bm25_k1: float = 1.2  # 词频饱和度参数
    bm25_b: float = 0.75  # 文档长度归一化参数
    bm25_drop_ratio: float = 0.2  # 搜索时丢弃低重要性词项的比例

    # 混合检索权重配置
    dense_weight: float = 0.6  # 密集向量权重
    sparse_weight: float = 0.4  # 稀疏向量权重


class EmbeddingConfig(BaseModel):
    """向量嵌入配置"""
    model: str = "openai"
    openai_api_key: str
    base_url: str = "https://api.openai.com/v1"
    model_name: str = "text-embedding-3-small"
    chunk_size: int = 500
    chunk_overlap: int = 100
    max_tokens: int = 8000


class BGEConfig(BaseModel):
    """BGE模型配置"""
    model_name: str = "BAAI/bge-large-zh-v1.5"
    device: str = "cpu"
    normalize_embeddings: bool = True


class LLMConfig(BaseModel):
    """大语言模型配置"""
    provider: str = "openai"
    api_key: str
    base_url: str = "https://api.openai.com/v1"
    model_name: str = "gpt-3.5-turbo"
    temperature: float = 0.7
    max_tokens: int = 2000


class FileConfig(BaseModel):
    """文件处理配置"""
    upload_dir: str = "uploads"
    temp_dir: str = "temp"
    max_file_size: int = 50  # MB
    allowed_extensions: List[str] = [".pdf"]


class LoggingConfig(BaseModel):
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: str = "logs/app.log"


class RAGConfig(BaseModel):
    """RAG检索配置"""
    top_k: int = 5
    similarity_threshold: float = 0.7
    max_context_length: int = 4000
    prompt_template: str = Field(default="""基于以下相关文档内容，请回答用户的问题。如果文档中没有相关信息，请说明无法从提供的文档中找到答案。

相关文档：
{context}

用户问题：{question}

请提供准确、详细的回答：""")


class DocumentParsingConfig(BaseModel):
    """文档解析配置"""
    use_mineru: bool = True
    mineru_config: Dict[str, Any] = {}
    fallback_parser: str = "pymupdf"


class RetrievalPreprocessingConfig(BaseModel):
    """检索前处理配置"""
    query_refinement: Dict[str, Any] = {}
    self_query: Dict[str, Any] = {}
    query_routing: Dict[str, Any] = {}


class EnsembleRetrievalConfig(BaseModel):
    """混合检索配置"""
    enabled: bool = True
    vector: Dict[str, Any] = {}
    bm25: Dict[str, Any] = {}
    final_top_k: int = 5


class RetrievalPostprocessingConfig(BaseModel):
    """检索后处理配置"""
    reranking: Dict[str, Any] = {}
    context_compression: Dict[str, Any] = {}


class EnsembleRetrieverConfig(BaseModel):
    """混合检索器配置"""
    use_milvus_bm25: bool = True
    fallback_to_traditional: bool = True
    dense_top_k: int = 10
    sparse_top_k: int = 10
    final_top_k: int = 5


class Settings(BaseModel):
    """全局配置类"""
    app: AppConfig
    mysql: MySQLConfig
    milvus: MilvusConfig
    embedding: EmbeddingConfig
    bge: BGEConfig
    llm: LLMConfig
    file: FileConfig
    logging: LoggingConfig
    rag: RAGConfig
    ensemble_retriever: EnsembleRetrieverConfig
    document_parsing: DocumentParsingConfig = DocumentParsingConfig()
    retrieval_preprocessing: RetrievalPreprocessingConfig = RetrievalPreprocessingConfig()
    ensemble_retrieval: EnsembleRetrievalConfig = EnsembleRetrievalConfig()
    retrieval_postprocessing: RetrievalPostprocessingConfig = RetrievalPostprocessingConfig()

    @property
    def mysql_url(self) -> str:
        """获取MySQL连接URL（兼容性属性）"""
        return self.mysql.database_url

    @classmethod
    def load_from_yaml(cls, config_path: str = "config.yaml") -> "Settings":
        """从YAML文件加载配置"""
        # 尝试多个可能的配置文件路径
        possible_paths = [
            config_path,
            os.path.join(os.getcwd(), config_path),
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), config_path),
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), config_path)
        ]

        config_file = None
        for path in possible_paths:
            if os.path.exists(path):
                config_file = path
                break

        if not config_file:
            raise FileNotFoundError(f"配置文件不存在，尝试的路径: {possible_paths}")

        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)

        return cls(**config_data)


@lru_cache()
def get_settings() -> Settings:
    """获取全局配置实例（单例模式）"""
    return Settings.load_from_yaml()


# 全局配置实例
settings = get_settings()
