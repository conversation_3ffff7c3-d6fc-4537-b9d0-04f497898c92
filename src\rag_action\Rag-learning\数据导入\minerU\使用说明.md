# PDF转Markdown工具使用指南

## 📁 文件说明

- `基础转换.py` - 基础转换脚本（推荐新手）
- `高质量转换.py` - 高质量转换脚本（VLM模式）
- `批量转换.py` - 批量处理脚本（并发处理）
- `高级转换.py` - 高级配置脚本（命令行参数）
- `配置文件.py` - 配置文件
- `使用示例.py` - 使用示例脚本
- `使用说明.md` - 详细使用说明文档
- `minerU_pdf2markdown.py` - 核心转换模块

## 🚀 快速开始

### 1. 安装依赖
```bash
uv add "mineru[core]" --default-index https://pypi.tuna.tsinghua.edu.cn/simple
```

### 2. 准备文件
```bash
# 创建目录结构
mkdir pdfs
mkdir output

# 将PDF文件放入pdfs目录
cp your_document.pdf pdfs/
```

### 3. 运行转换

#### 基础转换（推荐新手）
```bash
python 基础转换.py
```

#### 高质量转换
```bash
python 高质量转换.py
```

#### 批量处理
```bash
python 批量转换.py
```

#### 高级配置
```bash
# 转换英文文档
python 高级转换.py --lang en

# 使用VLM模式
python 高级转换.py --backend vlm-transformers

# 指定页面范围
python 高级转换.py --start-page 1 --end-page 10

# 查看所有选项
python 高级转换.py --help
```

## 🎯 模式对比

| 模式 | 速度 | 质量 | 适用场景 |
|------|------|------|----------|
| pipeline | 快 | 中等 | 标准文档、批量处理 |
| vlm-transformers | 慢 | 高 | 复杂文档、高质量要求 |
| vlm-sglang-engine | 中等 | 高 | 平衡性能和质量 |
| vlm-sglang-client | 快 | 高 | 需要服务器支持 |

## 📂 输出文件

转换完成后会生成：
- `.md` - Markdown文件
- `_middle.json` - 结构化数据
- `_content_list.json` - 内容列表
- `images/` - 提取的图片

## 🔧 故障排除

### 网络问题
```bash
# 设置环境变量使用国内源
export MINERU_MODEL_SOURCE="modelscope"
```

### 内存不足
```bash
# 使用pipeline模式
python 基础转换.py
```

### 转换质量不高
```bash
# 使用VLM模式
python 高质量转换.py
```

## 📊 使用效果对比

| 文档类型 | Pipeline模式 | VLM模式 | 推荐 |
|----------|-------------|---------|------|
| 标准文档 | ✅ 好 | ✅ 很好 | Pipeline |
| 学术论文 | ⚠️ 一般 | ✅ 很好 | VLM |
| 设计文档 | ❌ 差 | ✅ 很好 | VLM |
| 表格密集 | ⚠️ 一般 | ✅ 很好 | VLM |
| 公式文档 | ⚠️ 一般 | ✅ 很好 | VLM |

## 🛠️ 高级用法

### 使用配置文件
```python
from 配置文件 import get_config, setup_environment

# 设置环境
setup_environment()

# 获取配置
config = get_config("academic")
print(config)
```

### 自定义转换
```python
from minerU_pdf2markdown import parse_doc
from pathlib import Path

# 自定义转换
parse_doc(
    path_list=[Path("document.pdf")],
    output_dir="custom_output",
    lang="en",
    backend="vlm-transformers",
    start_page_id=1,
    end_page_id=10
)
```

## 📝 日志文件

- `conversion.log` - 基础转换日志
- `batch_conversion.log` - 批量转换日志
- `advanced_conversion.log` - 高级转换日志

## 🆘 支持

如有问题，请查看：
1. 日志文件中的错误信息
2. 确保已正确安装 mineru 依赖
3. 检查PDF文件是否损坏
4. 尝试不同的后端模式

## 📚 更多信息

- [minerU 官方文档](https://github.com/opendatalab/minerU)
- [PyPI 包页面](https://pypi.org/project/mineru/) 