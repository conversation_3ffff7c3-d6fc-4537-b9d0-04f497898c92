"""
LangChain集成的文档解析服务
使用LangChain的文档加载器和文本分割器
"""
import os
import logging
import tempfile
from typing import Dict, List, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

# 导入LangChain文档加载器
try:
    from langchain_community.document_loaders import PyMuPDFLoader, TextLoader
    from langchain.text_splitter import RecursiveCharacterTextSplitter, CharacterTextSplitter
    from langchain_core.documents import Document
    LANGCHAIN_AVAILABLE = True
    logger.info("LangChain文档处理组件可用")
except ImportError:
    LANGCHAIN_AVAILABLE = False
    logger.warning("LangChain文档处理组件不可用")

# 导入PyMuPDF作为备选
try:
    import pymupdf
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    logger.error("PyMuPDF也不可用，PDF解析功能将不可用")


class LangChainDocumentParser:
    """LangChain集成的文档解析器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.use_langchain = config.get("use_langchain", True) and LANGCHAIN_AVAILABLE
        self.chunk_size = config.get("chunk_size", 1000)
        self.chunk_overlap = config.get("chunk_overlap", 200)
        
        # 初始化文本分割器
        if self.use_langchain:
            self.text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=self.chunk_size,
                chunk_overlap=self.chunk_overlap,
                length_function=len,
                separators=["\n\n", "\n", " ", ""]
            )
        
        logger.info(f"LangChain文档解析器初始化: 使用LangChain={'是' if self.use_langchain else '否'}")
    
    def parse_pdf(self, file_path: str, filename: str = "") -> Dict[str, Any]:
        """
        解析PDF文件
        
        Args:
            file_path: PDF文件路径
            filename: 文件名（用于元数据）
            
        Returns:
            解析结果字典，包含页面映射和元数据
        """
        try:
            if self.use_langchain:
                return self._parse_with_langchain(file_path, filename)
            else:
                return self._parse_with_pymupdf(file_path, filename)
        except Exception as e:
            logger.error(f"PDF解析失败: {e}")
            # 尝试备选解析器
            if self.use_langchain and PYMUPDF_AVAILABLE:
                logger.info("尝试使用PyMuPDF备选解析器")
                return self._parse_with_pymupdf(file_path, filename)
            raise
    
    def _parse_with_langchain(self, file_path: str, filename: str) -> Dict[str, Any]:
        """使用LangChain解析PDF"""
        if not LANGCHAIN_AVAILABLE:
            raise ImportError("LangChain不可用")
        
        logger.info(f"使用LangChain解析PDF: {filename}")
        
        try:
            # 使用LangChain的PyMuPDFLoader
            loader = PyMuPDFLoader(file_path)
            documents = loader.load()
            
            # 处理文档
            page_map = []
            full_text = ""
            
            for i, doc in enumerate(documents, 1):
                page_text = doc.page_content
                page_map.append({
                    "page": i,
                    "text": page_text,
                    "metadata": doc.metadata
                })
                full_text += page_text + "\n\n"
            
            # 使用LangChain进行文本分割
            text_chunks = self.text_splitter.split_text(full_text.strip())
            
            # 创建分块信息
            chunks = []
            for i, chunk_text in enumerate(text_chunks):
                chunks.append({
                    "content": chunk_text,
                    "chunk_index": i,
                    "metadata": {
                        "source": filename,
                        "chunk_type": "langchain_split"
                    }
                })
            
            return {
                "content": full_text.strip(),
                "page_map": page_map,
                "chunks": chunks,
                "metadata": {
                    "filename": filename,
                    "total_pages": len(page_map),
                    "total_chunks": len(chunks),
                    "parser": "langchain_pymupdf",
                    "chunk_size": self.chunk_size,
                    "chunk_overlap": self.chunk_overlap,
                    "has_images": False,  # LangChain基础版本不检测图片
                    "has_tables": False   # LangChain基础版本不检测表格
                }
            }
            
        except Exception as e:
            logger.error(f"LangChain解析失败: {e}")
            raise
    
    def _parse_with_pymupdf(self, file_path: str, filename: str) -> Dict[str, Any]:
        """使用PyMuPDF解析PDF（备选方案）"""
        if not PYMUPDF_AVAILABLE:
            raise ImportError("PyMuPDF不可用")
        
        logger.info(f"使用PyMuPDF解析PDF: {filename}")
        
        try:
            doc = pymupdf.open(file_path)
            page_map = []
            full_text = ""
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                page_text = page.get_text()
                
                page_map.append({
                    "page": page_num + 1,
                    "text": page_text,
                    "metadata": {"page_number": page_num + 1}
                })
                full_text += page_text + "\n\n"
            
            doc.close()
            
            # 简单分块（如果没有LangChain）
            chunks = []
            if self.use_langchain:
                text_chunks = self.text_splitter.split_text(full_text.strip())
                for i, chunk_text in enumerate(text_chunks):
                    chunks.append({
                        "content": chunk_text,
                        "chunk_index": i,
                        "metadata": {
                            "source": filename,
                            "chunk_type": "langchain_split"
                        }
                    })
            else:
                # 简单的固定大小分块
                text = full_text.strip()
                for i in range(0, len(text), self.chunk_size):
                    chunk_text = text[i:i + self.chunk_size]
                    chunks.append({
                        "content": chunk_text,
                        "chunk_index": i // self.chunk_size,
                        "metadata": {
                            "source": filename,
                            "chunk_type": "fixed_size"
                        }
                    })
            
            return {
                "content": full_text.strip(),
                "page_map": page_map,
                "chunks": chunks,
                "metadata": {
                    "filename": filename,
                    "total_pages": len(page_map),
                    "total_chunks": len(chunks),
                    "parser": "pymupdf_fallback",
                    "chunk_size": self.chunk_size,
                    "has_images": False,
                    "has_tables": False
                }
            }
            
        except Exception as e:
            logger.error(f"PyMuPDF解析失败: {e}")
            raise
    
    def parse_text(self, text: str, filename: str = "") -> Dict[str, Any]:
        """
        解析纯文本
        
        Args:
            text: 文本内容
            filename: 文件名
            
        Returns:
            解析结果字典
        """
        try:
            # 使用LangChain进行文本分割
            if self.use_langchain:
                text_chunks = self.text_splitter.split_text(text)
                chunks = []
                for i, chunk_text in enumerate(text_chunks):
                    chunks.append({
                        "content": chunk_text,
                        "chunk_index": i,
                        "metadata": {
                            "source": filename,
                            "chunk_type": "langchain_split"
                        }
                    })
            else:
                # 简单分块
                chunks = []
                for i in range(0, len(text), self.chunk_size):
                    chunk_text = text[i:i + self.chunk_size]
                    chunks.append({
                        "content": chunk_text,
                        "chunk_index": i // self.chunk_size,
                        "metadata": {
                            "source": filename,
                            "chunk_type": "fixed_size"
                        }
                    })
            
            page_map = [{
                "page": 1,
                "text": text,
                "metadata": {"page_number": 1}
            }]
            
            return {
                "content": text,
                "page_map": page_map,
                "chunks": chunks,
                "metadata": {
                    "filename": filename,
                    "total_pages": 1,
                    "total_chunks": len(chunks),
                    "parser": "langchain_text" if self.use_langchain else "simple_text",
                    "chunk_size": self.chunk_size,
                    "has_images": False,
                    "has_tables": False
                }
            }
            
        except Exception as e:
            logger.error(f"文本解析失败: {e}")
            raise
    
    def create_langchain_documents(self, parse_result: Dict[str, Any]) -> List[Document]:
        """
        将解析结果转换为LangChain Document对象
        
        Args:
            parse_result: 解析结果
            
        Returns:
            LangChain Document对象列表
        """
        if not LANGCHAIN_AVAILABLE:
            raise ImportError("LangChain不可用")
        
        documents = []
        chunks = parse_result.get("chunks", [])
        
        for chunk in chunks:
            doc = Document(
                page_content=chunk["content"],
                metadata={
                    **chunk.get("metadata", {}),
                    "source": parse_result["metadata"]["filename"],
                    "parser": parse_result["metadata"]["parser"],
                    "chunk_index": chunk.get("chunk_index", 0)
                }
            )
            documents.append(doc)
        
        return documents


def get_langchain_document_parser(config: Dict[str, Any]) -> LangChainDocumentParser:
    """获取LangChain文档解析器实例"""
    return LangChainDocumentParser(config)
