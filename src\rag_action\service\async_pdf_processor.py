"""
异步PDF处理器
实现基于任务队列的异步PDF处理架构
"""
import asyncio
import uuid
import logging
import os
import time
from typing import Dict, Any, Optional, List
from datetime import datetime
from enum import Enum
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import threading

from rag_action.core.config import get_settings
from rag_action.service.note_service import NoteService

logger = logging.getLogger(__name__)
settings = get_settings()

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class ProcessingProgress:
    """处理进度信息"""
    stage: str = "初始化"
    progress: float = 0.0
    message: str = ""
    details: Dict[str, Any] = None

    def __post_init__(self):
        if self.details is None:
            self.details = {}

@dataclass
class PDFProcessingTask:
    """PDF处理任务"""
    task_id: str
    file_path: str
    file_name: str
    file_size: int
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress: ProcessingProgress = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.progress is None:
            self.progress = ProcessingProgress()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        # 转换枚举和日期时间
        data['status'] = self.status.value
        data['created_at'] = self.created_at.isoformat() if self.created_at else None
        data['started_at'] = self.started_at.isoformat() if self.started_at else None
        data['completed_at'] = self.completed_at.isoformat() if self.completed_at else None
        return data

class AsyncPDFProcessor:
    """异步PDF处理器"""
    
    def __init__(self, max_workers: int = 2):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.tasks: Dict[str, PDFProcessingTask] = {}
        self.task_lock = threading.Lock()
        self.note_service = NoteService()
        
        logger.info(f"异步PDF处理器初始化，最大并发数: {max_workers}")
    
    def create_task(self, file_path: str, file_name: str) -> str:
        """创建新的处理任务"""
        task_id = str(uuid.uuid4())
        file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        
        task = PDFProcessingTask(
            task_id=task_id,
            file_path=file_path,
            file_name=file_name,
            file_size=file_size
        )
        
        with self.task_lock:
            self.tasks[task_id] = task
        
        logger.info(f"创建PDF处理任务: {task_id} - {file_name}")
        return task_id
    
    def submit_task(self, task_id: str) -> bool:
        """提交任务到处理队列"""
        with self.task_lock:
            if task_id not in self.tasks:
                logger.error(f"任务不存在: {task_id}")
                return False
            
            task = self.tasks[task_id]
            if task.status != TaskStatus.PENDING:
                logger.warning(f"任务状态不正确: {task_id} - {task.status}")
                return False
        
        # 提交到线程池执行
        future = self.executor.submit(self._run_async_task, task_id)
        logger.info(f"任务已提交到处理队列: {task_id}")
        return True

    def _run_async_task(self, task_id: str):
        """在新的事件循环中运行异步任务"""
        try:
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # 运行异步任务
                loop.run_until_complete(self._process_pdf_task_async(task_id))
            finally:
                loop.close()

        except Exception as e:
            logger.error(f"异步任务运行失败: {task_id} - {e}")
            self._handle_task_error(task_id, str(e))

    async def _process_pdf_task_async(self, task_id: str):
        """处理PDF任务的核心异步逻辑"""
        try:
            with self.task_lock:
                task = self.tasks[task_id]
                task.status = TaskStatus.PROCESSING
                task.started_at = datetime.now()
                task.progress.stage = "开始处理"
                task.progress.progress = 0.0
                task.progress.message = "正在初始化处理流程"

            logger.info(f"开始处理PDF任务: {task_id}")

            # 阶段1: 文件验证
            self._update_progress(task_id, "文件验证", 10.0, "验证PDF文件")
            if not os.path.exists(task.file_path):
                raise Exception(f"文件不存在: {task.file_path}")

            # 阶段2: PDF解析
            self._update_progress(task_id, "PDF解析", 20.0, "正在解析PDF内容")

            # 创建临时的UploadFile对象来模拟文件上传
            class MockUploadFile:
                def __init__(self, file_path: str, filename: str):
                    self.filename = filename
                    self.size = os.path.getsize(file_path)
                    self._file_path = file_path

                async def read(self):
                    with open(self._file_path, 'rb') as f:
                        return f.read()

            mock_file = MockUploadFile(task.file_path, task.file_name)

            # 阶段3: 文档处理
            self._update_progress(task_id, "文档处理", 40.0, "正在处理文档内容")

            # 使用专门的异步处理方法
            result = await self._process_pdf_with_proper_async(mock_file, task_id)

            # 完成
            self._update_progress(task_id, "完成", 100.0, "处理完成")

            with self.task_lock:
                task = self.tasks[task_id]
                task.status = TaskStatus.COMPLETED
                task.completed_at = datetime.now()
                task.result = {
                    "note_id": result.id,
                    "title": result.title,
                    "content_length": len(result.content) if result.content else 0,
                    "source_file": result.source_file,
                    "total_pages": result.total_pages
                }

            logger.info(f"PDF任务处理完成: {task_id}")

        except Exception as e:
            self._handle_task_error(task_id, str(e))

        finally:
            # 清理临时文件
            self._cleanup_temp_file(task_id)

    async def _process_pdf_with_proper_async(self, mock_file, task_id: str):
        """使用正确的异步方式处理PDF"""
        try:
            # 阶段4: 向量化
            self._update_progress(task_id, "向量化", 70.0, "正在生成向量嵌入")

            # 创建新的NoteService实例，确保数据库连接正确
            note_service = NoteService()

            # 阶段5: 存储
            self._update_progress(task_id, "存储", 90.0, "正在存储到数据库")

            # 调用note_service处理PDF
            result = await note_service.create_note_from_pdf(mock_file)

            return result

        except Exception as e:
            logger.error(f"PDF处理失败: {e}")
            raise

    def _handle_task_error(self, task_id: str, error_msg: str):
        """处理任务错误"""
        logger.error(f"PDF任务处理失败: {task_id} - {error_msg}")

        with self.task_lock:
            if task_id in self.tasks:
                task = self.tasks[task_id]
                task.status = TaskStatus.FAILED
                task.completed_at = datetime.now()
                task.error = error_msg
                task.progress.stage = "失败"
                task.progress.message = f"处理失败: {error_msg}"

    def _cleanup_temp_file(self, task_id: str):
        """清理临时文件"""
        try:
            with self.task_lock:
                if task_id in self.tasks:
                    task = self.tasks[task_id]
                    if os.path.exists(task.file_path):
                        os.remove(task.file_path)
                        logger.info(f"清理临时文件: {task.file_path}")
        except Exception as e:
            logger.warning(f"清理临时文件失败: {e}")

    def _update_progress(self, task_id: str, stage: str, progress: float, message: str, details: Dict[str, Any] = None):
        """更新任务进度"""
        with self.task_lock:
            if task_id in self.tasks:
                task = self.tasks[task_id]
                task.progress.stage = stage
                task.progress.progress = progress
                task.progress.message = message
                if details:
                    task.progress.details.update(details)
        
        logger.info(f"任务进度更新: {task_id} - {stage} ({progress:.1f}%) - {message}")
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        with self.task_lock:
            if task_id in self.tasks:
                return self.tasks[task_id].to_dict()
            return None
    
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """获取所有任务状态"""
        with self.task_lock:
            return [task.to_dict() for task in self.tasks.values()]
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self.task_lock:
            if task_id not in self.tasks:
                return False
            
            task = self.tasks[task_id]
            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                return False
            
            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.now()
            task.progress.stage = "已取消"
            task.progress.message = "任务已被取消"
        
        logger.info(f"任务已取消: {task_id}")
        return True
    
    def cleanup_old_tasks(self, max_age_hours: int = 24):
        """清理旧任务"""
        cutoff_time = datetime.now().timestamp() - (max_age_hours * 3600)
        
        with self.task_lock:
            tasks_to_remove = []
            for task_id, task in self.tasks.items():
                if task.created_at.timestamp() < cutoff_time:
                    tasks_to_remove.append(task_id)
            
            for task_id in tasks_to_remove:
                del self.tasks[task_id]
                logger.info(f"清理旧任务: {task_id}")
        
        return len(tasks_to_remove)

# 全局异步PDF处理器实例
async_pdf_processor = AsyncPDFProcessor(max_workers=2)
