"""
演示 LangChain 几种不依赖中间件（如 RunnableWithMessageHistory）的记忆功能的用法
"""

from langchain_core.output_parsers import StrOutputParser
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain.memory import (
    ConversationBufferMemory,
    ConversationBufferWindowMemory,
    ConversationSummaryMemory,
    ConversationTokenBufferMemory,
)
from typing import Dict

# 创建 LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0
)

def buffer_memory_demo():
    """ConversationBufferMemory 示例"""
    print("=== ConversationBufferMemory 示例 ===")
    memory = ConversationBufferMemory(memory_key="chat_history", return_messages=True)
    prompt = ChatPromptTemplate.from_template("""
你是一个智能助手。请基于以下对话历史回答用户的问题：

对话历史：
{chat_history}

当前问题：{user_input}

请提供有帮助的回答：
""")
    conversations = [
        "你好，我叫张三",
        "我今年25岁",
        "我是一名程序员",
        "请总结一下我的信息"
    ]
    for i, user_input in enumerate(conversations, 1):
        print(f"\n--- 第{i}轮对话 ---")
        print(f"用户: {user_input}")
        # 组装输入
        inputs = {
            "user_input": user_input,
            "chat_history": memory.load_memory_variables({})["chat_history"]
        }
        # 执行链
        result = llm.invoke(prompt.format(**inputs))
        print(f"助手: {result.content if hasattr(result, 'content') else result}")
        # 保存到记忆
        memory.save_context({"user_input": user_input}, {"output": result.content if hasattr(result, 'content') else result})

def buffer_window_memory_demo():
    """ConversationBufferWindowMemory 示例"""
    print("\n=== ConversationBufferWindowMemory 示例 ===")
    memory = ConversationBufferWindowMemory(memory_key="chat_history", k=2, return_messages=True)
    prompt = ChatPromptTemplate.from_template("""
你是一个智能助手。请基于最近2轮对话历史回答用户的问题：

对话历史：
{chat_history}

当前问题：{user_input}

请提供有帮助的回答：
""")
    conversations = [
        "我喜欢旅游",
        "我去过北京和上海",
        "你能推荐下一个目的地吗？",
        "我喜欢美食"
    ]
    for i, user_input in enumerate(conversations, 1):
        print(f"\n--- 第{i}轮对话 ---")
        print(f"用户: {user_input}")
        inputs = {
            "user_input": user_input,
            "chat_history": memory.load_memory_variables({})["chat_history"]
        }
        result = llm.invoke(prompt.format(**inputs))
        print(f"助手: {result.content if hasattr(result, 'content') else result}")
        memory.save_context({"user_input": user_input}, {"output": result.content if hasattr(result, 'content') else result})

def summary_memory_demo():
    """ConversationSummaryMemory 示例"""
    print("\n=== ConversationSummaryMemory 示例 ===")
    memory = ConversationSummaryMemory(llm=llm, memory_key="chat_summary", return_messages=True)
    prompt = ChatPromptTemplate.from_template("""
你是一个智能助手。请基于以下对话摘要回答用户的问题：

对话摘要：
{chat_summary}

当前问题：{user_input}

请提供有帮助的回答：
""")
    conversations = [
        "我想学习机器学习",
        "我没有任何编程基础",
        "我应该从哪里开始？",
        "推荐一些入门书籍"
    ]
    for i, user_input in enumerate(conversations, 1):
        print(f"\n--- 第{i}轮对话 ---")
        print(f"用户: {user_input}")
        inputs = {
            "user_input": user_input,
            "chat_summary": memory.load_memory_variables({})["chat_summary"]
        }
        result = llm.invoke(prompt.format(**inputs))
        print(f"助手: {result.content if hasattr(result, 'content') else result}")
        memory.save_context({"user_input": user_input}, {"output": result.content if hasattr(result, 'content') else result})

def token_buffer_memory_demo():
    """ConversationTokenBufferMemory 示例"""
    print("\n=== ConversationTokenBufferMemory 示例 ===")
    memory = ConversationTokenBufferMemory(
        llm=llm,
        max_token_limit=1000,
        memory_key="chat_history",
        return_messages=True
    )
    prompt = ChatPromptTemplate.from_template("""
你是一个智能助手。请基于以下对话历史回答用户的问题：

对话历史：
{chat_history}

当前问题：{user_input}

请提供有帮助的回答：
""")
    conversations = [
        "请详细解释什么是人工智能",
        "机器学习和深度学习有什么区别？",
        "神经网络的基本原理是什么？",
        "什么是卷积神经网络？",
        "请总结一下我们刚才讨论的内容"
    ]
    for i, user_input in enumerate(conversations, 1):
        print(f"\n--- 第{i}轮对话 ---")
        print(f"用户: {user_input}")
        inputs = {
            "user_input": user_input,
            "chat_history": memory.load_memory_variables({})["chat_history"]
        }
        result = llm.invoke(prompt.format(**inputs))
        print(f"助手: {result.content if hasattr(result, 'content') else result}")
        memory.save_context({"user_input": user_input}, {"output": result.content if hasattr(result, 'content') else result})

def main():
    buffer_memory_demo()
    buffer_window_memory_demo()
    summary_memory_demo()
    token_buffer_memory_demo()

if __name__ == "__main__":
    main()