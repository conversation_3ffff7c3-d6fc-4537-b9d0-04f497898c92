"""
向量数据库服务
管理Milvus向量数据库的操作
"""
import logging
from typing import List, Dict, Any, Optional, Tuple
import uuid
from datetime import datetime
from pymilvus import Collection, connections, utility

from rag_action.core.config import get_settings
from rag_action.models.database import MilvusSchema

logger = logging.getLogger(__name__)
settings = get_settings()


class VectorService:
    """向量数据库服务类"""

    def __init__(self):
        self.collection_name = settings.milvus.collection_name
        self.collection = None
        self.enable_bm25 = settings.milvus.enable_bm25
        self._ensure_connection()
    
    def _ensure_connection(self):
        """确保Milvus连接"""
        try:
            # 检查连接是否存在
            if not connections.has_connection("default"):
                connections.connect(
                    alias="default",
                    host=settings.milvus.host,
                    port=settings.milvus.port
                )
            
            # 获取集合
            if utility.has_collection(self.collection_name):
                self.collection = Collection(self.collection_name)
                self.collection.load()
            else:
                logger.error(f"Milvus集合 '{self.collection_name}' 不存在")
                raise Exception(f"集合 '{self.collection_name}' 不存在")
                
        except Exception as e:
            logger.error(f"连接Milvus失败: {e}")
            raise
    
    async def insert_vector(self, vector_id: str, embedding: List[float], metadata: Dict[str, Any], content: str = None):
        """插入向量数据（支持父子文档结构和BM25）

        Args:
            vector_id: 向量ID
            embedding: 密集向量嵌入
            metadata: 元数据
            content: 原始文本内容（用于BM25）
        """
        try:
            # 准备数据（按照新的Schema顺序）
            data = [
                [vector_id],  # id
                [metadata.get("note_id", 0)],  # note_id
                [metadata.get("chunk_id", 0)],  # chunk_id
                [metadata.get("content", "")[:2000]],  # content (截断到最大长度)
                [embedding],  # embedding
                [metadata.get("chunk_type", "child")],  # chunk_type
                [metadata.get("parent_chunk_id", 0)],  # parent_chunk_id
                [metadata.get("page_number", 0)],  # page_number
                [metadata.get("created_at", int(datetime.now().timestamp()))]  # created_at
            ]

            # 插入数据
            self.collection.insert(data)
            self.collection.flush()

            logger.debug(f"成功插入向量: {vector_id} (类型: {metadata.get('chunk_type', 'child')})")

        except Exception as e:
            logger.error(f"插入向量失败: {e}")
            raise

    async def store_embedding(self, embedding: List[float], metadata: Dict[str, Any]) -> str:
        """存储单个向量（别名方法）"""
        # 正确调用insert_vector方法
        vector_id = metadata.get("id", "")
        await self.insert_vector(vector_id, embedding, metadata)
        return vector_id

    async def search(self, query_embedding: List[float], top_k: int = 5,
                   note_ids: Optional[List[int]] = None) -> List[Dict[str, Any]]:
        """搜索相似向量（别名方法）"""
        return await self.search_similar(query_embedding, top_k, note_ids)

    async def search_similar(self, query_embedding: List[float], top_k: int = 5,
                           note_ids: Optional[List[int]] = None) -> List[Dict[str, Any]]:
        """搜索相似向量"""
        try:
            # 构建搜索参数
            search_params = MilvusSchema.get_search_params()
            
            # 构建过滤表达式
            expr = None
            if note_ids:
                expr = f"note_id in {note_ids}"
            
            # 执行搜索
            results = self.collection.search(
                data=[query_embedding],
                anns_field="embedding",
                param=search_params,
                limit=top_k,
                expr=expr,
                output_fields=["note_id", "chunk_id", "content", "page_number", "created_at"]
            )
            
            # 处理搜索结果
            similar_docs = []
            for hits in results:
                for hit in hits:
                    similar_docs.append({
                        "vector_id": hit.id,
                        "note_id": hit.entity.get("note_id"),
                        "chunk_id": hit.entity.get("chunk_id"),
                        "content": hit.entity.get("content"),
                        "page_number": hit.entity.get("page_number"),
                        "similarity_score": float(hit.score),
                        "created_at": hit.entity.get("created_at")
                    })
            
            return similar_docs

        except Exception as e:
            logger.error(f"向量搜索失败: {e}")
            raise

    async def search_bm25(self, query_text: str, top_k: int = 10, note_ids: List[int] = None) -> List[Dict[str, Any]]:
        """BM25全文检索

        Args:
            query_text: 查询文本
            top_k: 返回结果数量
            note_ids: 限制搜索的笔记ID列表

        Returns:
            List[Dict]: 搜索结果列表
        """
        if not self.enable_bm25:
            raise ValueError("BM25功能未启用")

        try:
            # 构建过滤表达式
            expr = None
            if note_ids:
                note_ids_str = ",".join(map(str, note_ids))
                expr = f"note_id in [{note_ids_str}]"

            # BM25搜索参数
            search_params = MilvusSchema.get_search_params("sparse")

            # 执行BM25搜索
            results = self.collection.search(
                data=[query_text],  # 直接传入文本
                anns_field="sparse_embedding",
                param=search_params["params"],
                limit=top_k,
                expr=expr,
                output_fields=["note_id", "chunk_id", "content", "page_number", "created_at"]
            )

            # 处理结果
            similar_docs = []
            for hits in results:
                for hit in hits:
                    similar_docs.append({
                        "vector_id": hit.id,
                        "note_id": hit.entity.get("note_id"),
                        "chunk_id": hit.entity.get("chunk_id"),
                        "content": hit.entity.get("content"),
                        "page_number": hit.entity.get("page_number"),
                        "bm25_score": float(hit.score),
                        "created_at": hit.entity.get("created_at"),
                        "source": "bm25"
                    })

            logger.debug(f"BM25搜索返回 {len(similar_docs)} 个结果")
            return similar_docs

        except Exception as e:
            logger.error(f"BM25搜索失败: {e}")
            raise

    async def hybrid_search(self, query_text: str, query_embedding: List[float], top_k: int = 10,
                           note_ids: List[int] = None) -> List[Dict[str, Any]]:
        """混合检索（密集向量 + BM25稀疏向量）

        Args:
            query_text: 查询文本
            query_embedding: 查询向量
            top_k: 返回结果数量
            note_ids: 限制搜索的笔记ID列表

        Returns:
            List[Dict]: 混合检索结果
        """
        if not self.enable_bm25:
            # 如果未启用BM25，回退到普通向量搜索
            return await self.search(query_embedding, top_k, note_ids)

        try:
            from pymilvus import AnnSearchRequest, RRFRanker

            # 构建过滤表达式
            expr = None
            if note_ids:
                note_ids_str = ",".join(map(str, note_ids))
                expr = f"note_id in [{note_ids_str}]"

            # 创建搜索请求
            requests = []

            # 密集向量搜索请求
            dense_search_params = MilvusSchema.get_search_params("dense")
            dense_request = AnnSearchRequest(
                data=[query_embedding],
                anns_field="embedding",
                param=dense_search_params["params"],
                limit=top_k * 2,  # 获取更多结果用于重排序
                expr=expr
            )
            requests.append(dense_request)

            # 稀疏向量BM25搜索请求
            sparse_search_params = MilvusSchema.get_search_params("sparse")
            sparse_request = AnnSearchRequest(
                data=[query_text],
                anns_field="sparse_embedding",
                param=sparse_search_params["params"],
                limit=top_k * 2,
                expr=expr
            )
            requests.append(sparse_request)

            # RRF重排序器
            ranker = RRFRanker(100)

            # 执行混合搜索
            results = self.collection.hybrid_search(
                reqs=requests,
                rerank=ranker,
                limit=top_k,
                output_fields=["note_id", "chunk_id", "content", "page_number", "created_at"]
            )

            # 处理结果
            similar_docs = []
            for hits in results:
                for hit in hits:
                    similar_docs.append({
                        "vector_id": hit.id,
                        "note_id": hit.entity.get("note_id"),
                        "chunk_id": hit.entity.get("chunk_id"),
                        "content": hit.entity.get("content"),
                        "page_number": hit.entity.get("page_number"),
                        "hybrid_score": float(hit.score),
                        "created_at": hit.entity.get("created_at"),
                        "source": "hybrid"
                    })

            logger.debug(f"混合搜索返回 {len(similar_docs)} 个结果")
            return similar_docs

        except Exception as e:
            logger.error(f"混合搜索失败: {e}")
            # 回退到普通向量搜索
            logger.warning("混合搜索失败，回退到向量搜索")
            return await self.search(query_embedding, top_k, note_ids)
    
    async def delete_by_note_id(self, note_id: int):
        """根据笔记ID删除向量"""
        try:
            expr = f"note_id == {note_id}"
            self.collection.delete(expr)
            self.collection.flush()
            
            logger.info(f"删除笔记 {note_id} 的所有向量")
            
        except Exception as e:
            logger.error(f"删除向量失败: {e}")
            raise
    
    async def delete_by_vector_id(self, vector_id: str):
        """根据向量ID删除向量"""
        try:
            expr = f'id == "{vector_id}"'
            self.collection.delete(expr)
            self.collection.flush()
            
            logger.info(f"删除向量: {vector_id}")
            
        except Exception as e:
            logger.error(f"删除向量失败: {e}")
            raise
    
    async def update_vector(self, vector_id: str, embedding: List[float], metadata: Dict[str, Any]):
        """更新向量数据"""
        try:
            # Milvus不支持直接更新，需要先删除再插入
            await self.delete_by_vector_id(vector_id)
            await self.insert_vector(vector_id, embedding, metadata)
            
            logger.info(f"更新向量: {vector_id}")
            
        except Exception as e:
            logger.error(f"更新向量失败: {e}")
            raise
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """获取集合统计信息"""
        try:
            stats = {
                "collection_name": self.collection_name,
                "num_entities": self.collection.num_entities,
                "is_loaded": self.collection.is_loaded,
                "schema": {
                    "fields": [field.name for field in self.collection.schema.fields],
                    "description": self.collection.schema.description
                }
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取集合统计信息失败: {e}")
            return {}
    
    async def search_by_text(self, query_text: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """根据文本查询相似向量"""
        try:
            from rag_action.service.embedding_service import get_embedding_service
            
            # 生成查询向量
            embedding_service = get_embedding_service()
            query_embedding = embedding_service.embed_text(query_text)
            
            # 执行向量搜索
            return await self.search_similar(query_embedding, top_k)
            
        except Exception as e:
            logger.error(f"文本搜索失败: {e}")
            raise
    
    async def batch_insert_vectors(self, vectors_data: List[Dict[str, Any]]):
        """批量插入向量"""
        try:
            if not vectors_data:
                return
            
            # 准备批量数据（支持父子文档结构）
            ids = []
            note_ids = []
            chunk_ids = []
            contents = []
            embeddings = []
            chunk_types = []
            parent_chunk_ids = []
            page_numbers = []
            created_ats = []

            for data in vectors_data:
                ids.append(data["vector_id"])
                note_ids.append(data["metadata"].get("note_id", 0))
                chunk_ids.append(data["metadata"].get("chunk_id", 0))
                contents.append(data["metadata"].get("content", "")[:2000])
                embeddings.append(data["embedding"])
                chunk_types.append(data["metadata"].get("chunk_type", "child"))
                parent_chunk_ids.append(data["metadata"].get("parent_chunk_id", 0))
                page_numbers.append(data["metadata"].get("page_number", 0))
                created_ats.append(data["metadata"].get("created_at", int(datetime.now().timestamp())))

            # 批量插入（9个字段）
            batch_data = [ids, note_ids, chunk_ids, contents, embeddings, chunk_types, parent_chunk_ids, page_numbers, created_ats]
            self.collection.insert(batch_data)
            self.collection.flush()
            
            logger.info(f"批量插入 {len(vectors_data)} 个向量")
            
        except Exception as e:
            logger.error(f"批量插入向量失败: {e}")
            raise
    
    async def hybrid_search_legacy(self, query_text: str, filters: Dict[str, Any] = None,
                          top_k: int = 5) -> List[Dict[str, Any]]:
        """传统混合搜索：向量搜索 + 过滤条件（保留向后兼容）"""
        try:
            from rag_action.service.embedding_service import get_embedding_service

            # 生成查询向量
            embedding_service = get_embedding_service()
            query_embedding = embedding_service.embed_text(query_text)

            # 构建过滤表达式
            expr = None
            if filters:
                conditions = []
                if "note_ids" in filters and filters["note_ids"]:
                    conditions.append(f"note_id in {filters['note_ids']}")
                if "page_number" in filters:
                    conditions.append(f"page_number == {filters['page_number']}")
                if "created_after" in filters:
                    conditions.append(f"created_at > {filters['created_after']}")
                
                if conditions:
                    expr = " and ".join(conditions)
            
            # 执行搜索
            search_params = MilvusSchema.get_search_params()
            results = self.collection.search(
                data=[query_embedding],
                anns_field="embedding",
                param=search_params,
                limit=top_k,
                expr=expr,
                output_fields=["note_id", "chunk_id", "content", "page_number", "created_at"]
            )
            
            # 处理结果
            similar_docs = []
            for hits in results:
                for hit in hits:
                    similar_docs.append({
                        "vector_id": hit.id,
                        "note_id": hit.entity.get("note_id"),
                        "chunk_id": hit.entity.get("chunk_id"),
                        "content": hit.entity.get("content"),
                        "page_number": hit.entity.get("page_number"),
                        "similarity_score": float(hit.score),
                        "created_at": hit.entity.get("created_at")
                    })
            
            return similar_docs
            
        except Exception as e:
            logger.error(f"混合搜索失败: {e}")
            raise
