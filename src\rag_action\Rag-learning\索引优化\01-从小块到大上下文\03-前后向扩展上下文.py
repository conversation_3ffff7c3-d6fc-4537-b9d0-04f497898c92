from llama_index.core import VectorStoreIndex, StorageContext, Document, Settings
from llama_index.core.node_parser import SentenceSplitter
from llama_index.core.storage.docstore import SimpleDocumentStore
from llama_index.core.postprocessor import PrevNextNodePostprocessor, AutoPrevNextNodePostprocessor
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.huggingface import HuggingFaceEmbedding

# 配置全局设置
Settings.llm = OpenAI(
    model="gpt-4o-mini",
    temperature=0,
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    api_base="https://api.zhizengzeng.com/v1"
)
Settings.embed_model = HuggingFaceEmbedding(model_name="BAAI/bge-small-zh")
Settings.node_parser = SentenceSplitter()

# 准备游戏剧情文本
game_story = """悟空初醒时，发现自己被困在一座古老的山洞中。记忆模糊的他只记得自己是齐天大圣孙悟空，却想不起为何会在此处。洞中有一面破碎的镜子，透过镜子他看到自己伤痕累累，昔日的金箍棒也只剩下一截断柄。离开山洞后，悟空遇到了一位神秘的老僧。老僧告诉他，这里是“幻界”，是介于现实与虚幻之间的特殊空间。500年前，天庭遭遇了前所未有的浩劫，众神陨落，天界崩塌。当时正在大闹天宫的悟空也被卷入其中，失去了大部分法力和记忆，被封印在这个世界。老僧建议悟空去寻找散落在幻界各处的记忆碎片。第一站是位于东方的忘川寺，那里供奉着一面记忆之镜，或许能帮他找回部分记忆。然而，忘川寺已被一群邪魔占领，悟空需要先击败它们。在忘川寺，悟空通过记忆之镜看到了天庭浩劫的部分场景。原来是一个神秘的古老势力在背后操纵，他们利用了“众生之愿”的力量，扭曲了天地规则。当时的悟空虽然强大，却也无法阻止灾难的发生。获得这些记忆后，老僧告诉悟空下一站应该前往西方的业火山。那里有一支蜕变的魔族，他们掌握着更多真相。但业火山常年被熊熊烈火包围，普通生灵难以靠近。悟空需要先找到传说中的三昧火甲，才能安全进入。在寻找三昧火甲的过程中，悟空遇到了昔日的好友妖王。妖王告诉他，天庭崩塌后，六界秩序大乱，各方势力纷纷崛起。有的打着重建天庭的旗号，有的则想建立全新的秩序。一场更大的劫难正在酝酿。获得三昧火甲后，悟空成功潜入业火山。在与魔族首领的对决中，他终于想起了更多真相。原来那个古老势力的目标并非简单的破坏，而是想要重塑整个世界的规则。他们认为现有的秩序存在根本缺陷，导致众生皆苦。回到老僧身边，悟空表示要集结各方力量对抗那个幕后势力。老僧却告诉他，事情可能没有表面看起来那么简单。是否应该重塑世界秩序，这个问题并没有标准答案。建议悟空继续寻找更多真相，再做决定。悟空决定启程前往南方的沉星海。传说那里有一座古老的图书馆，收藏着关于世界起源的众多典籍。然而，在他出发前，幻界突然发生剧烈震动，似乎有什么巨大的变故即将发生……"""

# 创建Document对象
documents = [Document(text=game_story)]

# 解析：存储上下文相关代码详解
# 1. 首先用node_parser（句子切分器）将文档切分为节点（Node），每个节点通常对应一小段文本（如一句话）。
nodes = Settings.node_parser.get_nodes_from_documents(documents)

# 2. 创建一个简单的文档存储（SimpleDocumentStore），用于存放所有节点。这个docstore本质上是一个内存中的字典，key为节点id，value为节点对象。
docstore = SimpleDocumentStore()
docstore.add_documents(nodes)

# 3. 创建存储上下文（StorageContext），并将刚才的docstore传入。StorageContext是llama_index中用于统一管理底层存储（如docstore、向量存储等）的对象。
#    这样后续的索引、检索等操作都可以通过storage_context来访问和管理节点数据。
storage_context = StorageContext.from_defaults(docstore=docstore)

# 4. 构建向量索引（VectorStoreIndex），并将节点和存储上下文传入。这样索引对象就能通过storage_context访问节点的原始内容、元数据等。
index = VectorStoreIndex(nodes, storage_context=storage_context)

# 下面的查询引擎（QueryEngine）在检索时，也会用到storage_context来获取节点的上下文信息，特别是在需要前后文扩展时（如PrevNextNodePostprocessor、AutoPrevNextNodePostprocessor）。

# 创建不同的查询引擎
# 基础查询引擎
base_engine = index.as_query_engine(
    similarity_top_k=1,
    response_mode="tree_summarize"
)
# 带固定前后文的查询引擎
prev_next_engine = index.as_query_engine(
    similarity_top_k=1,
    node_postprocessors=[
        PrevNextNodePostprocessor(docstore=docstore, num_nodes=2)
    ],
    response_mode="tree_summarize"
)
# 带自动前后文的查询引擎
# 
# AutoPrevNextNodePostprocessor 自动分析用户问题的意图，主要依赖于大语言模型（如OpenAI GPT-4/3.5等）对问题的理解能力。
# 其核心技术流程如下：
# 1. 首先将用户的查询（question）与检索到的节点内容（如相关句子）一同输入到大模型中。
# 2. 通过提示工程（prompt engineering），让大模型判断用户更关心节点的前文、后文，还是当前内容本身。
# 3. 大模型会根据问题的语义（如“接下来发生了什么”“之前的原因是什么”等）输出扩展方向（向前/向后/两侧）和需要的上下文范围。
# 4. 系统据此自动选取最合适的前后文节点，拼接成最终的上下文窗口，提升答案的连贯性和完整性。
# 
# 这种自动意图分析和上下文扩展，底层依赖于大模型的理解与推理能力，而不是简单的规则或关键词匹配，因此能更智能地适应各种复杂问题。
# 原理说明：
# AutoPrevNextNodePostprocessor 会根据检索到的节点（最相关的句子），
# 自动分析用户问题的意图（如是问“后续发生了什么”还是“之前发生了什么”），
# 然后智能地在节点序列中向前、向后扩展，选取最有帮助的前后文节点（如前3句或后3句），
# 以便生成更连贯、完整的答案。它不是简单地固定取前后N句，而是根据问题内容和节点上下文动态调整扩展范围。
# verbose=True 会输出详细的扩展过程，便于调试和理解其决策逻辑。
auto_engine = index.as_query_engine(
    similarity_top_k=1,
    node_postprocessors=[
        AutoPrevNextNodePostprocessor(
            docstore=docstore,
            num_nodes=3,   # 最多自动扩展3个前后文节点
            verbose=True   # 输出详细的自动扩展决策过程
        )
    ],
    response_mode="tree_summarize"
)

# 测试不同类型的问题及不同的查询引擎
test_questions = [
    "悟空从忘川寺获得记忆后发生了什么？",  # 应该找后文
    "悟空是如何到达业火山的？",  # 应该找前文
    "悟空为什么会在山洞中醒来？",  # 应该找前文
]
print("=== 基础查询引擎的结果 ===")
for question in test_questions:
    print(f"\n问题：{question}")
    response = base_engine.query(question)
    print(f"回答：{response}\n")
    print("-" * 50)
print("\n=== 固定前后文查询引擎的结果 ===")
for question in test_questions:
    print(f"\n问题：{question}")
    response = prev_next_engine.query(question)
    print(f"回答：{response}\n")
    print("-" * 50)
print("\n=== 自动前后文查询引擎的结果 ===")
for question in test_questions:
    print(f"\n问题：{question}")
    response = auto_engine.query(question)
    print(f"回答：{response}\n")
print("-" * 50)
