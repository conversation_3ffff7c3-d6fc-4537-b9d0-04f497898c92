file_path = "C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/云冈石窟-en.pdf"
from langchain_unstructured import UnstructuredLoader

loader = UnstructuredLoader(file_path = file_path,strategy="hi_res")

docs = []

for doc in loader.lazy_load():
    docs.append(doc)


def extract_basic_structure(docs):
    """基础结构化提取：按文档类型组织内容"""
    # 定义类别映射
    category_map = {
        "Title": "title",
        "NarrativeText": "text",
        "Image": "image",
        "Table": "table",
        "Footer": "footer",
        "Header": "header",
    }

    # 初始化结构字典
    structure = {cat: [] for cat in category_map.values()}
    structure["metadata"] = []
    
    ##遍历文档并分类
    for doc in docs:
        category = doc.metadata.get("category", "unknown")
        content = {
            'content': doc.page_content,
            'page': doc.metadata.get('page_number'),
            'coordinates': doc.metadata.get('coordinates'),
        }

        target_category = category_map.get(category)
        if target_category:
            structure[target_category].append(content)

    return structure

# 使用示例
structure = extract_basic_structure(docs)


# 输出第1页的标题
for title in [t for t in structure['title'] if t['page'] == 1]:
    print(title['content'])
    print("-" * 50)


def analyze_layout(docs):
    """分析文档布局的版面布局结构"""
    layout_analysis = {}

    for doc in docs:
        page_number = doc.metadata.get('page_number')
        coordinates = doc.metadata.get('coordinates')

        #初始化页面信息
        if page_number not in layout_analysis:
            layout_analysis[page_number] = {
                'elements': [],
                'dimensions':{
                    'width': coordinates.get('layout_width', 0),
                    'height': coordinates.get('layout_height', 0),
                }
            }
        #获取元素位置信息
        points = coordinates.get('points', [])
        if points:
            # 只需要左上和右下坐标点
            (x1, y1), (_, _), (x2, y2), (_, _) = points
            # 构建元素信息
            element = {
                'type': doc.metadata.get("category"),
                'content': doc.page_content,
                'position': {
                    'x': x1,
                    'y': y1,
                    'width': x2 - x1,
                    'height': y2 - y1,
                }
            }
            layout_analysis[page_number]['elements'].append(element)
    return layout_analysis
# 使用示例
layout = analyze_layout(docs)
##分析第1页的布局
print("第一页布局分析:")
if 1 in layout:
    page = layout[1]
    print(f"页面尺寸：{page['dimensions']['width']}x{page['dimensions']['height']}")
    print("\n元素分布")

    ##按垂直位置排序显示元素
    for element in sorted(page['elements'], key=lambda x: x['position']['y']):
        print(f"类型: {element['type']}")
        print(f"内容: {element['content']}")
        print(f"位置: {element['position']}")
        print(f"尺寸: {element['position']['width']}x{element['position']['height']}")
        print("-" * 50)


print("===寻找父子关系===")
# 寻找父子关系
cave6_docs = []
parent_id = -1

for doc in docs:
    if doc.metadata.get('category') == 'Title' and "Cave 6" in doc.page_content:
        parent_id = doc.metadata['element_id']
    if doc.metadata.get('parent_id') == parent_id:
        cave6_docs.append(doc)

for doc in cave6_docs:
    print(doc.page_content)
    print("-" * 50)

external_docs = [] #创建列表来存储外部链接的子文档
parent_id = -1 # 初始化父ID为-1
for doc in docs:
    #检查文档是否为标题类型且内容包含"External Link"
    if doc.metadata.get('category') == 'Title' and "External links" in doc.page_content:
        parent_id = doc.metadata['element_id']
        external_docs.append(doc)
    if doc.metadata.get('parent_id') == parent_id:
        external_docs.append(doc)
for doc in external_docs:
    print(doc.page_content)
    print("-" * 50)



