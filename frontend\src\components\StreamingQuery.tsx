import React, { useState, useRef, useEffect } from 'react'
import { Button, Input, Card, Typography, Space, Tag, Spin, Alert } from 'antd'
import { SendOutlined, StopOutlined, ClearOutlined } from '@ant-design/icons'
import { useStreamingQuery, useStreamingProgress, useStreamingPerformance } from '@/hooks/useStreamingQuery'
import type { QuerySource } from '@/types'

const { TextArea } = Input
const { Title, Paragraph, Text } = Typography

interface StreamingQueryProps {
  conversationId?: string
  onConversationUpdate?: (conversationId: string) => void
  className?: string
}

/**
 * 流式问答组件
 * 提供完整的流式问答界面和交互功能
 */
export const StreamingQuery: React.FC<StreamingQueryProps> = ({
  conversationId,
  onConversationUpdate,
  className
}) => {
  const [query, setQuery] = useState('')
  const [method, setMethod] = useState<'GET' | 'POST'>('GET')
  const answerRef = useRef<HTMLDivElement>(null)

  const { state, startStreaming, stopStreaming, clearState } = useStreamingQuery()
  const { progress, updateProgress, clearProgress, getProgressIcon, getProgressColor } = useStreamingProgress()
  const { 
    startMonitoring, 
    recordFirstChunk, 
    recordChunk, 
    endMonitoring, 
    getPerformanceReport 
  } = useStreamingPerformance()

  // 自动滚动到答案底部
  useEffect(() => {
    if (answerRef.current) {
      answerRef.current.scrollTop = answerRef.current.scrollHeight
    }
  }, [state.answer])

  // 监听流式状态变化
  useEffect(() => {
    if (state.isStreaming && state.currentStage) {
      updateProgress(state.currentStage, state.stageMessage)
    } else if (!state.isStreaming) {
      clearProgress()
    }
  }, [state.isStreaming, state.currentStage, state.stageMessage, updateProgress, clearProgress])

  const handleSubmit = () => {
    if (!query.trim() || state.isStreaming) return

    startMonitoring()
    clearProgress()

    startStreaming({
      message: query.trim(),
      conversation_id: conversationId,
      top_k: 5,
      stream: true
    }, method)

    // 记录首次数据块
    const originalOnChunk = state.fullResponse
    recordFirstChunk()
  }

  const handleStop = () => {
    stopStreaming()
    endMonitoring()
    clearProgress()
  }

  const handleClear = () => {
    clearState()
    clearProgress()
    setQuery('')
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      handleSubmit()
    }
  }

  const performanceReport = getPerformanceReport()

  return (
    <div className={className}>
      <Card title="流式智能问答" className="mb-4">
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          {/* 查询输入区域 */}
          <div>
            <Space style={{ marginBottom: 8 }}>
              <Text strong>请输入您的问题：</Text>
              <Tag 
                color={method === 'GET' ? 'blue' : 'green'}
                style={{ cursor: 'pointer' }}
                onClick={() => setMethod(method === 'GET' ? 'POST' : 'GET')}
              >
                {method} 方式
              </Tag>
            </Space>
            <TextArea
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="输入问题，支持 Ctrl+Enter 快速提交"
              rows={3}
              disabled={state.isStreaming}
            />
            <div style={{ marginTop: 8, textAlign: 'right' }}>
              <Space>
                <Button
                  icon={<ClearOutlined />}
                  onClick={handleClear}
                  disabled={state.isStreaming}
                >
                  清空
                </Button>
                <Button
                  type="primary"
                  icon={state.isStreaming ? <StopOutlined /> : <SendOutlined />}
                  onClick={state.isStreaming ? handleStop : handleSubmit}
                  disabled={!query.trim() && !state.isStreaming}
                  danger={state.isStreaming}
                >
                  {state.isStreaming ? '停止' : '提交'}
                </Button>
              </Space>
            </div>
          </div>

          {/* 进度指示器 */}
          {progress.isActive && (
            <Card size="small" style={{ backgroundColor: '#f6ffed', border: '1px solid #b7eb8f' }}>
              <Space>
                <Spin size="small" />
                <Text style={{ color: getProgressColor(progress.stage) }}>
                  {getProgressIcon(progress.stage)} {progress.message}
                </Text>
              </Space>
            </Card>
          )}

          {/* 错误显示 */}
          {state.error && (
            <Alert
              message="查询失败"
              description={state.error}
              type="error"
              showIcon
              closable
              onClose={() => clearState()}
            />
          )}

          {/* 答案显示区域 */}
          {(state.answer || state.isStreaming) && (
            <Card 
              title={
                <Space>
                  <Text strong>AI 回答</Text>
                  {state.isStreaming && <Spin size="small" />}
                </Space>
              }
              size="small"
            >
              <div
                ref={answerRef}
                style={{
                  maxHeight: '400px',
                  overflowY: 'auto',
                  padding: '12px',
                  backgroundColor: '#fafafa',
                  borderRadius: '6px',
                  fontFamily: 'monospace',
                  lineHeight: '1.6',
                  whiteSpace: 'pre-wrap'
                }}
              >
                {state.answer}
                {state.isStreaming && (
                  <span 
                    style={{ 
                      animation: 'blink 1s infinite',
                      marginLeft: '2px',
                      color: '#1890ff'
                    }}
                  >
                    ▋
                  </span>
                )}
              </div>
            </Card>
          )}

          {/* 来源文档 */}
          {state.fullResponse?.sources && state.fullResponse.sources.length > 0 && (
            <Card title="参考来源" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                {state.fullResponse.sources.map((source: QuerySource, index: number) => (
                  <Card key={index} size="small" style={{ backgroundColor: '#f9f9f9' }}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Space>
                        <Tag color="blue">文档 {index + 1}</Tag>
                        <Text strong>{source.note_title}</Text>
                        <Text type="secondary">相似度: {(source.score * 100).toFixed(1)}%</Text>
                        {source.page_number && (
                          <Text type="secondary">第 {source.page_number} 页</Text>
                        )}
                      </Space>
                      <Paragraph 
                        ellipsis={{ rows: 3, expandable: true }}
                        style={{ margin: 0, fontSize: '13px' }}
                      >
                        {source.content}
                      </Paragraph>
                    </Space>
                  </Card>
                ))}
              </Space>
            </Card>
          )}

          {/* 性能统计 */}
          {performanceReport && state.fullResponse && (
            <Card title="性能统计" size="small">
              <Space wrap>
                <Tag>首块响应: {performanceReport.timeToFirstChunk}ms</Tag>
                <Tag>总时间: {performanceReport.totalTime}ms</Tag>
                <Tag>数据块: {performanceReport.totalChunks}个</Tag>
                <Tag>字符数: {performanceReport.totalCharacters}</Tag>
                <Tag>平均速度: {performanceReport.charactersPerSecond.toFixed(1)} 字符/秒</Tag>
              </Space>
            </Card>
          )}
        </Space>
      </Card>

      <style jsx>{`
        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }
      `}</style>
    </div>
  )
}

/**
 * 简化版流式问答组件
 */
export const SimpleStreamingQuery: React.FC<{
  onAnswer?: (answer: string) => void
}> = ({ onAnswer }) => {
  const [query, setQuery] = useState('')
  const { state, startStreaming, stopStreaming } = useStreamingQuery()

  const handleSubmit = () => {
    if (!query.trim()) return
    
    startStreaming({
      message: query.trim(),
      stream: true
    })
  }

  useEffect(() => {
    if (state.fullResponse?.answer) {
      onAnswer?.(state.fullResponse.answer)
    }
  }, [state.fullResponse, onAnswer])

  return (
    <Space.Compact style={{ width: '100%' }}>
      <Input
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        onPressEnter={handleSubmit}
        placeholder="输入问题..."
        disabled={state.isStreaming}
      />
      <Button
        type="primary"
        icon={state.isStreaming ? <StopOutlined /> : <SendOutlined />}
        onClick={state.isStreaming ? stopStreaming : handleSubmit}
        loading={state.isStreaming}
      >
        {state.isStreaming ? '停止' : '发送'}
      </Button>
    </Space.Compact>
  )
}

export default StreamingQuery
