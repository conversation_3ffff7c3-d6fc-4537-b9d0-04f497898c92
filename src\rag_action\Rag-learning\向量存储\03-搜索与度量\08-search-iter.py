"""
代码功能说明：

本代码演示了如何在 Milvus 中批量插入大规模向量数据，并利用 SearchIterator（搜索迭代器）进行高效的分批向量检索。

主要流程如下：
1. 连接 Milvus 服务，创建集合（Collection）并定义 schema，包括主键、向量字段和一个颜色字段。
2. 随机生成并插入 2 万条 128 维向量数据。
3. 为向量字段创建索引，并将集合加载到内存。
4. 构造一个查询向量，使用 Milvus 的 `search_iterator` 方法进行大规模向量检索。
5. 通过迭代器分批获取所有检索结果，并输出前若干条结果。
6. 最后释放集合资源。

迭代器（SearchIterator）的作用与适用场景：

- 作用：SearchIterator 允许用户在检索大量结果时，分批（batch）拉取数据，而不是一次性全部加载到内存。每次调用 `next()` 方法会返回一批结果，直到所有结果都被遍历完毕。
- 适用场景：当检索结果数量非常大（如上万、几十万条），一次性获取所有结果会导致内存压力大甚至 OOM。此时，使用迭代器可以边查边处理，节省内存，并支持流式处理大规模检索结果，适合大数据量的向量检索、分页展示、批量后处理等场景。

代码如下：
"""

from pymilvus import MilvusClient, DataType
import random

# 1. 设置 Milvus 客户端
client = MilvusClient(uri="http://localhost:19530")
COLLECTION_NAME = "search_iterator_demo"

# 如果集合已存在，则删除
if client.has_collection(COLLECTION_NAME):
    client.drop_collection(COLLECTION_NAME)

# 2. 创建 schema
schema = MilvusClient.create_schema(auto_id=False, enable_dynamic_field=True)
schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True)
schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=128)
schema.add_field(field_name="color", datatype=DataType.VARCHAR, max_length=100)

# 3. 创建集合
client.create_collection(collection_name=COLLECTION_NAME, schema=schema)

# 4. 插入随机向量数据
num_vectors = 20000  # 插入更多数据以演示 SearchIterator
vectors = [[random.random() for _ in range(128)] for _ in range(num_vectors)]
ids = list(range(num_vectors))
colors = [f"color_{random.randint(1, 1000)}" for _ in range(num_vectors)]
entities = [{"id": ids[i], "vector": vectors[i], "color": colors[i]} for i in range(num_vectors)]

client.insert(collection_name=COLLECTION_NAME, data=entities)
client.flush(collection_name=COLLECTION_NAME)

# 5. 创建索引
index_params = MilvusClient.prepare_index_params()
index_params.add_index(
    field_name="vector",
    metric_type="L2",
    index_type="FLAT",
    index_name="vector_index",
    params={}
)
client.create_index(
    collection_name=COLLECTION_NAME,
    index_params=index_params,
    sync=True
)

# 6. 加载集合
client.load_collection(collection_name=COLLECTION_NAME)

# 7. 使用 SearchIterator 进行搜索
print("\n=== 使用 SearchIterator 进行搜索 ===")
query_vector = [random.random() for _ in range(128)]

# 创建 SearchIterator
iterator = client.search_iterator(
    collection_name=COLLECTION_NAME,
    data=[query_vector],
    anns_field="vector",
    search_params={"metric_type": "L2"},
    batch_size=1000,  # 每批返回1000条结果
    limit=20000,      # 总共返回20000条结果
    output_fields=["color"]
)

# 使用迭代器获取结果
all_results = []
while True:
    result = iterator.next()
    if not result:
        iterator.close()
        break
    
    # 将结果转换为字典并添加到结果列表
    for hit in result:
        # 注意：有些 Milvus 版本返回的 hit 可能已经是 dict 类型
        if hasattr(hit, "to_dict"):
            all_results.append(hit.to_dict())
        else:
            all_results.append(dict(hit))

print(f"总共获取到 {len(all_results)} 条结果")
print("\n前5条结果:")
for result in all_results[:5]:
    print(f"ID: {result['id']}, 距离: {result['distance']}, 颜色: {result['entity']['color']}")

# 8. 清理
client.release_collection(collection_name=COLLECTION_NAME)
