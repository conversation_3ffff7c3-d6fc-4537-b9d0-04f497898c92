@use 'sass:map';

// 颜色变量
:root {
  // 主色调
  --primary-color: #409eff;
  --primary-light: #79bbff;
  --primary-dark: #337ecc;
  
  // 成功色
  --success-color: #67c23a;
  --success-light: #95d475;
  --success-dark: #529b2e;
  
  // 警告色
  --warning-color: #e6a23c;
  --warning-light: #ebb563;
  --warning-dark: #b88230;
  
  // 危险色
  --danger-color: #f56c6c;
  --danger-light: #f89898;
  --danger-dark: #c45656;
  
  // 信息色
  --info-color: #909399;
  --info-light: #b1b3b8;
  --info-dark: #73767a;
  
  // 文本颜色
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;
  
  // 边框颜色
  --border-base: #dcdfe6;
  --border-light: #e4e7ed;
  --border-lighter: #ebeef5;
  --border-extra-light: #f2f6fc;
  
  // 背景颜色
  --bg-color: #ffffff;
  --bg-page: #f2f3f5;
  --bg-overlay: rgba(255, 255, 255, 0.9);
  
  // 阴影
  --shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --shadow-dark: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  // 圆角
  --border-radius-base: 4px;
  --border-radius-small: 2px;
  --border-radius-large: 8px;
  --border-radius-round: 20px;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  // 字体大小
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  
  // 行高
  --line-height-base: 1.5;
  --line-height-sm: 1.25;
  --line-height-lg: 1.75;
  
  // 字体粗细
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  
  // 过渡动画
  --transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  --transition-fast: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  --transition-slow: all 0.5s cubic-bezier(0.645, 0.045, 0.355, 1);
  
  // Z-index
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;
}

// 暗色主题
[data-theme='dark'] {
  // 文本颜色
  --text-primary: #e5eaf3;
  --text-regular: #cfd3dc;
  --text-secondary: #a3a6ad;
  --text-placeholder: #8d9095;
  
  // 边框颜色
  --border-base: #4c4d4f;
  --border-light: #414243;
  --border-lighter: #363637;
  --border-extra-light: #2b2b2c;
  
  // 背景颜色
  --bg-color: #1d1e1f;
  --bg-page: #0a0a0a;
  --bg-overlay: rgba(29, 30, 31, 0.9);
  
  // 阴影
  --shadow-base: 0 2px 4px rgba(0, 0, 0, 0.24), 0 0 6px rgba(0, 0, 0, 0.08);
  --shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
  --shadow-dark: 0 4px 12px rgba(0, 0, 0, 0.3);
}

// 响应式断点
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1600px
);

// 媒体查询混入
@mixin respond-to($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    @media (min-width: map.get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}.";
  }
}

// 响应式工具类
@mixin respond-below($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    @media (max-width: map.get($breakpoints, $breakpoint) - 1px) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}.";
  }
}

// 文本省略
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

// Flexbox 居中
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 滚动条样式
@mixin scrollbar($width: 6px, $track-color: transparent, $thumb-color: var(--border-base)) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: calc($width / 2);
    
    &:hover {
      background: var(--border-light);
    }
  }
}

// 动画关键帧
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
