"""
RAG服务适配器 - 基于LangChain LCEL的现代化实现

本模块提供了基于LangChain LCEL的现代化RAG服务实现。
原有的兼容性代码已被移除，现在完全基于LangChain生态。

主要功能：
1. 基于LangChain LCEL的声明式流水线
2. 原生流式输出支持
3. 智能Memory对话管理
4. Agent动态路由
5. 标准化Retriever集成

使用方式：
```python
from rag_action.service.rag_service_adapter import get_rag_service

# 获取LangChain RAG服务
rag_service = get_rag_service()
response = await rag_service.answer_question("问题")
```
"""
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator, Union
from src.rag_action.core.config import settings
from src.rag_action.models.schemas import QAResponse

logger = logging.getLogger(__name__)


class RAGService:
    """
    基于LangChain LCEL的RAG服务

    这是一个简化的适配器，直接使用LangChain LCEL实现。
    所有兼容性和降级代码已被移除，专注于现代化的AI应用架构。
    """

    def __init__(self):
        """初始化LangChain RAG服务"""
        try:
            from src.rag_action.service.langchain_rag_service import LangChainRAGService
            self._service = LangChainRAGService()
            logger.info("✅ LangChain RAG服务初始化成功")
        except Exception as e:
            logger.error(f"❌ LangChain RAG服务初始化失败: {e}")
            raise
    
    async def answer_question(self, question: str, top_k: int = 5,
                            note_ids: Optional[List[int]] = None,
                            conversation_context: Optional[List[Dict[str, str]]] = None) -> QAResponse:
        """
        智能问答接口

        修复：正确处理LangChainRAGService返回的数据格式
        - 处理RetrievedDocument对象的序列化
        - 确保数据类型符合QAResponse规范
        - 处理异常情况和错误恢复

        Args:
            question: 用户问题
            top_k: 检索文档数量
            note_ids: 限制搜索的笔记ID列表
            conversation_context: 对话上下文

        Returns:
            QAResponse: 标准化的问答响应
        """
        try:
            # 调用底层服务
            response = await self._service.answer_question(
                question, top_k, note_ids, conversation_context
            )

            # 修复：确保返回的数据格式正确
            return self._normalize_qa_response(response, question)

        except Exception as e:
            logger.error(f"❌ 适配器问答处理失败: {e}")
            # 返回错误响应，确保数据格式正确
            return self._create_error_response(question, str(e))
    
    async def answer_question_stream(self, question: str, top_k: int = 5,
                                   note_ids: Optional[List[int]] = None,
                                   conversation_context: Optional[List[Dict[str, str]]] = None) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
        """
        流式问答接口

        修复：正确处理流式响应的数据格式
        - 处理sources字段的序列化
        - 确保最终响应格式正确
        - 处理异常情况

        Args:
            question: 用户问题
            top_k: 检索文档数量
            note_ids: 限制搜索的笔记ID列表
            conversation_context: 对话上下文

        Yields:
            流式响应内容
        """
        try:
            async for chunk in self._service.answer_question_stream(
                question, top_k, note_ids, conversation_context
            ):
                # 修复：处理最终完成响应的数据格式
                if chunk.get('type') == 'answer_complete' and 'sources' in chunk:
                    chunk = self._normalize_stream_chunk(chunk)

                yield chunk

        except Exception as e:
            logger.error(f"❌ 适配器流式问答处理失败: {e}")
            # 返回错误流式响应
            yield {
                'type': 'error',
                'content': f'处理过程中出现错误: {str(e)}',
                'finished': True,
                'error': str(e),
                'adapter_error': True
            }

    def _normalize_qa_response(self, response, question: str) -> QAResponse:
        """
        标准化QA响应格式

        修复：彻底解决RetrievedDocument对象的Pydantic验证问题
        - 使用字典格式创建RetrievedDocument，确保Pydantic验证通过
        - 严格的字段类型检查和转换
        - 完善的错误处理和默认值设置
        """
        try:
            from src.rag_action.models.schemas import RetrievedDocument

            # 处理sources字段 - 关键修复点
            normalized_sources = []
            if hasattr(response, 'sources') and response.sources:
                for i, source in enumerate(response.sources):
                    try:
                        # 创建标准化的字典数据
                        source_data = self._extract_source_data(source, i)

                        # 使用字典数据创建RetrievedDocument实例
                        # 这确保了Pydantic的严格验证
                        retrieved_doc = RetrievedDocument(**source_data)
                        normalized_sources.append(retrieved_doc)

                    except Exception as source_error:
                        logger.warning(f"⚠️ 处理源文档{i}失败: {source_error}")
                        # 创建默认的源文档
                        default_doc = RetrievedDocument(
                            content=f"源文档{i}处理失败: {str(source_error)}",
                            note_id=0,
                            note_title='处理失败的文档',
                            chunk_id=i,
                            page_number=1,
                            similarity_score=0.0
                        )
                        normalized_sources.append(default_doc)

            # 创建标准化的QAResponse
            return QAResponse(
                question=question,
                answer=getattr(response, 'answer', '无法生成回答'),
                sources=normalized_sources,
                total_tokens=getattr(response, 'total_tokens', None),
                processing_time=getattr(response, 'processing_time', 0.0),
                metadata=getattr(response, 'metadata', {})
            )

        except Exception as e:
            logger.error(f"❌ 响应格式标准化失败: {e}")
            import traceback
            logger.debug(f"详细错误信息: {traceback.format_exc()}")
            return self._create_error_response(question, f"响应格式化错误: {str(e)}")

    def _extract_source_data(self, source, index: int) -> Dict[str, Any]:
        """
        提取源文档数据并进行严格的类型转换

        修复：确保所有字段都符合RetrievedDocument的Pydantic模型要求
        - 严格的类型检查和转换
        - 处理各种可能的数据格式
        - 提供合理的默认值
        """
        try:
            # 初始化默认数据
            source_data = {
                'content': '',
                'note_id': 0,
                'note_title': 'Unknown',
                'chunk_id': index,
                'page_number': 1,
                'similarity_score': 0.0
            }

            if isinstance(source, dict):
                # 处理字典格式的源数据
                source_data.update({
                    'content': str(source.get('content', '')),
                    'note_id': int(source.get('note_id', 0)),
                    'note_title': str(source.get('note_title', 'Unknown')),
                    'chunk_id': int(source.get('chunk_id', index)),
                    'page_number': int(source.get('page_number', 1)) if source.get('page_number') is not None else 1,
                    'similarity_score': float(source.get('similarity_score', 0.0))
                })
            elif hasattr(source, '__dict__'):
                # 处理对象格式的源数据
                source_data.update({
                    'content': str(getattr(source, 'content', '')),
                    'note_id': int(getattr(source, 'note_id', 0)),
                    'note_title': str(getattr(source, 'note_title', 'Unknown')),
                    'chunk_id': int(getattr(source, 'chunk_id', index)),
                    'page_number': int(getattr(source, 'page_number', 1)) if getattr(source, 'page_number', None) is not None else 1,
                    'similarity_score': float(getattr(source, 'similarity_score', 0.0))
                })
            else:
                # 处理其他格式（字符串等）
                source_data['content'] = str(source)

            # 验证和清理数据
            source_data = self._validate_source_data(source_data)

            return source_data

        except Exception as e:
            logger.warning(f"⚠️ 提取源数据失败: {e}")
            # 返回安全的默认数据
            return {
                'content': f'数据提取失败: {str(e)}',
                'note_id': 0,
                'note_title': 'Error',
                'chunk_id': index,
                'page_number': 1,
                'similarity_score': 0.0
            }

    def _validate_source_data(self, source_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证和清理源数据

        修复：确保数据类型和值的有效性
        """
        try:
            # 确保content不为空
            if not source_data.get('content'):
                source_data['content'] = '无内容'

            # 确保note_id为非负整数
            if source_data.get('note_id', 0) < 0:
                source_data['note_id'] = 0

            # 确保page_number为正整数
            if source_data.get('page_number', 1) < 1:
                source_data['page_number'] = 1

            # 确保similarity_score在合理范围内
            score = source_data.get('similarity_score', 0.0)
            if score < 0.0:
                source_data['similarity_score'] = 0.0
            elif score > 1.0:
                source_data['similarity_score'] = 1.0

            # 确保note_title不为空
            if not source_data.get('note_title'):
                source_data['note_title'] = 'Unknown'

            return source_data

        except Exception as e:
            logger.warning(f"⚠️ 数据验证失败: {e}")
            return source_data

    def _create_error_response(self, question: str, error_message: str) -> QAResponse:
        """
        创建错误响应

        确保即使在错误情况下也返回正确格式的QAResponse
        """
        return QAResponse(
            question=question,
            answer=f"抱歉，处理您的问题时出现错误: {error_message}",
            sources=[],
            total_tokens=None,
            processing_time=0.0,
            metadata={"error": error_message, "adapter_error": True}
        )

    def _normalize_stream_chunk(self, chunk: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化流式响应块

        修复：处理流式响应中sources字段的格式问题
        """
        try:
            if 'sources' in chunk and chunk['sources']:
                from src.rag_action.models.schemas import RetrievedDocument

                normalized_sources = []
                for source in chunk['sources']:
                    if isinstance(source, dict):
                        # 如果已经是字典，确保格式正确
                        normalized_sources.append({
                            'content': source.get('content', ''),
                            'note_id': source.get('note_id', 0),
                            'note_title': source.get('note_title', 'Unknown'),
                            'chunk_id': source.get('chunk_id', 0),
                            'page_number': source.get('page_number', 1),
                            'similarity_score': source.get('similarity_score', 0.0)
                        })
                    elif hasattr(source, '__dict__'):
                        # 如果是对象，转换为字典
                        normalized_sources.append({
                            'content': getattr(source, 'content', ''),
                            'note_id': getattr(source, 'note_id', 0),
                            'note_title': getattr(source, 'note_title', 'Unknown'),
                            'chunk_id': getattr(source, 'chunk_id', 0),
                            'page_number': getattr(source, 'page_number', 1),
                            'similarity_score': getattr(source, 'similarity_score', 0.0)
                        })
                    else:
                        # 其他格式，创建默认字典
                        normalized_sources.append({
                            'content': str(source),
                            'note_id': 0,
                            'note_title': 'Unknown',
                            'chunk_id': 0,
                            'page_number': 1,
                            'similarity_score': 0.0
                        })

                # 更新chunk中的sources
                chunk['sources'] = normalized_sources

            return chunk

        except Exception as e:
            logger.error(f"❌ 流式响应块标准化失败: {e}")
            # 返回原始chunk，但添加错误信息
            chunk['normalization_error'] = str(e)
            return chunk

    def get_service_info(self) -> Dict[str, Any]:
        """
        获取服务状态信息

        Returns:
            包含服务状态的字典
        """
        return {
            "implementation": "langchain_lcel",
            "version": "2.0",
            "features": [
                "LCEL声明式流水线",
                "原生流式输出",
                "智能Memory管理",
                "Agent动态路由",
                "标准化Retriever"
            ]
        }


# 全局服务实例缓存
_global_rag_service = None

# 便捷的工厂函数
def get_rag_service() -> RAGService:
    """
    获取RAG服务实例（工厂函数）

    Returns:
        RAGService: LangChain RAG服务实例
    """
    global _global_rag_service
    if _global_rag_service is None:
        _global_rag_service = RAGService()
    return _global_rag_service
