from pymilvus import MilvusClient, DataType
import random
# HNSW（Hierarchical Navigable Small World）是一种高效的近似最近邻（ANN, Approximate Nearest Neighbor）搜索算法，常用于大规模向量检索场景。它通过构建多层次的图结构，使得在高维空间中查找相似向量变得高效且准确。

# HNSW 的核心思想是将所有向量组织成一个分层的图，每一层都是一个小世界图（Small World Graph），节点之间通过边相连。顶层的节点较少，底层的节点最多。搜索时，算法会从顶层开始，逐层向下，逐步靠近目标向量，最终在底层找到最相似的向量。

# HNSW 的主要参数有：
# - M：每个节点最大连接的邻居数。M 越大，图的连通性越好，检索精度越高，但索引构建和内存消耗也会增加。
# - efConstruction：构建索引时的候选邻居数。efConstruction 越大，索引质量越高，但构建速度越慢。
# - ef（查询时参数）：控制搜索时的候选集合大小，ef 越大，检索精度越高，但查询速度越慢。

# HNSW 的优点：
# 1. 检索速度快，适合大规模高维向量数据。
# 2. 支持动态插入和删除数据。
# 3. 检索精度高，接近暴力搜索。

# 在 Milvus 中，HNSW 是常用的向量索引类型之一，适用于需要高性能 ANN 检索的场景，如图像检索、文本检索、推荐系统等。

# 1. 设置 Milvus 客户端
client = MilvusClient(uri="http://localhost:19530")
COLLECTION_NAME = "flat_index_demo"

# 如果集合已存在，则删除
if client.has_collection(COLLECTION_NAME):
    client.drop_collection(COLLECTION_NAME)

# 2. 创建 schema
schema = MilvusClient.create_schema(auto_id=False, enable_dynamic_field=True)
schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True)
schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=128)

# 3. 创建集合
client.create_collection(collection_name=COLLECTION_NAME, schema=schema)

# 4. 插入随机向量数据
num_vectors = 1000
vectors = [[random.random() for _ in range(128)] for _ in range(num_vectors)]
ids = list(range(num_vectors))
entities = [{"id": ids[i], "vector": vectors[i]} for i in range(num_vectors)]

client.insert(collection_name=COLLECTION_NAME, data=entities)
# flush 保证数据落盘
client.flush(COLLECTION_NAME)

# 5. 创建索引（此时集合中已有数据）
index_params = MilvusClient.prepare_index_params()
index_params.add_index(
    field_name="vector",
    metric_type="L2",
    index_type="HNSW",
    index_name="vector_index",
    params={
        "M": 64,  # 最大邻居数
        "efConstruction": 100  # 构建时的候选邻居数
    }
)
client.create_index(
    collection_name=COLLECTION_NAME,
    index_params=index_params,
    sync=True
)

# 验证索引
print("索引列表:", client.list_indexes(collection_name=COLLECTION_NAME))
print("索引详情:", client.describe_index(
    collection_name=COLLECTION_NAME,
    index_name="vector_index"
))

# 6. load 后再搜索
client.load_collection(collection_name=COLLECTION_NAME)
search_vectors = [[random.random() for _ in range(128)]]
results = client.search(
    collection_name=COLLECTION_NAME,
    data=search_vectors,
    ann_field="vector",
    limit=5,
    output_fields=["id"],
    search_params={
        "params": {
            "ef": 10  # 搜索时的候选邻居数
        }
    }
)

print("\n搜索结果:")
for hits in results:
    for hit in hits:
        # 注意用 dict 方式访问
        print(f"ID: {hit['id']}, 距离: {hit['distance']}")

# 清理
client.release_collection(collection_name=COLLECTION_NAME)
# client.disconnect()
