# 使用直接运行方式启动MCP服务器
from fastmcp import FastMCP
import logging

# 设置日志级别
logging.basicConfig(level=logging.INFO)

mcp = FastMCP("local-tools")

@mcp.tool()
def add(a: int, b: int) -> int:
    """两个数相加"""
    print(f"调用add工具: {a} + {b}")
    return a + b

@mcp.tool()
def subtract(a: int, b: int) -> int:
    """两个数相减"""
    print(f"调用subtract工具: {a} - {b}")
    return a - b

if __name__ == "__main__":
    print("启动MCP服务器...")
    print("使用SSE传输，端口8000")
    # 使用SSE传输，确保与客户端兼容
    mcp.run(transport="sse", host="0.0.0.0", port=8000)
