"""
Milvus 混合检索示例 - 稀疏向量和密集向量的混合检索
支持 Milvus 2.5+ 版本的完整实现

本示例演示如何使用 Milvus 进行稀疏向量和密集向量的混合检索，
包括连接数据库、创建集合、插入数据、执行混合检索等完整流程。

作者: RAG Action Team
日期: 2025-08-05
"""

import logging
import time
from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
import os
from pymilvus import (
    MilvusClient, 
    DataType, 
    Function, 
    FunctionType,
    AnnSearchRequest,
    WeightedRanker,
    RRFRanker,
    connections,
    utility,
    FieldSchema,
    CollectionSchema,
    Collection
)
from pymilvus.model.hybrid import BGEM3EmbeddingFunction

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"
os.environ["HTTP_PROXY"] = "http://127.0.0.1:1080"
os.environ["HTTPS_PROXY"] = "http://127.0.0.1:1080"

class MilvusHybridSearcher:
    """
    Milvus 混合检索器
    
    支持稀疏向量和密集向量的混合检索功能，
    使用 BGE-M3 模型生成向量嵌入。
    """
    
    def __init__(self, 
                 uri: str = "http://localhost:19530",
                 collection_name: str = "hybrid_search_demo",
                 use_bge_m3: bool = True,
                 device: str = "cpu"):
        """
        初始化 Milvus 混合检索器
        
        Args:
            uri: Milvus 服务器地址，本地使用 "./milvus.db" 或远程使用 "http://localhost:19530"
            collection_name: 集合名称
            use_bge_m3: 是否使用 BGE-M3 模型
            device: 计算设备 ("cpu" 或 "cuda")
        """
        self.uri = uri
        self.collection_name = collection_name
        self.client = None
        self.collection = None
        self.embedding_function = None
        self.dense_dim = None
        self.device = device
        
        # 初始化嵌入模型
        if use_bge_m3:
            self._init_bge_m3_model()
        
        logger.info(f"初始化 MilvusHybridSearcher，URI: {uri}, 集合: {collection_name}")
    
    def _init_bge_m3_model(self):
        """初始化 BGE-M3 嵌入模型"""
        try:
            logger.info("正在初始化 BGE-M3 嵌入模型...")
            self.embedding_function = BGEM3EmbeddingFunction(
                use_fp16=False, 
                device=self.device
            )
            self.dense_dim = self.embedding_function.dim["dense"]
            logger.info(f"BGE-M3 模型初始化成功，密集向量维度: {self.dense_dim}")
        except Exception as e:
            logger.error(f"BGE-M3 模型初始化失败: {e}")
            raise
    
    def connect(self):
        """连接到 Milvus 数据库"""
        try:
            logger.info(f"正在连接到 Milvus 数据库: {self.uri}")
            self.client = MilvusClient(uri=self.uri)
            
            # 也建立传统连接用于某些操作
            if self.uri.startswith("http"):
                connections.connect("default", host=self.uri.split("://")[1].split(":")[0], 
                                  port=int(self.uri.split(":")[-1]))
            else:
                connections.connect("default", uri=self.uri)
            
            logger.info("Milvus 数据库连接成功")
        except Exception as e:
            logger.error(f"连接 Milvus 数据库失败: {e}")
            raise
    
    def create_collection_schema(self) -> CollectionSchema:
        """
        创建支持混合向量的集合模式
        
        Returns:
            CollectionSchema: 集合模式对象
        """
        logger.info("正在创建集合模式...")
        
        # 创建模式
        schema = self.client.create_schema()
        
        # 添加字段
        schema.add_field(
            field_name="id", 
            datatype=DataType.INT64, 
            is_primary=True, 
            auto_id=True
        )
        schema.add_field(
            field_name="text", 
            datatype=DataType.VARCHAR, 
            max_length=2000, 
            enable_analyzer=True  # 启用文本分析器用于全文检索
        )
        schema.add_field(
            field_name="dense_vector", 
            datatype=DataType.FLOAT_VECTOR, 
            dim=self.dense_dim
        )
        schema.add_field(
            field_name="sparse_vector", 
            datatype=DataType.SPARSE_FLOAT_VECTOR
        )
        
        # 添加 BM25 函数，将文本转换为稀疏向量
        bm25_function = Function(
            name="text_bm25_embedding",
            input_field_names=["text"],
            output_field_names=["sparse_vector"],
            function_type=FunctionType.BM25,
        )
        schema.add_function(bm25_function)
        
        logger.info("集合模式创建完成")
        return schema
    
    def create_collection(self, drop_existing: bool = True):
        """
        创建支持混合检索的集合
        
        Args:
            drop_existing: 是否删除已存在的同名集合
        """
        try:
            # 检查并删除已存在的集合
            if drop_existing and self.client.has_collection(self.collection_name):
                logger.info(f"删除已存在的集合: {self.collection_name}")
                self.client.drop_collection(self.collection_name)
            
            # 创建集合模式
            schema = self.create_collection_schema()
            
            # 准备索引参数
            index_params = self.client.prepare_index_params()
            
            # 为密集向量添加索引
            index_params.add_index(
                field_name="dense_vector",
                index_type="AUTOINDEX",
                metric_type="COSINE"
            )
            
            # 为稀疏向量添加索引
            index_params.add_index(
                field_name="sparse_vector",
                index_type="SPARSE_INVERTED_INDEX",
                metric_type="BM25"
            )
            
            # 创建集合
            logger.info(f"正在创建集合: {self.collection_name}")
            self.client.create_collection(
                collection_name=self.collection_name,
                schema=schema,
                index_params=index_params
            )
            
            # 获取集合对象
            self.collection = Collection(self.collection_name)
            self.collection.load()
            
            logger.info(f"集合 {self.collection_name} 创建成功")
            
        except Exception as e:
            logger.error(f"创建集合失败: {e}")
            raise
    
    def insert_data(self, texts: List[str], batch_size: int = 50):
        """
        插入包含稀疏和密集向量的示例数据
        
        Args:
            texts: 要插入的文本列表
            batch_size: 批处理大小
        """
        try:
            logger.info(f"正在插入 {len(texts)} 条数据...")
            
            # 生成嵌入向量
            logger.info("正在生成向量嵌入...")
            embeddings = self.embedding_function(texts)
            
            # 分批插入数据
            total_inserted = 0
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                batch_dense = embeddings["dense"][i:i + batch_size]
                
                # 准备批次数据
                batch_data = [
                    {
                        "text": text,
                        "dense_vector": dense_vec
                    }
                    for text, dense_vec in zip(batch_texts, batch_dense)
                ]
                
                # 插入数据
                result = self.client.insert(
                    collection_name=self.collection_name,
                    data=batch_data
                )
                
                total_inserted += result["insert_count"]
                logger.info(f"已插入 {total_inserted}/{len(texts)} 条数据")
            
            logger.info(f"数据插入完成，总计: {total_inserted} 条")
            
        except Exception as e:
            logger.error(f"插入数据失败: {e}")
            raise
    
    def dense_search(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        执行密集向量检索
        
        Args:
            query: 查询文本
            limit: 返回结果数量限制
            
        Returns:
            List[Dict]: 检索结果列表
        """
        try:
            logger.info(f"执行密集向量检索: {query}")
            
            # 生成查询向量
            query_embeddings = self.embedding_function([query])
            query_dense = query_embeddings["dense"][0]
            
            # 执行检索
            results = self.client.search(
                collection_name=self.collection_name,
                data=[query_dense],
                anns_field="dense_vector",
                limit=limit,
                output_fields=["text"],
                search_params={"metric_type": "COSINE"}
            )
            
            return [{"text": hit["entity"]["text"], "score": hit["distance"]} 
                   for hit in results[0]]
            
        except Exception as e:
            logger.error(f"密集向量检索失败: {e}")
            raise
    
    def sparse_search(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        执行稀疏向量检索（全文检索）
        
        Args:
            query: 查询文本
            limit: 返回结果数量限制
            
        Returns:
            List[Dict]: 检索结果列表
        """
        try:
            logger.info(f"执行稀疏向量检索: {query}")
            
            # 执行检索
            results = self.client.search(
                collection_name=self.collection_name,
                data=[query],
                anns_field="sparse_vector",
                limit=limit,
                output_fields=["text"],
                search_params={
                    "metric_type": "BM25",
                    "params": {"drop_ratio_search": 0.2}
                }
            )
            
            return [{"text": hit["entity"]["text"], "score": hit["distance"]} 
                   for hit in results[0]]
            
        except Exception as e:
            logger.error(f"稀疏向量检索失败: {e}")
            raise

    def hybrid_search(self,
                     query: str,
                     limit: int = 10,
                     sparse_weight: float = 0.7,
                     dense_weight: float = 1.0,
                     reranker_type: str = "weighted") -> List[Dict[str, Any]]:
        """
        执行混合检索（结合密集向量和稀疏向量）

        Args:
            query: 查询文本
            limit: 返回结果数量限制
            sparse_weight: 稀疏向量权重
            dense_weight: 密集向量权重
            reranker_type: 重排序器类型 ("weighted" 或 "rrf")

        Returns:
            List[Dict]: 检索结果列表
        """
        try:
            logger.info(f"执行混合检索: {query}")

            # 生成查询向量
            query_embeddings = self.embedding_function([query])
            query_dense = query_embeddings["dense"][0]

            # 创建密集向量检索请求
            dense_search_params = {"metric_type": "COSINE", "params": {}}
            dense_req = AnnSearchRequest(
                data=[query_dense],
                anns_field="dense_vector",
                param=dense_search_params,
                limit=limit
            )

            # 创建稀疏向量检索请求
            sparse_search_params = {
                "metric_type": "BM25",
                "params": {"drop_ratio_search": 0.2}
            }
            sparse_req = AnnSearchRequest(
                data=[query],
                anns_field="sparse_vector",
                param=sparse_search_params,
                limit=limit
            )

            # 选择重排序器
            if reranker_type == "weighted":
                reranker = WeightedRanker(sparse_weight, dense_weight)
            else:  # rrf
                reranker = RRFRanker()

            # 执行混合检索
            results = self.client.hybrid_search(
                collection_name=self.collection_name,
                reqs=[sparse_req, dense_req],
                ranker=reranker,
                limit=limit,
                output_fields=["text"]
            )

            return [{"text": hit["entity"]["text"], "score": hit["distance"]}
                   for hit in results[0]]

        except Exception as e:
            logger.error(f"混合检索失败: {e}")
            raise

    def get_collection_info(self) -> Dict[str, Any]:
        """
        获取集合信息

        Returns:
            Dict: 集合信息
        """
        try:
            if not self.collection:
                return {"error": "集合未创建"}

            info = {
                "collection_name": self.collection_name,
                "num_entities": self.collection.num_entities,
                "schema": str(self.collection.schema),
                "indexes": [str(index) for index in self.collection.indexes]
            }

            return info

        except Exception as e:
            logger.error(f"获取集合信息失败: {e}")
            return {"error": str(e)}

    def close(self):
        """关闭连接"""
        try:
            if self.collection:
                self.collection.release()
            if hasattr(connections, 'disconnect'):
                connections.disconnect("default")
            logger.info("连接已关闭")
        except Exception as e:
            logger.warning(f"关闭连接时出现警告: {e}")


def create_sample_data() -> List[str]:
    """
    创建示例数据

    Returns:
        List[str]: 示例文本列表
    """
    sample_texts = [
        "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
        "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。",
        "深度学习是机器学习的一个分支，使用神经网络来模拟人脑的工作方式。",
        "自然语言处理是人工智能的一个领域，专注于计算机与人类语言之间的交互。",
        "计算机视觉是人工智能的一个分支，使计算机能够理解和解释视觉信息。",
        "向量数据库是专门用于存储和检索高维向量数据的数据库系统。",
        "Milvus 是一个开源的向量数据库，专为人工智能应用而设计。",
        "语义搜索使用向量嵌入来理解查询和文档之间的语义关系。",
        "混合检索结合了密集向量和稀疏向量的优势，提供更准确的搜索结果。",
        "RAG（检索增强生成）是一种结合信息检索和文本生成的技术。",
        "Python 是一种广泛用于人工智能和机器学习的编程语言。",
        "TensorFlow 和 PyTorch 是两个流行的深度学习框架。",
        "Transformer 架构彻底改变了自然语言处理领域。",
        "BERT 和 GPT 是基于 Transformer 的预训练语言模型。",
        "向量嵌入将文本、图像等数据转换为数值向量表示。"
    ]

    return sample_texts


def main():
    """
    主函数 - 演示 Milvus 混合检索的完整流程
    """
    try:
        # 初始化检索器
        logger.info("=== Milvus 混合检索示例开始 ===")

        # 使用本地 Milvus Lite（推荐用于开发和测试）
        # 如果要连接到远程 Milvus 服务器，请使用: "http://localhost:19530"
        searcher = MilvusHybridSearcher(
            uri="http://localhost:19530",  # 本地文件数据库
            collection_name="ai_knowledge_base",
            device="cpu"
        )

        # 连接数据库
        searcher.connect()

        # 创建集合
        searcher.create_collection(drop_existing=True)

        # 准备示例数据
        sample_data = create_sample_data()
        logger.info(f"准备插入 {len(sample_data)} 条示例数据")

        # 插入数据
        searcher.insert_data(sample_data)

        # 等待数据索引完成
        time.sleep(2)

        # 获取集合信息
        collection_info = searcher.get_collection_info()
        logger.info(f"集合信息: 实体数量 = {collection_info.get('num_entities', 'N/A')}")

        # 执行不同类型的检索
        query = "什么是机器学习和深度学习？"
        logger.info(f"\n=== 检索查询: {query} ===")

        # 1. 密集向量检索（语义搜索）
        logger.info("\n--- 密集向量检索结果 ---")
        dense_results = searcher.dense_search(query, limit=5)
        for i, result in enumerate(dense_results, 1):
            logger.info(f"{i}. [分数: {result['score']:.4f}] {result['text']}")

        # 2. 稀疏向量检索（关键词搜索）
        logger.info("\n--- 稀疏向量检索结果 ---")
        sparse_results = searcher.sparse_search(query, limit=5)
        for i, result in enumerate(sparse_results, 1):
            logger.info(f"{i}. [分数: {result['score']:.4f}] {result['text']}")

        # 3. 混合检索（结合语义和关键词）
        logger.info("\n--- 混合检索结果 ---")
        hybrid_results = searcher.hybrid_search(
            query,
            limit=5,
            sparse_weight=0.7,
            dense_weight=1.0,
            reranker_type="weighted"
        )
        for i, result in enumerate(hybrid_results, 1):
            logger.info(f"{i}. [分数: {result['score']:.4f}] {result['text']}")

        # 测试另一个查询
        query2 = "向量数据库 Milvus"
        logger.info(f"\n=== 检索查询: {query2} ===")

        hybrid_results2 = searcher.hybrid_search(query2, limit=3)
        for i, result in enumerate(hybrid_results2, 1):
            logger.info(f"{i}. [分数: {result['score']:.4f}] {result['text']}")

        logger.info("\n=== 示例执行完成 ===")

    except Exception as e:
        logger.error(f"示例执行失败: {e}")
        raise
    finally:
        # 清理资源
        if 'searcher' in locals():
            searcher.close()


if __name__ == "__main__":
    main()
