#!/usr/bin/env python
"""
测试脚本：验证所有导入和基本功能是否正常
"""

import sys
import os

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    print("测试导入模块...")
    
    # 测试基本导入
    import sys
    sys.path.append('src/rag_action/single-agent')
    from Memory import MemoryClass
    from Prompt import PromptClass
    from Emotion import EmotionClass
    from Agents import AgentClass
    
    print("✓ 所有模块导入成功")
    
    # 测试 MemoryClass 实例化
    print("测试 MemoryClass...")
    memory = MemoryClass(memoryKey="test_history", model="gpt-4o-mini")
    print("✓ MemoryClass 实例化成功")
    
    # 测试 PromptClass 实例化
    print("测试 PromptClass...")
    prompt = PromptClass(memory_key="test_history", feeling={"feeling": "default", "score": 5})
    print("✓ PromptClass 实例化成功")
    
    # 测试 EmotionClass 实例化
    print("测试 EmotionClass...")
    emotion = EmotionClass(model="gpt-4o-mini")
    print("✓ EmotionClass 实例化成功")
    
    print("\n所有测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
