"""
Text-to-SQL向量存储管理工具
提供增删改查和维护功能
"""
import sys
import os
import json
import logging
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.rag_action.service.text_to_sql_vectorstore import (
    TextToSQLVectorStore, FewShotExample, SchemaInfo, get_text_to_sql_vectorstore
)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class Text2SQLVectorStoreManager:
    """Text-to-SQL向量存储管理器"""
    
    def __init__(self):
        self.vectorstore = get_text_to_sql_vectorstore()
    
    def add_few_shot_example(self, question: str, sql: str, explanation: str, 
                           category: str = "general", difficulty: str = "easy") -> bool:
        """添加单个Few-Shot示例"""
        try:
            example = FewShotExample(
                question=question,
                sql=sql,
                explanation=explanation,
                category=category,
                difficulty=difficulty
            )
            
            success = self.vectorstore.insert_few_shot_examples([example])
            if success:
                logger.info(f"成功添加Few-Shot示例: {question}")
            else:
                logger.error(f"添加Few-Shot示例失败: {question}")
            
            return success
            
        except Exception as e:
            logger.error(f"添加Few-Shot示例异常: {e}")
            return False
    
    def add_few_shot_examples_from_file(self, file_path: str) -> bool:
        """从JSON文件批量添加Few-Shot示例"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            examples = []
            for item in data:
                example = FewShotExample(
                    question=item.get('question', ''),
                    sql=item.get('sql', ''),
                    explanation=item.get('explanation', ''),
                    category=item.get('category', 'general'),
                    difficulty=item.get('difficulty', 'easy')
                )
                examples.append(example)
            
            success = self.vectorstore.insert_few_shot_examples(examples)
            if success:
                logger.info(f"成功从文件添加{len(examples)}个Few-Shot示例")
            else:
                logger.error("从文件添加Few-Shot示例失败")
            
            return success
            
        except Exception as e:
            logger.error(f"从文件添加Few-Shot示例异常: {e}")
            return False
    
    def search_examples(self, query: str, top_k: int = 5, category: str = None) -> List[Dict[str, Any]]:
        """搜索Few-Shot示例"""
        try:
            examples = self.vectorstore.search_few_shot_examples(query, top_k, category)
            logger.info(f"搜索到{len(examples)}个相关示例")
            return examples
        except Exception as e:
            logger.error(f"搜索示例失败: {e}")
            return []
    
    def search_schemas(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """搜索Schema信息"""
        try:
            schemas = self.vectorstore.search_schema_info(query, top_k)
            logger.info(f"搜索到{len(schemas)}个相关Schema")
            return schemas
        except Exception as e:
            logger.error(f"搜索Schema失败: {e}")
            return []
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            stats = self.vectorstore.get_collection_stats()
            logger.info("获取统计信息成功")
            return stats
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def export_examples_to_file(self, file_path: str, query: str = "", top_k: int = 100) -> bool:
        """导出Few-Shot示例到文件"""
        try:
            examples = self.vectorstore.search_few_shot_examples(query or "SQL查询示例", top_k)
            
            # 转换为可序列化格式
            export_data = []
            for example in examples:
                export_data.append({
                    "question": example.get('question', ''),
                    "sql": example.get('sql', ''),
                    "explanation": example.get('explanation', ''),
                    "category": example.get('category', 'general'),
                    "difficulty": example.get('difficulty', 'easy'),
                    "similarity_score": example.get('similarity_score', 0.0)
                })
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"成功导出{len(export_data)}个示例到文件: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出示例到文件失败: {e}")
            return False
    
    def reinitialize_collections(self) -> bool:
        """重新初始化集合（危险操作）"""
        try:
            logger.warning("正在重新初始化集合，这将删除所有现有数据！")
            self.vectorstore.initialize_collections()
            logger.info("集合重新初始化完成")
            return True
        except Exception as e:
            logger.error(f"重新初始化集合失败: {e}")
            return False


def main():
    """主函数 - 命令行界面"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Text-to-SQL向量存储管理工具")
    parser.add_argument("command", choices=[
        "add-example", "add-examples-file", "search-examples", "search-schemas",
        "stats", "export-examples", "reinit"
    ], help="要执行的命令")
    
    # 添加示例相关参数
    parser.add_argument("--question", help="问题文本")
    parser.add_argument("--sql", help="SQL语句")
    parser.add_argument("--explanation", help="解释说明")
    parser.add_argument("--category", default="general", help="类别")
    parser.add_argument("--difficulty", default="easy", help="难度")
    
    # 搜索相关参数
    parser.add_argument("--query", help="搜索查询")
    parser.add_argument("--top-k", type=int, default=5, help="返回结果数量")
    
    # 文件相关参数
    parser.add_argument("--file", help="文件路径")
    
    args = parser.parse_args()
    
    manager = Text2SQLVectorStoreManager()
    
    try:
        if args.command == "add-example":
            if not all([args.question, args.sql, args.explanation]):
                logger.error("添加示例需要提供 --question, --sql, --explanation 参数")
                return False
            
            success = manager.add_few_shot_example(
                args.question, args.sql, args.explanation, 
                args.category, args.difficulty
            )
            return success
        
        elif args.command == "add-examples-file":
            if not args.file:
                logger.error("需要提供 --file 参数")
                return False
            
            success = manager.add_few_shot_examples_from_file(args.file)
            return success
        
        elif args.command == "search-examples":
            if not args.query:
                logger.error("需要提供 --query 参数")
                return False
            
            examples = manager.search_examples(args.query, args.top_k)
            print(f"\n找到{len(examples)}个相关示例:")
            for i, example in enumerate(examples, 1):
                print(f"\n{i}. 问题: {example['question']}")
                print(f"   SQL: {example['sql']}")
                print(f"   说明: {example['explanation']}")
                print(f"   相似度: {example.get('similarity_score', 0.0):.3f}")
            return True
        
        elif args.command == "search-schemas":
            if not args.query:
                logger.error("需要提供 --query 参数")
                return False
            
            schemas = manager.search_schemas(args.query, args.top_k)
            print(f"\n找到{len(schemas)}个相关Schema:")
            for i, schema in enumerate(schemas, 1):
                print(f"\n{i}. 表名: {schema['table_name']}")
                print(f"   描述: {schema['description']}")
                print(f"   相似度: {schema.get('similarity_score', 0.0):.3f}")
            return True
        
        elif args.command == "stats":
            stats = manager.get_stats()
            print("\n集合统计信息:")
            print(json.dumps(stats, ensure_ascii=False, indent=2))
            return True
        
        elif args.command == "export-examples":
            if not args.file:
                logger.error("需要提供 --file 参数")
                return False
            
            success = manager.export_examples_to_file(args.file, args.query or "", args.top_k)
            return success
        
        elif args.command == "reinit":
            confirm = input("这将删除所有现有数据，确认吗？(yes/no): ")
            if confirm.lower() == "yes":
                success = manager.reinitialize_collections()
                return success
            else:
                logger.info("操作已取消")
                return True
        
        else:
            logger.error(f"未知命令: {args.command}")
            return False
    
    except Exception as e:
        logger.error(f"执行命令失败: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
