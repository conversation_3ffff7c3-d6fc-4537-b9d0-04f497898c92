<template>
  <div class="app-layout" :class="{ 'is-mobile': appStore.isMobile }">
    <!-- 侧边栏 -->
    <aside 
      class="app-sidebar" 
      :class="{ 'is-collapsed': appStore.sidebarCollapsed }"
    >
      <div class="sidebar-header">
        <div class="logo">
          <el-icon class="logo-icon"><Document /></el-icon>
          <span v-show="!appStore.sidebarCollapsed" class="logo-text">AI笔记系统</span>
        </div>
      </div>
      
      <nav class="sidebar-nav">
        <el-menu
          :default-active="currentMenuActive"
          :collapse="appStore.sidebarCollapsed"
          :unique-opened="true"
          class="sidebar-menu"
          @select="handleMenuSelect"
        >
          <el-menu-item
            v-for="item in appStore.menuItems"
            :key="item.path"
            :index="item.path"
          >
            <el-icon>
              <Monitor v-if="item.icon === 'Dashboard'" />
              <Document v-else-if="item.icon === 'Document'" />
              <Upload v-else-if="item.icon === 'Upload'" />
              <ChatDotRound v-else-if="item.icon === 'ChatDotRound'" />
              <Lightning v-else-if="item.icon === 'Lightning'" />
              <Monitor v-else-if="item.icon === 'Monitor'" />
              <Setting v-else-if="item.icon === 'Setting'" />
              <Document v-else />
            </el-icon>
            <template #title>{{ item.title }}</template>
          </el-menu-item>
        </el-menu>
      </nav>
    </aside>

    <!-- 主内容区 -->
    <div class="app-main">
      <!-- 顶部导航 -->
      <header class="app-header">
        <div class="header-left">
          <el-button
            type="text"
            @click="appStore.toggleSidebar()"
            class="sidebar-toggle"
          >
            <el-icon><Expand v-if="appStore.sidebarCollapsed" /><Fold v-else /></el-icon>
          </el-button>
          
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item
              v-for="item in breadcrumbs"
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <!-- 系统状态指示器 -->
          <div class="system-status">
            <el-tooltip :content="systemStatusText" placement="bottom">
              <el-badge
                :type="systemStatusType"
                is-dot
                class="status-badge"
              >
                <el-icon><Monitor /></el-icon>
              </el-badge>
            </el-tooltip>
          </div>
          
          <!-- 主题切换 -->
          <el-dropdown @command="handleThemeChange" class="theme-switcher">
            <el-button type="text">
              <el-icon><Sunny v-if="appStore.isDark" /><Moon v-else /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="light">浅色主题</el-dropdown-item>
                <el-dropdown-item command="dark">深色主题</el-dropdown-item>
                <el-dropdown-item command="auto">跟随系统</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          
          <!-- 全屏切换 -->
          <el-button type="text" @click="toggleFullscreen" class="fullscreen-btn">
            <el-icon><FullScreen /></el-icon>
          </el-button>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="app-content">
        <!-- 智能问答模式 -->
        <div v-if="showQueryMode" class="query-mode-container">
          <div class="query-mode-header">
            <div class="query-mode-title">
              <el-icon><ChatDotRound /></el-icon>
              <span>智能问答</span>
            </div>
            <el-button type="text" @click="exitQueryMode" class="exit-btn">
              <el-icon><Close /></el-icon>
              退出问答模式
            </el-button>
          </div>
          <div class="query-mode-content">
            <QueryComponent :embedded="true" />
          </div>
        </div>

        <!-- 正常路由内容 -->
        <div v-else class="normal-content">
          <router-view v-slot="{ Component, route }">
            <transition name="fade" mode="out-in">
              <keep-alive :include="keepAliveComponents">
                <component :is="Component" :key="route.path" />
              </keep-alive>
            </transition>
          </router-view>
        </div>
      </main>
    </div>

    <!-- 全局加载遮罩 -->
    <div v-if="appStore.globalLoading" class="global-loading" v-loading="true">
      <p>加载中...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore, useSystemStore } from '@/stores'
import {
  Document,
  Expand,
  Fold,
  Monitor,
  Sunny,
  Moon,
  FullScreen,
  Upload,
  ChatDotRound,
  Lightning,
  Setting,
  Close
} from '@element-plus/icons-vue'
import QueryComponent from '@/views/Query/index.vue'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const systemStore = useSystemStore()

// 智能问答模式
const showQueryMode = computed(() => appStore.queryMode)

// 当前激活的菜单项
const currentMenuActive = computed(() => {
  if (showQueryMode.value) {
    return '/query'
  }
  return route.path
})

// 面包屑导航
const breadcrumbs = computed(() => {
  if (showQueryMode.value) {
    return [{ path: '/', title: '首页' }, { path: '/query', title: '智能问答' }]
  }
  const matched = route.matched.filter(item => item.meta?.title)
  return matched.map(item => ({
    path: item.path,
    title: item.meta?.title || item.name
  }))
})

// 智能问答模式控制
const enterQueryMode = () => {
  console.log('🚀 进入智能问答模式')
  appStore.enterQueryMode()
  // 更新URL但不触发路由跳转
  window.history.pushState({}, '', '/query')
}

const exitQueryMode = () => {
  appStore.exitQueryMode()
  // 返回到首页
  router.push('/')
}

// 暴露方法给全局使用
defineExpose({
  enterQueryMode,
  exitQueryMode
})

// 菜单选择处理
const handleMenuSelect = (index: string) => {
  if (index === '/query') {
    // 智能问答菜单，进入嵌入模式
    enterQueryMode()
  } else {
    // 其他菜单，退出问答模式并正常路由
    if (showQueryMode.value) {
      appStore.exitQueryMode()
    }
    router.push(index)
  }
}

// 需要缓存的组件
const keepAliveComponents = computed(() => {
  return route.matched
    .filter(item => item.meta?.keepAlive)
    .map(item => item.name)
})

// 系统状态
const systemStatusText = computed(() => {
  const status = systemStore.systemStatus
  const statusMap = {
    healthy: '系统运行正常',
    degraded: '系统性能下降',
    unhealthy: '系统异常',
    unknown: '系统状态未知'
  }
  return statusMap[status] || '系统状态未知'
})

const systemStatusType = computed(() => {
  const status = systemStore.systemStatus
  const typeMap = {
    healthy: 'success',
    degraded: 'warning',
    unhealthy: 'danger',
    unknown: 'info'
  }
  return typeMap[status] || 'info'
})

// 主题切换
const handleThemeChange = (theme: 'light' | 'dark' | 'auto') => {
  appStore.setTheme(theme)
}

// 全屏切换
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

// 初始化
onMounted(() => {
  appStore.initApp()
  systemStore.fetchHealth()
  
  // 定期检查系统状态
  const healthCheckInterval = setInterval(() => {
    systemStore.fetchHealth()
  }, 30000) // 30秒检查一次
  
  onUnmounted(() => {
    clearInterval(healthCheckInterval)
  })
})
</script>

<style lang="scss" scoped>
.app-layout {
  display: flex;
  height: 100vh;
  background-color: var(--bg-page);
  
  &.is-mobile {
    .app-sidebar {
      position: fixed;
      z-index: var(--z-index-fixed);
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      
      &:not(.is-collapsed) {
        transform: translateX(0);
      }
    }
    
    .app-main {
      margin-left: 0;
    }
  }
}

.app-sidebar {
  width: 240px;
  background-color: var(--bg-color);
  border-right: 1px solid var(--border-lighter);
  transition: width 0.3s ease;
  
  &.is-collapsed {
    width: 64px;
  }
  
  .sidebar-header {
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 var(--spacing-md);
    border-bottom: 1px solid var(--border-lighter);
    
    .logo {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      
      .logo-icon {
        font-size: 24px;
        color: var(--primary-color);
      }
      
      .logo-text {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
      }
    }
  }
  
  .sidebar-nav {
    height: calc(100vh - 60px);
    overflow-y: auto;
    @include scrollbar;
    
    .sidebar-menu {
      border: none;
      
      :deep(.el-menu-item) {
        height: 48px;
        line-height: 48px;
        
        &.is-active {
          background-color: var(--primary-color);
          color: white;
          
          .el-icon {
            color: white;
          }
        }
      }
    }
  }
}

.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 240px;
  transition: margin-left 0.3s ease;
  
  .app-layout.is-mobile & {
    margin-left: 0;
  }
  
  .app-sidebar.is-collapsed + & {
    margin-left: 64px;
  }
}

.app-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-lg);
  background-color: var(--bg-color);
  border-bottom: 1px solid var(--border-lighter);
  
  .header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    
    .sidebar-toggle {
      font-size: 18px;
    }
    
    .breadcrumb {
      :deep(.el-breadcrumb__item) {
        .el-breadcrumb__inner {
          color: var(--text-regular);
          
          &:hover {
            color: var(--primary-color);
          }
        }
        
        &:last-child .el-breadcrumb__inner {
          color: var(--text-primary);
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    
    .system-status {
      .status-badge {
        cursor: pointer;
      }
    }
    
    .theme-switcher,
    .fullscreen-btn {
      font-size: 18px;
    }
  }
}

.app-content {
  flex: 1;
  overflow: hidden;
  position: relative;
  padding: var(--spacing-lg);
  background: var(--bg-page);

  .query-mode-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .query-mode-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      padding: var(--spacing-md) 0;
      border-bottom: 1px solid var(--border-lighter);

      .query-mode-title {
        display: flex;
        align-items: center;
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-medium);
        color: var(--text-primary);

        .el-icon {
          margin-right: var(--spacing-sm);
          color: var(--primary-color);
        }
      }

      .exit-btn {
        color: var(--text-secondary);

        &:hover {
          color: var(--primary-color);
        }
      }
    }

    .query-mode-content {
      flex: 1;
      overflow: hidden;

      // 重置Query组件的样式以适应嵌入模式
      :deep(.query-page) {
        height: 100%;
        padding: 0;

        .query-container {
          height: 100%;
        }
      }
    }
  }

  .normal-content {
    height: 100%;
    overflow-y: auto;
    @include scrollbar;
  }
}

.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-overlay);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);

  p {
    margin-top: var(--spacing-md);
    color: var(--text-regular);
  }
}

// 页面切换动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
