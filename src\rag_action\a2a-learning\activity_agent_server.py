# activity_agent_server.py
import time
import re # 用于从查询中提取天气信息
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Dict, Any, Optional
import uvicorn
from python_a2a import A2AServer, AgentCard, AgentSkill, Task, TaskStatus, TaskState, Message, TextContent, MessageRole

class ActivityAgent(A2AServer):
    def __init__(self, port=5002):
        self.port = port
        agent_card = AgentCard(
            name="Activity Agent",
            description="根据城市和天气推荐活动",
            url=f"http://localhost:{self.port}",
            version="1.0.0",
            skills=[
                AgentSkill(
                    name="GetActivityRecommendations",
                    description="获取基于天气和地点的活动推荐",
                    tags=["activity", "recommendation", "travel", "leisure"]
                )
            ]
        )
        super().__init__(agent_card=agent_card)

    def _get_simulated_activities(self, city, weather_condition):
        print(f"[ActivityAgent] 正在为 '{city}' (天气: {weather_condition}) 生成模拟活动推荐...")
        time.sleep(1.5) # 模拟处理时间
        is_rainy_or_bad_weather = any(kw in weather_condition.lower() for kw in ["雨", "雷", "雪", "糟", "霾"])
        
        recommendations = {
            "北京": {
                "indoor": "故宫博物院深度游、国家博物馆看展、798艺术区感受艺术氛围。",
                "outdoor": "颐和园漫步、长城徒步（天气好时）、什刹海胡同骑行。"
            },
            "上海": {
                "indoor": "上海博物馆探索历史、中华艺术宫赏析艺术、体验密室逃脱。",
                "outdoor": "外滩漫步欣赏万国建筑群、南京路步行街购物、豫园游览。"
            },
            "广州": {
                "indoor": "广东省博物馆、广州塔室内观光层、正佳广场极地海洋世界。",
                "outdoor": "白云山登高望远、越秀公园五羊石像、珠江夜游。"
            },
            "深圳": {
                "indoor": "深圳博物馆、当代艺术与城市规划馆、万象城购物体验。",
                "outdoor": "世界之窗（天气好时）、莲花山公园放风筝、深圳湾公园骑行。"
            },
            "伦敦": {
                "indoor": "大英博物馆、国家美术馆、自然历史博物馆。",
                "outdoor": "海德公园野餐、伦敦眼俯瞰城市、泰晤士河畔散步。"
            },
            "巴黎": {
                "indoor": "卢浮宫、奥赛博物馆、蓬皮杜艺术中心。",
                "outdoor": "埃菲尔铁塔下草坪休闲、塞纳河畔漫步、蒙马特高地探索。"
            }
        }
        
        city_activities = recommendations.get(city, {
            "indoor": f"在{city}可以逛逛当地的博物馆或购物中心。",
            "outdoor": f"在{city}可以探索当地的公园或著名地标。"
        })
        
        if is_rainy_or_bad_weather:
            return f"由于{city}的天气是'{weather_condition}'，推荐室内活动：{city_activities['indoor']}"
        else:
            return f"{city}天气'{weather_condition}'，很棒！推荐户外活动：{city_activities['outdoor']}"

    def _extract_info_from_query(self, query_text):
        city = "未知城市"
        weather = "未知天气"

        known_cities = ["北京", "上海", "广州", "深圳", "伦敦", "巴黎"]
        for c in known_cities:
            if c in query_text:
                city = c
                break
        
        weather_keywords = {
            "晴朗": ["晴", "太阳", "sunny"],
            "多云": ["多云", "cloudy"],
            "阴": ["阴天", "overcast"],
            "雨": ["雨", "rainy", "雷阵雨"],
            "雪": ["雪", "snowy"]
        }
        for condition, kws in weather_keywords.items():
            if any(kw in query_text for kw in kws):
                weather = condition
                break
        
        if weather == "未知天气":
            match = re.search(r"天气是([^，。]+)|状况是([^，。]+)", query_text)
            if match:
                weather = match.group(1) or match.group(2) or "宜人"
            elif "天气" in query_text:
                 weather = "宜人（具体情况未知）"

        return city, weather

    def handle_task(self, task: Task) -> Task:
        query_text = ""
        if task.message and task.message.content and hasattr(task.message.content, 'text'):
            query_text = task.message.content.text
        
        city_match = re.search(r"in (\w+)", query_text, re.IGNORECASE)
        city = city_match.group(1) if city_match else "北京"
        
        weather_match = re.search(r"weather: (.+)", query_text, re.IGNORECASE)
        weather_condition = weather_match.group(1) if weather_match else "天气不错"
        if not weather_match:
            if "推荐活动" in query_text:
                 parts = query_text.split("在")
                 if len(parts) > 1:
                    potential_weather = parts[-1].replace("下推荐活动","").strip()
                    if potential_weather:
                        weather_condition = potential_weather

        activity_data = self._get_simulated_activities(city, weather_condition)
        
        print(f"[ActivityAgent] 为 '{city}' (天气: {weather_condition}) 推荐的活动: {activity_data}")

        task.artifacts = [{
            "parts": [{"type": "text", "text": activity_data}]
        }]
        task.status = TaskStatus(state=TaskState.COMPLETED)
        return task

# Pydantic models for request/response
class TaskRequest(BaseModel):
    jsonrpc: Optional[str] = None
    method: Optional[str] = None
    params: Optional[Dict[str, Any]] = None
    id: Optional[str] = None

class MessageRequest(BaseModel):
    content: Optional[Dict[str, Any]] = None
    message_id: Optional[str] = None

# ---- FastAPI App Setup ----
def create_app(agent_instance: ActivityAgent) -> FastAPI:
    app = FastAPI(
        title="Activity Agent Server",
        description="A2A协议活动推荐智能体服务",
        version="1.0.0"
    )

    @app.get('/agent.json')
    async def get_agent_card():
        return agent_instance.agent_card.to_dict()

    @app.get('/a2a/agent.json')
    async def get_a2a_agent_card():
        """A2A标准兼容端点"""
        return agent_instance.agent_card.to_dict()

    @app.post('/tasks/send')
    async def handle_send_task(request: Dict[str, Any]):
        try:
            # 兼容JSON-RPC风格的请求
            if "jsonrpc" in request and "method" in request and request["method"] == "tasks/send":
                task_dict = request.get("params", {})
            else:
                task_dict = request
            
            task = Task.from_dict(task_dict)
            updated_task = agent_instance.handle_task(task)
            response_data = updated_task.to_dict()

            if "jsonrpc" in request:
                return {"jsonrpc": "2.0", "id": request.get("id"), "result": response_data}
            return response_data
        except Exception as e:
            error_response = {"code": -32603, "message": str(e)}
            if "jsonrpc" in request:
                return JSONResponse(
                    status_code=500,
                    content={"jsonrpc": "2.0", "id": request.get("id"), "error": error_response}
                )
            raise HTTPException(status_code=500, detail=error_response)
            
    @app.post('/')
    async def handle_root_message(request: Dict[str, Any]):
        """兼容旧版Message端点"""
        try:
            # 尝试将整个请求体作为Task处理
            if isinstance(request, dict) and "id" in request and "status" in request:
                return await handle_send_task(request)

            # 否则，尝试作为Message处理
            text_content = request.get("content", {}).get("text", "") if isinstance(request.get("content"), dict) else str(request)
            message = Message(content=TextContent(text=text_content), role=MessageRole.USER)
            # 模拟任务转换
            task = Task(id=f"task-{time.time_ns()}", message=message, status=TaskStatus(state=TaskState.PENDING))
            updated_task = agent_instance.handle_task(task)
            # 从Task结果中提取文本给Message响应
            response_text = "处理完成。"
            if updated_task.artifacts and updated_task.artifacts[0].get("parts"):
                response_text = updated_task.artifacts[0]["parts"][0].get("text", response_text)
            
            response_message = Message(
                content=TextContent(text=response_text),
                role=MessageRole.AGENT,
                message_id=f"response-{time.time_ns()}",
                parent_message_id=request.get("message_id")
            )
            return response_message.to_dict()
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))
            
    return app

if __name__ == "__main__":
    PORT = 5002
    activity_agent = ActivityAgent(port=PORT)
    app = create_app(activity_agent)
    print(f"ActivityAgent 正在启动，监听端口: http://localhost:{PORT}")
    uvicorn.run(app, host='0.0.0.0', port=PORT)