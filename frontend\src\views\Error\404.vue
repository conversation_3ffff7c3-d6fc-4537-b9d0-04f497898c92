<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-icon">
        <el-icon><Warning /></el-icon>
      </div>
      
      <h1 class="error-title">404</h1>
      <p class="error-message">抱歉，您访问的页面不存在</p>
      
      <div class="error-actions">
        <el-button type="primary" @click="goHome">
          <el-icon><House /></el-icon>
          返回首页
        </el-button>
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回上页
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { Warning, House, ArrowLeft } from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.back()
}
</script>

<style lang="scss" scoped>
.error-page {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-page);
  
  .error-content {
    text-align: center;
    
    .error-icon {
      font-size: 120px;
      color: var(--warning-color);
      margin-bottom: var(--spacing-lg);
    }
    
    .error-title {
      font-size: 72px;
      font-weight: var(--font-weight-bold);
      color: var(--text-primary);
      margin-bottom: var(--spacing-md);
    }
    
    .error-message {
      font-size: var(--font-size-lg);
      color: var(--text-secondary);
      margin-bottom: var(--spacing-xl);
    }
    
    .error-actions {
      display: flex;
      gap: var(--spacing-md);
      justify-content: center;
    }
  }
}

@media (max-width: 768px) {
  .error-page {
    .error-content {
      .error-icon {
        font-size: 80px;
      }

      .error-title {
        font-size: 48px;
      }

      .error-actions {
        flex-direction: column;
        align-items: center;
      }
    }
  }
}
</style>
