"""
优化版 - LangGraph ToolNode 工具调用案例
使用现代API，简化工具定义，解决兼容性问题
"""

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
from langgraph.graph import StateGraph, MessagesState, START, END
from langgraph.prebuilt import ToolNode
from langchain_core.tools import tool
from typing import Dict, Any, List
import json
import datetime
import math

# 创建 LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0.7
)

# ==================== 工具定义 ====================

@tool
def get_current_time() -> str:
    """获取当前时间"""
    return f"当前时间是: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

@tool
def calculate_math(expression: str) -> str:
    """计算数学表达式
    
    Args:
        expression: 要计算的数学表达式，如 '2 + 3 * 4' 或 'sqrt(16)'
    """
    try:
        # 安全地计算数学表达式
        allowed_names = {
            'abs': abs, 'round': round, 'min': min, 'max': max,
            'sum': sum, 'len': len, 'pow': pow, 'sqrt': math.sqrt
        }
        
        # 替换 sqrt 为 math.sqrt
        expression = expression.replace('sqrt', 'math.sqrt')
        
        result = eval(expression, {"__builtins__": {}}, allowed_names)
        return f"计算结果: {expression} = {result}"
    except Exception as e:
        return f"计算错误: {str(e)}"

@tool
def get_weather(city: str) -> str:
    """获取指定城市的天气信息
    
    Args:
        city: 城市名称
    """
    weather_data = {
        "北京": "晴天，温度 25°C，湿度 60%",
        "上海": "多云，温度 28°C，湿度 70%",
        "广州": "小雨，温度 30°C，湿度 80%",
        "深圳": "晴天，温度 32°C，湿度 65%"
    }
    
    if city in weather_data:
        return f"{city}的天气: {weather_data[city]}"
    else:
        return f"抱歉，没有{city}的天气信息"

@tool
def search_web(query: str) -> str:
    """搜索网络信息
    
    Args:
        query: 搜索查询
    """
    search_results = {
        "人工智能": "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
        "机器学习": "机器学习是人工智能的一个子集，使计算机能够在没有明确编程的情况下学习和改进。",
        "深度学习": "深度学习使用神经网络来模拟人脑的工作方式，是机器学习的一个重要分支。"
    }
    
    for key, value in search_results.items():
        if key in query:
            return f"搜索结果 - {key}: {value}"
    
    return f"搜索 '{query}' 的结果: 这是模拟的搜索结果，实际应用中会连接到真实的搜索引擎。"

@tool
def get_user_info(user_id: str) -> str:
    """获取用户信息
    
    Args:
        user_id: 用户ID
    """
    user_database = {
        "user001": {"name": "张三", "age": 25, "city": "北京", "interests": ["编程", "音乐"]},
        "user002": {"name": "李四", "age": 30, "city": "上海", "interests": ["阅读", "旅行"]},
        "user003": {"name": "王五", "age": 28, "city": "广州", "interests": ["运动", "美食"]}
    }
    
    if user_id in user_database:
        user = user_database[user_id]
        return f"用户信息: 姓名={user['name']}, 年龄={user['age']}, 城市={user['city']}, 兴趣={', '.join(user['interests'])}"
    else:
        return f"未找到用户ID: {user_id}"

# ==================== 创建工具列表和绑定 ====================

# 创建工具列表
tools = [
    get_current_time,
    calculate_math,
    get_weather,
    search_web,
    get_user_info
]

# 打印工具信息用于调试
print("=== 工具信息 ===")
for i, t in enumerate(tools):
    print(f"工具 {i+1}: {t.name} -> {type(t)}")
print()

# 使用现代API绑定工具到LLM
llm_with_tools = llm.bind_tools(tools)

# 使用 ToolNode 预构建节点
tool_node = ToolNode(tools)

# ==================== 简化的节点定义 ====================

def chat_node(state: MessagesState) -> MessagesState:
    """聊天节点 - 使用绑定工具的LLM"""
    messages = state["messages"]
    response = llm_with_tools.invoke(messages)
    return {"messages": [response]}

def should_continue(state: MessagesState) -> str:
    """判断是否需要调用工具"""
    messages = state["messages"]
    last_message = messages[-1]
    
    # 检查最后一条消息是否包含工具调用
    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        return "tools"
    return "end"

# ==================== 创建图 ====================

# 创建图
workflow = StateGraph(MessagesState)

# 添加节点
workflow.add_node("chat", chat_node)
workflow.add_node("tools", tool_node)

# 设置入口点和条件边
workflow.set_entry_point("chat")
workflow.add_conditional_edges(
    "chat",
    should_continue,
    {
        "tools": "tools",
        "end": END
    }
)
workflow.add_edge("tools", "chat")  # 工具执行后回到聊天节点

# 编译图
app = workflow.compile()

# ==================== 演示函数 ====================

def tool_calling_demo():
    """工具调用演示"""
    print("=== LangGraph ToolNode 优化版演示 ===")
    print()
    
    # 测试用例
    test_cases = [
        "现在几点了？",
        "请计算 15 * 8 + 32",
        "北京今天天气怎么样？",
        "请搜索人工智能的相关信息",
        "计算 sqrt(144) 的结果",
        "获取用户 user001 的信息"
    ]
    
    for i, query in enumerate(test_cases, 1):
        print(f"测试 {i}: {query}")
        print("-" * 50)
        
        try:
            result = app.invoke({
                "messages": [HumanMessage(content=query)]
            })
            
            # 显示结果
            if result["messages"]:
                for msg in result["messages"]:
                    if isinstance(msg, ToolMessage):
                        print(f"🔧 工具结果: {msg.content}")
                    elif isinstance(msg, AIMessage):
                        if hasattr(msg, 'tool_calls') and msg.tool_calls:
                            print(f"🤖 AI调用工具: {[tc['name'] for tc in msg.tool_calls]}")
                        else:
                            print(f"🤖 AI回复: {msg.content}")
            
        except Exception as e:
            print(f"❌ 执行出错: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n" + "=" * 60 + "\n")

def multi_tool_demo():
    """多工具组合演示"""
    print("=== 多工具组合演示 ===")
    print()
    
    complex_queries = [
        "现在几点了？然后告诉我北京的天气",
        "计算 25 的平方根，然后搜索机器学习的信息",
        "获取用户 user002 的信息，然后告诉我上海的天气"
    ]
    
    for i, query in enumerate(complex_queries, 1):
        print(f"复杂查询 {i}: {query}")
        print("-" * 50)
        
        try:
            result = app.invoke({
                "messages": [HumanMessage(content=query)]
            })
            
            # 显示最终结果
            if result["messages"]:
                final_message = result["messages"][-1]
                if isinstance(final_message, AIMessage):
                    print(f"🤖 最终回复: {final_message.content}")
            
        except Exception as e:
            print(f"❌ 执行出错: {e}")
        
        print("\n" + "=" * 60 + "\n")

def interactive_demo():
    """交互式演示"""
    print("=== 交互式工具调用演示 ===")
    print("输入 'quit' 退出")
    print()
    
    while True:
        try:
            user_input = input("👤 请输入您的问题: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            print("🤖 处理中...")
            result = app.invoke({
                "messages": [HumanMessage(content=user_input)]
            })
            
            if result["messages"]:
                final_message = result["messages"][-1]
                if isinstance(final_message, AIMessage):
                    print(f"🤖 回复: {final_message.content}")
            
            print()
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 出错了: {e}")

def main():
    """主函数"""
    tool_calling_demo()
    multi_tool_demo()
    
    # 可选：启用交互式演示
    # interactive_demo()

if __name__ == "__main__":
    main()
