#!/usr/bin/env python3
"""
批量PDF转Markdown转换脚本
参考 minerU_pdf2markdown.py 的实现方式
使用并发处理，适合大批量文档
"""
import os
import json
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from loguru import logger

# 导入mineru相关模块
from mineru.cli.common import convert_pdf_bytes_to_bytes_by_pypdfium2, prepare_env, read_fn
from mineru.data.data_reader_writer import FileBasedDataWriter
from mineru.utils.enum_class import MakeMode
from mineru.backend.pipeline.pipeline_analyze import doc_analyze as pipeline_doc_analyze
from mineru.backend.pipeline.pipeline_middle_json_mkcontent import union_make as pipeline_union_make
from mineru.backend.pipeline.model_json_to_middle_json import result_to_middle_json as pipeline_result_to_middle_json

def main():
    """批量转换主函数"""
    print("🚀 开始批量PDF转Markdown转换...")
    
    # 设置路径
    __dir__ = os.path.dirname(os.path.abspath(__file__))
    pdf_files_dir = os.path.join(__dir__, "pdfs")
    output_dir = os.path.join(__dir__, "output")
    
    # 确保目录存在
    os.makedirs(pdf_files_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)
    
    # 收集PDF文件
    pdf_suffixes = [".pdf"]
    doc_path_list = []
    
    for doc_path in Path(pdf_files_dir).glob('*'):
        if doc_path.suffix.lower() in pdf_suffixes:
            doc_path_list.append(doc_path)
    
    print(f"📁 找到 {len(doc_path_list)} 个PDF文件:")
    for path in doc_path_list:
        print(f"   - {path.name}")
    
    if not doc_path_list:
        print("❌ 未找到PDF文件！请将PDF文件放入 'pdfs' 目录")
        return
    
    # 开始批量转换
    try:
        print("\n🔄 开始批量转换...")
        
        # 使用线程池进行并发处理
        max_workers = min(4, len(doc_path_list))  # 最多4个线程
        print(f"🔧 使用 {max_workers} 个线程进行并发处理")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_path = {
                executor.submit(convert_single_pdf, path, output_dir): path 
                for path in doc_path_list
            }
            
            # 处理完成的任务
            completed_count = 0
            failed_count = 0
            
            for future in as_completed(future_to_path):
                path = future_to_path[future]
                try:
                    result = future.result()
                    completed_count += 1
                    print(f"✅ 完成: {path.name}")
                except Exception as e:
                    failed_count += 1
                    print(f"❌ 失败: {path.name} - {e}")
        
        print(f"\n📊 批量转换完成！")
        print(f"✅ 成功: {completed_count} 个文件")
        print(f"❌ 失败: {failed_count} 个文件")
        print(f"📂 输出目录: {output_dir}")
        
    except Exception as e:
        print(f"❌ 批量转换失败: {e}")
        logger.exception(e)

def convert_single_pdf(pdf_path: Path, output_dir: str):
    """转换单个PDF文件"""
    try:
        file_name = str(pdf_path.stem)
        pdf_bytes = read_fn(pdf_path)
        
        # 使用pipeline模式进行转换
        parse_single_with_pipeline(
            output_dir=output_dir,
            pdf_file_name=file_name,
            pdf_bytes=pdf_bytes,
            lang="ch"
        )
        
        return True
        
    except Exception as e:
        logger.error(f"转换文件 {pdf_path.name} 失败: {e}")
        raise e

def parse_single_with_pipeline(
    output_dir: str,
    pdf_file_name: str,
    pdf_bytes: bytes,
    lang: str = "ch",
    parse_method: str = "auto",
    formula_enable: bool = True,
    table_enable: bool = True
):
    """使用pipeline模式解析单个PDF"""
    
    # 使用pipeline进行分析
    infer_results, all_image_lists, all_pdf_docs, lang_list, ocr_enabled_list = pipeline_doc_analyze(
        [pdf_bytes], 
        [lang], 
        parse_method=parse_method, 
        formula_enable=formula_enable,
        table_enable=table_enable
    )

    # 处理结果
    model_list = infer_results[0]
    images_list = all_image_lists[0]
    pdf_doc = all_pdf_docs[0]
    _lang = lang_list[0]
    _ocr_enable = ocr_enabled_list[0]
    
    # 准备环境
    local_image_dir, local_md_dir = prepare_env(output_dir, pdf_file_name, parse_method)
    image_writer, md_writer = FileBasedDataWriter(local_image_dir), FileBasedDataWriter(local_md_dir)
    
    # 转换为中间JSON格式
    middle_json = pipeline_result_to_middle_json(
        model_list, images_list, pdf_doc, image_writer, _lang, _ocr_enable, formula_enable
    )

    pdf_info = middle_json["pdf_info"]

    # 生成Markdown文件
    image_dir = str(os.path.basename(local_image_dir))
    md_content_str = pipeline_union_make(pdf_info, MakeMode.MM_MD, image_dir)
    md_writer.write_string(f"{pdf_file_name}.md", md_content_str)

    # 生成内容列表
    content_list = pipeline_union_make(pdf_info, MakeMode.CONTENT_LIST, image_dir)
    md_writer.write_string(
        f"{pdf_file_name}_content_list.json",
        json.dumps(content_list, ensure_ascii=False, indent=4)
    )

    # 生成中间JSON
    md_writer.write_string(
        f"{pdf_file_name}_middle.json",
        json.dumps(middle_json, ensure_ascii=False, indent=4)
    )

    logger.info(f"文件 {pdf_file_name} 转换完成，输出目录: {local_md_dir}")

if __name__ == "__main__":
    main() 