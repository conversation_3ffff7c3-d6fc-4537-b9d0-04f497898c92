"""
测试配置文件
提供测试用的fixtures和配置
"""
import pytest
import tempfile
import os
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.rag_action.main import app
from src.rag_action.core.database import get_db, Base
from src.rag_action.core.config import Settings


@pytest.fixture
def test_settings():
    """测试配置"""
    return Settings(
        app={
            "host": "127.0.0.1",
            "port": 8000,
            "debug": True,
            "title": "AI笔记系统测试",
            "description": "测试环境",
            "version": "1.0.0-test"
        },
        mysql={
            "host": "localhost",
            "port": 3306,
            "user": "root",
            "password": "root",
            "database": "test_ai_notes",
            "charset": "utf8mb4",
            "echo": False
        },
        milvus={
            "host": "localhost",
            "port": 19530,
            "collection_name": "test_note_embeddings",
            "embedding_dim": 1536,
            "index_type": "HNSW",
            "metric_type": "COSINE"
        },
        embedding={
            "model": "openai",
            "openai_api_key": "sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
            "base_url": "https://api.zhizengzeng.com/v1",
            "model_name": "text-embedding-3-small",
            "chunk_size": 500,
            "chunk_overlap": 100,
            "max_tokens": 8000
        },
        bge={
            "model_name": "BAAI/bge-large-zh-v1.5",
            "device": "cpu",
            "normalize_embeddings": True
        },
        llm={
            "provider": "openai",
            "api_key": "sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
            "base_url": "https://api.zhizengzeng.com/v1",
            "model_name": "gpt-3.5-turbo",
            "temperature": 0.7,
            "max_tokens": 2000
        },
        file={
            "upload_dir": "test_uploads",
            "temp_dir": "test_temp",
            "max_file_size": 50,
            "allowed_extensions": [".pdf"]
        },
        logging={
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "file": "test_logs/app.log"
        },
        rag={
            "top_k": 5,
            "similarity_threshold": 0.7,
            "max_context_length": 4000,
            "prompt_template": "基于以下相关文档内容，请回答用户的问题。\n\n相关文档：\n{context}\n\n用户问题：{question}\n\n请提供准确、详细的回答："
        }
    )


@pytest.fixture
def test_db():
    """测试数据库"""
    # 使用内存SQLite数据库
    engine = create_engine("sqlite:///:memory:", echo=False)
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    # 创建表
    Base.metadata.create_all(bind=engine)
    
    def override_get_db():
        try:
            db = TestingSessionLocal()
            yield db
        finally:
            db.close()
    
    app.dependency_overrides[get_db] = override_get_db
    
    yield TestingSessionLocal
    
    # 清理
    app.dependency_overrides.clear()


@pytest.fixture
def client(test_db):
    """测试客户端"""
    return TestClient(app)


@pytest.fixture
def mock_embedding_service():
    """模拟嵌入服务"""
    mock_service = Mock()
    mock_service.embed_text.return_value = [0.1] * 1536  # 模拟1536维向量
    mock_service.embed_texts.return_value = [[0.1] * 1536, [0.2] * 1536]
    mock_service.get_embedding_dim.return_value = 1536
    
    with patch('src.rag_action.service.embedding_service.get_embedding_service', return_value=mock_service):
        yield mock_service


@pytest.fixture
def mock_vector_service():
    """模拟向量服务"""
    mock_service = Mock()
    mock_service.insert_vector.return_value = None
    mock_service.search_similar.return_value = [
        {
            "vector_id": "test_vector_1",
            "note_id": 1,
            "chunk_id": 1,
            "content": "测试内容1",
            "page_number": 1,
            "similarity_score": 0.9,
            "created_at": 1234567890
        }
    ]
    
    with patch('src.rag_action.service.vector_service.VectorService', return_value=mock_service):
        yield mock_service


@pytest.fixture
def mock_llm_service():
    """模拟LLM服务"""
    mock_service = Mock()
    mock_service.generate_response.return_value = ("这是一个测试回答", 100)
    
    with patch('src.rag_action.service.rag_service.LLMService', return_value=mock_service):
        yield mock_service


@pytest.fixture
def sample_pdf_file():
    """示例PDF文件"""
    # 创建临时PDF文件
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as f:
        # 这里应该创建一个真实的PDF文件，但为了简化测试，我们只创建一个空文件
        f.write(b'%PDF-1.4\n%EOF')  # 最简单的PDF文件格式
        temp_path = f.name
    
    yield temp_path
    
    # 清理
    if os.path.exists(temp_path):
        os.remove(temp_path)


@pytest.fixture
def mock_loading_service():
    """模拟文档加载服务"""
    mock_service = Mock()
    mock_service.load_pdf.return_value = "这是从PDF提取的测试文本内容。"
    mock_service.get_total_pages.return_value = 1
    mock_service.get_page_map.return_value = {
        1: {
            "start": 0,
            "end": 20,
            "text": "这是从PDF提取的测试文本内容。",
            "length": 20
        }
    }
    
    with patch('src.rag_action.service.loading_service.LoadingService', return_value=mock_service):
        yield mock_service


@pytest.fixture
def mock_chunking_service():
    """模拟分块服务"""
    mock_service = Mock()
    mock_service.chunk_text.return_value = [
        {
            "text": "这是第一个文本块",
            "page_number": 1,
            "chunk_index": 0
        },
        {
            "text": "这是第二个文本块",
            "page_number": 1,
            "chunk_index": 1
        }
    ]
    
    with patch('src.rag_action.service.chuking_service.ChunkingService', return_value=mock_service):
        yield mock_service


@pytest.fixture(autouse=True)
def setup_test_dirs():
    """设置测试目录"""
    test_dirs = ["test_uploads", "test_temp", "test_logs"]
    
    for dir_name in test_dirs:
        os.makedirs(dir_name, exist_ok=True)
    
    yield
    
    # 清理测试目录
    import shutil
    for dir_name in test_dirs:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
