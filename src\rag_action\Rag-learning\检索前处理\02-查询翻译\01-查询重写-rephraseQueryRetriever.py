import logging
from chromadb import Client
from chromadb.types import C
from langchain.retrievers import RePhraseQueryRetriever
from langchain_community.vectorstores import Chroma
from langchain_community.document_loaders import TextLoader
from langchain_openai import ChatOpenAI
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_text_splitters import RecursiveCharacterTextSplitter
# 设置日志记录
logging.basicConfig()
logging.getLogger("langchain.retrievers.re_phraser").setLevel(logging.INFO)
# 加载游戏文档数据
loader = TextLoader("C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/黑悟空wiki.txt", encoding='utf-8')
data = loader.load()
# 文本分块
text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=0)
all_splits = text_splitter.split_documents(data)
# 创建向量存储
embed_model = HuggingFaceEmbeddings(model_name="BAAI/bge-small-zh")
vectorstore = Chroma.from_documents(documents=all_splits, embedding= embed_model)
# 设置RePhraseQueryRetriever
llm = ChatOpenAI(model="gpt-4o-mini", temperature=0,api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",base_url="https://api.zhizengzeng.com/v1")

# RePhraseQueryRetriever 是 LangChain 提供的一个检索器增强工具。它的作用是在检索前，先用大语言模型（LLM）对用户的原始查询进行重写（rephrase），使其更适合向量检索（比如去除无关信息、用更精确的术语表达等），然后再用重写后的查询去检索文档。这样可以提升检索的相关性和准确性。
retriever_from_llm = RePhraseQueryRetriever.from_llm(
    retriever=vectorstore.as_retriever(),
    llm=llm # 使用OpenAI模型做重写器
)

query = "还没玩过黑神话，大概有哪几个大的情节？"
# 调用RePhraseQueryRetriever进行查询重写
docs = retriever_from_llm.invoke(query)


prompt = f"""
你是一个黑神话的游戏客服，现在给你提供一下资料，请根据资料回答用户的问题。
资料：{docs}
用户问题：{query}
请直接给出回答（不要加任何前缀或者说明）。
"""

client  = ChatOpenAI(model="gpt-4o-mini", temperature=0.6,api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",base_url="https://api.zhizengzeng.com/v1")
response = client.invoke(prompt)
print(response.content)

