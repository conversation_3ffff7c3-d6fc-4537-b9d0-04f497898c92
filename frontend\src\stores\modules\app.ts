import { defineStore } from 'pinia'
import { storage } from '@/utils'
import type { UserSettings, MenuItem } from '@/types'

interface AppState {
  // 主题设置
  theme: 'light' | 'dark' | 'auto'
  
  // 侧边栏状态
  sidebarCollapsed: boolean
  
  // 用户设置
  settings: UserSettings
  
  // 菜单数据
  menuItems: MenuItem[]
  
  // 当前激活的菜单
  activeMenu: string
  
  // 页面加载状态
  pageLoading: boolean
  
  // 全局加载状态
  globalLoading: boolean
  
  // 设备类型
  device: 'desktop' | 'tablet' | 'mobile'

  // 智能问答模式
  queryMode: boolean
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    theme: storage.get('theme', 'light'),
    sidebarCollapsed: storage.get('sidebarCollapsed', false),
    settings: {
      theme: 'light',
      language: 'zh-CN',
      pageSize: 20,
      autoSave: true,
      notifications: true
    },
    menuItems: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        title: '仪表盘',
        icon: 'Dashboard',
        meta: { title: '仪表盘' }
      },
      {
        path: '/notes',
        name: 'Notes',
        title: '笔记管理',
        icon: 'Document',
        meta: { title: '笔记管理' }
      },
      {
        path: '/upload',
        name: 'Upload',
        title: '文档上传',
        icon: 'Upload',
        meta: { title: '文档上传' }
      },
      {
        path: '/query',
        name: 'Query',
        title: '智能问答',
        icon: 'ChatDotRound',
        meta: { title: '智能问答' }
      },
      {
        path: '/streaming-test',
        name: 'StreamingTest',
        title: '流式测试',
        icon: 'Lightning',
        meta: { title: '流式问答测试' }
      },
      {
        path: '/system',
        name: 'System',
        title: '系统监控',
        icon: 'Monitor',
        meta: { title: '系统监控' }
      }
    ],
    activeMenu: '',
    pageLoading: false,
    globalLoading: false,
    device: 'desktop',
    queryMode: false
  }),

  getters: {
    // 获取当前主题
    currentTheme: (state) => {
      if (state.theme === 'auto') {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
      }
      return state.theme
    },
    
    // 是否为移动端
    isMobile: (state) => state.device === 'mobile',
    
    // 是否为暗色主题
    isDark: (state) => {
      if (state.theme === 'auto') {
        return window.matchMedia('(prefers-color-scheme: dark)').matches
      }
      return state.theme === 'dark'
    }
  },

  actions: {
    // 设置主题
    setTheme(theme: 'light' | 'dark' | 'auto') {
      this.theme = theme
      storage.set('theme', theme)
      this.applyTheme()
    },

    // 应用主题
    applyTheme() {
      const theme = this.currentTheme
      document.documentElement.setAttribute('data-theme', theme)
      document.documentElement.classList.toggle('dark', theme === 'dark')
    },

    // 切换侧边栏
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
      storage.set('sidebarCollapsed', this.sidebarCollapsed)
    },

    // 设置侧边栏状态
    setSidebarCollapsed(collapsed: boolean) {
      this.sidebarCollapsed = collapsed
      storage.set('sidebarCollapsed', collapsed)
    },

    // 设置激活菜单
    setActiveMenu(path: string) {
      this.activeMenu = path
    },

    // 设置页面加载状态
    setPageLoading(loading: boolean) {
      this.pageLoading = loading
    },

    // 设置全局加载状态
    setGlobalLoading(loading: boolean) {
      this.globalLoading = loading
    },

    // 设置设备类型
    setDevice(device: 'desktop' | 'tablet' | 'mobile') {
      this.device = device
      
      // 移动端自动收起侧边栏
      if (device === 'mobile') {
        this.setSidebarCollapsed(true)
      }
    },

    // 更新用户设置
    updateSettings(settings: Partial<UserSettings>) {
      this.settings = { ...this.settings, ...settings }
      storage.set('userSettings', this.settings)
    },

    // 初始化应用
    initApp() {
      // 加载用户设置
      const savedSettings = storage.get<UserSettings>('userSettings')
      if (savedSettings) {
        this.settings = { ...this.settings, ...savedSettings }
      }

      // 应用主题
      this.applyTheme()

      // 监听系统主题变化
      if (this.theme === 'auto') {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
        mediaQuery.addEventListener('change', () => {
          this.applyTheme()
        })
      }

      // 监听窗口大小变化
      this.handleResize()
      window.addEventListener('resize', this.handleResize)
    },

    // 处理窗口大小变化
    handleResize() {
      const width = window.innerWidth

      if (width < 768) {
        this.setDevice('mobile')
      } else if (width < 1024) {
        this.setDevice('tablet')
      } else {
        this.setDevice('desktop')
      }
    },

    // 智能问答模式控制
    enterQueryMode() {
      console.log('📝 Store: 进入智能问答模式')
      this.queryMode = true
    },

    exitQueryMode() {
      console.log('📝 Store: 退出智能问答模式')
      this.queryMode = false
    }
  }
})
