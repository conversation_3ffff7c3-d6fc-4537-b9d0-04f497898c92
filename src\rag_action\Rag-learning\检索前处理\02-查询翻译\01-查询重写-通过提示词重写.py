from langchain_openai import ChatOpenAI


client = ChatOpenAI(
    model="gpt-4o-mini",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    base_url="https://api.zhizengzeng.com/v1",
)


def rewrite_query(question: str) -> str:

    prompt = f"""
    作为一个游戏客服人员，你需要帮助用户重写他们的问题。
    规则：
    1. 移除无关信息，只保留核心问题
    2. 使用精确的游戏术语表达
    3. 保持问题的核心意图
    4. 将模糊问题转换为具体的查询
    原始问题:{question}
    请直接给出重写后的查询（不要加任何前缀或者说明）。
    """

    
    response = client.invoke(prompt)
    rewritten_query = response.content.strip()
    return rewritten_query


query = "那个，我刚开始玩这个游戏，感觉很难，在普陀山那一关，嗯，怎么也过不去。先学什么技能比较好？新手求指导！"
print(f"\n原始查询：{query}")
print(f"重写查询：{rewrite_query(query)}")




