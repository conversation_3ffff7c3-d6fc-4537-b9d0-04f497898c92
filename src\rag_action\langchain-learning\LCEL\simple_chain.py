from langchain_core.output_parsers import StrOutputParser
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate

llm = ChatOpenAI(base_url="https://api.zhizengzeng.com/v1",
 api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2", 
 model="gpt-4o-mini", temperature=0)

prompt = ChatPromptTemplate.from_template("""

  给我讲一下关于{joke}的笑话
    
""")

chain = prompt | llm | StrOutputParser()


result = chain.invoke({"joke": "为什么程序员喜欢喝咖啡？"})

print(result)



