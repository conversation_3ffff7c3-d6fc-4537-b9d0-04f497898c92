# ============================================================================
# LangChain PDF 文档问答系统
# 功能：从 PDF 文档中提取信息并回答用户问题
# 技术栈：LangChain + OpenAI + FAISS向量数据库
# ============================================================================

# 导入必要的库
from langchain_openai import OpenAIEmbeddings, ChatOpenAI  # OpenAI 嵌入模型和聊天模型
from langchain_community.document_loaders import PyPDFLoader  # PDF 文档加载器
from langchain.text_splitter import RecursiveCharacterTextSplitter  # 文本分割器
from langchain_community.vectorstores import FAISS  # FAISS 向量数据库
from langchain.chains import RetrievalQA  # 检索问答链
from dotenv import load_dotenv  # 环境变量加载器
from pydantic import SecretStr  # 用于安全存储 API 密钥
from langchain_community.document_loaders import UnstructuredPDFLoader

# ============================================================================
# 第一步：环境配置和模型初始化
# ============================================================================

# 加载环境变量（从 .env 文件中读取 API 密钥等配置）
load_dotenv()
import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 初始化 OpenAI 嵌入模型 - 用于将文本转换为向量
embeddings = OpenAIEmbeddings(api_key=SecretStr("sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"),
                              base_url="https://api.zhizengzeng.com/v1",
                              model="text-embedding-3-small")

# 初始化 OpenAI 聊天模型 - 用于生成回答
llm = ChatOpenAI(api_key=SecretStr("sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"),
                 base_url="https://api.zhizengzeng.com/v1",
                 model="gpt-4o-mini", temperature=0)

# ============================================================================
# 第二步：PDF 文档加载
# ============================================================================

# 创建 PDF 加载器，指定 PDF 文件路径
loader = UnstructuredPDFLoader("C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/uber_10q_march_2022.pdf")

# 加载 PDF 文档，返回 Document 对象列表
# 每个 Document 对象包含页面内容和元数据
documents = loader.load()

# ============================================================================
# 第三步：文本分割（Chunking）
# ============================================================================

# 创建递归字符文本分割器
text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=1000,        # 每个文本块的最大字符数
    chunk_overlap=200,      # 相邻文本块的重叠字符数（保持上下文连贯性）
    length_function=len,    # 计算文本长度的函数
    add_start_index=True,   # 为每个文本块添加起始索引
)

# 将文档分割成更小的文本块
# 这样做的好处：
# 1. 提高检索精度 - 可以定位到具体的文本片段
# 2. 控制上下文长度 - 避免超出模型的最大输入长度
# 3. 提高处理效率 - 并行处理多个小文本块
chunks = text_splitter.split_documents(documents)

# ============================================================================
# 第四步：创建向量数据库
# ============================================================================

# 使用 FAISS 创建向量存储
# FAISS 是 Facebook 开发的高效向量相似性搜索库
vectorstore = FAISS.from_documents(chunks, embeddings)

# 向量化过程：
# 1. 每个文本块被转换为高维向量（通常是 1536 维）
# 2. 向量存储在 FAISS 索引中
# 3. 支持快速相似性搜索

# ============================================================================
# 第五步：创建检索问答链
# ============================================================================

# 创建检索问答链，这是 RAG 系统的核心组件
qa_chain = RetrievalQA.from_chain_type(
    llm=llm,                                    # 使用的大语言模型
    chain_type="stuff",                        # 链类型：stuff（将所有检索到的文档拼接后发送给模型）
    retriever=vectorstore.as_retriever(        # 检索器：从向量数据库中检索相关文档
        search_kwargs={"k": 5}                 # 检索前 5 个最相关的文档
    ),
    return_source_documents=True,              # 返回源文档（用于显示参考来源）
    verbose=True                               # 启用详细输出（显示处理过程）
)

# 检索问答链的工作流程：
# 1. 接收用户问题
# 2. 将问题转换为向量
# 3. 在向量数据库中搜索相似文档
# 4. 将检索到的文档作为上下文
# 5. 将问题和上下文发送给大语言模型
# 6. 生成最终答案

# ============================================================================
# 第六步：执行查询和问答
# ============================================================================

# 查询示例 1：关于自由现金流变化的问题
query = "What is the change of free cash flow and what is the rate from the financial and operational highlights?"

# 执行查询
response = qa_chain.invoke({"query": query})

# 输出结果
print("\n************LangChain Query Response************")
print("Answer:", response["result"])  # 模型生成的答案
print("\nSource Documents:")          # 检索到的源文档
for i, doc in enumerate(response["source_documents"], 1):
    print(f"\nDocument {i}:")
    print(doc.page_content[:200] + "...")  # 显示每个源文档的前 200 个字符

# 查询示例 2：关于 2021 年 COVID-19 响应计划的问题
query = "how many COVID-19 response initiatives (In millions) in year 2021?"

response = qa_chain.invoke({"query": query})

print("\n************LangChain Query Response************")
print("Answer:", response["result"])
print("\nSource Documents:")
for i, doc in enumerate(response["source_documents"], 1):
    print(f"\nDocument {i}:")
    print(doc.page_content[:200] + "...")

# 查询示例 3：关于 COVID-19 后 EBITDA 利润改善的问题
query = "After the year of COVID-19, how much EBITDA profit improved?"

response = qa_chain.invoke({"query": query})

print("\n************LangChain Query Response************")
print("Answer:", response["result"])
print("\nSource Documents:")
for i, doc in enumerate(response["source_documents"], 1):
    print(f"\nDocument {i}:")
    print(doc.page_content[:200] + "...")

# 查询示例 4：关于 2022 年 COVID-19 响应计划的问题
query = "how many COVID-19 response initiatives (In millions) in year 2022?"

response = qa_chain.invoke({"query": query})

print("\n************LangChain Query Response************")
print("Answer:", response["result"])
print("\nSource Documents:")
for i, doc in enumerate(response["source_documents"], 1):
    print(f"\nDocument {i}:")
    print(doc.page_content[:200] + "...")

# 查询示例 5：关于 2023 年 COVID-19 响应计划的问题
query = "how many COVID-19 response initiatives (In millions) in year 2023?"

response = qa_chain.invoke({"query": query})

print("\n************LangChain Query Response************")
print("Answer:", response["result"])
print("\nSource Documents:")
for i, doc in enumerate(response["source_documents"], 1):
    print(f"\nDocument {i}:")
    print(doc.page_content[:200] + "...")

# ============================================================================
# 系统优势总结：
# ============================================================================
# 1. 高效检索：使用向量相似性搜索，快速找到相关文档
# 2. 准确回答：基于检索到的具体文档内容生成答案
# 3. 可追溯性：提供源文档，用户可以验证答案的准确性
# 4. 可扩展性：可以轻松处理大量文档
# 5. 灵活性：支持各种类型的文档和问题