<template>
  <div class="query-page" :class="{ 'embedded-mode': isEmbedded }">
    <div class="query-container">
      <!-- 左侧：查询界面 -->
      <div class="query-main">
        <div v-if="!isEmbedded" class="query-header">
          <h1 class="page-title">智能问答</h1>
          <p class="page-subtitle">基于您的笔记内容进行智能问答</p>
        </div>

        <!-- 对话模式控制 -->
        <div class="conversation-controls">
          <div class="control-group">
            <el-switch
              v-model="conversationMode"
              @change="handleConversationModeChange"
              active-text="对话模式"
              inactive-text="单次问答"
            />
            <el-button
              v-if="conversationMode && hasActiveConversation"
              type="text"
              size="small"
              @click="startNewConversation"
            >
              <el-icon><Plus /></el-icon>
              新对话
            </el-button>
          </div>
          <div v-if="conversationMode && hasActiveConversation" class="conversation-info">
            <span class="conversation-id">对话ID: {{ currentConversationId?.slice(-8) }}</span>
            <span class="message-count">{{ currentMessages.length }} 条消息</span>
          </div>
        </div>

        <!-- 对话状态指示器 -->
        <div v-if="conversationMode && hasActiveConversation" style="background: rgba(0, 122, 255, 0.05); padding: 8px 12px; margin: 10px 0; border-radius: 8px; font-size: 12px; color: #007AFF; border: 1px solid rgba(0, 122, 255, 0.1);">
          💬 多轮对话模式 • {{ currentMessages.length }} 条消息 • 会话ID: {{ currentConversationId?.slice(-8) }}
        </div>

        <!-- 对话历史显示区域 -->
        <div
          v-if="conversationMode"
          :class="['conversation-history', { 'resizing': isResizing }]"
          :style="{ height: dynamicHistoryHeight }"
        >
          <div class="history-header">
            <h4>对话历史</h4>
            <div class="header-actions">
              <span class="height-indicator">{{ Math.round(historyHeight) }}%</span>
              <el-button type="text" size="small" @click="clearCurrentConversation">
                <el-icon><Delete /></el-icon>
                清空对话
              </el-button>
            </div>
          </div>
          <div ref="messagesContainer" class="messages-container">
            <div v-if="currentMessages.length === 0" style="padding: 40px 20px; text-align: center; color: #8E8E93; font-size: 14px;">
              <div style="font-size: 48px; margin-bottom: 16px;">💬</div>
              <div style="font-weight: 500; margin-bottom: 8px;">开始新对话</div>
              <div style="font-size: 12px; opacity: 0.8;">发送消息开始与AI助手的对话</div>
            </div>
            <div
              v-for="message in currentMessages"
              :key="message.id"
              :class="['message-item', `message-${message.role}`]"
              :style="{
                display: 'flex',
                width: '100%',
                marginBottom: '16px',
                padding: '0',
                justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start',
                position: 'relative'
              }"
            >
              <div
                class="message-bubble"
                :style="{
                  display: 'block',
                  maxWidth: '75%',
                  padding: '12px 16px',
                  borderRadius: message.role === 'user' ? '20px 20px 6px 20px' : '20px 20px 20px 6px',
                  wordWrap: 'break-word',
                  wordBreak: 'break-word',
                  lineHeight: '1.5',
                  fontSize: '14px',
                  position: 'relative',
                  background: message.role === 'user'
                    ? 'linear-gradient(135deg, #007AFF 0%, #5856D6 100%)'
                    : '#FFFFFF',
                  color: message.role === 'user' ? '#FFFFFF' : '#1D1D1F',
                  marginLeft: message.role === 'user' ? 'auto' : '0',
                  marginRight: message.role === 'assistant' ? 'auto' : '0',
                  boxShadow: message.role === 'user'
                    ? '0 2px 12px rgba(0, 122, 255, 0.25), 0 1px 3px rgba(0, 122, 255, 0.1)'
                    : '0 2px 12px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.05)',
                  border: message.role === 'assistant' ? '1px solid #E8E8ED' : 'none',
                  transition: 'all 0.2s ease-in-out',
                  minHeight: 'auto'
                }"
              >
                <div class="message-header">
                  <span class="message-role">{{ getRoleDisplayName(message.role) }}</span>
                  <span class="message-time">{{ timeAgo(message.timestamp) }}</span>
                </div>
                <div class="message-content">
                  <div v-if="message.role === 'assistant'" v-html="renderMarkdown(message.content)"></div>
                  <div v-else>{{ message.content }}</div>
                </div>

                <!-- 消息时间戳 -->
                <div :style="{
                  fontSize: '11px',
                  opacity: '0.6',
                  marginTop: '6px',
                  textAlign: message.role === 'user' ? 'right' : 'left',
                  color: message.role === 'user' ? 'rgba(255, 255, 255, 0.8)' : '#8E8E93'
                }">
                  {{ timeAgo(message.timestamp) }}
                </div>

                <div v-if="message.metadata?.sources?.length" class="message-sources">
                  <div class="sources-header">参考来源：</div>
                  <div class="sources-list">
                    <div
                      v-for="source in message.metadata.sources"
                      :key="`${source.note_id}-${source.score}`"
                      class="source-item"
                      @click="viewSourceNote(source.note_id)"
                    >
                      <div class="source-title">{{ source.note_title }}</div>
                      <div class="source-score">相关度: {{ (source.score * 100).toFixed(1) }}%</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 可调节分割线 -->
          <div
            class="resize-handle"
            @mousedown="startResize"
            @touchstart="startResize"
            @click="() => console.log('🖱️ 分割线被点击')"
          >
            <div class="resize-indicator">
              <div class="resize-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <div class="resize-text">拖拽调节</div>
            </div>
          </div>
        </div>

        <!-- 查询输入区 -->
        <div class="query-input-section">
          <div class="input-wrapper">
            <el-input
              v-model="currentQuery"
              type="textarea"
              :rows="2"
              :autosize="{ minRows: 2, maxRows: 4 }"
              :placeholder="conversationMode ? '继续对话...' : '请输入您的问题...'"
              :disabled="loading"
              @keydown.ctrl.enter="handleQuery"
              @keydown.meta.enter="handleQuery"
            />
            
            <div class="input-actions">
              <div class="input-tips">
                <el-icon><InfoFilled /></el-icon>
                <span>Ctrl + Enter 发送</span>
              </div>
              
              <div class="action-buttons">
                <el-button @click="clearQuery" :disabled="loading">
                  清空
                </el-button>
                <el-button
                  type="primary"
                  @click="handleQuery"
                  :loading="loading"
                  :disabled="!currentQuery.trim()"
                >
                  <el-icon><Search /></el-icon>
                  {{ loading ? '查询中...' : (conversationMode ? '发送' : '查询') }}
                </el-button>
              </div>
            </div>
          </div>

          <!-- 智能提示 -->
          <div class="query-tips">
            <el-alert
              title="智能问答"
              description="系统会自动识别您的查询意图，并提供最佳的回答结果。无需设置任何参数。"
              type="info"
              :closable="false"
              show-icon
            />
          </div>
        </div>

        <!-- 查询结果 -->
        <div v-if="currentResult" class="query-result">
          <div class="result-header">
            <h3>查询结果</h3>
            <div class="result-meta">
              <span>查询时间: {{ currentResult.query_time }}ms</span>
              <span v-if="currentResult.total_tokens">
                Token数: {{ currentResult.total_tokens }}
              </span>
            </div>
          </div>
          
          <div class="result-content">
            <div class="answer-section">
              <div class="answer-text" v-html="formatAnswer(currentResult.answer)"></div>
            </div>
            
            <div v-if="currentResult.sources.length > 0" class="sources-section">
              <h4>相关来源</h4>
              <div class="sources-list">
                <div
                  v-for="(source, index) in currentResult.sources"
                  :key="index"
                  class="source-item"
                  @click="viewSourceNote(source.note_id)"
                >
                  <div class="source-header">
                    <div class="source-title">{{ source.note_title }}</div>
                    <div class="source-score">相似度: {{ (source.score * 100).toFixed(1) }}%</div>
                  </div>
                  <div class="source-content">{{ source.content }}</div>
                  <div class="source-meta">
                    <span v-if="source.page_number">第 {{ source.page_number }} 页</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!currentResult && !loading" class="empty-state">
          <el-empty description="请输入问题开始查询">
            <div class="quick-questions">
              <h4>试试这些问题：</h4>
              <div class="question-list">
                <el-button
                  v-for="question in suggestedQuestions"
                  :key="question"
                  type="text"
                  @click="setQuery(question)"
                >
                  {{ question }}
                </el-button>
              </div>
            </div>
          </el-empty>
        </div>
      </div>

      <!-- 右侧：历史记录 -->
      <div class="query-sidebar">
        <div class="sidebar-header">
          <h3>查询历史</h3>
          <el-button type="text" size="small" @click="clearHistory">
            <el-icon><Delete /></el-icon>
            清空
          </el-button>
        </div>
        
        <div class="history-list">
          <div
            v-for="item in queryHistory"
            :key="item.id"
            class="history-item"
            @click="loadHistoryItem(item)"
          >
            <div class="history-query">{{ item.query }}</div>
            <div class="history-time">{{ timeAgo(item.timestamp) }}</div>
          </div>
          
          <el-empty v-if="queryHistory.length === 0" description="暂无查询历史" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useQueryStore, useNotesStore } from '@/stores'
import { timeAgo } from '@/utils'
import { marked } from 'marked'
import {
  InfoFilled,
  Search,
  Delete,
  Plus
} from '@element-plus/icons-vue'

// Props
interface Props {
  embedded?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  embedded: false
})

// 计算是否为嵌入模式
const isEmbedded = computed(() => props.embedded)

const router = useRouter()
const route = useRoute()
const queryStore = useQueryStore()
const notesStore = useNotesStore()

// 响应式数据
const currentQuery = ref('')

const suggestedQuestions = ref([
  '什么是机器学习？',
  '深度学习的主要应用有哪些？',
  '如何选择合适的算法？',
  '数据预处理的步骤是什么？'
])

// 对话模式状态 - 从store获取，确保状态同步
const conversationMode = computed({
  get: () => queryStore.config.enable_conversation,
  set: (value: boolean) => {
    queryStore.toggleConversationMode(value)
  }
})

// 布局控制
const historyHeight = ref(60) // 对话历史区域占可视高度的百分比
const isResizing = ref(false)
const minHistoryHeight = 30 // 最小30%
const maxHistoryHeight = 80 // 最大80%

// 计算属性
const loading = computed(() => queryStore.loading)
const currentResult = computed(() => queryStore.currentResult)
const queryHistory = computed(() => queryStore.recentHistory)

// 对话相关计算属性
const currentConversationId = computed(() => queryStore.currentConversationId)
const currentConversation = computed(() => queryStore.currentConversation)
const currentMessages = computed(() => queryStore.currentMessages)
const hasActiveConversation = computed(() => queryStore.hasActiveConversation)

// 动态高度计算
const dynamicHistoryHeight = computed(() => {
  // 基础计算：可视高度减去固定元素的高度
  const viewportHeight = window.innerHeight
  const headerHeight = isEmbedded.value ? 0 : 120 // 页面标题区域
  const controlsHeight = 80 // 对话控制区域
  const inputHeight = 140 // 优化后的输入区域高度（减少了60px）
  const availableHeight = viewportHeight - headerHeight - controlsHeight - inputHeight

  // 计算对话历史区域的高度（占可用高度的百分比）
  const calculatedHeight = Math.max(
    Math.min(availableHeight * (historyHeight.value / 100), availableHeight * 0.8),
    300 // 最小高度300px
  )

  console.log('📐 高度计算:', {
    viewportHeight,
    headerHeight,
    controlsHeight,
    inputHeight,
    availableHeight,
    historyPercent: historyHeight.value,
    calculatedHeight
  })

  return `${calculatedHeight}px`
})

// 响应式断点检测
const isMobile = computed(() => window.innerWidth < 768)
const isTablet = computed(() => window.innerWidth >= 768 && window.innerWidth < 1024)
const isDesktop = computed(() => window.innerWidth >= 1024)

// 方法
const handleQuery = async () => {
  if (!currentQuery.value.trim() || loading.value) return

  try {
    // 使用对话模式进行查询
    await queryStore.intelligentQuery(currentQuery.value, conversationMode.value)

    // 查询成功后清空输入框
    currentQuery.value = ''
  } catch (error) {
    ElMessage.error('查询失败，请重试')
  }
}

const clearQuery = () => {
  currentQuery.value = ''
  if (!conversationMode.value) {
    queryStore.clearCurrentResult()
  }
}

const setQuery = (question: string) => {
  currentQuery.value = question
}

// 对话管理方法
const handleConversationModeChange = (enabled: boolean) => {
  // 由于使用了computed，这里的逻辑已经在setter中处理
  // 不需要额外的处理，toggleConversationMode会自动调用
}

const startNewConversation = async () => {
  try {
    await queryStore.startNewConversation()
    ElMessage.success('已开始新对话')
  } catch (error) {
    ElMessage.error('开始新对话失败')
  }
}

const clearCurrentConversation = async () => {
  try {
    await ElMessageBox.confirm('确定要清空当前对话吗？这将开始一个新的对话。', '确认清空', {
      type: 'warning'
    })

    // 修复：开始新对话而不是删除当前对话
    // 测试结果：✅ 清空对话功能已修复，符合用户预期行为
    await queryStore.startNewConversation()
    ElMessage.success('已清空对话，开始新的对话')
  } catch (error) {
    // 用户取消或操作失败
    if (error !== 'cancel') {
      ElMessage.error('清空对话失败')
    }
  }
}

const getRoleDisplayName = (role: string) => {
  const roleMap = {
    'user': '用户',
    'assistant': '助手',
    'system': '系统'
  }
  return roleMap[role as keyof typeof roleMap] || role
}

const renderMarkdown = (content: string) => {
  return marked(content)
}

// 布局调节功能
const startResize = (event: Event) => {
  event.preventDefault()
  isResizing.value = true

  const mouseEvent = event as MouseEvent
  const touchEvent = event as TouchEvent
  const startY = touchEvent.touches ? touchEvent.touches[0].clientY : mouseEvent.clientY
  const startHeight = historyHeight.value

  console.log('🎯 开始拖拽调节，起始高度:', startHeight + '%')

  const handleResize = (moveEvent: Event) => {
    if (!isResizing.value) return

    const mouseMoveEvent = moveEvent as MouseEvent
    const touchMoveEvent = moveEvent as TouchEvent
    const currentY = touchMoveEvent.touches ? touchMoveEvent.touches[0].clientY : mouseMoveEvent.clientY
    const deltaY = currentY - startY
    const viewportHeight = window.innerHeight

    // 计算新的高度百分比
    const deltaPercent = (deltaY / viewportHeight) * 100
    const newHeight = Math.max(
      minHistoryHeight,
      Math.min(maxHistoryHeight, startHeight + deltaPercent)
    )

    historyHeight.value = newHeight
    console.log('📏 调节中，当前高度:', Math.round(newHeight) + '%')
  }

  const stopResize = () => {
    isResizing.value = false
    document.removeEventListener('mousemove', handleResize)
    document.removeEventListener('mouseup', stopResize)
    document.removeEventListener('touchmove', handleResize)
    document.removeEventListener('touchend', stopResize)

    // 保存用户偏好
    localStorage.setItem('queryHistoryHeight', historyHeight.value.toString())
    console.log('✅ 拖拽结束，最终高度:', Math.round(historyHeight.value) + '%')
  }

  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  document.addEventListener('touchmove', handleResize)
  document.addEventListener('touchend', stopResize)
}

// 响应式布局调整
const adjustLayoutForDevice = () => {
  if (isMobile.value) {
    // 移动端：更大的历史区域比例
    historyHeight.value = Math.max(historyHeight.value, 50)
  } else if (isTablet.value) {
    // 平板：中等比例
    historyHeight.value = Math.max(historyHeight.value, 55)
  } else {
    // 桌面端：默认比例
    historyHeight.value = Math.max(historyHeight.value, 60)
  }
}

// 窗口大小变化监听
const handleResize = () => {
  adjustLayoutForDevice()
}

// 从本地存储恢复用户偏好
const restoreUserPreferences = () => {
  const savedHeight = localStorage.getItem('queryHistoryHeight')
  if (savedHeight) {
    historyHeight.value = Math.max(
      minHistoryHeight,
      Math.min(maxHistoryHeight, parseFloat(savedHeight))
    )
  } else {
    adjustLayoutForDevice()
  }
}

const formatAnswer = (answer: string) => {
  // 使用marked解析Markdown格式的答案
  return marked(answer)
}

const viewSourceNote = (noteId: number) => {
  router.push(`/notes/${noteId}`)
}

const loadHistoryItem = (item: any) => {
  currentQuery.value = item.query
  queryStore.currentResult = {
    answer: item.answer,
    sources: item.sources,
    query_time: item.query_time
  }
}

const clearHistory = async () => {
  try {
    await ElMessageBox.confirm('确定要清空查询历史吗？', '确认清空', {
      type: 'warning'
    })
    
    queryStore.clearHistory()
    ElMessage.success('历史记录已清空')
  } catch (error) {
    // 用户取消
  }
}

// 自动滚动到最新消息
// UI改进：实现了真正的聊天界面，包括消息气泡、自动滚动等现代化特性
// 修复：更新了移动端样式以匹配新的气泡设计
const messagesContainer = ref<HTMLElement>()

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 监听消息变化，自动滚动到底部
watch(() => currentMessages.value.length, () => {
  if (conversationMode.value && currentMessages.value.length > 0) {
    scrollToBottom()
  }
}, { flush: 'post' })

// 生命周期
onMounted(async () => {
  await queryStore.init()
  notesStore.fetchNotes({ page: 1, page_size: 20 })

  // 加载对话列表（如果对话模式启用）
  if (queryStore.config.enable_conversation) {
    queryStore.loadConversations()
  }

  // 处理URL参数中的问题
  const urlQuery = route.query.q as string
  if (urlQuery) {
    currentQuery.value = urlQuery
    // 自动执行查询
    setTimeout(() => {
      handleQuery()
    }, 500)
  }

  // 初始化布局
  restoreUserPreferences()
  window.addEventListener('resize', handleResize)
})

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<!-- 全局样式，强制应用聊天界面 -->
<style>
/* 聊天界面强制样式 - 使用最高优先级确保生效 */
div.query-page div.conversation-history div.messages-container div.message-item {
  display: flex !important;
  width: 100% !important;
  margin-bottom: 16px !important;
  padding: 0 !important;
  background: none !important;
  border: none !important;
  box-shadow: none !important;
}

div.query-page div.conversation-history div.messages-container div.message-item.message-user {
  justify-content: flex-end !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

div.query-page div.conversation-history div.messages-container div.message-item.message-assistant {
  justify-content: flex-start !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

div.query-page div.conversation-history div.messages-container div.message-item div.message-bubble {
  display: block !important;
  max-width: 80% !important;
  padding: 12px 16px !important;
  border-radius: 18px !important;
  word-wrap: break-word !important;
  line-height: 1.4 !important;
  position: relative !important;
  margin: 0 !important;
}

div.query-page div.conversation-history div.messages-container div.message-item.message-user div.message-bubble {
  background: linear-gradient(135deg, #007AFF, #5856D6) !important;
  color: white !important;
  margin-left: 20% !important;
  margin-right: 0 !important;
  border-radius: 18px 18px 4px 18px !important;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3) !important;
  border: none !important;
}

div.query-page div.conversation-history div.messages-container div.message-item.message-assistant div.message-bubble {
  background: #F2F2F7 !important;
  color: #1D1D1F !important;
  margin-right: 20% !important;
  margin-left: 0 !important;
  border-radius: 18px 18px 18px 4px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #E5E5EA !important;
}

/* 消息头部样式 */
.query-page .conversation-history .messages-container .message-item .message-bubble .message-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 8px !important;
  font-size: 11px !important;
  opacity: 0.7 !important;
}

/* 用户消息头部颜色 */
.query-page .conversation-history .messages-container .message-item.message-user .message-bubble .message-header .message-role,
.query-page .conversation-history .messages-container .message-item.message-user .message-bubble .message-header .message-time {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* AI消息头部颜色 */
.query-page .conversation-history .messages-container .message-item.message-assistant .message-bubble .message-header .message-role,
.query-page .conversation-history .messages-container .message-item.message-assistant .message-bubble .message-header .message-time {
  color: rgba(29, 29, 31, 0.6) !important;
}

/* 消息内容样式 */
.query-page .conversation-history .messages-container .message-item .message-bubble .message-content {
  line-height: 1.5 !important;
  font-size: 14px !important;
}

.query-page .conversation-history .messages-container .message-item.message-user .message-bubble .message-content {
  color: white !important;
}

.query-page .conversation-history .messages-container .message-item.message-assistant .message-bubble .message-content {
  color: #1D1D1F !important;
}

/* 消息动画 */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.query-page .conversation-history .messages-container .message-item {
  animation: messageSlideIn 0.3s ease-out !important;
}

/* 悬停效果 */
.query-page .conversation-history .messages-container .message-item .message-bubble:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}
</style>

<style lang="scss" scoped>
.query-page {
  height: 100%;
  padding: var(--spacing-lg);

  // 嵌入模式样式
  &.embedded-mode {
    padding: 0;

    .query-container {
      height: 100%;
      max-width: none;
      margin: 0;
    }

    .query-main {
      .query-input-section {
        margin-bottom: var(--spacing-md);
      }
    }

    .query-sidebar {
      max-height: 300px;
    }
  }

  // 响应式样式
  @media (max-width: 768px) {
    // 移动端样式
    .conversation-history {
      margin-bottom: var(--spacing-md);
      min-height: 250px;

      .history-header {
        padding: var(--spacing-sm);

        h4 {
          font-size: var(--font-size-sm);
        }

        .header-actions {
          gap: var(--spacing-xs);

          .height-indicator {
            font-size: 10px;
            padding: 1px 4px;
          }
        }
      }

      .messages-container {
        padding: 12px 8px;
        background: #F8F9FA;
        // 移动端消息样式由全局样式控制
      }

      // 移动端分割线调整
      .resize-handle {
        height: 16px !important;
        bottom: -8px !important;

        .resize-indicator {
          padding: 3px 10px !important;
        }
      }
    }
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    // 平板样式
    .conversation-history {
      min-height: 280px;
    }
  }

  @media (min-width: 1025px) {
    // 桌面端样式
    .conversation-history {
      min-height: 320px;
    }
  }

  .query-container {
    display: flex;
    height: 100%;
    gap: var(--spacing-lg);
    max-width: 1400px;
    margin: 0 auto;
  }
  
  .query-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    
    .query-header {
      margin-bottom: var(--spacing-xl);

      .page-title {
        font-size: 28px;
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
      }

      .page-subtitle {
        color: var(--text-secondary);
        font-size: var(--font-size-lg);
      }
    }

    // 对话控制样式
    .conversation-controls {
      margin-bottom: var(--spacing-lg);
      padding: var(--spacing-md);
      background: var(--bg-color);
      border-radius: var(--border-radius-base);
      border: 1px solid var(--border-lighter);

      .control-group {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-sm);
      }

      .conversation-info {
        display: flex;
        gap: var(--spacing-md);
        font-size: var(--font-size-sm);
        color: var(--text-secondary);

        .conversation-id {
          font-family: monospace;
          background: var(--bg-page);
          padding: 2px 6px;
          border-radius: 4px;
        }
      }
    }

    // 对话历史样式
    .conversation-history {
      margin-bottom: var(--spacing-lg);
      background: white;
      border-radius: 12px;
      border: 1px solid #E5E5EA;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      // 移除固定高度，使用动态高度
      min-height: 300px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      position: relative;
      transition: height 0.2s ease;

      .history-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-md);
        border-bottom: 1px solid var(--border-lighter);
        background: var(--bg-page);
        flex-shrink: 0;

        h4 {
          margin: 0;
          color: var(--text-primary);
          font-size: var(--font-size-md);
        }

        .header-actions {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);

          .height-indicator {
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
            background: var(--bg-color);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
          }
        }
      }

      .messages-container {
        flex: 1;
        overflow-y: auto;
        padding: 16px 12px;
        background: #F8F9FA !important;
        @include scrollbar;
        // 消息样式由全局样式控制
      }
      }

      // 可调节分割线样式
      .resize-handle {
        position: absolute;
        bottom: -6px;
        left: 0;
        right: 0;
        height: 12px;
        cursor: row-resize;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 100;
        background: transparent;
        user-select: none;

        &:hover {
          .resize-indicator {
            opacity: 1;
            transform: scale(1.1);
          }
        }

        &:active {
          .resize-indicator {
            opacity: 1;
            transform: scale(1.2);
            background: var(--primary-color-dark);
          }
        }

        .resize-indicator {
          background: var(--primary-color);
          border-radius: 6px;
          padding: 4px 12px;
          opacity: 0.6;
          transition: all 0.2s ease;
          box-shadow: var(--shadow-base);
          pointer-events: none;
          display: flex;
          align-items: center;
          gap: 6px;

          .resize-dots {
            display: flex;
            gap: 2px;

            span {
              width: 3px;
              height: 3px;
              background: white;
              border-radius: 50%;
              display: block;
            }
          }

          .resize-text {
            color: white;
            font-size: 10px;
            font-weight: 500;
            white-space: nowrap;
          }
        }
      }

      // 拖拽时的样式
      &.resizing {
        user-select: none;

        .resize-handle .resize-indicator {
          opacity: 1;
          transform: scale(1.2);
        }
      }
    }
    
    .query-input-section {
      margin-bottom: var(--spacing-md); // 减少底部间距

      .input-wrapper {
        background: var(--bg-color);
        border-radius: var(--border-radius-base);
        padding: var(--spacing-md); // 减少内边距
        box-shadow: var(--shadow-base);
        margin-bottom: var(--spacing-sm); // 减少底部间距

        .el-textarea {
          margin-bottom: var(--spacing-sm); // 减少底部间距

          :deep(.el-textarea__inner) {
            border: none;
            box-shadow: none;
            resize: none;
            font-size: var(--font-size-base);
            line-height: 1.5; // 稍微减少行高
            padding: var(--spacing-sm); // 减少内边距

            &:focus {
              box-shadow: none;
            }
          }
        }

        .input-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: var(--spacing-xs); // 减少顶部间距

          .input-tips {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
          }

          .action-buttons {
            display: flex;
            gap: var(--spacing-sm);
          }
        }
      }
      
      .query-config {
        background: var(--bg-color);
        border-radius: var(--border-radius-base);
        box-shadow: var(--shadow-base);
        
        :deep(.el-collapse) {
          border: none;
          
          .el-collapse-item__header {
            border-bottom: 1px solid var(--border-lighter);
            padding: var(--spacing-md) var(--spacing-lg);
          }
          
          .el-collapse-item__content {
            padding: var(--spacing-lg);
          }
        }
      }
    }
    
    .query-result {
      flex: 1;
      background: var(--bg-color);
      border-radius: var(--border-radius-base);
      box-shadow: var(--shadow-base);
      overflow: hidden;
      border: 1px solid var(--border-lighter);
      display: flex;
      flex-direction: column;
      
      .result-header {
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--border-lighter);
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        h3 {
          font-size: var(--font-size-lg);
          font-weight: var(--font-weight-medium);
          color: var(--text-primary);
        }
        
        .result-meta {
          display: flex;
          gap: var(--spacing-md);
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
        }
      }
      
      .result-content {
        flex: 1;
        padding: 0;
        overflow-y: auto;
        @include scrollbar;
        
        .answer-section {
          margin-bottom: var(--spacing-xl);
          background: var(--bg-color);
          border-radius: var(--border-radius-base);
          padding: var(--spacing-xl);
          border: 1px solid var(--border-lighter);
          box-shadow: var(--shadow-light);
          
          .answer-text {
            line-height: 1.8;
            color: var(--text-primary);
            font-size: var(--font-size-base);

            // 段落样式
            :deep(p) {
              margin-bottom: var(--spacing-lg);
              text-align: justify;

              &:last-child {
                margin-bottom: 0;
              }
            }

            // 标题样式
            :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
              margin: var(--spacing-xl) 0 var(--spacing-md) 0;
              font-weight: var(--font-weight-bold);
              color: var(--text-primary);

              &:first-child {
                margin-top: 0;
              }
            }

            :deep(h1) { font-size: 1.8em; }
            :deep(h2) { font-size: 1.6em; }
            :deep(h3) { font-size: 1.4em; }
            :deep(h4) { font-size: 1.2em; }
            :deep(h5) { font-size: 1.1em; }
            :deep(h6) { font-size: 1em; }

            // 列表样式
            :deep(ul), :deep(ol) {
              margin-bottom: var(--spacing-lg);
              padding-left: var(--spacing-xl);

              li {
                margin-bottom: var(--spacing-sm);
                line-height: 1.6;

                &:last-child {
                  margin-bottom: 0;
                }
              }
            }

            :deep(ul) {
              li {
                list-style-type: disc;

                &::marker {
                  color: var(--primary-color);
                }
              }
            }

            :deep(ol) {
              li {
                list-style-type: decimal;

                &::marker {
                  color: var(--primary-color);
                  font-weight: var(--font-weight-medium);
                }
              }
            }

            // 行内代码样式
            :deep(code) {
              background: var(--bg-page);
              color: var(--primary-color);
              padding: 3px 6px;
              border-radius: 4px;
              font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
              font-size: 0.9em;
              border: 1px solid var(--border-lighter);
            }

            // 代码块样式
            :deep(pre) {
              background: var(--bg-page);
              border: 1px solid var(--border-lighter);
              border-radius: var(--border-radius-base);
              padding: var(--spacing-lg);
              margin: var(--spacing-lg) 0;
              overflow-x: auto;
              position: relative;

              code {
                background: none;
                border: none;
                padding: 0;
                color: var(--text-primary);
                font-size: 0.9em;
                line-height: 1.5;
              }

              &::before {
                content: 'Code';
                position: absolute;
                top: 8px;
                right: 12px;
                font-size: 12px;
                color: var(--text-secondary);
                background: var(--bg-color);
                padding: 2px 6px;
                border-radius: 3px;
                border: 1px solid var(--border-lighter);
              }
            }

            // 引用样式
            :deep(blockquote) {
              border-left: 4px solid var(--primary-color);
              background: var(--bg-page);
              margin: var(--spacing-lg) 0;
              padding: var(--spacing-md) var(--spacing-lg);
              font-style: italic;
              color: var(--text-regular);

              p {
                margin-bottom: var(--spacing-sm);

                &:last-child {
                  margin-bottom: 0;
                }
              }
            }

            // 表格样式
            :deep(table) {
              width: 100%;
              border-collapse: collapse;
              margin: var(--spacing-lg) 0;
              border: 1px solid var(--border-lighter);
              border-radius: var(--border-radius-base);
              overflow: hidden;

              th, td {
                padding: var(--spacing-md);
                text-align: left;
                border-bottom: 1px solid var(--border-lighter);
              }

              th {
                background: var(--bg-page);
                font-weight: var(--font-weight-medium);
                color: var(--text-primary);
              }

              tr:last-child td {
                border-bottom: none;
              }

              tr:hover {
                background: var(--bg-page);
              }
            }

            // 分割线样式
            :deep(hr) {
              border: none;
              height: 1px;
              background: var(--border-lighter);
              margin: var(--spacing-xl) 0;
            }

            // 强调样式
            :deep(strong), :deep(b) {
              font-weight: var(--font-weight-bold);
              color: var(--text-primary);
            }

            :deep(em), :deep(i) {
              font-style: italic;
              color: var(--text-regular);
            }

            // 链接样式
            :deep(a) {
              color: var(--primary-color);
              text-decoration: none;
              border-bottom: 1px solid transparent;
              transition: var(--transition-base);

              &:hover {
                border-bottom-color: var(--primary-color);
              }
            }
          }
        }
        
        .sources-section {
          margin-top: var(--spacing-xl);
          padding: var(--spacing-xl);
          background: var(--bg-page);
          border-radius: var(--border-radius-base);
          border: 1px solid var(--border-lighter);

          h4 {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;

            &::before {
              content: '📚';
              margin-right: var(--spacing-sm);
            }
          }
          
          .sources-list {
            .source-item {
              background: var(--bg-color);
              border: 1px solid var(--border-lighter);
              border-radius: var(--border-radius-base);
              padding: var(--spacing-lg);
              margin-bottom: var(--spacing-lg);
              cursor: pointer;
              transition: var(--transition-base);
              position: relative;

              &:hover {
                border-color: var(--primary-color);
                box-shadow: var(--shadow-base);
                transform: translateY(-2px);
              }

              &:last-child {
                margin-bottom: 0;
              }
              
              .source-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: var(--spacing-md);

                .source-title {
                  font-weight: var(--font-weight-medium);
                  color: var(--text-primary);
                  font-size: var(--font-size-base);
                  flex: 1;
                  margin-right: var(--spacing-md);
                  line-height: 1.4;
                }

                .source-score {
                  font-size: var(--font-size-sm);
                  color: var(--primary-color);
                  background: var(--primary-color-light);
                  padding: 4px 8px;
                  border-radius: 12px;
                  font-weight: var(--font-weight-medium);
                  white-space: nowrap;
                }
              }
              
              .source-content {
                color: var(--text-regular);
                line-height: 1.6;
                margin-bottom: var(--spacing-md);
                background: var(--bg-page);
                padding: var(--spacing-md);
                border-radius: var(--border-radius-small);
                border-left: 3px solid var(--primary-color-light);
                font-size: var(--font-size-sm);
                @include text-ellipsis(4);
              }

              .source-meta {
                font-size: var(--font-size-sm);
                color: var(--text-secondary);
                display: flex;
                align-items: center;

                span {
                  display: flex;
                  align-items: center;

                  &::before {
                    content: '📄';
                    margin-right: 4px;
                  }
                }
              }
            }
          }
        }
      }
    }
    
    .empty-state {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .quick-questions {
        text-align: center;
        
        h4 {
          margin-bottom: var(--spacing-md);
          color: var(--text-primary);
        }
        
        .question-list {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-sm);
          
          .el-button {
            text-align: left;
            justify-content: flex-start;
          }
        }
      }
    }
  }
  
  .query-sidebar {
    width: 300px;
    background: var(--bg-color);
    border-radius: var(--border-radius-base);
    box-shadow: var(--shadow-base);
    display: flex;
    flex-direction: column;
    
    .sidebar-header {
      padding: var(--spacing-lg);
      border-bottom: 1px solid var(--border-lighter);
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h3 {
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-medium);
        color: var(--text-primary);
      }
    }
    
    .history-list {
      flex: 1;
      overflow-y: auto;
      @include scrollbar;
      
      .history-item {
        padding: var(--spacing-md);
        border-bottom: 1px solid var(--border-extra-light);
        cursor: pointer;
        transition: var(--transition-base);
        
        &:hover {
          background: var(--bg-page);
        }
        
        .history-query {
          color: var(--text-primary);
          margin-bottom: var(--spacing-sm);
          @include text-ellipsis(2);
        }
        
        .history-time {
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
        }
      }
    }
  }

@include respond-below(md) {
  .query-page {
    padding: var(--spacing-md);

    .query-container {
      flex-direction: column;
      gap: var(--spacing-md);

      .query-sidebar {
        width: 100%;
        height: 300px;
        order: 2;
      }

      .query-main {
        order: 1;

        .query-header {
          margin-bottom: var(--spacing-lg);

          .page-title {
            font-size: 24px;
          }
        }

        .query-input-section {
          margin-bottom: var(--spacing-sm); // 移动端进一步减少间距

          .input-wrapper {
            padding: var(--spacing-sm); // 移动端减少内边距

            .input-actions {
              flex-direction: column;
              gap: var(--spacing-xs); // 减少间距
              align-items: stretch;
              margin-top: var(--spacing-xs);

              .input-tips {
                order: 2;
                justify-content: center;
              }

              .action-buttons {
                order: 1;
                width: 100%;
                justify-content: stretch;

                .el-button {
                  flex: 1;
                }
              }
            }
          }
        }

        .query-result {
          .result-content {
            .answer-section {
              padding: var(--spacing-md);
              margin-bottom: var(--spacing-lg);

              .answer-text {
                font-size: var(--font-size-sm);

                :deep(pre) {
                  padding: var(--spacing-md);
                  font-size: 0.8em;

                  &::before {
                    font-size: 10px;
                  }
                }

                :deep(table) {
                  font-size: var(--font-size-sm);

                  th, td {
                    padding: var(--spacing-sm);
                  }
                }
              }
            }

            .sources-section {
              padding: var(--spacing-md);

              h4 {
                font-size: var(--font-size-base);
                margin-bottom: var(--spacing-md);
              }

              .sources-list {
                .source-item {
                  padding: var(--spacing-md);

                  .source-header {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: var(--spacing-sm);

                    .source-title {
                      margin-right: 0;
                      font-size: var(--font-size-sm);
                    }

                    .source-score {
                      align-self: flex-end;
                    }
                  }

                  .source-content {
                    font-size: var(--font-size-sm);
                    padding: var(--spacing-sm);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
