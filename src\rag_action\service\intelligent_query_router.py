"""
智能查询路由服务
使用意图识别判断用户查询应该路由到数据库查询还是知识库检索
"""
import logging
import json
import hashlib
import time
from typing import Dict, List, Any, Optional, Tuple
import re

logger = logging.getLogger(__name__)

# 导入LangChain组件
try:
    from langchain_openai import ChatOpenAI
    from langchain_core.messages import HumanMessage, SystemMessage
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    logger.warning("LangChain不可用，智能路由功能将受限")
    logger.warning("向量服务不可用")

from rag_action.core.config import settings


class IntelligentQueryRouter:
    """智能查询路由器"""
    
    def __init__(self):
        self.llm = None

        # LLM路由缓存
        self.semantic_cache = {}
        self.cache_ttl = 3600  # 缓存1小时
        self.max_cache_size = 1000  # 最大缓存条目数

        # 初始化LLM
        if LANGCHAIN_AVAILABLE:
            try:
                self.llm = ChatOpenAI(
                    openai_api_key=settings.llm.api_key,
                    openai_api_base=settings.llm.base_url,
                    model_name=settings.llm.model_name,
                    temperature=0.1,
                    max_tokens=500
                )

                logger.info("智能查询路由器LLM初始化成功")
            except Exception as e:
                logger.error(f"智能查询路由器LLM初始化失败: {e}")
    


    
    def route_query(self, query: str, conversation_context: Optional[List[Dict[str, str]]] = None) -> Dict[str, Any]:
        """
        智能路由查询（基于语义理解）

        Args:
            query: 用户查询
            conversation_context: 对话上下文

        Returns:
            路由结果字典
        """
        try:
            start_time = time.time()

            # 直接使用LLM进行意图识别
            result = self._llm_intent_analysis(query, conversation_context)

            elapsed_time = time.time() - start_time
            logger.info(f"LLM路由结果: {query} -> {result['intent']} (置信度: {result['confidence']:.2f}, 耗时: {elapsed_time:.3f}s)")

            return result

        except Exception as e:
            logger.error(f"LLM路由失败: {e}")
            # 简单降级：返回默认的知识搜索意图
            return {
                "intent": "knowledge_search",
                "confidence": 0.5,
                "reason": f"LLM路由失败，使用默认策略: {str(e)}",
                "routing_method": "fallback"
            }



    def _llm_intent_analysis(self, query: str, conversation_context: Optional[List[Dict[str, str]]] = None) -> Dict[str, Any]:
        """简化的LLM意图分析（单一路由方案）"""
        try:
            if not self.llm:
                return {"intent": "knowledge_search", "confidence": 0.5, "reason": "LLM不可用"}

            # 构建上下文信息
            context_info = self._build_context_summary(conversation_context) if conversation_context else "无对话历史"

            # 检查缓存
            cache_key = self._generate_cache_key(query, context_info)
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                logger.debug(f"使用缓存的LLM路由结果: {query}")
                return cached_result

            # 构建简化的意图分析提示词
            intent_prompt = self._build_intent_analysis_prompt(query, context_info)

            # 调用LLM进行意图分析
            messages = [
                SystemMessage(content="你是一个专业的查询意图分析专家，能够准确识别用户查询的意图类型。"),
                HumanMessage(content=intent_prompt)
            ]

            response = self.llm.invoke(messages)
            result = self._parse_intent_response(response.content)

            # 添加路由方法标识
            result["routing_method"] = "llm_intent"

            # 缓存结果
            self._cache_result(cache_key, result)

            return result

        except Exception as e:
            logger.error(f"LLM意图分析失败: {e}")
            return {
                "intent": "knowledge_search",
                "confidence": 0.5,
                "reason": f"LLM意图分析错误: {str(e)}",
                "routing_method": "llm_error"
            }

    def _build_context_summary(self, conversation_context: List[Dict[str, str]]) -> str:
        """构建对话上下文摘要"""
        if not conversation_context or len(conversation_context) == 0:
            return "无对话历史"

        # 获取最近的几轮对话
        recent_context = conversation_context[-6:]  # 最近3轮对话（用户+助手）

        context_lines = []
        for msg in recent_context:
            role = "用户" if msg.get("role") == "user" else "助手"
            content = msg.get("content", "")[:200]  # 限制长度
            context_lines.append(f"{role}: {content}")

        return "\n".join(context_lines)

    def _build_intent_analysis_prompt(self, query: str, context_info: str) -> str:
        """构建简化的意图分析提示词"""
        return f"""分析用户查询的意图类型。

**对话历史：**
{context_info}

**用户查询：**
{query}

**意图类型：**
1. **simple_conversation** - 简单对话（谢谢、好的、还有吗等）
2. **context_based_answer** - 上下文查询（它、这个、第一句话等指代词）
3. **database_query** - 数据库查询（统计、查询、最近的等）
4. **knowledge_search** - 知识检索（什么是、如何、关于等）
5. **hybrid_query** - 混合查询（既要数据又要解释）

**判断规则：**
- 有指代词且对话历史有内容 → context_based_answer
- 明确要统计数据或查询具体信息 → database_query
- 简单礼貌用语或确认 → simple_conversation
- 概念解释或内容搜索 → knowledge_search
- 复杂分析需求 → hybrid_query

**输出JSON格式：**
{{
    "intent": "意图类型",
    "confidence": 0.95,
    "reason": "判断理由"
}}"""

    def _parse_intent_response(self, response_content: str) -> Dict[str, Any]:
        """解析LLM的意图分析响应"""
        try:
            # 尝试直接解析JSON
            if response_content.strip().startswith('{'):
                result = json.loads(response_content.strip())
            else:
                # 提取JSON部分
                import re
                json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())
                else:
                    raise ValueError("无法找到有效的JSON响应")

            # 验证必需字段
            if "intent" not in result:
                result["intent"] = "knowledge_search"
            if "confidence" not in result:
                result["confidence"] = 0.8
            if "reason" not in result:
                result["reason"] = "LLM分析结果"

            # 验证意图类型
            valid_intents = [
                "simple_conversation", "context_based_answer",
                "database_query", "knowledge_search", "hybrid_query"
            ]
            if result["intent"] not in valid_intents:
                logger.warning(f"未知的意图类型: {result['intent']}，默认为knowledge_search")
                result["intent"] = "knowledge_search"

            # 确保置信度在合理范围内
            confidence = float(result["confidence"])
            result["confidence"] = max(0.0, min(1.0, confidence))

            return result

        except Exception as e:
            logger.error(f"解析意图响应失败: {e}, 原始响应: {response_content}")
            return {
                "intent": "knowledge_search",
                "confidence": 0.8,
                "reason": f"响应解析失败，使用默认策略: {str(e)}"
            }

    def _generate_cache_key(self, query: str, context_info: str) -> str:
        """生成缓存键"""
        # 使用查询和上下文信息生成唯一键
        content = f"{query}|{context_info}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()

    def _get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """获取缓存结果"""
        if cache_key not in self.semantic_cache:
            return None

        cached_item = self.semantic_cache[cache_key]
        current_time = time.time()

        # 检查是否过期
        if current_time - cached_item["timestamp"] > self.cache_ttl:
            del self.semantic_cache[cache_key]
            return None

        return cached_item["result"]

    def _cache_result(self, cache_key: str, result: Dict[str, Any]) -> None:
        """缓存结果"""
        # 如果缓存已满，删除最旧的条目
        if len(self.semantic_cache) >= self.max_cache_size:
            oldest_key = min(self.semantic_cache.keys(),
                           key=lambda k: self.semantic_cache[k]["timestamp"])
            del self.semantic_cache[oldest_key]

        # 添加新的缓存条目
        self.semantic_cache[cache_key] = {
            "result": result,
            "timestamp": time.time()
        }
    

    

    

    

    



def get_intelligent_query_router() -> IntelligentQueryRouter:
    """获取智能查询路由器实例"""
    return IntelligentQueryRouter()
