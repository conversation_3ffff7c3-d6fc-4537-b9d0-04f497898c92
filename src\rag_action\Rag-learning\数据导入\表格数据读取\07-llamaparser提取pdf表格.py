import time
from llama_index.core.indices.vector_store import VectorStoreIndex
from llama_parse import LlamaParse
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding


pdf_path = "C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/billionaires_page-1-5.pdf"

llm = OpenAI( api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    api_base="https://api.zhizengzeng.com/v1",
    model="gpt-4o-mini")

embed_model = OpenAIEmbedding(
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    api_base="https://api.zhizengzeng.com/v1",
    model="text-embedding-3-small"
)

start_time = time.time()


documents  = LlamaParse(result_type= "markdown",api_key="llx-adfMAwWUmfNsv3xVtbc2lEkjvrkbNdWfBZdso2az99XCaUSE").load_data(pdf_path)

end_time = time.time()

print(f"解析时间: {end_time - start_time:.2f}秒")

# 打印解析后的文档内容
for i, doc in enumerate(documents,1):
    print(f"\n文档{i}:")
    print(doc.text)
    print("\n" + "-"*50)

# 构建索引
index = VectorStoreIndex.from_documents(documents,embed_model=embed_model)

# 创建查询引擎
query_engine = index.as_query_engine(llm=llm)

# 示例问答
questions = [
    "2021年谁是最富有的人?",
    "2022年谁是最富有的人?",
    "2023年谁是最富有的人?",
    "最年轻的富豪是谁?"
]

print("\n===== 问答演示 =====")
for question in questions:
    response = query_engine.query(question)
    print(f"\n问题: {question}")
    print(f"回答: {response}")



