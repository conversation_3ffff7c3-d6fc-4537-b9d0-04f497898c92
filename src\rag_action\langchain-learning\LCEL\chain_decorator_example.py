"""
@chain 修饰符的优点在于可以简洁、快速地将函数转化为可复用的链式组件，提升代码的模块化和可读性。它适用于需要将多个处理步骤组合成流水线、实现复杂任务分解、或在多种场景下复用链式逻辑的应用场景，如问答系统、信息抽取、自动化分析等。
"""

from langchain_core.output_parsers import StrOutputParser
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain_core.runnables import chain
import asyncio

# 创建 LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0
)

# 使用 @chain 修饰符创建链
@chain
def explain_topic(topic: str) -> str:
    """解释主题的链"""
    prompt = ChatPromptTemplate.from_template("请详细解释：{topic}")
    return prompt | llm | StrOutputParser()

@chain
def analyze_pros_cons(topic: str) -> str:
    """分析优缺点的链"""
    prompt = ChatPromptTemplate.from_template("请分析 {topic} 的优缺点：")
    return prompt | llm | StrOutputParser()

@chain
def list_applications(topic: str) -> str:
    """列举应用场景的链"""
    prompt = ChatPromptTemplate.from_template("请列举 {topic} 的主要应用场景：")
    return prompt | llm | StrOutputParser()

# 组合多个链 - 修复版
@chain
def comprehensive_analysis(topic: str) -> dict:
    """综合分析链"""
    return {
        "技术解释": explain_topic.invoke(topic),
        "优缺点分析": analyze_pros_cons.invoke(topic),
        "应用场景": list_applications.invoke(topic)
    }

def main():
    """主函数"""
    
    # 使用单个链
    print("=== 单个链示例 ===")
    result = explain_topic.invoke("人工智能")
    print(f"技术解释: {result[:100]}...")
    
    # 使用组合链
    print("\n=== 组合链示例 ===")
    results = comprehensive_analysis.invoke("机器学习")
    
    for name, content in results.items():
        print(f"\n【{name}】")
        print(f"{content[:150]}...")

if __name__ == "__main__":
    main() 