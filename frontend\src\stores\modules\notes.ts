import { defineStore } from 'pinia'
import { notesApi } from '@/api'
import type { Note, Tag, NotesListResponse, SearchFilters, Pagination } from '@/types'

interface NotesState {
  // 笔记列表
  notes: Note[]
  
  // 当前笔记
  currentNote: Note | null
  
  // 标签列表
  tags: Tag[]
  
  // 分页信息
  pagination: Pagination
  
  // 搜索筛选条件
  filters: SearchFilters
  
  // 加载状态
  loading: boolean
  
  // 笔记详情加载状态
  noteLoading: boolean
  
  // 统计信息
  stats: {
    total_notes: number
    total_tags: number
    total_size: number
    by_source_type: Record<string, number>
    by_month: Array<{ month: string; count: number }>
  } | null
}

export const useNotesStore = defineStore('notes', {
  state: (): NotesState => ({
    notes: [],
    currentNote: null,
    tags: [],
    pagination: {
      page: 1,
      pageSize: 20,
      total: 0
    },
    filters: {
      keyword: '',
      tags: [],
      source_type: '',
      date_range: undefined
    },
    loading: false,
    noteLoading: false,
    stats: null
  }),

  getters: {
    // 获取笔记总数
    totalNotes: (state) => state.pagination.total,
    
    // 获取当前页笔记
    currentPageNotes: (state) => state.notes,
    
    // 根据标签筛选笔记
    notesByTags: (state) => (tagNames: string[]) => {
      if (!tagNames.length) return state.notes
      return state.notes.filter(note => 
        note.tags.some(tag => tagNames.includes(tag.name))
      )
    },
    
    // 根据来源类型筛选笔记
    notesBySourceType: (state) => (sourceType: string) => {
      if (!sourceType) return state.notes
      return state.notes.filter(note => note.source_type === sourceType)
    },
    
    // 获取标签使用统计
    tagStats: (state) => {
      const tagCount: Record<string, number> = {}
      state.notes.forEach(note => {
        note.tags.forEach(tag => {
          tagCount[tag.name] = (tagCount[tag.name] || 0) + 1
        })
      })
      return tagCount
    }
  },

  actions: {
    // 获取笔记列表
    async fetchNotes(params?: {
      page?: number
      page_size?: number
      keyword?: string
      tags?: string[]
      source_type?: string
    }) {
      this.loading = true
      try {
        const response = await notesApi.getNotes({
          page: this.pagination.page,
          page_size: this.pagination.pageSize,
          ...this.filters,
          ...params
        })
        
        this.notes = response.notes
        this.pagination = {
          page: response.page,
          pageSize: response.page_size,
          total: response.total
        }
      } catch (error) {
        console.error('获取笔记列表失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 获取笔记详情
    async fetchNoteById(id: number) {
      this.noteLoading = true
      try {
        const note = await notesApi.getNoteById(id)
        this.currentNote = note
        return note
      } catch (error) {
        console.error('获取笔记详情失败:', error)
        throw error
      } finally {
        this.noteLoading = false
      }
    },

    // 创建笔记
    async createNote(noteData: { title: string; content: string; tags?: string[] }) {
      try {
        const note = await notesApi.createNote(noteData)
        this.notes.unshift(note)
        this.pagination.total += 1
        return note
      } catch (error) {
        console.error('创建笔记失败:', error)
        throw error
      }
    },

    // 更新笔记
    async updateNote(id: number, noteData: Partial<{ title: string; content: string; tags?: string[] }>) {
      try {
        const updatedNote = await notesApi.updateNote(id, noteData)
        
        // 更新列表中的笔记
        const index = this.notes.findIndex(note => note.id === id)
        if (index !== -1) {
          this.notes[index] = updatedNote
        }
        
        // 更新当前笔记
        if (this.currentNote?.id === id) {
          this.currentNote = updatedNote
        }
        
        return updatedNote
      } catch (error) {
        console.error('更新笔记失败:', error)
        throw error
      }
    },

    // 删除笔记
    async deleteNote(id: number) {
      try {
        await notesApi.deleteNote(id)
        
        // 从列表中移除
        this.notes = this.notes.filter(note => note.id !== id)
        this.pagination.total -= 1
        
        // 清空当前笔记
        if (this.currentNote?.id === id) {
          this.currentNote = null
        }
      } catch (error) {
        console.error('删除笔记失败:', error)
        throw error
      }
    },

    // 获取标签列表
    async fetchTags() {
      try {
        this.tags = await notesApi.getTags()
      } catch (error) {
        console.error('获取标签列表失败:', error)
      }
    },

    // 创建标签
    async createTag(name: string, color?: string) {
      try {
        const tag = await notesApi.createTag(name, color)
        this.tags.push(tag)
        return tag
      } catch (error) {
        console.error('创建标签失败:', error)
        throw error
      }
    },

    // 删除标签
    async deleteTag(id: number) {
      try {
        await notesApi.deleteTag(id)
        this.tags = this.tags.filter(tag => tag.id !== id)
      } catch (error) {
        console.error('删除标签失败:', error)
        throw error
      }
    },

    // 搜索笔记
    async searchNotes(keyword: string, filters?: Partial<SearchFilters>) {
      this.loading = true
      try {
        const searchFilters = { ...this.filters, keyword, ...filters }
        const response = await notesApi.searchNotes({
          keyword,
          page: 1,
          page_size: this.pagination.pageSize,
          ...filters
        })
        
        this.notes = response.notes
        this.pagination = {
          page: response.page,
          pageSize: response.page_size,
          total: response.total
        }
        this.filters = searchFilters
      } catch (error) {
        console.error('搜索笔记失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 设置筛选条件
    setFilters(filters: Partial<SearchFilters>) {
      this.filters = { ...this.filters, ...filters }
    },

    // 清空筛选条件
    clearFilters() {
      this.filters = {
        keyword: '',
        tags: [],
        source_type: '',
        date_range: undefined
      }
    },

    // 设置分页
    setPagination(pagination: Partial<Pagination>) {
      this.pagination = { ...this.pagination, ...pagination }
    },

    // 获取统计信息
    async fetchStats() {
      try {
        this.stats = await notesApi.getNotesStats()
      } catch (error) {
        console.error('获取统计信息失败:', error)
      }
    },

    // 清空当前笔记
    clearCurrentNote() {
      this.currentNote = null
    },

    // 重置状态
    reset() {
      this.notes = []
      this.currentNote = null
      this.pagination = {
        page: 1,
        pageSize: 20,
        total: 0
      }
      this.clearFilters()
    }
  }
})
