from deepeval.metrics import AnswerRelevancyMetric
from deepeval.test_case.llm_test_case import LLMTestCase
import os
os.environ["OPENAI_API_KEY"] = "sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
os.environ["OPENAI_BASE_URL"] = "https://api.zhizengzeng.com/v1"

# 这四个参数的含义如下：
# input：用户输入的问题或请求，即需要模型回答的内容。
# actual_output：模型实际生成的回答。
# expected_output：期望模型给出的理想回答（用于对比和评估）。
# retrieval_context：检索到的相关上下文信息，辅助模型生成更准确的回答。

test_case = LLMTestCase(
    input="如果这双鞋不合脚怎么办？",  # 用户输入
    actual_output="顾客可以在30天内退货并获得全额退款",  # 模型实际输出
    expected_output="顾客可以在30天内退货并获得全额退款。",  # 期望输出
    retrieval_context=["所有顾客都有资格享受30天无理由全额退款服务。"]  # 检索到的上下文
)

# 定义评估指标
# 定义评估指标 - 使用正确的类
answer_relevancy =AnswerRelevancyMetric(model="gpt-4o-mini")
answer_relevancy.measure(test_case)
print("上下文精确度得分: ", answer_relevancy.score)
print("原因: ", answer_relevancy.reason) 
