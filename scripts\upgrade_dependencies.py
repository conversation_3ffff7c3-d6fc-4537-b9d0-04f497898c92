#!/usr/bin/env python3
"""
依赖升级脚本
升级到支持Milvus 2.5 BM25的最新版本
"""
import subprocess
import sys
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(command):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            logger.error(f"命令执行失败: {command}")
            logger.error(f"错误信息: {result.stderr}")
            return False
        logger.info(f"命令执行成功: {command}")
        return True
    except Exception as e:
        logger.error(f"执行命令时出错: {e}")
        return False

def check_milvus_version():
    """检查Milvus版本是否支持BM25"""
    try:
        import pymilvus
        version = pymilvus.__version__
        logger.info(f"当前pymilvus版本: {version}")
        
        # 检查是否支持BM25功能
        try:
            from pymilvus import Function, FunctionType
            if hasattr(FunctionType, 'BM25'):
                logger.info("✅ Milvus支持BM25功能")
                return True
            else:
                logger.warning("❌ Milvus版本不支持BM25功能")
                return False
        except ImportError:
            logger.warning("❌ 无法导入BM25相关功能")
            return False
    except ImportError:
        logger.error("❌ pymilvus未安装")
        return False

def upgrade_dependencies():
    """升级依赖包"""
    logger.info("开始升级依赖包...")
    
    # 升级核心依赖
    dependencies = [
        "pymilvus>=2.5.12",
        "langchain>=0.3.26",
        "langchain-community>=0.3.27",
        "rank-bm25>=0.2.2"
    ]
    
    for dep in dependencies:
        logger.info(f"升级 {dep}...")
        if not run_command(f"pip install --upgrade {dep}"):
            logger.error(f"升级 {dep} 失败")
            return False
    
    return True

def main():
    """主函数"""
    logger.info("=== Milvus 2.5 BM25依赖升级 ===")
    
    # 1. 升级依赖
    if not upgrade_dependencies():
        logger.error("依赖升级失败")
        sys.exit(1)
    
    # 2. 检查Milvus版本
    if not check_milvus_version():
        logger.error("Milvus版本检查失败，请确保使用支持BM25的版本")
        sys.exit(1)
    
    logger.info("✅ 依赖升级完成，Milvus 2.5 BM25功能可用")

if __name__ == "__main__":
    main()
