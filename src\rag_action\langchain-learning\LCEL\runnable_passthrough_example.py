"""
使用 RunnablePassthrough 的示例
"""

from langchain_core.output_parsers import StrOutputParser
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough
import asyncio

# 创建 LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0
)

def basic_passthrough_example():
    """基础 RunnablePassthrough 示例"""
    
    print("=== 基础 RunnablePassthrough 示例 ===")
    
    # 创建链：传递原始输入并添加处理结果
    chain = (
        {"original_input": RunnablePassthrough()} |
        ChatPromptTemplate.from_template("请分析：{original_input}") |
        llm |
        StrOutputParser()
    )
    
    result = chain.invoke("人工智能技术")
    print(f"结果: {result[:100]}...")

def passthrough_with_processing():
    """带处理的 RunnablePassthrough 示例"""
    
    print("\n=== 带处理的 RunnablePassthrough 示例 ===")
    
    # 修复：使用 RunnableParallel 创建并行链
    from langchain_core.runnables import RunnableParallel
    
    chain = RunnableParallel({
        "topic": RunnablePassthrough(),
        "analysis": ChatPromptTemplate.from_template("请分析：{topic}") | llm | StrOutputParser(),
        "summary": ChatPromptTemplate.from_template("请总结：{topic}") | llm | StrOutputParser()
    })
    
    result = chain.invoke("机器学习")
    print("原始主题:", result["topic"])
    print("分析结果:", result["analysis"][:100] + "...")
    print("总结结果:", result["summary"][:100] + "...")

def passthrough_with_conditional():
    """条件 RunnablePassthrough 示例"""
    
    print("\n=== 条件 RunnablePassthrough 示例 ===")
    
    chain = (
        {"topic": RunnablePassthrough()} |
        RunnablePassthrough.assign(**{
            "type": lambda x: "详细主题" if len(x["topic"]) > 10 else "简单主题",
            "analysis": lambda x: "需要深入分析" if len(x["topic"]) > 10 else "可以简要说明"
        }) |
        ChatPromptTemplate.from_template("请根据类型 {type} 分析主题：{topic}") |
        llm |
        StrOutputParser()
    )
    
    # 测试不同长度的主题
    topics = ["AI", "人工智能技术发展与应用前景"]
    
    for topic in topics:
        print(f"\n主题: {topic}")
        result = chain.invoke({"topic": topic})
        print(f"结果: {result[:100]}...")

def passthrough_with_multiple_inputs():
    """多输入 RunnablePassthrough 示例"""
    
    print("\n=== 多输入 RunnablePassthrough 示例 ===")
    
    # 修复：使用 RunnableParallel
    from langchain_core.runnables import RunnableParallel
    
    chain = RunnableParallel({
        "topic": RunnablePassthrough(),
        "context": RunnablePassthrough(),
        "analysis": ChatPromptTemplate.from_template("请基于上下文 {context} 分析：{topic}") | llm | StrOutputParser(),
        "comparison": ChatPromptTemplate.from_template("请比较 {topic} 和 {context}") | llm | StrOutputParser()
    })
    
    result = chain.invoke({
        "topic": "深度学习",
        "context": "传统机器学习"
    })
    
    print("主题:", result["topic"])
    print("上下文:", result["context"])
    print("分析结果:", result["analysis"][:100] + "...")
    print("比较结果:", result["comparison"][:100] + "...")

def passthrough_with_streaming():
    """流式 RunnablePassthrough 示例"""
    
    print("\n=== 流式 RunnablePassthrough 示例 ===")
    
    # 修复：使用 RunnableParallel
    from langchain_core.runnables import RunnableParallel
    
    chain = RunnableParallel({
        "topic": RunnablePassthrough(),
        "streaming_output": ChatPromptTemplate.from_template("请详细解释：{topic}") | llm | StrOutputParser()
    })
    
    print("开始流式处理...")
    for chunk in chain.stream({"topic": "区块链技术"}):
        if "streaming_output" in chunk:
            print(chunk["streaming_output"], end="", flush=True)
    print("\n流式处理完成")

def passthrough_with_error_handling():
    """带错误处理的 RunnablePassthrough 示例"""
    
    print("\n=== 带错误处理的 RunnablePassthrough 示例 ===")
    
    # 修复：使用字符串输入而不是字典
    chain = (
        RunnablePassthrough.assign(**{
            "error_check": lambda x: "主题为空" if not x or len(str(x).strip()) == 0 else None
        }) |
        ChatPromptTemplate.from_template("请分析：{input}") |
        llm |
        StrOutputParser()
    )
    
    # 测试正常和异常情况
    test_cases = ["人工智能", "", "   "]
    
    for topic in test_cases:
        try:
            result = chain.invoke(topic)
            print(f"主题: '{topic}' -> 结果: {result[:50]}...")
        except Exception as e:
            print(f"主题: '{topic}' -> 错误: {str(e)}")

def passthrough_with_async():
    """异步 RunnablePassthrough 示例"""
    
    print("\n=== 异步 RunnablePassthrough 示例 ===")
    
    async def async_passthrough():
        chain = (
            {"topic": RunnablePassthrough()} |
            ChatPromptTemplate.from_template("请分析：{topic}") |
            llm |
            StrOutputParser()
        )
        
        result = await chain.ainvoke({"topic": "量子计算"})
        print(f"异步结果: {result[:100]}...")
    
    asyncio.run(async_passthrough())

def passthrough_with_complex_chain():
    """复杂链中的 RunnablePassthrough 示例"""
    
    print("\n=== 复杂链中的 RunnablePassthrough 示例 ===")
    
    # 创建多个处理步骤
    step1 = ChatPromptTemplate.from_template("第一步：请分析 {topic} 的基本概念") | llm | StrOutputParser()
    step2 = ChatPromptTemplate.from_template("第二步：基于第一步的结果，请深入分析 {topic} 的应用") | llm | StrOutputParser()
    step3 = ChatPromptTemplate.from_template("第三步：请总结 {topic} 的发展趋势") | llm | StrOutputParser()
    
    # 组合成复杂链
    complex_chain = (
        {"topic": RunnablePassthrough()} |
        RunnablePassthrough.assign(**{
            "step1_result": step1,
            "step2_result": step2,
            "step3_result": step3
        }) |
        ChatPromptTemplate.from_template("""
        请基于以下三个步骤的结果，给出关于 {topic} 的完整分析：
        
        步骤1结果：{step1_result}
        步骤2结果：{step2_result}
        步骤3结果：{step3_result}
        """) |
        llm |
        StrOutputParser()
    )
    
    result = complex_chain.invoke({"topic": "机器学习"})
    print(f"复杂链结果: {result[:200]}...")

def main():
    """主函数"""
    basic_passthrough_example()
    passthrough_with_processing()
    passthrough_with_conditional()
    passthrough_with_multiple_inputs()
    passthrough_with_streaming()
    passthrough_with_error_handling()
    passthrough_with_async()
    passthrough_with_complex_chain()

if __name__ == "__main__":
    main() 