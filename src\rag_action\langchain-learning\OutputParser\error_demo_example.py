"""
故意制造错误的案例
展示各种错误情况和容错机制的效果
"""

from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict, ValidationError
from langchain.output_parsers import PydanticOutputParser, OutputFixingParser, RetryOutputParser
from langchain.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from langchain_core.runnables import RunnablePassthrough, RunnableLambda

class UserProfile(BaseModel):
    """用户档案模型"""
    
    model_config = ConfigDict(
        extra="forbid",
        str_strip_whitespace=True,
    )
    
    name: str = Field(description="用户姓名")
    age: int = Field(description="用户年龄", ge=0, le=150)
    email: str = Field(description="邮箱地址")
    interests: List[str] = Field(description="兴趣爱好列表")
    bio: Optional[str] = Field(description="个人简介", default=None)

def extract_content(message):
    """提取消息内容"""
    if hasattr(message, 'content'):
        return message.content
    return str(message)

def demo_format_error():
    """演示格式错误"""
    
    print("=== 格式错误演示 ===")
    
    # 创建解析器
    parser = PydanticOutputParser(pydantic_object=UserProfile)
    fixing_parser = OutputFixingParser.from_llm(
        parser=parser,
        llm=ChatOpenAI(
            temperature=0,
            base_url="https://api.zhizengzeng.com/v1",
            api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
        )
    )
    
    # 故意制造格式错误的输出
    malformed_outputs = [
        # 缺少字段
        """
        {
            "name": "张三",
            "age": 28
        }
        """,
        
        # 字段类型错误
        """
        {
            "name": "李四",
            "age": "二十五",
            "email": "<EMAIL>",
            "interests": ["音乐", "电影"],
            "bio": "音乐爱好者"
        }
        """,
        
        # JSON 格式错误
        """
        {
            "name": "王五",
            "age": 30,
            "email": "<EMAIL>",
            "interests": ["摄影", "旅行"],
            "bio": "摄影师"
        """,
        
        # 完全错误的格式
        """
        姓名：赵六
        年龄：27岁
        邮箱：<EMAIL>
        兴趣爱好：健身、烹饪
        个人简介：健身教练
        """
    ]
    
    for i, malformed_output in enumerate(malformed_outputs, 1):
        print(f"\n--- 错误案例 {i} ---")
        print(f"错误输出:\n{malformed_output}")
        
        # 尝试原始解析器
        try:
            result = parser.parse(malformed_output)
            print("✅ 原始解析器成功")
        except Exception as e:
            print(f"❌ 原始解析器失败: {type(e).__name__}: {e}")
            
            # 尝试修复解析器
            try:
                result = fixing_parser.parse(malformed_output)
                print("✅ 修复解析器成功:")
                print(f"   姓名: {result.name}")
                print(f"   年龄: {result.age}")
                print(f"   邮箱: {result.email}")
                print(f"   兴趣爱好: {result.interests}")
                print(f"   个人简介: {result.bio}")
            except Exception as e2:
                print(f"❌ 修复解析器也失败: {type(e2).__name__}: {e2}")

def demo_llm_error():
    """演示 LLM 输出错误"""
    
    print("\n=== LLM 输出错误演示 ===")
    
    # 创建解析器
    parser = PydanticOutputParser(pydantic_object=UserProfile)
    fixing_parser = OutputFixingParser.from_llm(
        parser=parser,
        llm=ChatOpenAI(
            temperature=0,
            base_url="https://api.zhizengzeng.com/v1",
            api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
        )
    )
    retry_parser = RetryOutputParser.from_llm(
        parser=parser,
        llm=ChatOpenAI(
            temperature=0,
            base_url="https://api.zhizengzeng.com/v1",
            api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
        )
    )
    
    # 创建提示模板
    prompt = PromptTemplate(
        template="请根据以下信息创建用户档案：\n{format_instructions}\n\n用户信息：{user_info}",
        input_variables=["user_info"],
        partial_variables={"format_instructions": parser.get_format_instructions()}
    )
    
    # 故意制造有问题的用户信息
    problematic_user_info = """
    姓名：测试用户
    年龄：二百岁
    邮箱：不是邮箱格式
    个人简介：这是一个故意制造错误的测试案例，包含各种格式问题和逻辑错误
    兴趣爱好：这个字段应该是列表格式，但这里故意写成了字符串
    """
    
    print(f"问题用户信息:\n{problematic_user_info}")
    
    # 使用高温度的 LLM 增加错误概率
    problematic_llm = ChatOpenAI(
        temperature=0.9,  # 高温度增加随机性
        base_url="https://api.zhizengzeng.com/v1",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
    )
    
    # 生成响应
    response = problematic_llm.invoke(prompt.format(user_info=problematic_user_info))
    text = extract_content(response)
    
    print(f"\nLLM 输出:\n{text}")
    
    # 尝试解析
    try:
        result = parser.parse(text)
        print("✅ 原始解析器成功")
    except Exception as e:
        print(f"❌ 原始解析器失败: {type(e).__name__}: {e}")
        
        try:
            result = fixing_parser.parse(text)
            print("✅ 修复解析器成功:")
            print(f"   姓名: {result.name}")
            print(f"   年龄: {result.age}")
            print(f"   邮箱: {result.email}")
            print(f"   兴趣爱好: {result.interests}")
            print(f"   个人简介: {result.bio}")
        except Exception as e2:
            print(f"❌ 修复解析器失败: {type(e2).__name__}: {e2}")
            
            try:
                result = retry_parser.parse_with_prompt(
                    text, 
                    prompt.format(user_info=problematic_user_info)
                )
                print("✅ 重试解析器成功:")
                print(f"   姓名: {result.name}")
                print(f"   年龄: {result.age}")
                print(f"   邮箱: {result.email}")
                print(f"   兴趣爱好: {result.interests}")
                print(f"   个人简介: {result.bio}")
            except Exception as e3:
                print(f"❌ 重试解析器也失败: {type(e3).__name__}: {e3}")

def demo_lcel_error_handling():
    """演示 LCEL 错误处理"""
    
    print("\n=== LCEL 错误处理演示 ===")
    
    # 创建 LLM
    llm = ChatOpenAI(
        temperature=0.8,  # 高温度增加错误概率
        base_url="https://api.zhizengzeng.com/v1",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
    )
    
    # 创建解析器
    parser = PydanticOutputParser(pydantic_object=UserProfile)
    fixing_parser = OutputFixingParser.from_llm(parser=parser, llm=llm)
    
    # 创建提示模板
    prompt = PromptTemplate(
        template="请根据以下信息创建用户档案：\n{format_instructions}\n\n用户信息：{user_info}",
        input_variables=["user_info"],
        partial_variables={"format_instructions": parser.get_format_instructions()}
    )
    
    # 定义错误处理函数
    def robust_parse(message):
        """健壮的解析函数"""
        text = extract_content(message)
        print(f"解析文本: {text[:200]}...")
        
        try:
            result = parser.parse(text)
            print("✅ 原始解析成功")
            return result
        except ValidationError as e:
            print(f"❌ 验证错误: {e}")
            try:
                result = fixing_parser.parse(text)
                print("✅ 修复解析成功")
                return result
            except Exception as e2:
                print(f"❌ 修复解析失败: {e2}")
                return UserProfile(
                    name="解析失败",
                    age=0,
                    email="<EMAIL>",
                    interests=["未知"],
                    bio="解析失败"
                )
        except Exception as e:
            print(f"❌ 其他错误: {e}")
            return UserProfile(
                name="解析失败",
                age=0,
                email="<EMAIL>",
                interests=["未知"],
                bio="解析失败"
            )
    
    # 构建 LCEL 链
    chain = prompt | llm | RunnableLambda(robust_parse)
    
    # 故意制造有问题的用户信息
    problematic_user_info = """
    姓名：LCEL测试用户
    年龄：999岁
    邮箱：invalid-email
    个人简介：这是一个用于测试LCEL错误处理的用户信息，包含各种格式问题
    兴趣爱好：这里故意不按照列表格式写，而是写成了普通的文本描述
    """
    
    print(f"问题用户信息:\n{problematic_user_info}")
    
    # 执行链
    try:
        result = chain.invoke({"user_info": problematic_user_info})
        print("\n最终结果:")
        print(f"姓名: {result.name}")
        print(f"年龄: {result.age}")
        print(f"邮箱: {result.email}")
        print(f"兴趣爱好: {result.interests}")
        print(f"个人简介: {result.bio}")
        
    except Exception as e:
        print(f"❌ LCEL 链执行失败: {e}")

def demo_edge_cases():
    """演示边界情况"""
    
    print("\n=== 边界情况演示 ===")
    
    # 创建解析器
    parser = PydanticOutputParser(pydantic_object=UserProfile)
    fixing_parser = OutputFixingParser.from_llm(
        parser=parser,
        llm=ChatOpenAI(
            temperature=0,
            base_url="https://api.zhizengzeng.com/v1",
            api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
        )
    )
    
    # 边界情况测试
    edge_cases = [
        # 空字符串
        "",
        
        # 只有部分字段
        '{"name": "测试"}',
        
        # 字段值超出范围
        '{"name": "测试", "age": 200, "email": "<EMAIL>", "interests": ["测试"], "bio": "测试"}',
        
        # 特殊字符
        '{"name": "测试用户@#$%", "age": 25, "email": "<EMAIL>", "interests": ["测试"], "bio": "包含特殊字符的简介！@#￥%"}',
        
        # 超长文本
        '{"name": "' + "超长" * 50 + '", "age": 25, "email": "<EMAIL>", "interests": ["测试"], "bio": "' + "超长简介" * 100 + '"}',
        
        # 空列表
        '{"name": "测试", "age": 25, "email": "<EMAIL>", "interests": [], "bio": "测试"}',
        
        # 空值
        '{"name": "", "age": 0, "email": "", "interests": [], "bio": null}'
    ]
    
    for i, edge_case in enumerate(edge_cases, 1):
        print(f"\n--- 边界情况 {i} ---")
        print(f"输入: {edge_case[:100]}...")
        
        try:
            result = parser.parse(edge_case)
            print("✅ 原始解析器成功")
        except Exception as e:
            print(f"❌ 原始解析器失败: {type(e).__name__}: {e}")
            
            try:
                result = fixing_parser.parse(edge_case)
                print("✅ 修复解析器成功")
            except Exception as e2:
                print(f"❌ 修复解析器也失败: {type(e2).__name__}: {e2}")

def main():
    """主函数"""
    demo_format_error()
    demo_llm_error()
    demo_lcel_error_handling()
    demo_edge_cases()

if __name__ == "__main__":
    main() 