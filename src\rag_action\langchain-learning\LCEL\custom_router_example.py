"""
使用 LCEL 自定义路由链示例
"""

from langchain_core.output_parsers import StrOutputParser
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableLambda, RunnableParallel, RunnablePassthrough
from langchain_core.runnables.router import RouterRunnable
from typing import Dict, Any, List, Union
import re
import json

# 创建 LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0
)

def basic_router_example():
    """基础路由示例"""
    
    print("=== 基础路由示例 ===")
    
    # 定义路由函数
    def route_by_keywords(input_dict: Dict[str, Any]) -> str:
        """根据关键词路由"""
        user_input = input_dict.get("user_input", "").lower()
        
        if any(word in user_input for word in ["技术", "编程", "代码", "开发"]):
            return "tech"
        elif any(word in user_input for word in ["商业", "市场", "营销", "销售"]):
            return "business"
        elif any(word in user_input for word in ["学习", "教育", "培训", "课程"]):
            return "education"
        else:
            return "general"
    
    # 创建不同的处理链
    tech_chain = ChatPromptTemplate.from_template("""
你是一个技术专家。请从技术角度回答以下问题：

问题：{user_input}

请提供技术性的回答：
""") | llm | StrOutputParser()
    
    business_chain = ChatPromptTemplate.from_template("""
你是一个商业顾问。请从商业角度回答以下问题：

问题：{user_input}

请提供商业性的回答：
""") | llm | StrOutputParser()
    
    education_chain = ChatPromptTemplate.from_template("""
你是一个教育专家。请从教育角度回答以下问题：

问题：{user_input}

请提供教育性的回答：
""") | llm | StrOutputParser()
    
    general_chain = ChatPromptTemplate.from_template("""
你是一个智能助手。请回答以下问题：

问题：{user_input}

请提供有帮助的回答：
""") | llm | StrOutputParser()
    
    # 创建路由链
    router = RouterRunnable(
        {
            "tech": tech_chain,
            "business": business_chain,
            "education": education_chain,
            "general": general_chain
        },
        route_by_keywords
    )
    
    # 测试路由
    test_inputs = [
        "Python编程有什么优势？",
        "如何制定营销策略？",
        "推荐一些在线学习平台",
        "今天天气怎么样？"
    ]
    
    for user_input in test_inputs:
        print(f"\n用户问题: {user_input}")
        result = router.invoke({"user_input": user_input})
        print(f"回答: {result}")

def llm_router_example():
    """使用 LLM 进行路由"""
    
    print("\n=== LLM 路由示例 ===")
    
    # 创建路由 LLM
    router_llm = ChatOpenAI(
        base_url="https://api.zhizengzeng.com/v1",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
        model="gpt-4o-mini",
        temperature=0
    )
    
    # 路由提示模板
    router_prompt = ChatPromptTemplate.from_template("""
请分析以下问题，并选择最合适的处理方式。

问题：{user_input}

可选的处理方式：
1. "tech" - 技术相关问题（编程、开发、技术架构等）
2. "business" - 商业相关问题（营销、市场、商业模式等）
3. "education" - 教育相关问题（学习、培训、课程等）
4. "general" - 一般性问题

请只返回对应的关键词（tech/business/education/general）：
""")
    
    # 路由链
    router_chain = router_prompt | router_llm | StrOutputParser()
    
    # 处理链
    tech_chain = ChatPromptTemplate.from_template("技术专家回答：{user_input}") | llm | StrOutputParser()
    business_chain = ChatPromptTemplate.from_template("商业顾问回答：{user_input}") | llm | StrOutputParser()
    education_chain = ChatPromptTemplate.from_template("教育专家回答：{user_input}") | llm | StrOutputParser()
    general_chain = ChatPromptTemplate.from_template("智能助手回答：{user_input}") | llm | StrOutputParser()
    
    # 创建路由映射
    def route_with_llm(input_dict: Dict[str, Any]) -> str:
        """使用 LLM 进行路由"""
        route_result = router_chain.invoke(input_dict)
        return route_result.strip().lower()
    
    # 创建路由链
    router = RouterRunnable(
        {
            "tech": tech_chain,
            "business": business_chain,
            "education": education_chain,
            "general": general_chain
        },
        route_with_llm
    )
    
    # 测试
    test_inputs = [
        "如何优化数据库查询性能？",
        "制定产品定价策略的方法",
        "推荐机器学习入门课程",
        "解释一下量子计算"
    ]
    
    for user_input in test_inputs:
        print(f"\n用户问题: {user_input}")
        result = router.invoke({"user_input": user_input})
        print(f"回答: {result}")

def conditional_router_example():
    """条件路由示例"""
    
    print("\n=== 条件路由示例 ===")
    
    # 定义条件检查函数
    def check_complexity(input_dict: Dict[str, Any]) -> Dict[str, Any]:
        """检查问题复杂度"""
        user_input = input_dict.get("user_input", "")
        word_count = len(user_input.split())
        
        return {
            **input_dict,
            "complexity": "complex" if word_count > 10 else "simple",
            "word_count": word_count
        }
    
    def check_intent(input_dict: Dict[str, Any]) -> Dict[str, Any]:
        """检查用户意图"""
        user_input = input_dict.get("user_input", "").lower()
        
        if "?" in user_input or any(word in user_input for word in ["什么", "如何", "为什么"]):
            intent = "question"
        elif any(word in user_input for word in ["请", "帮我", "需要"]):
            intent = "request"
        else:
            intent = "statement"
        
        return {**input_dict, "intent": intent}
    
    # 创建不同的处理链
    simple_question_chain = ChatPromptTemplate.from_template("""
简单问题回答：{user_input}
""") | llm | StrOutputParser()
    
    complex_question_chain = ChatPromptTemplate.from_template("""
复杂问题详细回答：{user_input}
请提供详细的解释和示例。
""") | llm | StrOutputParser()
    
    request_chain = ChatPromptTemplate.from_template("""
处理用户请求：{user_input}
请提供具体的解决方案。
""") | llm | StrOutputParser()
    
    statement_chain = ChatPromptTemplate.from_template("""
回应用户陈述：{user_input}
请提供相关的见解和建议。
""") | llm | StrOutputParser()
    
    # 创建条件路由函数
    def conditional_route(input_dict: Dict[str, Any]) -> str:
        """条件路由"""
        complexity = input_dict.get("complexity", "simple")
        intent = input_dict.get("intent", "question")
        
        if intent == "question":
            return "complex_question" if complexity == "complex" else "simple_question"
        elif intent == "request":
            return "request"
        else:
            return "statement"
    
    # 创建条件路由链
    conditional_router = (
        RunnablePassthrough.assign(
            complexity=check_complexity,
            intent=check_intent
        ) |
        RouterRunnable(
            {
                "simple_question": simple_question_chain,
                "complex_question": complex_question_chain,
                "request": request_chain,
                "statement": statement_chain
            },
            conditional_route
        )
    )
    
    # 测试
    test_inputs = [
        "什么是Python？",
        "请详细解释机器学习中的神经网络是如何工作的，包括前向传播、反向传播、梯度下降等核心概念，以及在实际应用中如何选择合适的网络结构和超参数？",
        "请帮我写一个Python函数",
        "我今天学习了新的编程技术"
    ]
    
    for user_input in test_inputs:
        print(f"\n用户输入: {user_input}")
        result = conditional_router.invoke({"user_input": user_input})
        print(f"回答: {result}")

def parallel_router_example():
    """并行路由示例"""
    
    print("\n=== 并行路由示例 ===")
    
    # 创建多个并行处理链
    analysis_chain = ChatPromptTemplate.from_template("""
分析问题：{user_input}
请提供问题分析：
""") | llm | StrOutputParser()
    
    solution_chain = ChatPromptTemplate.from_template("""
提供解决方案：{user_input}
请提供解决方案：
""") | llm | StrOutputParser()
    
    example_chain = ChatPromptTemplate.from_template("""
提供示例：{user_input}
请提供相关示例：
""") | llm | StrOutputParser()
    
    # 创建并行处理链
    parallel_chain = RunnableParallel(
        analysis=analysis_chain,
        solution=solution_chain,
        example=example_chain
    )
    
    # 创建汇总链
    summary_chain = ChatPromptTemplate.from_template("""
基于以下信息提供综合回答：

分析：{analysis}
解决方案：{solution}
示例：{example}

原始问题：{user_input}

请提供综合回答：
""") | llm | StrOutputParser()
    
    # 创建完整的并行路由链
    parallel_router = (
        parallel_chain |
        RunnablePassthrough.assign(
            user_input=lambda x: x.get("user_input", "")
        ) |
        summary_chain
    )
    
    # 测试
    test_inputs = [
        "如何学习机器学习？",
        "Python中的装饰器是什么？"
    ]
    
    for user_input in test_inputs:
        print(f"\n用户问题: {user_input}")
        result = parallel_router.invoke({"user_input": user_input})
        print(f"综合回答: {result}")

def main():
    """主函数"""
    basic_router_example()
    llm_router_example()
    conditional_router_example()
    parallel_router_example()

if __name__ == "__main__":
    main() 