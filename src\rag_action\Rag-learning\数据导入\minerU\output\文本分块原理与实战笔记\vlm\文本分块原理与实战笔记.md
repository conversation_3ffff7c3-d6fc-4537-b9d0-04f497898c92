# 文本分块原理与实战笔记

笔记本： 我的第一个笔记本创建时间： 2025- 06- 09 22:16作者： 高晓琪

更新时间： 2025- 06- 09 22:24

# 文本分块原理与实战笔记

# 一、文本分块的原理与重要性

# 1. 为什么分块？

- 上下文窗口限制：大语言模型（如 GPT）输入受限于最大 token 数，例如 GPT-3.5 是 4k，GPT-4 支持最多 128k。- 减少噪声，提高匹配精度：将长文本切成短块，避免无关信息干扰嵌入或生成。- 提升召回率：小块语义更清晰，有助于更精确的相似度计算与检索。- 避免“Lost in the Middle”现象：生成模型对中间内容注意力减弱，分块有助于模型识别重点信息。

# 2. 嵌入模型 vs 生成模型

# 嵌入模型（Embedding Model）：

- 文本被拆成多个 token，每个 token 映射成向量，整体再池化（如平均）成一个句向量。- 长文本越长，平均后信息损失越严重。

# 生成模型（LLM）：

- 太长的输入易导致主题稀释，注意力集中在前后两端，中间信息被忽略。

# 二、文本分块方法

# 1. CharacterTextSplitter（固定字符切分）

- 按固定字符数分块，如每 1000 字符一块，设置重叠（如 100 字符）。- 适合规则性文本，如新闻、小说等。

# 2. RecursiveCharacterTextSplitter（递归分割）

定义多个分隔符（如  $\sqrt[n]{n}$  ，"，"，"），按优先级递归使用。- 可保证语义完整性，适合结构化文档。

# 3. 按格式或语言切分

- 支持如 get_separators_for_language(Language.JAVA) 的语言级分块。- 用于代码文档、技术手册等。

# 4. SemanticSplitter（语义分块）

- Ilamalndex 提供的语义切块方式。- 使用 embedding 向量余弦相似度将语义接近的句子组合为一个块。

# 5. Unstructured 文档结构分块

- 适用于 PDF、Word 等复杂结构文档。- Basic 策略：按元素顺序合并，超限则切分。- By Title：遇标题即分块，适合分章节文档。- By Page：每页为一个块。- By Similarity：将语义相近的元素合并为块。

# 三、分块高级技巧

# 1. 滑动窗口策略

- 分块时设置重叠区域（如 overlap=100 tokens），保证上下文连续性。

# 2. 父子块结构

- 大文档先生成摘要（父块），然后对子块细分。- Ilamalndex 示例：先 5000 字摘要，再分 500 字子块。

# 3. 分块元数据注入

- 在文本块中附加 metadata，如：文件名、页码、创建时间、类别。

- 有助于后续检索过滤和 UI 展示。

# 4. 多层索引构建

- 构建Treelndex、Summarylndex、Vectorlndex等层级结构。- 实现摘要  $\rightarrow$  全文定位，节省成本。

# 5. Chunk Embedding 入向量库

- 每个 chunk 经 embedding 后写入向量数据库（如 FAISS、Milvus、Weaviate）。- 与 metadata 一起入库，提升检索能力。

# 6. Late Chunking (延迟分块)

- 先对全文 embedding，再切分，以保留全局语义。- 有研究发现能提升长文召回准确率。

# 四、最佳 Chunk 参数推荐

- 简短问答任务推荐：64-128 tokens- 一般文档摘要推荐：256-512 tokens- 全文语义召回推荐：512-1024 tokens- 一般建议设置 overlap：50-100 tokens

# 五、可视化调试工具

- chunk-viz：社区提供的文本分块可视化工具。- 用于调试块大小、分割边界是否合理。- 网址示例：https://chunkviz.com

# 六、小结关键词对照表

<table><tr><td>项目</td><td>工具/策略</td><td>特点</td></tr><tr><td>固定分块</td><td>CharacterTextSplitter</td><td>简单高效</td></tr><tr><td>递归分块</td><td>RecursiveCharacterTextSplitter</td><td>多级语义粒度</td></tr></table>

<table><tr><td>项目</td><td>工具/策略</td><td>特点</td></tr><tr><td>语义分块</td><td>SemanticSplitter</td><td>基于余弦相似度</td></tr><tr><td>结构感知</td><td>Unstructured</td><td>适合PDF/复杂格式</td></tr><tr><td>可视化</td><td>chunk-viz</td><td>直观查看分块效果</td></tr><tr><td>高级技巧</td><td>overlap, 层级索引</td><td>提升效果与召回</td></tr></table>

该笔记适用于构建 RAG 系统、语义搜索系统、文档 QA 系统等所有对“文本块化处理”有依赖的场景。