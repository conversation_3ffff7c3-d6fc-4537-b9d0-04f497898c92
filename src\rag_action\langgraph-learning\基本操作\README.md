# LangGraph 学习

这个目录包含了 LangGraph 的学习示例，展示了如何使用 LangGraph 构建复杂的 AI 应用。

## 文件结构

- `01-simple_control_flow.py` - 简单控制流，展示基础的线性控制流
- `02-serial_control_flow.py` - 串行控制流，展示多个节点按顺序执行
- `03-branch_control_flow.py` - 分支控制流，展示根据条件选择不同路径
- `04-conditional_branch_and_loop.py` - 条件分支与循环，展示复杂的条件分支和循环控制流
- `05-runtime_configuration.py` - 图的运行时配置，展示如何在运行时动态配置和修改图
- `06-map_reduce_pattern.py` - Map-Reduce 模式，展示如何使用 LangGraph 实现 Map-Reduce 模式处理大量数据
- `07-parallel_processing.py` - 并行处理，展示 LangGraph 支持的多种并行处理模式

## 01-simple_control_flow.py

这是最简单的 LangGraph 应用，展示了基础的线性控制流。

### 核心概念

#### 1. 状态管理 (State Management)
```python
class AgentState(TypedDict):
    messages: Annotated[List, add_messages]  # 消息历史
    user_input: str  # 用户输入
    current_step: str  # 当前步骤
    result: str  # 处理结果
```

#### 2. 节点 (Nodes)
节点是图中的基本处理单元，每个节点都是一个函数，接收状态并返回更新后的状态。

```python
def analyze_node(state: AgentState) -> AgentState:
    """分析节点 - 分析用户输入"""
    # 处理逻辑
    return updated_state
```

#### 3. 图 (Graph)
图由节点和边组成，定义了工作流的执行路径。

```python
workflow = StateGraph(AgentState)
workflow.add_node("analyze", analyze_node)
workflow.add_node("respond", respond_node)
```

#### 4. 控制流 (Control Flow)

##### 基础控制流
```python
# 线性执行
workflow.add_edge("analyze", "respond")
workflow.add_edge("respond", END)
```

##### 条件控制流
```python
# 根据条件选择下一步
workflow.add_conditional_edges(
    "classify",
    route_node,
    {
        "technical": "technical",
        "business": "business",
        "general": "general"
    }
)
```

##### 循环控制流
```python
# 循环执行直到满足条件
workflow.add_conditional_edges(
    "process",
    should_continue,
    {
        "continue": "process",
        "end": END
    }
)
```

### 核心概念

#### 1. 状态管理 (State Management)
```python
class SimpleState(TypedDict):
    messages: Annotated[List, add_messages]  # 消息历史
    user_input: str  # 用户输入
    result: str  # 处理结果
```

#### 2. 节点 (Nodes)
节点是图中的基本处理单元，每个节点都是一个函数，接收状态并返回更新后的状态。

```python
def process_node(state: SimpleState) -> SimpleState:
    """处理节点：处理用户输入并生成回答"""
    # 处理逻辑
    return updated_state
```

#### 3. 图 (Graph)
图由节点和边组成，定义了工作流的执行路径。

```python
workflow = StateGraph(SimpleState)
workflow.add_node("process", process_node)
workflow.set_entry_point("process")
workflow.add_edge("process", END)
```

### 运行方式

```bash
cd src/rag_action/langgraph-learning
python 01-simple_control_flow.py
```

## 02-serial_control_flow.py

这是第二个 LangGraph 应用，展示了串行控制流。

### 串行控制流

#### 1. 多节点串行执行
```python
# 分析 -> 生成 -> 优化
workflow.add_edge("analyze", "generate")
workflow.add_edge("generate", "optimize")
workflow.add_edge("optimize", END)
```

#### 2. 状态传递
```python
class SerialState(TypedDict):
    messages: Annotated[List, add_messages]  # 消息历史
    user_input: str  # 用户输入
    analysis: str  # 分析结果
    answer: str  # 最终回答
    steps: List[str]  # 执行步骤
```

#### 3. 步骤跟踪
每个节点都会更新状态，记录执行步骤，便于调试和监控。

### 运行方式

```bash
cd src/rag_action/langgraph-learning
python 02-serial_control_flow.py
```

## 03-branch_control_flow.py

这是第三个 LangGraph 应用，展示了分支控制流。

### 分支控制流

#### 1. 条件路由
```python
def route_node(state: BranchState) -> str:
    """路由节点：根据问题类型选择路径"""
    question_type = state["question_type"]
    
    if "technical" in question_type:
        return "technical"
    elif "business" in question_type:
        return "business"
    elif "learning" in question_type:
        return "learning"
    else:
        return "general"
```

#### 2. 条件边
```python
workflow.add_conditional_edges(
    "classify",
    route_node,
    {
        "technical": "technical",
        "business": "business",
        "learning": "learning",
        "general": "general"
    }
)
```

#### 3. 专业处理
根据问题类型选择不同的专家进行处理：
- 技术问题 → 技术专家
- 商业问题 → 商业顾问
- 学习问题 → 教育专家
- 一般问题 → 智能助手

### 运行方式

```bash
cd src/rag_action/langgraph-learning
python 03-branch_control_flow.py
```

## 04-conditional_branch_and_loop.py

这是第四个 LangGraph 应用，展示了复杂的条件分支和循环控制流。

### 复杂控制流

#### 1. 多层条件判断
```python
def route_by_type_and_complexity(state: ComplexState) -> str:
    """根据类型和复杂度路由"""
    question_type = state["question_type"]
    complexity = state["complexity"]
    
    if question_type == "technical":
        if complexity == "simple":
            return "simple_technical"
        else:
            return "complex_technical"
    # ...
```

#### 2. 循环优化
```python
def should_continue_optimizing(state: ComplexState) -> str:
    """判断是否继续优化"""
    iteration_count = state["iteration_count"]
    complexity = state["complexity"]
    
    # 简单问题最多优化1次，复杂问题最多优化3次
    max_iterations = 1 if complexity == "simple" else 3
    
    if iteration_count >= max_iterations:
        return "end"
    else:
        return "continue"
```

#### 3. 路径跟踪
记录完整的执行路径，包括分类、复杂度判断、处理类型和优化次数。

### 运行方式

```bash
cd src/rag_action/langgraph-learning
python 04-conditional_branch_and_loop.py
```

## 05-runtime_configuration.py

这是第五个 LangGraph 应用，展示了图的运行时配置。

### 运行时配置

#### 1. 动态配置生成
```python
def config_loader_node(state: ConfigState) -> ConfigState:
    """配置加载节点：根据用户输入加载配置"""
    # 使用 LLM 分析用户输入并生成配置
    response = llm.invoke([...])
    config = json.loads(response.content)
    return {**state, "config": config}
```

#### 2. 自适应处理
```python
def adaptive_process_node(state: ConfigState) -> ConfigState:
    """自适应处理节点：根据配置调整处理方式"""
    config = state["config"]
    
    # 根据配置构建不同的提示词
    detail_prompts = {
        "simple": "请提供简洁的回答",
        "medium": "请提供详细的回答",
        "detailed": "请提供非常详细的回答，包含深入分析"
    }
```

#### 3. 条件执行
根据配置决定是否执行某些节点：
- `include_examples`: 是否添加示例
- `include_resources`: 是否添加资源
- `max_iterations`: 最大优化次数

#### 4. 外部配置源
```python
external_configs = {
    "user_preferences": {...},
    "system_settings": {...},
    "performance_mode": {...}
}
```

### 配置参数

- **detail_level**: 详细程度 (simple/medium/detailed)
- **response_style**: 语言风格 (formal/casual/technical)
- **max_iterations**: 最大迭代次数 (1-5)
- **include_examples**: 是否包含示例 (true/false)
- **include_resources**: 是否包含资源 (true/false)

### 运行方式

```bash
cd src/rag_action/langgraph-learning
python 05-runtime_configuration.py
```

## 06-map_reduce_pattern.py

这是第六个 LangGraph 应用，展示了 Map-Reduce 模式。

### Map-Reduce 模式

#### 1. 简单 Map-Reduce
```python
def split_data_node(state: MapReduceState) -> MapReduceState:
    """数据分块节点：将输入数据分割成多个块"""
    # 将长文本分割成多个段落或句子
    
def map_node(state: MapReduceState) -> MapReduceState:
    """Map 节点：并行处理每个数据块"""
    # 对每个数据块进行独立处理
    
def reduce_node(state: MapReduceState) -> MapReduceState:
    """Reduce 节点：合并所有 Map 结果"""
    # 将所有处理结果合并成最终输出
```

#### 2. 并行 Map-Reduce
```python
async def async_map_node(state: MapReduceState) -> MapReduceState:
    """异步 Map 节点：并行处理数据块"""
    # 使用 asyncio.gather 并行处理所有数据块
    
async def async_reduce_node(state: MapReduceState) -> MapReduceState:
    """异步 Reduce 节点"""
    # 异步合并处理结果
```

#### 3. 复杂 Map-Reduce
多阶段处理流程：
- **分析 Map**：对每个数据块进行深度分析
- **分类 Reduce**：对分析结果进行分类汇总
- **最终总结**：生成专业的最终报告

### 应用场景

- **大数据处理**：处理大量文档、日志、用户数据
- **并行计算**：提高处理效率，充分利用计算资源
- **分布式处理**：将任务分散到多个节点执行
- **实时分析**：对实时数据流进行快速分析

### 运行方式

```bash
cd src/rag_action/langgraph-learning
python 06-map_reduce_pattern.py
```

## 07-parallel_processing.py

这是第七个 LangGraph 应用，展示了 LangGraph 支持的多种并行处理模式。

### 并行处理模式

#### 1. 并行节点执行
```python
# 多个节点同时执行，然后合并结果
workflow.add_edge("sentiment", "merge")
workflow.add_edge("keywords", "merge")
workflow.add_edge("summary", "merge")
```

#### 2. 异步并行处理
```python
async def async_sentiment_node(state: ParallelState) -> ParallelState:
    """异步情感分析节点"""
    response = await llm.ainvoke([...])
    return updated_state

async def async_keywords_node(state: ParallelState) -> ParallelState:
    """异步关键词提取节点"""
    response = await llm.ainvoke([...])
    return updated_state
```

#### 3. 条件并行处理
```python
def route_by_complexity(state: ParallelState) -> str:
    """根据复杂度路由"""
    complexity_result = state["parallel_results"].get("complexity", {})
    needs_parallel = complexity_result.get("needs_parallel", False)
    
    if needs_parallel:
        return "parallel"
    else:
        return "simple"
```

### 并行处理特性

#### 1. 真正的并行执行
- 使用 `async/await` 实现真正的并发
- 多个任务同时执行，提高效率
- 支持异步 LLM 调用

#### 2. 结果合并
- 自动收集所有并行节点的结果
- 智能合并和冲突解决
- 生成综合报告

#### 3. 条件并行
- 根据输入复杂度决定是否并行
- 简单任务使用串行处理
- 复杂任务使用并行处理

### 应用场景

- **多维度分析**：同时进行情感分析、关键词提取、文本摘要
- **性能优化**：通过并行处理提高响应速度
- **资源利用**：充分利用计算资源
- **智能路由**：根据任务复杂度选择处理方式

### 运行方式

```bash
cd src/rag_action/langgraph-learning
python 07-parallel_processing.py
```

### 学习路径

这七个示例按照复杂度递增的顺序设计：

1. **01-simple_control_flow.py** - 入门级，理解基础概念
2. **02-serial_control_flow.py** - 初级，学习多节点串行执行
3. **03-branch_control_flow.py** - 中级，掌握条件分支
4. **04-conditional_branch_and_loop.py** - 高级，综合运用条件分支和循环
5. **05-runtime_configuration.py** - 专家级，掌握运行时配置和动态调整
6. **06-map_reduce_pattern.py** - 大师级，掌握 Map-Reduce 模式处理大数据
7. **07-parallel_processing.py** - 专家级，掌握并行处理模式

### 下一步学习

- 学习状态持久化
- 探索工具调用集成
- 了解错误处理机制
- 学习异步工作流
- 构建生产级应用
- 性能优化和监控 