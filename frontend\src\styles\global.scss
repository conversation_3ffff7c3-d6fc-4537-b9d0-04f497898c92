@use './variables.scss' as *;

// 全局重置样式
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 14px;
  line-height: var(--line-height-base);
  color: var(--text-primary);
  background-color: var(--bg-page);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  color: var(--text-primary);
  background-color: var(--bg-page);
  transition: var(--transition-base);
}

// 链接样式
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-fast);
  
  &:hover {
    color: var(--primary-light);
  }
  
  &:active {
    color: var(--primary-dark);
  }
}

// 按钮样式重置
button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  transition: var(--transition-fast);
  
  &:focus {
    outline: none;
  }
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

// 输入框样式重置
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  border: 1px solid var(--border-base);
  border-radius: var(--border-radius-base);
  padding: var(--spacing-sm);
  transition: var(--transition-fast);
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }
  
  &::placeholder {
    color: var(--text-placeholder);
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--border-base);
  border-radius: 3px;
  
  &:hover {
    background: var(--border-light);
  }
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--text-primary); }
.text-regular { color: var(--text-regular); }
.text-secondary { color: var(--text-secondary); }
.text-placeholder { color: var(--text-placeholder); }

.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-info { color: var(--info-color); }

.bg-primary { background-color: var(--primary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }
.bg-info { background-color: var(--info-color); }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-bold { font-weight: var(--font-weight-bold); }

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }

// 间距工具类
@each $size, $value in (xs: var(--spacing-xs), sm: var(--spacing-sm), md: var(--spacing-md), lg: var(--spacing-lg), xl: var(--spacing-xl)) {
  .m-#{$size} { margin: #{$value}; }
  .mt-#{$size} { margin-top: #{$value}; }
  .mr-#{$size} { margin-right: #{$value}; }
  .mb-#{$size} { margin-bottom: #{$value}; }
  .ml-#{$size} { margin-left: #{$value}; }
  .mx-#{$size} { margin-left: #{$value}; margin-right: #{$value}; }
  .my-#{$size} { margin-top: #{$value}; margin-bottom: #{$value}; }
  
  .p-#{$size} { padding: #{$value}; }
  .pt-#{$size} { padding-top: #{$value}; }
  .pr-#{$size} { padding-right: #{$value}; }
  .pb-#{$size} { padding-bottom: #{$value}; }
  .pl-#{$size} { padding-left: #{$value}; }
  .px-#{$size} { padding-left: #{$value}; padding-right: #{$value}; }
  .py-#{$size} { padding-top: #{$value}; padding-bottom: #{$value}; }
}

// Flex 工具类
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

// 显示/隐藏
.hidden { display: none; }
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }

// 位置
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

// 溢出
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }

// 文本省略
.truncate {
  @include text-ellipsis(1);
}

.line-clamp-2 {
  @include text-ellipsis(2);
}

.line-clamp-3 {
  @include text-ellipsis(3);
}

// 圆角
.rounded { border-radius: var(--border-radius-base); }
.rounded-sm { border-radius: var(--border-radius-small); }
.rounded-lg { border-radius: var(--border-radius-large); }
.rounded-full { border-radius: 50%; }

// 阴影
.shadow { box-shadow: var(--shadow-base); }
.shadow-light { box-shadow: var(--shadow-light); }
.shadow-dark { box-shadow: var(--shadow-dark); }
.shadow-none { box-shadow: none; }

// 过渡动画
.transition { transition: var(--transition-base); }
.transition-fast { transition: var(--transition-fast); }
.transition-slow { transition: var(--transition-slow); }

// 动画类
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.fade-in-up {
  animation: fadeInUp 0.3s ease-in-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-in-out;
}

.pulse {
  animation: pulse 2s infinite;
}

.spin {
  animation: spin 1s linear infinite;
}

// 响应式工具类
@include respond-below(md) {
  .hidden-mobile { display: none !important; }
  .block-mobile { display: block !important; }
}

@include respond-to(md) {
  .hidden-desktop { display: none !important; }
  .block-desktop { display: block !important; }
}

// 自定义组件样式
.card {
  background: var(--bg-color);
  border: 1px solid var(--border-lighter);
  border-radius: var(--border-radius-base);
  box-shadow: var(--shadow-base);
  padding: var(--spacing-lg);
  transition: var(--transition-base);
  
  &:hover {
    box-shadow: var(--shadow-light);
  }
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-align: center;
  cursor: pointer;
  transition: var(--transition-fast);
  user-select: none;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    
    &:hover:not(:disabled) {
      background-color: var(--primary-light);
      border-color: var(--primary-light);
    }
  }
  
  &.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
    
    &:hover:not(:disabled) {
      background-color: var(--success-light);
      border-color: var(--success-light);
    }
  }
  
  &.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: white;
    
    &:hover:not(:disabled) {
      background-color: var(--warning-light);
      border-color: var(--warning-light);
    }
  }
  
  &.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
    
    &:hover:not(:disabled) {
      background-color: var(--danger-light);
      border-color: var(--danger-light);
    }
  }
}
