#!/usr/bin/env python3
"""
AI笔记系统启动脚本
"""
import os
import sys
import subprocess
import argparse

def check_config():
    """检查配置文件"""
    if not os.path.exists("config.yaml"):
        print("[ERROR] 配置文件 config.yaml 不存在")
        print("请复制 config.yaml 并修改其中的配置项")
        return False

    print("[OK] 配置文件存在")
    return True

def init_database():
    """初始化数据库"""
    print("正在初始化数据库...")
    try:
        result = subprocess.run([
            sys.executable, "-m", "src.rag_action.core.init_db", "init"
        ], check=True, capture_output=True, text=True)
        
        print(result.stdout)
        print("[OK] 数据库初始化成功")
        return True

    except subprocess.CalledProcessError as e:
        print(f"[ERROR] 数据库初始化失败: {e}")
        print(e.stdout)
        print(e.stderr)
        return False

def check_database():
    """检查数据库连接"""
    print("正在检查数据库连接...")
    try:
        result = subprocess.run([
            sys.executable, "-m", "src.rag_action.core.init_db", "check"
        ], check=True, capture_output=True, text=True)
        
        print(result.stdout)
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] 数据库检查失败: {e}")
        return False

def start_server(host="0.0.0.0", port=8000, reload=False):
    """启动服务器"""
    print(f"正在启动AI笔记系统服务器 {host}:{port}...")

    try:
        # 设置PYTHONPATH环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = os.getcwd()

        # 使用uvicorn直接启动
        cmd = [
            sys.executable, "-m", "uvicorn",
            "src.rag_action.main:app",
            "--host", host,
            "--port", str(port)
        ]

        if reload:
            cmd.append("--reload")

        subprocess.run(cmd, check=True, env=env)

    except subprocess.CalledProcessError as e:
        print(f"[ERROR] 服务器启动失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n服务器已停止")
        return True

def main():
    parser = argparse.ArgumentParser(description="AI笔记系统启动脚本")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--reload", action="store_true", help="开启自动重载")
    parser.add_argument("--init-db", action="store_true", help="初始化数据库")
    parser.add_argument("--check-db", action="store_true", help="检查数据库")
    parser.add_argument("--skip-db-check", action="store_true", help="跳过数据库检查")
    
    args = parser.parse_args()
    
    print("AI笔记系统启动器")
    print("=" * 50)

    # 检查配置文件
    if not check_config():
        return 1
    
    # 初始化数据库
    if args.init_db:
        if not init_database():
            return 1
        return 0
    
    # 检查数据库
    if args.check_db:
        if not check_database():
            return 1
        return 0
    
    # 检查数据库连接（除非跳过）
    if not args.skip_db_check:
        if not check_database():
            print("\n数据库连接失败，请检查配置或运行 --init-db 初始化数据库")
            return 1
    
    # 启动服务器
    print("\n" + "=" * 50)
    print("[SUCCESS] 系统检查通过，正在启动服务器...")
    print(f"API文档: http://{args.host}:{args.port}/docs")
    print(f"ReDoc文档: http://{args.host}:{args.port}/redoc")
    print("按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    return 0 if start_server(args.host, args.port, args.reload) else 1

if __name__ == "__main__":
    sys.exit(main())
