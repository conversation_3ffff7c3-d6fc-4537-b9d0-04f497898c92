#!/usr/bin/env python3
"""
minerU PDF转Markdown示例使用脚本
展示不同场景下的使用方法
"""
import os
from pathlib import Path

def example_basic_usage():
    """示例1：基础使用"""
    print("=" * 50)
    print("示例1：基础使用")
    print("=" * 50)
    
    print("使用方法：")
    print("1. 将PDF文件放入 'pdfs' 目录")
    print("2. 运行命令：python 基础转换.py")
    print("3. 查看 'output' 目录中的结果")
    
    print("\n适用场景：")
    print("- 新手用户")
    print("- 标准文档")
    print("- 快速转换需求")

def example_high_quality_usage():
    """示例2：高质量转换"""
    print("\n" + "=" * 50)
    print("示例2：高质量转换")
    print("=" * 50)
    
    print("使用方法：")
    print("1. 将PDF文件放入 'pdfs' 目录")
    print("2. 运行命令：python 高质量转换.py")
    print("3. 查看 'output' 目录中的结果")
    
    print("\n适用场景：")
    print("- 学术论文")
    print("- 复杂布局文档")
    print("- 包含公式和表格的文档")
    print("- 对转换质量要求较高的场景")

def example_batch_usage():
    """示例3：批量处理"""
    print("\n" + "=" * 50)
    print("示例3：批量处理")
    print("=" * 50)
    
    print("使用方法：")
    print("1. 将多个PDF文件放入 'pdfs' 目录")
    print("2. 运行命令：python 批量转换.py")
    print("3. 查看 'output' 目录中的结果")
    
    print("\n适用场景：")
    print("- 大量文档处理")
    print("- 批量转换需求")
    print("- 需要并发处理的场景")

def example_advanced_usage():
    """示例4：高级配置"""
    print("\n" + "=" * 50)
    print("示例4：高级配置")
    print("=" * 50)
    
    print("使用方法：")
    print("1. 将PDF文件放入 'pdfs' 目录")
    print("2. 运行命令（示例）：")
    print("   python 高级转换.py --help")
    print("   python 高级转换.py --lang en")
    print("   python 高级转换.py --backend vlm-transformers")
    print("   python 高级转换.py --start-page 1 --end-page 10")
    
    print("\n适用场景：")
    print("- 需要精确控制参数")
    print("- 英文文档处理")
    print("- 指定页面范围")
    print("- 自定义转换选项")

def example_command_line_options():
    """示例5：命令行选项说明"""
    print("\n" + "=" * 50)
    print("示例5：命令行选项说明")
    print("=" * 50)
    
    options = [
        ("--input-dir, -i", "输入PDF文件目录", "pdfs"),
        ("--output-dir, -o", "输出目录", "output"),
        ("--lang, -l", "文档语言", "ch (中文), en (英文)"),
        ("--backend, -b", "转换后端", "pipeline, vlm-transformers"),
        ("--method, -m", "解析方法", "auto, txt, ocr"),
        ("--start-page", "开始页面", "0"),
        ("--end-page", "结束页面", "全部"),
        ("--no-formula", "禁用公式解析", "False"),
        ("--no-table", "禁用表格解析", "False"),
        ("--draw-bbox", "绘制边界框", "False")
    ]
    
    print("高级转换脚本支持的命令行选项：")
    for option, desc, default in options:
        print(f"  {option:<20} {desc:<15} (默认: {default})")

def example_usage_tips():
    """显示使用提示"""
    print("\n" + "=" * 50)
    print("使用提示")
    print("=" * 50)
    
    tips = [
        "1. 首次使用建议先运行 基础转换.py",
        "2. 如果转换质量不满意，尝试 高质量转换.py",
        "3. 大批量文件使用 批量转换.py",
        "4. 需要精确控制参数使用 高级转换.py",
        "5. 网络问题可以设置 MINERU_MODEL_SOURCE=modelscope",
        "6. 查看日志文件了解详细错误信息",
        "7. VLM模式需要更多内存和计算资源",
        "8. Pipeline模式更稳定，适合大多数场景"
    ]
    
    for tip in tips:
        print(f"💡 {tip}")

def show_file_structure():
    """显示文件结构"""
    print("\n" + "=" * 50)
    print("文件结构说明")
    print("=" * 50)
    
    structure = [
        ("基础转换.py", "最简单的转换脚本，使用pipeline模式"),
        ("高质量转换.py", "使用VLM模式的高质量转换"),
        ("批量转换.py", "并发处理多个文件的批量转换"),
        ("高级转换.py", "支持命令行参数的高级转换"),
        ("使用示例.py", "本文件，展示各种使用方法"),
        ("使用说明.md", "详细的使用说明文档"),
        ("快速开始.md", "快速开始指南"),
        ("minerU_pdf2markdown.py", "核心转换模块（参考实现）")
    ]
    
    print("当前目录下的文件说明：")
    for filename, desc in structure:
        print(f"  {filename:<20} {desc}")

def main():
    """主函数"""
    print("🚀 minerU PDF转Markdown示例使用指南")
    print("本脚本展示不同场景下的使用方法")
    
    # 显示文件结构
    show_file_structure()
    
    # 运行各种示例
    example_basic_usage()
    example_high_quality_usage()
    example_batch_usage()
    example_advanced_usage()
    example_command_line_options()
    example_usage_tips()
    
    print("\n" + "=" * 50)
    print("📚 更多信息请查看 使用说明.md")
    print("🚀 快速开始请查看 快速开始.md")
    print("=" * 50)

if __name__ == "__main__":
    main() 