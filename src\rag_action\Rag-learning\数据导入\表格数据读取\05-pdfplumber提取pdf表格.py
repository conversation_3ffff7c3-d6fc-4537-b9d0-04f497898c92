from importlib import metadata
from this import d
from openai import base_url
import pdfplumber
import pandas as pd
from llama_index.core import VectorStoreIndex
from llama_index.core import Document
from typing import List
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding

pdf_path = "C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/billionaires_page-1-5.pdf"

llm = OpenAI( api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    api_base="https://api.zhizengzeng.com/v1",
    model="gpt-4o-mini")

embed_model = OpenAIEmbedding(
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    api_base="https://api.zhizengzeng.com/v1",
    model="text-embedding-3-small"
)

# 打开pdf并解析表格
with pdfplumber.open(pdf_path) as pdf:
    tables = []
    for page in pdf.pages:
        table = page.extract_tables()
        if table:
            tables.append(table)


# 转含所有表格为DataFrame 并构建文档

documents:List[Document] = []
if tables:
    # 遍历所有表格
    for i , table in enumerate(tables,1):
        
        # 将表格转换为DataFrame
        df = pd.DataFrame(table)
        # DataFrame转换为文本
        text = df.to_string()

        # 创建document对象
        doc = Document(text=text, metadata={"source": f"表格{i}"})

        documents.append(doc)


# 构建索引
index = VectorStoreIndex.from_documents(documents,embed_model=embed_model)

# 创建查询引擎
query_engine = index.as_query_engine(llm=llm)

# 示例问答
questions = [
    "2022年谁是最富有的人?",
    "最年轻的富豪是谁?"
]

print("\n===== 问答演示 =====")
for question in questions:
    response = query_engine.query(question)
    print(f"\n问题: {question}")
    print(f"回答: {response}")