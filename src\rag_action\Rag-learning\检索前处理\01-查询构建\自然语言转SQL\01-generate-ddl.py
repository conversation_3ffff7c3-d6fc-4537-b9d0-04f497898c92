import os   
from pymysql import cursors
import yaml
import pymysql

host = os.getenv("host","localhost")
port = int(os.getenv("port",3306))
user = os.getenv("user","root")
password = os.getenv("password","root")
database = os.getenv("database","sakila")

conn = pymysql.connect(
    host=host,port=port,user=user,password=password,
    database=database,cursorclass=cursors.Cursor)

ddl_map = {}
try:
    with conn.cursor() as cursor:
        cursor.execute(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = %s",
            (database,)
        )
        tables = [row[0] for row in cursor.fetchall()]

        for table in tables:
            cursor.execute(
                f"SHOW CREATE TABLE `{database}`.`{table}`;"
            )
            result = cursor.fetchone()
            ddl_map[table] = result[1]

finally:
    conn.close()


with open("ddl_map.yaml","w") as f:
    yaml.dump(ddl_map,f, sort_keys=True,allow_unicode=True)
    print("DDL map generated successfully")



