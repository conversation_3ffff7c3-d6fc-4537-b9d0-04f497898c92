from langchain_openai.llms import OpenAI
from pdf2image import convert_from_path
import base64
import os
from langchain_openai import ChatOpenAI
from pydantic import SecretStr
from langchain_core.messages import HumanMessage
from PIL import Image
import io

client = ChatOpenAI(
    api_key=SecretStr("sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"),
    base_url="https://api.zhizengzeng.com/v1",
    model="gpt-4o-mini"
)

file_path = "C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/黑神话悟空.pdf"

images = convert_from_path(file_path)

for i, image in enumerate(images):
    # 将图像转换为 JPEG 格式
    img_buffer = io.BytesIO()
    image.save(img_buffer, format='JPEG')
    img_buffer.seek(0)
    
    # 转换为 base64
    image_base64 = base64.b64encode(img_buffer.getvalue()).decode("utf-8")
    
    # 创建包含图像的消息
    message = HumanMessage(
        content=[
            {
                "type": "text",
                "text": "请详细描述这张PPT幻灯片的内容，包括标题、正文和图片内容。"
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{image_base64}"
                }
            }
        ]
    )
    
    response = client.invoke([message])
    print(f"第 {i+1} 页:")
    print(response.content)
    print("-" * 50)




