"""
混合检索服务
结合BM25稀疏检索和向量密集检索
"""
import logging
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

# 尝试导入BM25
try:
    from rank_bm25 import BM25Okapi
    BM25_AVAILABLE = True
except ImportError:
    BM25_AVAILABLE = False
    logger.warning("BM25不可用，请安装: pip install rank-bm25")

# 尝试导入jieba用于中文分词
try:
    import jieba
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False
    logger.warning("jieba不可用，请安装: pip install jieba")


class EnsembleRetriever:
    """混合检索器，支持传统BM25和Milvus BM25"""

    def __init__(self, vector_service, embedding_service, config: Dict[str, Any]):
        self.vector_service = vector_service
        self.embedding_service = embedding_service
        self.config = config

        # 检查是否使用Milvus BM25
        self.use_milvus_bm25 = config.get("use_milvus_bm25", True)
        self.enable_bm25 = getattr(vector_service, 'enable_bm25', False)

        if self.use_milvus_bm25 and self.enable_bm25:
            # 使用Milvus BM25
            from .milvus_bm25_retriever import MilvusBM25EnsembleRetriever
            self.milvus_retriever = MilvusBM25EnsembleRetriever(vector_service, embedding_service, config)
            logger.info("使用Milvus 2.5原生BM25混合检索")
        else:
            # 使用传统内存BM25
            self.milvus_retriever = None
            self._init_traditional_bm25(config)
            logger.info("使用传统内存BM25混合检索")

    def _init_traditional_bm25(self, config):
        """初始化传统BM25配置"""
        # 检索器权重
        self.vector_weight = config.get("vector", {}).get("weight", 0.7)
        self.bm25_weight = config.get("bm25", {}).get("weight", 0.3)

        # 各检索器的top_k
        self.vector_top_k = config.get("vector", {}).get("top_k", 10)
        self.bm25_top_k = config.get("bm25", {}).get("top_k", 10)
        self.final_top_k = config.get("final_top_k", 5)

        # BM25相关配置
        self.bm25_language = config.get("bm25", {}).get("language", "chinese")

        # BM25索引（内存中维护）
        self.bm25_index = None
        self.bm25_documents = []
        self.bm25_doc_ids = []
        
        logger.info(f"混合检索器初始化: 向量权重={self.vector_weight}, BM25权重={self.bm25_weight}")
    
    def build_bm25_index(self, db: Session):
        """构建BM25索引"""
        if not BM25_AVAILABLE or not JIEBA_AVAILABLE:
            logger.warning("BM25或jieba不可用，跳过BM25索引构建")
            return
        
        try:
            # 从数据库获取所有文档
            from ..models.database import NoteChunk
            chunks = db.query(NoteChunk).all()
            
            if not chunks:
                logger.info("没有文档可用于构建BM25索引")
                return
            
            # 准备文档和ID
            documents = []
            doc_ids = []
            
            for chunk in chunks:
                # 中文分词
                tokens = list(jieba.cut(chunk.content))
                documents.append(tokens)
                doc_ids.append(chunk.id)
            
            # 构建BM25索引
            self.bm25_index = BM25Okapi(documents)
            self.bm25_documents = documents
            self.bm25_doc_ids = doc_ids
            
            logger.info(f"BM25索引构建完成，包含 {len(documents)} 个文档")
            
        except Exception as e:
            logger.error(f"构建BM25索引失败: {e}")
    
    def search_bm25(self, query: str, top_k: int = None) -> List[Tuple[int, float]]:
        """BM25检索"""
        if not self.bm25_index or not BM25_AVAILABLE or not JIEBA_AVAILABLE:
            return []
        
        if top_k is None:
            top_k = self.bm25_top_k
        
        try:
            # 查询分词
            query_tokens = list(jieba.cut(query))
            
            # BM25检索
            scores = self.bm25_index.get_scores(query_tokens)
            
            # 获取top_k结果
            top_indices = np.argsort(scores)[::-1][:top_k]
            
            results = []
            for idx in top_indices:
                if scores[idx] > 0:  # 只返回有相关性的结果
                    doc_id = self.bm25_doc_ids[idx]
                    score = float(scores[idx])
                    results.append((doc_id, score))
            
            logger.debug(f"BM25检索返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"BM25检索失败: {e}")
            return []
    
    def search_vector(self, query: str, top_k: int = None) -> List[Tuple[int, float]]:
        """向量检索"""
        if top_k is None:
            top_k = self.vector_top_k

        try:
            # 生成查询向量
            query_embedding = self.embedding_service.embed_text(query)

            # 向量检索 - 使用同步方式调用异步方法
            import asyncio
            try:
                # 尝试获取当前事件循环
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果在异步上下文中，创建新的任务
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, self.vector_service.search(query_embedding, top_k))
                        results = future.result()
                else:
                    # 如果不在异步上下文中，直接运行
                    results = asyncio.run(self.vector_service.search(query_embedding, top_k))
            except RuntimeError:
                # 如果没有事件循环，创建新的
                results = asyncio.run(self.vector_service.search(query_embedding, top_k))

            # 转换为统一格式 (doc_id, score)
            vector_results = []
            for result in results:
                doc_id = result.get("vector_id") or result.get("id")
                score = result.get("similarity_score") or result.get("score", 0.0)
                if doc_id is not None:
                    vector_results.append((doc_id, score))

            logger.debug(f"向量检索返回 {len(vector_results)} 个结果")
            return vector_results

        except Exception as e:
            logger.error(f"向量检索失败: {e}")
            return []
    
    def ensemble_search(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """混合检索"""
        if top_k is None:
            top_k = self.final_top_k
        
        # 分别进行BM25和向量检索
        bm25_results = self.search_bm25(query)
        vector_results = self.search_vector(query)
        
        # 合并结果
        combined_scores = {}
        
        # 处理BM25结果
        if bm25_results:
            # 归一化BM25分数
            bm25_scores = [score for _, score in bm25_results]
            if bm25_scores:
                max_bm25_score = max(bm25_scores)
                for doc_id, score in bm25_results:
                    normalized_score = score / max_bm25_score if max_bm25_score > 0 else 0
                    combined_scores[doc_id] = combined_scores.get(doc_id, 0) + normalized_score * self.bm25_weight
        
        # 处理向量结果
        if vector_results:
            # 向量相似度通常已经在[0,1]范围内
            for doc_id, score in vector_results:
                combined_scores[doc_id] = combined_scores.get(doc_id, 0) + score * self.vector_weight
        
        # 按分数排序
        sorted_results = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 获取top_k结果的详细信息
        final_results = []
        for doc_id, score in sorted_results[:top_k]:
            try:
                # 从数据库获取文档详情
                from ..models.database import NoteChunk, Note
                from ..core.database import get_db
                
                db = next(get_db())
                chunk = db.query(NoteChunk).filter(NoteChunk.id == doc_id).first()
                if chunk:
                    note = db.query(Note).filter(Note.id == chunk.note_id).first()
                    final_results.append({
                        "id": doc_id,
                        "content": chunk.content,
                        "score": score,
                        "page_number": chunk.page_number,
                        "note_id": chunk.note_id,
                        "note_title": note.title if note else "",
                        "source": "ensemble"
                    })
                db.close()
                
            except Exception as e:
                logger.error(f"获取文档详情失败 (doc_id={doc_id}): {e}")
                continue
        
        logger.info(f"混合检索返回 {len(final_results)} 个结果")
        return final_results

    async def ensemble_search_unified(self, query: str, top_k: int = None, note_ids: List[int] = None) -> List[Dict[str, Any]]:
        """统一的混合检索接口"""
        if self.milvus_retriever:
            # 使用Milvus BM25混合检索
            return await self.milvus_retriever.ensemble_search(query, top_k, note_ids)
        else:
            # 使用传统混合检索
            if top_k is None:
                top_k = self.final_top_k
            return self.ensemble_search(query, top_k, note_ids)

    def refresh_bm25_index(self, db: Session):
        """刷新BM25索引（当有新文档添加时调用）"""
        if self.milvus_retriever:
            logger.info("使用Milvus BM25，无需刷新内存索引")
        else:
            logger.info("刷新传统BM25索引")
            self.build_bm25_index(db)


def get_ensemble_retriever(vector_service, embedding_service, config: Dict[str, Any]) -> EnsembleRetriever:
    """获取混合检索器实例"""
    return EnsembleRetriever(vector_service, embedding_service, config)
