# 导入所需的库
from langchain.chains.query_constructor.schema import AttributeInfo
from langchain.retrievers.self_query.base import SelfQueryRetriever
from langchain_community.vectorstores import Chroma
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field
from langchain_core.documents import Document

# 定义图书元数据模型
class BookMetadata(BaseModel):
    """图书元数据模型，定义了需要提取的图书属性"""
    isbn: str = Field(description="图书ISBN编号")
    title: str = Field(description="图书标题")
    author: str = Field(description="作者")
    publish_date: str = Field(description="出版日期")
    page_count: int = Field(description="页数")
    publisher: str = Field(description="出版社")
    category: str = Field(description="类别")
    rating: float = Field(description="评分")

# 构造图书数据（模拟数据，数量较多）
books_data = [
    {
        "isbn": "9787302423287",
        "title": "深入理解计算机系统",
        "author": "<PERSON><PERSON>, <PERSON>",
        "publish_date": "2016-01-01",
        "page_count": 1120,
        "publisher": "机械工业出版社",
        "category": "计算机科学",
        "rating": 9.5
    },
    {
        "isbn": "9787111128069",
        "title": "算法导论",
        "author": "<PERSON> H. Cormen 等",
        "publish_date": "2006-07-01",
        "page_count": 688,
        "publisher": "机械工业出版社",
        "category": "计算机科学",
        "rating": 9.2
    },
    {
        "isbn": "9787115428028",
        "title": "Python编程：从入门到实践",
        "author": "Eric Matthes",
        "publish_date": "2019-05-01",
        "page_count": 480,
        "publisher": "人民邮电出版社",
        "category": "编程",
        "rating": 8.8
    },
    {
        "isbn": "9787302297314",
        "title": "人工智能：一种现代的方法",
        "author": "Stuart Russell, Peter Norvig",
        "publish_date": "2012-08-01",
        "page_count": 1132,
        "publisher": "机械工业出版社",
        "category": "人工智能",
        "rating": 9.3
    },
    {
        "isbn": "9787115473820",
        "title": "深度学习",
        "author": "Ian Goodfellow, Yoshua Bengio, Aaron Courville",
        "publish_date": "2017-11-01",
        "page_count": 800,
        "publisher": "人民邮电出版社",
        "category": "人工智能",
        "rating": 9.0
    },
    {
        "isbn": "9787111213826",
        "title": "C程序设计语言",
        "author": "Brian W. Kernighan, Dennis M. Ritchie",
        "publish_date": "2004-01-01",
        "page_count": 272,
        "publisher": "机械工业出版社",
        "category": "编程",
        "rating": 8.7
    },
    {
        "isbn": "9787111128069",
        "title": "操作系统概念",
        "author": "Abraham Silberschatz, Peter B. Galvin, Greg Gagne",
        "publish_date": "2014-06-01",
        "page_count": 944,
        "publisher": "机械工业出版社",
        "category": "计算机科学",
        "rating": 8.9
    },
    {
        "isbn": "9787115428028",
        "title": "数据结构与算法分析",
        "author": "Mark Allen Weiss",
        "publish_date": "2011-09-01",
        "page_count": 624,
        "publisher": "机械工业出版社",
        "category": "计算机科学",
        "rating": 8.6
    },
    {
        "isbn": "9787115473820",
        "title": "机器学习",
        "author": "周志华",
        "publish_date": "2016-01-01",
        "page_count": 400,
        "publisher": "清华大学出版社",
        "category": "人工智能",
        "rating": 9.1
    },
    {
        "isbn": "9787111213826",
        "title": "代码大全",
        "author": "Steve McConnell",
        "publish_date": "2014-10-01",
        "page_count": 900,
        "publisher": "电子工业出版社",
        "category": "软件工程",
        "rating": 9.0
    },
    {
        "isbn": "9787111128069",
        "title": "编译原理",
        "author": "Alfred V. Aho, Monica S. Lam, Ravi Sethi, Jeffrey D. Ullman",
        "publish_date": "2007-08-01",
        "page_count": 912,
        "publisher": "机械工业出版社",
        "category": "计算机科学",
        "rating": 8.5
    },
    {
        "isbn": "9787115428028",
        "title": "计算机网络：自顶向下方法",
        "author": "James F. Kurose, Keith W. Ross",
        "publish_date": "2017-03-01",
        "page_count": 672,
        "publisher": "机械工业出版社",
        "category": "计算机科学",
        "rating": 8.8
    },
    {
        "isbn": "9787115473820",
        "title": "深入理解Linux内核",
        "author": "Daniel P. Bovet, Marco Cesati",
        "publish_date": "2010-01-01",
        "page_count": 944,
        "publisher": "机械工业出版社",
        "category": "操作系统",
        "rating": 8.7
    },
    {
        "isbn": "9787111213826",
        "title": "UNIX环境高级编程",
        "author": "W. Richard Stevens",
        "publish_date": "2013-06-01",
        "page_count": 744,
        "publisher": "人民邮电出版社",
        "category": "操作系统",
        "rating": 8.9
    },
    {
        "isbn": "9787111128069",
        "title": "TCP/IP详解 卷1",
        "author": "W. Richard Stevens",
        "publish_date": "2006-01-01",
        "page_count": 672,
        "publisher": "机械工业出版社",
        "category": "网络",
        "rating": 8.8
    },
    {
        "isbn": "9787115428028",
        "title": "设计模式：可复用面向对象软件的基础",
        "author": "Erich Gamma, Richard Helm, Ralph Johnson, John Vlissides",
        "publish_date": "2007-09-01",
        "page_count": 395,
        "publisher": "机械工业出版社",
        "category": "软件工程",
        "rating": 9.1
    },
    {
        "isbn": "9787115473820",
        "title": "人月神话",
        "author": "Frederick P. Brooks Jr.",
        "publish_date": "2010-01-01",
        "page_count": 320,
        "publisher": "电子工业出版社",
        "category": "软件工程",
        "rating": 8.6
    },
    {
        "isbn": "9787111213826",
        "title": "黑客与画家",
        "author": "Paul Graham",
        "publish_date": "2011-01-01",
        "page_count": 264,
        "publisher": "人民邮电出版社",
        "category": "随笔",
        "rating": 8.9
    },
    {
        "isbn": "9787111128069",
        "title": "数学之美",
        "author": "吴军",
        "publish_date": "2014-01-01",
        "page_count": 320,
        "publisher": "人民邮电出版社",
        "category": "科普",
        "rating": 9.0
    },
    {
        "isbn": "9787115428028",
        "title": "浪潮之巅",
        "author": "吴军",
        "publish_date": "2013-01-01",
        "page_count": 400,
        "publisher": "电子工业出版社",
        "category": "科普",
        "rating": 8.7
    },
]

# 构造Document对象列表
documents = []
for book in books_data:
    content = f"{book['title']}，作者：{book['author']}，出版社：{book['publisher']}，类别：{book['category']}，评分：{book['rating']}"
    documents.append(
        Document(
            page_content=content,
            metadata=book
        )
    )

# 创建向量存储
embed_model = HuggingFaceEmbeddings(model_name="BAAI/bge-small-zh")
vectorstore = Chroma.from_documents(documents, embed_model)

# 配置检索器的元数据字段
metadata_field_info = [
    AttributeInfo(
        name="title",
        description="图书标题（字符串）",
        type="string", 
    ),
    AttributeInfo(
        name="author",
        description="图书作者（字符串）",
        type="string",
    ),
    AttributeInfo(
        name="publish_date",
        description="图书出版日期，格式为YYYY-MM-DD的字符串",
        type="string",
    ),
    AttributeInfo(
        name="page_count",
        description="图书页数（整数）",
        type="integer"
    ),
    AttributeInfo(
        name="publisher",
        description="出版社（字符串）",
        type="string"
    ),
    AttributeInfo(
        name="category",
        description="图书类别（字符串）",
        type="string"
    ),
    AttributeInfo(
        name="rating",
        description="图书评分（浮点数）",
        type="float"
    ),
]

# 子查询检索器（SelfQueryRetriever）的作用是：让大模型能够自动理解用户的自然语言查询，并将其转化为结构化的检索条件（如过滤、排序、范围等），从而在向量数据库中高效地检索出符合条件的文档。
# 
# 原理如下：
# 1. 用户输入自然语言查询（如“找出评分高于9.0的图书”）。
# 2. SelfQueryRetriever 利用大模型（如ChatOpenAI）对查询进行解析，将其转化为结构化的检索指令（如：过滤条件 rating > 9.0）。
# 3. 检索器根据解析结果，自动构建向量检索和元数据过滤的复合查询，向向量数据库（如Chroma）发起检索。
# 4. 返回满足条件的文档，并可支持排序、分页、字段筛选等复杂检索需求。
# 
# 这样，用户无需了解底层数据库的查询语法，只需用自然语言描述需求，系统即可自动完成复杂的检索操作。

# ChatOpenAI 和 OpenAI 都是 langchain_openai 包中的类，用于与 OpenAI 的大模型 API 交互，但有以下区别：
# 1. ChatOpenAI 用于对话模型（如 gpt-3.5-turbo、gpt-4、gpt-4o 等），输入输出为消息格式，适合多轮对话和结构化消息。
# 2. OpenAI 用于传统的 completion（补全）模型（如 text-davinci-003、text-curie-001 等），输入输出为纯文本，适合单轮文本生成。
# 3. ChatOpenAI 支持 role-based message（如 system、user、assistant），OpenAI 只支持 prompt。
# 4. 两者的参数和用法略有不同，ChatOpenAI 更适合现代对话式 LLM，OpenAI 适合老的 completion API。
# 例如：
# llm = ChatOpenAI(model="gpt-4o", ...)
# llm = OpenAI(model="text-davinci-003", ...)

llm = ChatOpenAI(
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",  # API密钥
    base_url="https://api.zhizengzeng.com/v1",                      # API基础地址
    model="gpt-4o-mini",                                            # 使用的模型名称
    temperature=0                                                   # 确定性输出
)

# SelfQueryRetriever 的原理如下：
# 1. 用户输入自然语言查询（如“找出评分高于9.0的图书”）。
# 2. SelfQueryRetriever 利用大模型（如 ChatOpenAI）对查询进行语义解析，将其自动转化为结构化的检索条件（如过滤、排序、范围等）。
# 3. 检索器根据解析结果，自动构建向量检索和元数据过滤的复合查询，向向量数据库（如 Chroma）发起检索。
# 4. 返回满足条件的文档，并可支持排序、分页、字段筛选等复杂检索需求。
# 这样，用户无需了解底层数据库的查询语法，只需用自然语言描述需求，系统即可自动完成复杂的检索操作。
retriever = SelfQueryRetriever.from_llm(
    llm=llm,
    vectorstore=vectorstore,
    document_contents="包含图书标题、作者、出版日期、页数、出版社、类别、评分等信息的图书元数据",
    metadata_field_info=metadata_field_info,
    enable_limit=True,
    verbose=True
)

# 执行示例查询
queries = [
    "找出评分高于9.0的图书",
    "显示出版日期最晚的三本书",
    "查找类别为人工智能的图书",
    "找出页数超过800页的图书"
]

# 执行查询并输出结果
for query in queries:
    print(f"\n查询：{query}")
    try:
        results = retriever.invoke(query)
        if not results:
            print("未找到匹配的图书")
            continue            
        for doc in results:
            print(f"标题：{doc.metadata['title']}")
            print(f"作者：{doc.metadata['author']}")
            print(f"出版日期：{doc.metadata['publish_date']}")
            print(f"类别：{doc.metadata['category']}")
            print(f"评分：{doc.metadata['rating']}")
            print(f"页数：{doc.metadata['page_count']}")
            print(f"出版社：{doc.metadata['publisher']}")
            print("-" * 40)
    except Exception as e:
        print(f"查询出错：{str(e)}")
        continue
