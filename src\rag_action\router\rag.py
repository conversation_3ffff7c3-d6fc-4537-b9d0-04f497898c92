"""
AI笔记系统 - RAG路由模块
提供PDF上传、笔记管理、语义搜索等核心API接口
"""
import os
from typing import List, Optional
from fastapi import APIRouter, File, Form, HTTPException, UploadFile, Query, Depends
import logging
from datetime import datetime

from rag_action.core.config import get_settings
from rag_action.core.service_manager import service_manager
from rag_action.service.note_service import NoteService
from rag_action.service.rag_service_adapter import RAGService
from rag_action.service.database_query_service import get_database_query_service
from rag_action.service.async_pdf_processor import async_pdf_processor
from rag_action.service.conversation_service import get_conversation_manager, ConversationManager
from rag_action.models.conversation import ConversationRequest, ConversationResponse, MessageRole
from rag_action.models.schemas import (
    NoteResponse,
    NoteCreate,
    NotesListResponse,
    TagResponse
)

router = APIRouter(prefix="/api", tags=["AI笔记系统"])
logger = logging.getLogger(__name__)

# 依赖注入
def get_note_service() -> NoteService:
    """获取笔记服务实例"""
    # 使用服务管理器获取单例服务，如果不存在则创建
    note_service = service_manager.get_service("note_service")
    if note_service is None:
        note_service = NoteService()
        service_manager.register_service("note_service", note_service)
    return note_service

def get_rag_service() -> RAGService:
    """
    获取RAG服务实例

    修复：使用正确的RAGService类（来自rag_service_adapter.py）
    - 该类是基于LangChain LCEL的现代化RAG服务适配器
    - 内部包装了LangChainRAGService，提供完整的RAG功能
    - 支持智能问答、流式输出、多轮对话等企业级特性
    """
    # 使用服务管理器获取单例服务，如果不存在则创建
    rag_service = service_manager.get_service("rag_service")
    if rag_service is None:
        try:
            rag_service = RAGService()
            service_manager.register_service("rag_service", rag_service)
            logger.info("✅ RAG服务实例创建成功")
        except Exception as e:
            logger.error(f"❌ RAG服务实例创建失败: {e}")
            raise
    return rag_service

def get_conversation_service() -> ConversationManager:
    """获取会话管理器实例"""
    return get_conversation_manager()

def get_db_query_service():
    """获取数据库查询服务实例"""
    # 使用服务管理器获取单例服务，如果不存在则创建
    db_service = service_manager.get_service("database_query_service")
    if db_service is None:
        db_service = get_database_query_service()
        service_manager.register_service("database_query_service", db_service)
    return db_service

# PDF服务暂时移除，使用NoteService处理PDF上传


@router.post("/upload/pdf", response_model=NoteResponse, summary="上传PDF文档")
async def upload_pdf(
    file: UploadFile = File(..., description="PDF文件"),
    tags: Optional[str] = Form(None, description="标签，多个标签用逗号分隔"),
    note_service: NoteService = Depends(get_note_service)
):
    """
    上传PDF文档，自动提取文本并转换为Markdown格式的笔记
    - 支持PDF文档解析
    - 自动生成向量嵌入
    - 存储到MySQL和Milvus
    """
    try:
        # 验证文件类型
        if not file.filename or not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="只支持PDF文件")

        # 处理标签
        tag_list = []
        if tags:
            tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]

        # 处理PDF文件
        note = await note_service.create_note_from_pdf(file, tag_list)
        return note

    except Exception as e:
        logger.error(f"PDF上传处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")


@router.post("/upload/pdf/async", summary="异步上传PDF文档")
async def upload_pdf_async(
    file: UploadFile = File(..., description="PDF文件"),
    tags: Optional[str] = Form(None, description="标签，多个标签用逗号分隔")
):
    """
    异步上传PDF文档，立即返回任务ID
    - 支持大文件处理（>5MB）
    - 立即返回任务ID，不等待处理完成
    - 可通过任务ID查询处理状态和进度
    """
    try:
        # 验证文件类型
        if not file.filename or not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="只支持PDF文件")

        # 验证文件大小（可选，这里设置最大100MB）
        max_size = 100 * 1024 * 1024  # 100MB
        file_content = await file.read()
        if len(file_content) > max_size:
            raise HTTPException(status_code=400, detail="文件大小超过限制（100MB）")

        # 保存临时文件
        settings = get_settings()
        temp_dir = settings.file.temp_dir
        os.makedirs(temp_dir, exist_ok=True)

        temp_file_path = os.path.join(temp_dir, f"async_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file.filename}")

        with open(temp_file_path, 'wb') as f:
            f.write(file_content)

        # 创建异步处理任务
        task_id = async_pdf_processor.create_task(temp_file_path, file.filename)

        # 提交任务到处理队列
        if not async_pdf_processor.submit_task(task_id):
            # 如果提交失败，清理临时文件
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
            raise HTTPException(status_code=500, detail="任务提交失败")

        return {
            "task_id": task_id,
            "status": "processing",
            "message": "PDF文件已提交处理，请使用task_id查询处理状态",
            "file_name": file.filename,
            "file_size": len(file_content),
            "estimated_time": "1-5分钟"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"异步PDF上传失败: {e}")
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")


@router.get("/task/{task_id}/status", summary="查询任务状态")
async def get_task_status(task_id: str):
    """
    查询异步任务的处理状态和进度
    - 返回任务状态（pending/processing/completed/failed/cancelled）
    - 返回处理进度（0-100%）
    - 返回详细的处理信息
    """
    try:
        task_status = async_pdf_processor.get_task_status(task_id)

        if task_status is None:
            raise HTTPException(status_code=404, detail="任务不存在")

        return task_status

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")


@router.get("/tasks", summary="获取所有任务状态")
async def get_all_tasks():
    """
    获取所有异步任务的状态列表
    - 返回所有任务的概要信息
    - 用于管理和监控任务队列
    """
    try:
        tasks = async_pdf_processor.get_all_tasks()
        return {
            "total_tasks": len(tasks),
            "tasks": tasks
        }

    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@router.delete("/task/{task_id}", summary="取消任务")
async def cancel_task(task_id: str):
    """
    取消指定的异步任务
    - 只能取消pending或processing状态的任务
    - 已完成或失败的任务无法取消
    """
    try:
        success = async_pdf_processor.cancel_task(task_id)

        if not success:
            raise HTTPException(status_code=400, detail="任务无法取消（不存在或已完成）")

        return {
            "task_id": task_id,
            "status": "cancelled",
            "message": "任务已取消"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消失败: {str(e)}")


@router.post("/note", response_model=NoteResponse, summary="创建Markdown笔记")
async def create_note(
    note_data: NoteCreate,
    note_service: NoteService = Depends(get_note_service)
):
    """
    手动创建Markdown格式的笔记
    - 支持自定义标题和内容
    - 自动生成向量嵌入
    - 支持标签分类
    """
    try:
        note = await note_service.create_note(note_data)
        return note
    except Exception as e:
        logger.error(f"笔记创建失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建失败: {str(e)}")


@router.get("/notes", response_model=NotesListResponse, summary="获取笔记列表")
async def get_notes(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    tag: Optional[str] = Query(None, description="按标签筛选"),
    note_service: NoteService = Depends(get_note_service)
):
    """
    获取所有笔记的元信息列表
    - 支持分页查询
    - 支持按标签筛选
    - 返回笔记基本信息
    """
    try:
        notes = await note_service.get_notes(page=page, page_size=page_size, tag=tag)
        return notes
    except Exception as e:
        logger.error(f"获取笔记列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@router.get("/note/{note_id}", response_model=NoteResponse, summary="获取笔记详情")
async def get_note(
    note_id: int,
    note_service: NoteService = Depends(get_note_service)
):
    """
    获取指定笔记的完整内容
    - 返回笔记全文
    - 包含所有元信息
    """
    try:
        note = await note_service.get_note_by_id(note_id)
        if not note:
            raise HTTPException(status_code=404, detail="笔记不存在")
        return note
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取笔记详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")



@router.get("/tags", response_model=List[TagResponse], summary="获取标签列表")
async def get_tags(
    note_service: NoteService = Depends(get_note_service)
):
    """
    获取所有已有标签列表
    - 返回标签名称和使用次数
    - 按使用频率排序
    """
    try:
        tags = await note_service.get_all_tags()
        return tags
    except Exception as e:
        logger.error(f"获取标签列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


# PDF预览功能暂时移除

@router.post("/upload/markdown", response_model=NoteResponse, summary="上传Markdown文件并入库")
async def upload_markdown(
    file: UploadFile = File(..., description="Markdown文件"),
    title: str = Form(..., description="笔记标题"),
    tags: Optional[str] = Form(None, description="标签，多个标签用逗号分隔"),
    original_filename: Optional[str] = Form(None, description="原始PDF文件名"),
    note_service: NoteService = Depends(get_note_service)
):
    """
    上传Markdown文件，进行文档加载、切块、向量化并入库
    - 支持Markdown文件上传
    - 自动生成向量嵌入
    - 存储到MySQL和Milvus
    """
    try:
        # 验证文件类型
        if not file.filename or not file.filename.lower().endswith('.md'):
            raise HTTPException(status_code=400, detail="只支持Markdown文件")

        # 读取Markdown内容
        content = await file.read()
        markdown_content = content.decode('utf-8')

        # 处理标签
        tag_list = []
        if tags:
            tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]

        # 创建笔记
        note_data = NoteCreate(
            title=title,
            content=markdown_content,
            tags=tag_list
        )
        
        note = await note_service.create_note(note_data)
        
        # 更新源文件信息
        if original_filename:
            # 这里可以更新note的source_file字段
            pass
            
        return note

    except Exception as e:
        logger.error(f"Markdown上传处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")


# ==================== 数据库查询API ====================


@router.post("/query/database", summary="数据库查询", description="执行自然语言数据库查询")
async def database_query(
    question: str = Query(..., description="自然语言问题"),
    db_service = Depends(get_db_query_service)
):
    """
    数据库查询

    执行自然语言数据库查询，返回结构化结果
    """
    try:
        result = db_service.execute_natural_language_query(question)
        return {
            "question": question,
            "success": result.get("success", False),
            "sql": result.get("sql", ""),
            "explanation": result.get("explanation", ""),
            "data": result.get("data", []),
            "columns": result.get("columns", []),
            "row_count": result.get("row_count", 0),
            "execution_time": result.get("execution_time", 0),
            "summary": result.get("summary", ""),
            "error": result.get("error", ""),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"数据库查询失败: {e}")
        raise HTTPException(status_code=500, detail=f"数据库查询失败: {str(e)}")


@router.get("/schema/tables", summary="获取表结构", description="获取数据库表结构信息")
async def get_table_schema(
    table_name: Optional[str] = Query(None, description="表名（可选，不指定则返回所有表）"),
    db_service = Depends(get_db_query_service)
):
    """
    获取数据库表结构信息
    """
    try:
        schema_info = db_service.get_table_info(table_name)
        return {
            "table_name": table_name,
            "schema": schema_info,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取表结构失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取表结构失败: {str(e)}")


@router.get("/examples/sql", summary="获取SQL示例", description="获取Text-to-SQL的示例查询")
async def get_sql_examples(
    db_service = Depends(get_db_query_service)
):
    """
    获取SQL查询示例
    """
    try:
        examples = db_service.get_sample_queries()
        return {
            "examples": examples,
            "count": len(examples),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取SQL示例失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取SQL示例失败: {str(e)}")



@router.post("/query/intelligent/stream", summary="流式智能问答", description="企业级流式智能问答接口，支持复杂参数和多轮对话")
async def intelligent_query_stream(
    request: ConversationRequest,
    rag_service: RAGService = Depends(get_rag_service),
    conversation_manager: ConversationManager = Depends(get_conversation_service)
):
    """
    企业级流式智能问答接口

    使用Server-Sent Events (SSE) 提供实时的流式响应，
    基于LangChain的真实流式输出，让用户能够看到AI回答的实时生成过程。

    支持的功能：
    - 多轮对话管理
    - 复杂查询参数
    - 实时流式输出
    - 来源文档追踪
    - 性能监控

    请求参数：
    - query: 用户查询文本
    - conversation_id: 会话ID（可选，为空时创建新会话）
    - top_k: 检索文档数量（默认5）
    - note_ids: 限制搜索的笔记ID列表（可选）
    - stream: 是否使用流式输出（默认false）

    响应格式：
    - data: {"type": "retrieval_start", "content": "正在搜索相关知识..."}
    - data: {"type": "retrieval_complete", "content": "找到 3 个相关文档", "sources_count": 3}
    - data: {"type": "generation_start", "content": "正在生成回答..."}
    - data: {"type": "answer_chunk", "content": "实时文本块"}
    - data: {"type": "answer_complete", "content": "完整答案", "sources": [...], "finished": true}
    """
    from fastapi.responses import StreamingResponse
    import json

    async def generate_stream():
        try:
            # 1. 会话管理
            current_conversation_id = request.conversation_id
            if current_conversation_id is None:
                # 创建新会话
                current_conversation_id = conversation_manager.create_conversation(request.query)
            else:
                # 验证会话是否存在
                if conversation_manager.get_conversation(current_conversation_id) is None:
                    yield f"data: {json.dumps({'type': 'error', 'message': f'会话不存在: {current_conversation_id}'}, ensure_ascii=False)}\n\n"
                    return

            # 2. 添加用户消息到会话
            user_message_id = conversation_manager.add_message(
                current_conversation_id, MessageRole.USER, request.query
            )

            # 3. 获取会话上下文
            conversation_context = conversation_manager.get_conversation_context(current_conversation_id)

            # 4. 执行真实的流式智能问答（修复：使用智能路由）
            full_answer = ""
            sources_data = []
            assistant_message_id = None

            # 使用请求中的参数
            top_k = getattr(request, 'top_k', 5)
            note_ids = getattr(request, 'note_ids', None)

            # 修复：使用智能路由的流式问答，而不是直接调用answer_question_stream
            async for chunk in rag_service.answer_question_stream(
                request.query,
                top_k=top_k,
                note_ids=note_ids,
                conversation_context=conversation_context
            ):
                chunk_type = chunk.get("type", "unknown")
                content = chunk.get("content", "")
                finished = chunk.get("finished", False)

                # 发送流式数据块
                stream_data = {
                    "type": chunk_type,
                    "content": content,
                    "finished": finished
                }

                # 添加额外的元数据
                if "sources_count" in chunk:
                    stream_data["sources_count"] = chunk["sources_count"]
                if "processing_time" in chunk:
                    stream_data["processing_time"] = chunk["processing_time"]

                yield f"data: {json.dumps(stream_data, ensure_ascii=False)}\n\n"

                # 如果是答案完成，保存相关信息
                if chunk_type == "answer_complete":
                    full_answer = content
                    sources = chunk.get("sources", [])

                    # 转换sources为可序列化的格式
                    for source in sources:
                        try:
                            if hasattr(source, 'model_dump'):
                                sources_data.append(source.model_dump())
                            elif hasattr(source, 'dict'):
                                sources_data.append(source.dict())
                            elif isinstance(source, dict):
                                sources_data.append(source)
                            else:
                                # 手动构建字典
                                source_dict = {}
                                for attr in ['content', 'note_id', 'note_title', 'chunk_id', 'page_number', 'similarity_score']:
                                    if hasattr(source, attr):
                                        source_dict[attr] = getattr(source, attr)
                                if source_dict:
                                    sources_data.append(source_dict)
                        except Exception as e:
                            logger.warning(f"源文档序列化失败: {e}")
                            sources_data.append({"error": "无法序列化源文档"})

                    # 5. 添加助手回答到会话
                    assistant_message_id = conversation_manager.add_message(
                        current_conversation_id, MessageRole.ASSISTANT, full_answer,
                        metadata={
                            "sources": sources_data,
                            "processing_time": chunk.get("processing_time", 0),
                            "query_type": chunk.get("metadata", {}).get("intent", "unknown")
                        }
                    )

                    # 发送最终的完成信息
                    complete_data = {
                        "type": "session_complete",
                        "conversation_id": current_conversation_id,
                        "message_id": assistant_message_id,
                        "user_message_id": user_message_id,
                        "sources": sources_data,
                        "processing_time": chunk.get("processing_time", 0),
                        "metadata": chunk.get("metadata", {})
                    }
                    yield f"data: {json.dumps(complete_data, ensure_ascii=False)}\n\n"
                    break

        except Exception as e:
            logger.error(f"POST流式智能问答失败: {e}")
            error_data = {
                "type": "error",
                "message": f"查询失败: {str(e)}"
            }
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )


# 会话管理相关接口
@router.get("/conversations", summary="获取会话列表", description="获取用户的会话历史列表")
async def list_conversations(
    limit: int = Query(50, description="返回数量限制"),
    conversation_manager: ConversationManager = Depends(get_conversation_service)
):
    """获取会话列表"""
    try:
        conversations = conversation_manager.list_conversations(limit)
        return {
            "conversations": [conv.model_dump() for conv in conversations],
            "total": len(conversations)
        }
    except Exception as e:
        logger.error(f"获取会话列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取会话列表失败: {str(e)}")


@router.get("/conversations/{conversation_id}", summary="获取会话详情", description="获取指定会话的完整对话历史")
async def get_conversation(
    conversation_id: str,
    conversation_manager: ConversationManager = Depends(get_conversation_service)
):
    """获取会话详情"""
    try:
        conversation = conversation_manager.get_conversation(conversation_id)
        if conversation is None:
            raise HTTPException(status_code=404, detail=f"会话不存在: {conversation_id}")

        return conversation.model_dump()
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取会话详情失败: {str(e)}")


@router.delete("/conversations/{conversation_id}", summary="删除会话", description="删除指定的会话")
async def delete_conversation(
    conversation_id: str,
    conversation_manager: ConversationManager = Depends(get_conversation_service)
):
    """删除会话"""
    try:
        success = conversation_manager.delete_conversation(conversation_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"会话不存在: {conversation_id}")

        return {"message": "会话删除成功", "conversation_id": conversation_id}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除会话失败: {str(e)}")


@router.delete("/conversations", summary="清空所有会话", description="删除所有会话历史")
async def clear_all_conversations(
    conversation_manager: ConversationManager = Depends(get_conversation_service)
):
    """清空所有会话"""
    try:
        count = conversation_manager.clear_all_conversations()
        return {"message": f"已清空 {count} 个会话", "deleted_count": count}
    except Exception as e:
        logger.error(f"清空会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"清空会话失败: {str(e)}")


@router.get("/conversations/stats", summary="获取会话统计", description="获取会话系统的统计信息")
async def get_conversation_stats(
    conversation_manager: ConversationManager = Depends(get_conversation_service)
):
    """获取会话统计"""
    try:
        stats = conversation_manager.get_stats()
        return stats
    except Exception as e:
        logger.error(f"获取会话统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取会话统计失败: {str(e)}")

