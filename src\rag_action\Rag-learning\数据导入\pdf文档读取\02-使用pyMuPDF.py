import pymupdf

file_path = "C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/黑神话悟空.pdf"

doc = pymupdf.open(file_path)

text = [page.get_text() for page in doc]

print(text)

print("===pyMuPDF 基本信息提取 ===")
print(f"文件名: {doc.name}")
print(f"文档页数: {len(doc)}")
print(f"文档标题: {doc.metadata['title']}")


for page_num, page in enumerate(doc):
 # 提取文本
    text = page.get_text()
    print(f"\n--- 第{page_num + 1}页 ---")
    print("文本内容:", text[:200])  # 显示前200个字符
    
    # 提取图片
    images = page.get_images()
    print(f"图片数量: {len(images)}")
    
    # 获取页面链接
    links = page.get_links()
    print(f"链接数量: {len(links)}")
    
    # 获取页面大小
    width, height = page.rect.width, page.rect.height
    print(f"页面尺寸: {width} x {height}")

doc.close()


