<template>
  <div class="upload-page">
    <div class="page-header">
      <h1 class="page-title">文档上传</h1>
      <p class="page-subtitle">上传PDF或Markdown文档，系统将自动解析并建立索引</p>
    </div>

    <!-- 上传组件 -->
    <div class="upload-section">
      <FileUpload />
    </div>

    <!-- 上传统计 -->
    <div class="upload-stats">
      <div class="stats-card">
        <div class="stat-item">
          <div class="stat-value">{{ uploadStore.stats.total }}</div>
          <div class="stat-label">总文件数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value text-success">{{ uploadStore.stats.success }}</div>
          <div class="stat-label">成功上传</div>
        </div>
        <div class="stat-item">
          <div class="stat-value text-warning">{{ uploadStore.stats.pending }}</div>
          <div class="stat-label">等待上传</div>
        </div>
        <div class="stat-item">
          <div class="stat-value text-danger">{{ uploadStore.stats.failed }}</div>
          <div class="stat-label">上传失败</div>
        </div>
      </div>
    </div>

    <!-- 上传指南 -->
    <div class="upload-guide">
      <div class="guide-card">
        <div class="guide-header">
          <el-icon><InfoFilled /></el-icon>
          <h3>上传指南</h3>
        </div>
        
        <div class="guide-content">
          <div class="guide-section">
            <h4>支持的文件格式</h4>
            <ul>
              <li><strong>PDF文档</strong> - 支持文本PDF，图片PDF需要OCR处理</li>
              <li><strong>Markdown文件</strong> - 支持标准Markdown语法</li>
              <li><strong>文本文件</strong> - 纯文本格式文件</li>
            </ul>
          </div>
          
          <div class="guide-section">
            <h4>文件要求</h4>
            <ul>
              <li>单个文件大小不超过 {{ uploadStore.config.maxFileSize }}MB</li>
              <li>文件名建议使用有意义的名称</li>
              <li>避免上传重复内容的文件</li>
            </ul>
          </div>
          
          <div class="guide-section">
            <h4>处理流程</h4>
            <div class="process-steps">
              <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                  <div class="step-title">文件上传</div>
                  <div class="step-desc">将文件上传到服务器</div>
                </div>
              </div>
              <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                  <div class="step-title">内容解析</div>
                  <div class="step-desc">使用AI技术解析文档内容</div>
                </div>
              </div>
              <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                  <div class="step-title">智能分块</div>
                  <div class="step-desc">将内容分割为语义块</div>
                </div>
              </div>
              <div class="step">
                <div class="step-number">4</div>
                <div class="step-content">
                  <div class="step-title">向量化</div>
                  <div class="step-desc">生成向量嵌入并建立索引</div>
                </div>
              </div>
              <div class="step">
                <div class="step-number">5</div>
                <div class="step-content">
                  <div class="step-title">完成</div>
                  <div class="step-desc">文档可用于搜索和问答</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="guide-section">
            <h4>最佳实践</h4>
            <ul>
              <li>为文档添加合适的标签，便于分类管理</li>
              <li>上传前检查文档内容的完整性</li>
              <li>定期清理不需要的文档</li>
              <li>使用批量上传功能提高效率</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近上传 -->
    <div class="recent-uploads">
      <div class="section-header">
        <h3>最近上传</h3>
        <el-button type="text" @click="refreshRecentUploads">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      
      <div class="uploads-list">
        <div
          v-for="note in recentNotes"
          :key="note.id"
          class="upload-item"
          @click="viewNote(note.id)"
        >
          <div class="item-icon">
            <el-icon>
              <Document v-if="note.source_type === 'pdf'" />
              <EditPen v-else-if="note.source_type === 'markdown'" />
              <DocumentCopy v-else />
            </el-icon>
          </div>
          
          <div class="item-content">
            <div class="item-title">{{ note.title }}</div>
            <div class="item-meta">
              <span class="item-type">{{ getSourceTypeLabel(note.source_type) }}</span>
              <span class="item-size" v-if="note.file_size">{{ formatFileSize(note.file_size) }}</span>
              <span class="item-time">{{ timeAgo(note.created_at) }}</span>
            </div>
            <div class="item-tags">
              <el-tag
                v-for="tag in note.tags.slice(0, 3)"
                :key="tag.id"
                size="small"
                type="info"
              >
                {{ tag.name }}
              </el-tag>
              <span v-if="note.tags.length > 3" class="more-tags">
                +{{ note.tags.length - 3 }}
              </span>
            </div>
          </div>
          
          <div class="item-actions">
            <el-button type="text" size="small" @click.stop="viewNote(note.id)">
              查看
            </el-button>
            <el-button type="text" size="small" @click.stop="editNote(note.id)">
              编辑
            </el-button>
          </div>
        </div>
        
        <el-empty v-if="recentNotes.length === 0" description="暂无最近上传的文档" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUploadStore, useNotesStore } from '@/stores'
import { formatFileSize, timeAgo } from '@/utils'
import FileUpload from '@/components/Upload/FileUpload.vue'
import {
  InfoFilled,
  Refresh,
  Document,
  EditPen,
  DocumentCopy
} from '@element-plus/icons-vue'

const router = useRouter()
const uploadStore = useUploadStore()
const notesStore = useNotesStore()

const recentNotes = ref([])
const loading = ref(false)

// 计算属性
const uploadStats = computed(() => uploadStore.stats)

// 方法
const getSourceTypeLabel = (type: string) => {
  const labelMap = {
    pdf: 'PDF文档',
    markdown: 'Markdown',
    manual: '手动创建'
  }
  return labelMap[type] || type
}

const refreshRecentUploads = async () => {
  loading.value = true
  try {
    await notesStore.fetchNotes({ page: 1, page_size: 10 })
    recentNotes.value = notesStore.notes
  } finally {
    loading.value = false
  }
}

const viewNote = (noteId: number) => {
  router.push(`/notes/${noteId}`)
}

const editNote = (noteId: number) => {
  router.push(`/notes/${noteId}/edit`)
}

// 生命周期
onMounted(() => {
  refreshRecentUploads()
})
</script>

<style lang="scss" scoped>
.upload-page {
  padding: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
  
  .page-header {
    margin-bottom: var(--spacing-xl);
    text-align: center;
    
    .page-title {
      font-size: 28px;
      font-weight: var(--font-weight-bold);
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
    }
    
    .page-subtitle {
      color: var(--text-secondary);
      font-size: var(--font-size-lg);
    }
  }
  
  .upload-section {
    margin-bottom: var(--spacing-xl);
  }
  
  .upload-stats {
    margin-bottom: var(--spacing-xl);
    
    .stats-card {
      background: var(--bg-color);
      border-radius: var(--border-radius-base);
      padding: var(--spacing-lg);
      box-shadow: var(--shadow-base);
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: var(--spacing-lg);
      
      .stat-item {
        text-align: center;
        
        .stat-value {
          font-size: 24px;
          font-weight: var(--font-weight-bold);
          margin-bottom: var(--spacing-sm);
        }
        
        .stat-label {
          color: var(--text-secondary);
          font-size: var(--font-size-sm);
        }
      }
    }
  }
  
  .upload-guide {
    margin-bottom: var(--spacing-xl);
    
    .guide-card {
      background: var(--bg-color);
      border-radius: var(--border-radius-base);
      box-shadow: var(--shadow-base);
      overflow: hidden;
      
      .guide-header {
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--border-lighter);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        
        .el-icon {
          font-size: 20px;
          color: var(--primary-color);
        }
        
        h3 {
          font-size: var(--font-size-lg);
          font-weight: var(--font-weight-medium);
          color: var(--text-primary);
        }
      }
      
      .guide-content {
        padding: var(--spacing-lg);
        
        .guide-section {
          margin-bottom: var(--spacing-lg);
          
          &:last-child {
            margin-bottom: 0;
          }
          
          h4 {
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
          }
          
          ul {
            list-style: none;
            padding: 0;
            
            li {
              padding: var(--spacing-sm) 0;
              color: var(--text-regular);
              position: relative;
              padding-left: var(--spacing-md);
              
              &::before {
                content: '•';
                color: var(--primary-color);
                position: absolute;
                left: 0;
              }
              
              strong {
                color: var(--text-primary);
              }
            }
          }
          
          .process-steps {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
            
            .step {
              flex: 1;
              min-width: 200px;
              display: flex;
              align-items: flex-start;
              gap: var(--spacing-sm);
              
              .step-number {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                background: var(--primary-color);
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: var(--font-size-sm);
                font-weight: var(--font-weight-bold);
                flex-shrink: 0;
              }
              
              .step-content {
                .step-title {
                  font-weight: var(--font-weight-medium);
                  color: var(--text-primary);
                  margin-bottom: 2px;
                }
                
                .step-desc {
                  font-size: var(--font-size-sm);
                  color: var(--text-secondary);
                }
              }
            }
          }
        }
      }
    }
  }
  
  .recent-uploads {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      
      h3 {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-medium);
        color: var(--text-primary);
      }
    }
    
    .uploads-list {
      background: var(--bg-color);
      border-radius: var(--border-radius-base);
      box-shadow: var(--shadow-base);
      overflow: hidden;
      
      .upload-item {
        display: flex;
        align-items: center;
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--border-extra-light);
        cursor: pointer;
        transition: var(--transition-base);
        
        &:hover {
          background: var(--bg-page);
        }
        
        &:last-child {
          border-bottom: none;
        }
        
        .item-icon {
          width: 40px;
          height: 40px;
          border-radius: var(--border-radius-base);
          background: var(--primary-color);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: var(--spacing-md);
          font-size: 18px;
        }
        
        .item-content {
          flex: 1;
          min-width: 0;
          
          .item-title {
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            @include text-ellipsis(1);
          }
          
          .item-meta {
            display: flex;
            gap: var(--spacing-md);
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-sm);
          }
          
          .item-tags {
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
            
            .more-tags {
              font-size: var(--font-size-sm);
              color: var(--text-secondary);
            }
          }
        }
        
        .item-actions {
          display: flex;
          gap: var(--spacing-sm);
        }
      }
    }
  }
}

@include respond-below(md) {
  .upload-page {
    padding: var(--spacing-md);
    
    .upload-stats .stats-card {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .guide-content .process-steps {
      flex-direction: column;
      
      .step {
        min-width: auto;
      }
    }
  }
}
</style>
