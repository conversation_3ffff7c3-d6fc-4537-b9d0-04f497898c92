"""
本脚本演示了如何使用 LangChain 的 MultiVectorRetriever 构建多表示（多向量）索引，实现更丰富的检索能力。整个流程如下：

1. 加载网页文档：使用 WebBaseLoader 加载指定网页内容，得到原始文档列表。
2. 生成文档摘要：对每个原始文档调用 LLM 生成摘要，摘要作为文档的另一种“表示”。
3. 构建多向量检索器：将摘要向量化后存入向量数据库，同时保存原始文档内容，实现多表示检索。
4. 检索：用户查询时，先用摘要向量检索，再返回原始文档，实现更灵活的检索效果。
"""

# 1. 加载网页文档
from langchain_community.document_loaders import WebBaseLoader

# 使用 WebBaseLoader 加载指定网页，返回文档对象列表
loader = WebBaseLoader("https://lilianweng.github.io/posts/2023-06-23-agent/")
docs = loader.load()  # docs 是原始网页文档的列表

# 2. 生成文档摘要
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import StrOutputParser

# 构建摘要链：输入文档内容，输出摘要
chain = (
    {"doc": lambda x: x.page_content}  # 提取文档正文
    | ChatPromptTemplate.from_template("Summarize the following document:\n\n{doc}")  # 摘要提示词
    | ChatOpenAI(
        model="gpt-4o-mini",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
        base_url="https://api.zhizengzeng.com/v1"
    )  # 调用 LLM 生成摘要
    | StrOutputParser()  # 解析输出为字符串
)

# 批量对 docs 里的每个文档生成摘要，max_concurrency=5 表示并发处理
summaries = chain.batch(docs, {"max_concurrency": 5})

# 3. 构建多向量检索器
from langchain.storage import InMemoryByteStore  # 内存向量存储
from langchain_huggingface import HuggingFaceEmbeddings  # 向量模型
from langchain_community.vectorstores import Chroma  # 向量数据库
from langchain.retrievers.multi_vector import MultiVectorRetriever  # 多向量检索器
from langchain.storage import InMemoryStore  # 文档存储

# 初始化向量模型（用于摘要向量化）
embed_model = HuggingFaceEmbeddings(model_name="BAAI/bge-small-zh")

# 创建 Chroma 向量数据库，用于存储摘要向量
vectorstore = Chroma(collection_name="summaries", embedding_function=embed_model)

# 初始化内存存储
store = InMemoryByteStore()  # 存储向量的 byte_store
docstore = InMemoryStore()   # 存储原始文档内容
id_key = "doc_id"            # 每个文档的唯一标识字段

# 创建多向量检索器
retriever = MultiVectorRetriever(
    vectorstore=vectorstore,  # 摘要向量存储
    byte_store=store,         # 向量的底层存储
    docstore=docstore,        # 原始文档存储
    id_key=id_key,            # 文档ID字段
)

import uuid
from langchain_core.documents import Document

# 为每个原始文档生成唯一ID
doc_ids = [str(uuid.uuid4()) for _ in docs]

# 构建摘要文档对象，每个摘要文档的 metadata 里带有 doc_id
summary_docs = [
    Document(page_content=s, metadata={id_key: doc_ids[i]})
    for i, s in enumerate(summaries)
]

# 将摘要文档添加到向量数据库（用于向量检索）
retriever.vectorstore.add_documents(summary_docs)

# 将原始文档内容与 doc_id 绑定，批量存入 docstore
retriever.docstore.mset(list(zip(doc_ids, docs)))

# 4. 检索：用户输入查询，先用摘要向量检索，再返回原始文档
query = "Memory in agents"
retrieved_docs = retriever.get_relevant_documents(query, n_results=1)

# 打印检索到的原始文档内容
print(retrieved_docs)