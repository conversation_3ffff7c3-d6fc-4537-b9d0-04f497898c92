"""
查询预处理服务
包括查询重写、自查询和查询路由
"""
import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)


class QueryProcessor:
    """查询预处理器"""
    
    def __init__(self, llm_service, config: Dict[str, Any]):
        self.llm_service = llm_service
        self.config = config
        
        # 查询重写配置
        self.query_refinement_enabled = config.get("query_refinement", {}).get("enabled", True)
        self.max_refinements = config.get("query_refinement", {}).get("max_refinements", 2)
        
        # 自查询配置
        self.self_query_enabled = config.get("self_query", {}).get("enabled", True)
        self.metadata_fields = config.get("self_query", {}).get("metadata_fields", [])
        
        # 查询路由配置
        self.query_routing_enabled = config.get("query_routing", {}).get("enabled", True)
        self.routes = config.get("query_routing", {}).get("routes", [])
        
        logger.info("查询预处理器初始化完成")
    
    def process_query(self, query: str) -> Dict[str, Any]:
        """
        处理查询，返回处理结果
        
        Args:
            query: 原始查询
            
        Returns:
            处理结果字典
        """
        result = {
            "original_query": query,
            "refined_query": query,
            "metadata_filters": {},
            "route": "rag_search",
            "processing_steps": []
        }
        
        try:
            # 1. 查询路由
            if self.query_routing_enabled:
                route = self._route_query(query)
                result["route"] = route
                result["processing_steps"].append(f"查询路由: {route}")
            
            # 2. 自查询（提取元数据过滤条件）
            if self.self_query_enabled:
                metadata_filters = self._extract_metadata_filters(query)
                result["metadata_filters"] = metadata_filters
                if metadata_filters:
                    result["processing_steps"].append(f"元数据过滤: {metadata_filters}")
            
            # 3. 查询重写
            if self.query_refinement_enabled and result["route"] == "rag_search":
                refined_query = self._refine_query(query)
                result["refined_query"] = refined_query
                if refined_query != query:
                    result["processing_steps"].append(f"查询重写: {query} -> {refined_query}")
            
            logger.info(f"查询处理完成: {result['processing_steps']}")
            return result
            
        except Exception as e:
            logger.error(f"查询处理失败: {e}")
            return result
    
    def _route_query(self, query: str) -> str:
        """查询路由，判断查询类型"""
        try:
            # 简单的基于关键词的路由
            query_lower = query.lower()
            
            for route in self.routes:
                keywords = route.get("keywords", [])
                for keyword in keywords:
                    if keyword in query_lower:
                        logger.debug(f"查询路由到: {route['name']} (匹配关键词: {keyword})")
                        return route["name"]
            
            # 默认路由到RAG搜索
            return "rag_search"
            
        except Exception as e:
            logger.error(f"查询路由失败: {e}")
            return "rag_search"
    
    def _extract_metadata_filters(self, query: str) -> Dict[str, Any]:
        """提取元数据过滤条件"""
        filters = {}
        
        try:
            # 提取标签过滤
            if "tags" in self.metadata_fields:
                tag_patterns = [
                    r"标签[是为]?[：:]?\s*([^\s，,。.]+)",
                    r"tag[s]?[：:]?\s*([^\s，,。.]+)",
                    r"关于\s*([^\s，,。.]+)",
                ]
                
                for pattern in tag_patterns:
                    matches = re.findall(pattern, query, re.IGNORECASE)
                    if matches:
                        filters["tags"] = matches[0].strip()
                        break
            
            # 提取时间过滤
            if "created_at" in self.metadata_fields:
                time_patterns = [
                    r"(\d{4}年\d{1,2}月)",
                    r"(\d{4}-\d{1,2})",
                    r"(今天|昨天|本周|本月|最近)",
                ]
                
                for pattern in time_patterns:
                    matches = re.findall(pattern, query)
                    if matches:
                        filters["time_range"] = matches[0]
                        break
            
            # 提取来源过滤
            if "source" in self.metadata_fields:
                source_patterns = [
                    r"来源[是为]?[：:]?\s*([^\s，,。.]+)",
                    r"source[：:]?\s*([^\s，,。.]+)",
                    r"从\s*([^\s，,。.]+)\s*中",
                ]
                
                for pattern in source_patterns:
                    matches = re.findall(pattern, query, re.IGNORECASE)
                    if matches:
                        filters["source"] = matches[0].strip()
                        break
            
            return filters
            
        except Exception as e:
            logger.error(f"提取元数据过滤条件失败: {e}")
            return {}
    
    def _refine_query(self, query: str) -> str:
        """查询重写，提升检索效果"""
        try:
            # 构建查询重写的提示词
            prompt = f"""
请帮我重写以下查询，使其更适合在文档数据库中进行语义搜索。

原始查询: {query}

重写要求:
1. 保持查询的核心意图不变
2. 使用更具体、更准确的词汇
3. 如果查询过于简短，适当扩展相关概念
4. 如果查询过于复杂，提取核心关键词
5. 只返回重写后的查询，不要其他解释

重写后的查询:"""

            # 调用LLM进行查询重写
            response = self.llm_service.generate(
                prompt=prompt,
                max_tokens=100,
                temperature=0.3
            )
            
            refined_query = response.strip()
            
            # 简单验证重写结果
            if len(refined_query) > 0 and len(refined_query) < 200:
                return refined_query
            else:
                logger.warning(f"查询重写结果异常，使用原查询: {refined_query}")
                return query
                
        except Exception as e:
            logger.error(f"查询重写失败: {e}")
            return query
    
    def apply_metadata_filters(self, search_results: List[Dict[str, Any]], 
                             metadata_filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """应用元数据过滤"""
        if not metadata_filters:
            return search_results
        
        try:
            filtered_results = []
            
            for result in search_results:
                should_include = True
                
                # 标签过滤
                if "tags" in metadata_filters:
                    target_tag = metadata_filters["tags"].lower()
                    result_tags = result.get("tags", [])
                    if isinstance(result_tags, str):
                        result_tags = [result_tags]
                    
                    tag_match = any(target_tag in tag.lower() for tag in result_tags)
                    if not tag_match:
                        should_include = False
                
                # 来源过滤
                if "source" in metadata_filters and should_include:
                    target_source = metadata_filters["source"].lower()
                    result_source = result.get("source", "").lower()
                    if target_source not in result_source:
                        should_include = False
                
                # 时间过滤（简化实现）
                if "time_range" in metadata_filters and should_include:
                    # 这里可以根据具体需求实现时间过滤逻辑
                    pass
                
                if should_include:
                    filtered_results.append(result)
            
            logger.info(f"元数据过滤: {len(search_results)} -> {len(filtered_results)}")
            return filtered_results
            
        except Exception as e:
            logger.error(f"应用元数据过滤失败: {e}")
            return search_results


def get_query_processor(llm_service, config: Dict[str, Any]) -> QueryProcessor:
    """获取查询预处理器实例"""
    return QueryProcessor(llm_service, config)
