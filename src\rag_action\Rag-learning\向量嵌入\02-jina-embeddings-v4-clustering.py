import pandas as pd
import numpy as np
import requests
from sklearn.cluster import KMeans

# 1. 配置Jina API
url = 'https://api.jina.ai/v1/embeddings'
headers = {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer jina_ca1d9ccb7c7740a080840a6a1d00b7carYMMvEUSmmHAgT_nsI0sFeCtqFFd'
}

# 2. 读取游戏描述数据
df = pd.read_csv("C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/游戏描述.csv")
texts = df['description'].tolist()

# 3. 获取文本嵌入
data = {
    "model": "jina-embeddings-v4",
    "task": "text-matching",
    "input": texts
}

response = requests.post(url, headers=headers, json=data)

if response.status_code != 200:
    raise RuntimeError(f"API调用失败: {response.status_code} - {response.text}")

embeddings = [item['embedding'] for item in response.json().get('data', [])]
if not embeddings:
    raise RuntimeError("API未返回嵌入向量")

embeddings = np.array(embeddings)

# 4. 聚类分析
# 下面这两行代码是用KMeans算法对文本嵌入向量进行聚类：
# kmeans = KMeans(n_clusters=3, random_state=42)
# 这行代码的意思是：创建一个KMeans聚类模型，n_clusters=3表示我们希望把所有文本分成3个类别。
# random_state=42 是用来设置随机数种子的。KMeans聚类在初始化时会有一些随机过程，比如初始聚类中心的选择。
# 如果不设置random_state，每次运行聚类时结果可能会不一样。设置成42（其实可以是任意整数），
# 就能保证每次运行时聚类的结果都是一样的，方便复现和调试。
kmeans = KMeans(n_clusters=3, random_state=42)
# labels = kmeans.fit_predict(embeddings)
# 这行代码的意思是：用KMeans算法对所有文本的嵌入向量进行聚类，并返回每个文本所属的类别标签（比如0、1、2），这些标签保存在labels变量中。
labels = kmeans.fit_predict(embeddings)

# 5. 打印结果
print("\n聚类结果：")
for i, lbl in enumerate(labels):
    print(f"Cluster {lbl}: {texts[i]}")