"""
测试 BGE-M3 稀疏向量兼容性修复

本脚本测试修复后的代码是否能正确处理 csr_array 和 csr_matrix 两种类型
"""

import os
import logging
import numpy as np
from scipy.sparse import csr_matrix, csr_array

# 设置环境变量
os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"
os.environ["HTTP_PROXY"] = "http://127.0.0.1:1080"
os.environ["HTTPS_PROXY"] = "http://127.0.0.1:1080"

from pymilvus.model.hybrid import BGEM3EmbeddingFunction

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_sparse_matrix_compatibility():
    """测试稀疏矩阵兼容性"""
    
    logger.info("=== 测试稀疏矩阵兼容性 ===")
    
    try:
        # 初始化 BGE-M3 模型
        logger.info("🔧 初始化 BGE-M3 模型...")
        embedding_function = BGEM3EmbeddingFunction(
            model_name='BAAI/bge-m3',
            device='cpu',
            use_fp16=False
        )
        
        # 测试文本
        test_texts = ["机器学习是人工智能的子集"]
        
        # 生成嵌入向量
        logger.info("📝 生成嵌入向量...")
        embeddings = embedding_function.encode_documents(test_texts)
        
        # 检查稀疏向量类型
        sparse_vector = embeddings["sparse"][0]
        logger.info(f"🔍 稀疏向量类型: {type(sparse_vector)}")
        logger.info(f"🔍 稀疏向量形状: {sparse_vector.shape}")
        
        # 测试兼容性方法
        logger.info("🧪 测试兼容性方法...")
        
        # 方法1：检查是否有 getrow 方法
        has_getrow = hasattr(sparse_vector, 'getrow')
        logger.info(f"✅ 是否有 getrow 方法: {has_getrow}")
        
        # 方法2：测试索引访问
        try:
            if has_getrow:
                row_data = sparse_vector.getrow(0)
                logger.info("✅ getrow 方法可用")
            else:
                row_data = sparse_vector[0:1]
                logger.info("✅ 索引访问可用")
            
            logger.info(f"✅ 行数据类型: {type(row_data)}")
            logger.info(f"✅ 行数据形状: {row_data.shape}")
            
        except Exception as e:
            logger.error(f"❌ 访问行数据失败: {e}")
        
        # 方法3：测试 nonzero 方法
        try:
            _, col_indices = sparse_vector.nonzero()
            values = sparse_vector.data
            logger.info(f"✅ 非零元素数量: {len(values)}")
            logger.info(f"✅ 列索引范围: {col_indices.min()} ~ {col_indices.max()}")
            logger.info(f"✅ 权重范围: {values.min():.4f} ~ {values.max():.4f}")
            
        except Exception as e:
            logger.error(f"❌ 获取非零元素失败: {e}")
        
        # 方法4：测试转换为字典
        try:
            sparse_dict = convert_sparse_to_dict_test(sparse_vector)
            logger.info(f"✅ 转换为字典成功，包含 {len(sparse_dict)} 个元素")
            
            # 显示前5个元素
            items = list(sparse_dict.items())[:5]
            logger.info("✅ 前5个元素:")
            for idx, weight in items:
                logger.info(f"   词汇索引 {idx}: 权重 {weight:.4f}")
                
        except Exception as e:
            logger.error(f"❌ 转换为字典失败: {e}")
        
        logger.info("🎉 兼容性测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        raise


def convert_sparse_to_dict_test(sparse_matrix):
    """
    测试版本的稀疏矩阵转换函数
    """
    # 兼容处理：确保是 2D 格式
    if sparse_matrix.ndim == 1:
        sparse_matrix = sparse_matrix.reshape(1, -1)
    elif sparse_matrix.shape[0] > 1:
        sparse_matrix = sparse_matrix[0:1]
    
    # 获取非零元素的索引和值
    _, col_indices = sparse_matrix.nonzero()
    values = sparse_matrix.data
    
    # 转换为字典格式
    sparse_dict = {}
    for col_idx, value in zip(col_indices, values):
        sparse_dict[int(col_idx)] = float(value)
    
    return sparse_dict


def get_sparse_row_test(sparse_matrix, row_index: int):
    """
    测试版本的获取稀疏矩阵行的函数
    """
    if hasattr(sparse_matrix, 'getrow'):
        # csr_matrix 有 getrow 方法
        return sparse_matrix.getrow(row_index)
    else:
        # csr_array 使用索引访问
        return sparse_matrix[row_index:row_index+1]


def test_batch_processing():
    """测试批处理兼容性"""
    
    logger.info("\n=== 测试批处理兼容性 ===")
    
    try:
        # 初始化模型
        embedding_function = BGEM3EmbeddingFunction(
            model_name='BAAI/bge-m3',
            device='cpu',
            use_fp16=False
        )
        
        # 测试多个文本
        test_texts = [
            "机器学习是人工智能的子集",
            "深度学习使用神经网络",
            "自然语言处理理解人类语言"
        ]
        
        logger.info(f"📝 处理 {len(test_texts)} 个文本...")
        embeddings = embedding_function.encode_documents(test_texts)
        
        batch_sparse = embeddings["sparse"]
        logger.info(f"🔍 批量稀疏向量类型: {type(batch_sparse)}")
        logger.info(f"🔍 批量稀疏向量形状: {batch_sparse.shape}")
        
        # 测试逐行处理
        logger.info("🧪 测试逐行处理...")
        for i in range(len(test_texts)):
            try:
                # 使用兼容方法获取行
                sparse_row = get_sparse_row_test(batch_sparse, i)
                sparse_dict = convert_sparse_to_dict_test(sparse_row)
                
                logger.info(f"✅ 文本 {i+1}: 转换成功，{len(sparse_dict)} 个非零元素")
                
            except Exception as e:
                logger.error(f"❌ 文本 {i+1} 处理失败: {e}")
        
        logger.info("🎉 批处理测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 批处理测试失败: {e}")
        raise


def main():
    """主测试函数"""
    
    logger.info("🚀 开始 BGE-M3 稀疏向量兼容性测试")
    
    try:
        # 测试基本兼容性
        test_sparse_matrix_compatibility()
        
        # 测试批处理兼容性
        test_batch_processing()
        
        logger.info("\n" + "="*50)
        logger.info("🎯 所有测试通过！修复成功！")
        logger.info("✅ 代码现在兼容 csr_array 和 csr_matrix 两种类型")
        logger.info("✅ 可以正常运行 bge_m3_native_sparse_example.py")
        logger.info("="*50)
        
    except Exception as e:
        logger.error(f"💥 测试失败: {e}")
        logger.error("请检查网络连接和模型下载情况")


if __name__ == "__main__":
    main()
