#!/usr/bin/env python3
"""
测试LangChain RAG服务的核心问答功能
"""
import asyncio
import time
from src.rag_action.service.rag_service_adapter import get_rag_service

async def test_basic_qa():
    """测试基础问答功能"""
    print("🤖 测试基础问答功能")
    print("=" * 50)
    
    try:
        # 获取RAG服务
        rag_service = get_rag_service()
        print("✅ RAG服务获取成功")
        
        # 测试问题
        test_questions = [
            "什么是人工智能？",
            "机器学习有哪些应用？",
            "深度学习的基本原理是什么？"
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n📝 测试问题 {i}: {question}")
            
            start_time = time.time()
            response = await rag_service.answer_question(question)
            end_time = time.time()
            
            print(f"⏱️ 响应时间: {end_time - start_time:.2f}s")
            print(f"📄 回答长度: {len(response.answer)}字符")
            print(f"📚 源文档数: {len(response.sources)}")
            print(f"🔧 处理方法: {response.metadata.get('processing_method', 'unknown')}")
            
            # 显示回答预览
            answer_preview = response.answer[:100] + "..." if len(response.answer) > 100 else response.answer
            print(f"💬 回答预览: {answer_preview}")
            
        return True
        
    except Exception as e:
        print(f"❌ 基础问答测试失败: {e}")
        return False

async def test_streaming_qa():
    """测试流式问答功能"""
    print("\n🌊 测试流式问答功能")
    print("=" * 50)
    
    try:
        # 获取RAG服务
        rag_service = get_rag_service()
        
        question = "请详细解释一下深度学习的工作原理"
        print(f"📝 流式问题: {question}")
        
        print("🔄 开始流式输出:")
        print("-" * 30)
        
        start_time = time.time()
        first_token_time = None
        token_count = 0
        
        async for chunk in rag_service.answer_question_stream(question):
            if first_token_time is None:
                first_token_time = time.time()
                print(f"⚡ 首字延迟: {first_token_time - start_time:.3f}s")
            
            print(chunk, end='', flush=True)
            token_count += 1
        
        end_time = time.time()
        print(f"\n-" * 30)
        print(f"⏱️ 总时间: {end_time - start_time:.2f}s")
        print(f"📊 Token数: {token_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 流式问答测试失败: {e}")
        return False

async def test_conversation_context():
    """测试对话上下文功能"""
    print("\n💬 测试对话上下文功能")
    print("=" * 50)
    
    try:
        # 获取RAG服务
        rag_service = get_rag_service()
        
        # 模拟对话历史
        conversation_context = [
            {"role": "user", "content": "什么是机器学习？"},
            {"role": "assistant", "content": "机器学习是人工智能的一个分支，通过算法让计算机从数据中学习模式。"},
            {"role": "user", "content": "它有哪些主要类型？"}
        ]
        
        question = "深度学习属于哪一类？"
        print(f"📝 上下文问题: {question}")
        print(f"📚 对话历史: {len(conversation_context)}轮")
        
        start_time = time.time()
        response = await rag_service.answer_question(
            question, 
            conversation_context=conversation_context
        )
        end_time = time.time()
        
        print(f"⏱️ 响应时间: {end_time - start_time:.2f}s")
        print(f"💬 回答: {response.answer[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 对话上下文测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🎉 LangChain RAG服务功能测试")
    print("=" * 80)
    
    tests = [
        ("基础问答功能", test_basic_qa),
        ("流式问答功能", test_streaming_qa),
        ("对话上下文功能", test_conversation_context)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🚀 开始 {test_name}...")
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
                
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 80)
    print("📊 功能测试结果总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  - {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.0f}%)")
    
    if passed == total:
        print("🎊 所有功能测试通过！LangChain RAG服务运行正常！")
    else:
        print("⚠️ 部分功能测试失败，请检查具体问题")

if __name__ == "__main__":
    asyncio.run(main())
