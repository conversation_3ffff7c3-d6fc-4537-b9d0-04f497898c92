<template>
  <div class="test-page">
    <div class="container">
      <h1 class="title">🎉 AI笔记系统</h1>
      <p class="subtitle">Vue.js 应用已成功启动！</p>
      
      <div class="info-cards">
        <div class="card">
          <h3>✅ Vue 3</h3>
          <p>组合式API正常工作</p>
        </div>
        
        <div class="card">
          <h3>✅ Element Plus</h3>
          <p>UI组件库已加载</p>
          <el-button type="primary" @click="showMessage">测试按钮</el-button>
        </div>
        
        <div class="card">
          <h3>✅ Vue Router</h3>
          <p>路由系统正常</p>
          <p>当前路由: {{ $route.path }}</p>
        </div>
        
        <div class="card">
          <h3>✅ Pinia</h3>
          <p>状态管理正常</p>
          <p>主题: 浅色模式</p>
        </div>
      </div>
      
      <div class="actions">
        <el-button type="success" @click="toggleTheme">
          切换主题
        </el-button>
        <el-button type="info" @click="testRouter">
          测试路由
        </el-button>
        <el-button type="primary" @click="testApiConnection">
          测试API连接
        </el-button>
        <el-button type="warning" @click="testNotesApi">
          测试笔记API
        </el-button>
        <el-button type="success" @click="testApiModule">
          测试API模块
        </el-button>
      </div>
      
      <div class="status">
        <p>🚀 系统状态: <span class="status-ok">正常运行</span></p>
        <p>📅 启动时间: {{ startTime }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import { notesApi } from '@/api'

const router = useRouter()
const startTime = ref('')

// 方法
const showMessage = () => {
  ElMessage.success('Element Plus 组件工作正常！')
}

const toggleTheme = () => {
  ElMessage.info('主题切换功能正常')
}

const testRouter = () => {
  ElMessage.info('路由系统正常工作！')
}

const testApiConnection = async () => {
  try {
    ElMessage.info('正在测试API连接...')

    // 测试后端健康检查接口
    const response = await axios.get('/api/health')

    if (response.status === 200) {
      ElMessage.success(`API连接成功！后端服务正常运行`)
      console.log('健康检查响应:', response.data)
    } else {
      ElMessage.warning(`API连接异常，状态码: ${response.status}`)
    }
  } catch (error: any) {
    console.error('API连接错误:', error)

    if (error.response) {
      // 服务器响应了错误状态码
      ElMessage.error(`API连接失败: ${error.response.status} - ${error.response.statusText}`)
    } else if (error.request) {
      // 请求发出但没有收到响应
      ElMessage.error('API连接失败: 无法连接到后端服务')
    } else {
      // 其他错误
      ElMessage.error(`API连接失败: ${error.message}`)
    }
  }
}

const testNotesApi = async () => {
  try {
    ElMessage.info('正在测试笔记API...')

    // 测试获取笔记列表接口
    const response = await axios.get('/api/notes')

    if (response.status === 200) {
      ElMessage.success(`笔记API连接成功！获取到 ${response.data.notes?.length || 0} 条笔记`)
      console.log('笔记列表响应:', response.data)
    } else {
      ElMessage.warning(`笔记API异常，状态码: ${response.status}`)
    }
  } catch (error: any) {
    console.error('笔记API错误:', error)

    if (error.response) {
      ElMessage.error(`笔记API失败: ${error.response.status} - ${error.response.statusText}`)
      console.log('错误详情:', error.response.data)
    } else if (error.request) {
      ElMessage.error('笔记API失败: 无法连接到后端服务')
    } else {
      ElMessage.error(`笔记API失败: ${error.message}`)
    }
  }
}

const testApiModule = async () => {
  try {
    ElMessage.info('正在测试API模块...')

    // 使用封装的API模块
    const response = await notesApi.getNotes({ page: 1, page_size: 10 })

    ElMessage.success(`API模块工作正常！获取到 ${response.notes?.length || 0} 条笔记`)
    console.log('API模块响应:', response)
  } catch (error: any) {
    console.error('API模块错误:', error)
    ElMessage.error(`API模块失败: ${error.message}`)
  }
}

// 生命周期
onMounted(() => {
  startTime.value = new Date().toLocaleString('zh-CN')
})
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.container {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  width: 100%;
  text-align: center;
}

.title {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 10px;
  font-weight: 700;
}

.subtitle {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 40px;
}

.info-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 2px solid #e9ecef;
  transition: transform 0.2s ease;
  
  &:hover {
    transform: translateY(-5px);
    border-color: #409eff;
  }
  
  h3 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.1rem;
  }
  
  p {
    color: #666;
    margin-bottom: 10px;
    font-size: 0.9rem;
  }
}

.actions {
  margin-bottom: 30px;
  
  .el-button {
    margin: 0 10px;
  }
}

.status {
  background: #f0f9ff;
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #409eff;
  
  p {
    margin: 5px 0;
    color: #333;
  }
  
  .status-ok {
    color: #67c23a;
    font-weight: 600;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 20px;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .info-cards {
    grid-template-columns: 1fr;
  }
}
</style>
