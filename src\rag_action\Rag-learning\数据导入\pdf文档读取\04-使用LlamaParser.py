from dotenv import load_dotenv
load_dotenv()

from llama_parse import LlamaParse



documents = LlamaParse(result_type="markdown",
 api_key="llx-adfMAwWUmfNsv3xVtbc2lEkjvrkbNdWfBZdso2az99XCaUSE").load_data("C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/黑神话悟空.pdf")

print(documents)


from llama_index.core.node_parser import MarkdownNodeParser

node_parser = MarkdownNodeParser()

nodes = node_parser.get_nodes_from_documents(documents)


print(nodes)





