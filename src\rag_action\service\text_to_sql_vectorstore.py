"""
Text-to-SQL向量存储服务
负责Few-Shot示例和数据库Schema的向量化存储与检索
"""
import logging
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pymilvus import MilvusClient, DataType, FieldSchema, CollectionSchema

try:
    from rag_action.core.config import get_settings
    from rag_action.service.embedding_service import get_embedding_service
except ImportError:
    # 兼容不同的导入方式
    try:
        from src.rag_action.core.config import get_settings
        from src.rag_action.service.embedding_service import get_embedding_service
    except ImportError:
        # 如果都失败，创建模拟对象用于测试
        class MockSettings:
            def __init__(self):
                self.milvus = type('obj', (object,), {'host': 'localhost', 'port': 19530})()
                self.embedding = type('obj', (object,), {'model_name': 'text-embedding-3-small'})()

        class MockEmbeddingService:
            def embed_texts(self, texts):
                import random
                return [[random.random() for _ in range(1536)] for _ in texts]

            def embed_text(self, text):
                import random
                return [random.random() for _ in range(1536)]

        def get_settings():
            return MockSettings()

        def get_embedding_service():
            return MockEmbeddingService()

logger = logging.getLogger(__name__)
settings = get_settings()


@dataclass
class FewShotExample:
    """Few-Shot示例数据结构"""
    question: str
    sql: str
    explanation: str
    category: str = "general"  # 查询类别：general, join, aggregate, filter等
    difficulty: str = "easy"   # 难度：easy, medium, hard
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "question": self.question,
            "sql": self.sql,
            "explanation": self.explanation,
            "category": self.category,
            "difficulty": self.difficulty
        }


@dataclass
class DDLSchema:
    """DDL语句数据结构"""
    table_name: str
    ddl_statement: str

    def to_dict(self) -> Dict[str, Any]:
        return {
            "table_name": self.table_name,
            "ddl_statement": self.ddl_statement
        }

    def get_searchable_text(self) -> str:
        """获取用于向量检索的文本"""
        return f"表名: {self.table_name}\nDDL语句: {self.ddl_statement}"


@dataclass
class ColumnDescription:
    """字段描述数据结构"""
    table_name: str
    column_name: str
    data_type: str
    description: str
    constraints: str = ""

    def to_dict(self) -> Dict[str, Any]:
        return {
            "table_name": self.table_name,
            "column_name": self.column_name,
            "data_type": self.data_type,
            "description": self.description,
            "constraints": self.constraints
        }

    def get_searchable_text(self) -> str:
        """获取用于向量检索的文本"""
        constraint_text = f" 约束: {self.constraints}" if self.constraints else ""
        return f"表: {self.table_name} 字段: {self.column_name} 类型: {self.data_type} 描述: {self.description}{constraint_text}"


# 保持向后兼容的SchemaInfo类
@dataclass
class SchemaInfo:
    """数据库Schema信息数据结构（向后兼容）"""
    table_name: str
    description: str
    ddl: str
    columns: Dict[str, Dict[str, Any]]

    def to_dict(self) -> Dict[str, Any]:
        return {
            "table_name": self.table_name,
            "description": self.description,
            "ddl": self.ddl,
            "columns": json.dumps(self.columns, ensure_ascii=False)
        }

    def get_searchable_text(self) -> str:
        """获取用于向量化的可搜索文本"""
        column_texts = []
        for col_name, col_info in self.columns.items():
            col_text = f"{col_name}: {col_info.get('description', '')} ({col_info.get('type', '')})"
            column_texts.append(col_text)

        searchable_text = f"""
表名: {self.table_name}
描述: {self.description}
字段信息: {'; '.join(column_texts)}
DDL: {self.ddl}
        """.strip()

        return searchable_text

    def to_ddl_schema(self) -> DDLSchema:
        """转换为DDL Schema"""
        return DDLSchema(
            table_name=self.table_name,
            ddl_statement=self.ddl
        )

    def to_column_descriptions(self) -> List[ColumnDescription]:
        """转换为字段描述列表"""
        descriptions = []
        for col_name, col_info in self.columns.items():
            descriptions.append(ColumnDescription(
                table_name=self.table_name,
                column_name=col_name,
                data_type=col_info.get('type', ''),
                description=col_info.get('description', ''),
                constraints=col_info.get('constraints', '')
            ))
        return descriptions


class TextToSQLVectorStore:
    """Text-to-SQL向量存储管理器 - 支持三个细粒度集合"""

    def __init__(self):
        self.embedding_service = get_embedding_service()
        self.milvus_client = MilvusClient(
            host=settings.milvus.host,
            port=settings.milvus.port
        )

        # 三个细粒度集合名称
        self.ddl_collection = "text2sql_ddl_schema"
        self.few_shot_collection = "text2sql_few_shot_examples"
        self.column_collection = "text2sql_column_descriptions"

        # 向量维度
        self.vector_dim = settings.embedding.model_name == "text-embedding-3-small" and 1536 or 3072

        logger.info("Text-to-SQL向量存储服务初始化完成 - 支持三个细粒度集合")
    
    def initialize_collections(self):
        """初始化Milvus集合"""
        self._create_ddl_collection()
        self._create_few_shot_collection()
        self._create_column_collection()
        logger.info("Milvus三个细粒度集合初始化完成")

    def _create_ddl_collection(self):
        """创建DDL集合"""
        if self.milvus_client.has_collection(self.ddl_collection):
            logger.info(f"DDL集合 {self.ddl_collection} 已存在，删除重建")
            self.milvus_client.drop_collection(self.ddl_collection)

        # 定义字段
        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=self.vector_dim),
            FieldSchema(name="table_name", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="ddl_statement", dtype=DataType.VARCHAR, max_length=3000),
            FieldSchema(name="created_at", dtype=DataType.INT64),
        ]

        schema = CollectionSchema(
            fields,
            description="Text-to-SQL DDL Schema Collection",
            enable_dynamic_field=False
        )

        # 创建集合
        self.milvus_client.create_collection(
            collection_name=self.ddl_collection,
            schema=schema
        )

        # 创建索引
        index_params = self.milvus_client.prepare_index_params()
        index_params.add_index(
            field_name="vector",
            index_type="HNSW",
            metric_type="COSINE",
            params={"M": 16, "efConstruction": 200}
        )
        self.milvus_client.create_index(
            collection_name=self.ddl_collection,
            index_params=index_params
        )

        logger.info(f"创建DDL集合: {self.ddl_collection}")

    def _create_few_shot_collection(self):
        """创建Few-Shot示例集合"""
        if self.milvus_client.has_collection(self.few_shot_collection):
            logger.info(f"Few-Shot集合 {self.few_shot_collection} 已存在")
            return
        
        # 定义字段
        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=self.vector_dim),
            FieldSchema(name="question", dtype=DataType.VARCHAR, max_length=1000),
            FieldSchema(name="sql", dtype=DataType.VARCHAR, max_length=2000),
            FieldSchema(name="explanation", dtype=DataType.VARCHAR, max_length=500),
            FieldSchema(name="category", dtype=DataType.VARCHAR, max_length=50),
            FieldSchema(name="difficulty", dtype=DataType.VARCHAR, max_length=20),
            FieldSchema(name="created_at", dtype=DataType.INT64),  # 时间戳
        ]
        
        schema = CollectionSchema(
            fields, 
            description="Text-to-SQL Few-Shot Examples",
            enable_dynamic_field=False
        )
        
        # 创建集合
        self.milvus_client.create_collection(
            collection_name=self.few_shot_collection,
            schema=schema
        )
        
        # 创建索引
        index_params = self.milvus_client.prepare_index_params()
        index_params.add_index(
            field_name="vector",
            index_type="HNSW",
            metric_type="COSINE",
            params={"M": 16, "efConstruction": 200}
        )
        self.milvus_client.create_index(
            collection_name=self.few_shot_collection,
            index_params=index_params
        )
        
        logger.info(f"创建Few-Shot集合: {self.few_shot_collection}")
    
    def _create_column_collection(self):
        """创建字段描述集合"""
        if self.milvus_client.has_collection(self.column_collection):
            logger.info(f"字段描述集合 {self.column_collection} 已存在，删除重建")
            self.milvus_client.drop_collection(self.column_collection)

        # 定义字段
        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=self.vector_dim),
            FieldSchema(name="table_name", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="column_name", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="data_type", dtype=DataType.VARCHAR, max_length=50),
            FieldSchema(name="description", dtype=DataType.VARCHAR, max_length=1000),
            FieldSchema(name="constraints", dtype=DataType.VARCHAR, max_length=500),
            FieldSchema(name="created_at", dtype=DataType.INT64),
        ]

        schema = CollectionSchema(
            fields,
            description="Text-to-SQL Column Descriptions Collection",
            enable_dynamic_field=False
        )

        # 创建集合
        self.milvus_client.create_collection(
            collection_name=self.column_collection,
            schema=schema
        )

        # 创建索引
        index_params = self.milvus_client.prepare_index_params()
        index_params.add_index(
            field_name="vector",
            index_type="HNSW",
            metric_type="COSINE",
            params={"M": 16, "efConstruction": 200}
        )
        self.milvus_client.create_index(
            collection_name=self.column_collection,
            index_params=index_params
        )

        logger.info(f"创建字段描述集合: {self.column_collection}")

    def insert_ddl_schemas(self, ddl_schemas: List[DDLSchema]) -> bool:
        """批量插入DDL Schema"""
        try:
            # 准备搜索文本
            search_texts = [ddl.get_searchable_text() for ddl in ddl_schemas]

            # 生成向量嵌入
            logger.info(f"开始为{len(search_texts)}个DDL Schema生成向量嵌入...")
            embeddings = self.embedding_service.embed_texts(search_texts)

            # 准备插入数据
            records = []
            current_time = int(time.time())

            for ddl, embedding in zip(ddl_schemas, embeddings):
                record = ddl.to_dict()
                record["vector"] = embedding
                record["created_at"] = current_time
                records.append(record)

            # 批量插入
            result = self.milvus_client.insert(
                collection_name=self.ddl_collection,
                data=records
            )

            logger.info(f"成功插入{len(records)}个DDL Schema到Milvus")
            return True

        except Exception as e:
            logger.error(f"插入DDL Schema失败: {e}")
            return False

    def insert_few_shot_examples(self, examples: List[FewShotExample]) -> bool:
        """批量插入Few-Shot示例"""
        try:
            # 准备数据
            questions = [example.question for example in examples]
            
            # 生成向量嵌入
            logger.info(f"开始为{len(questions)}个Few-Shot示例生成向量嵌入...")
            embeddings = self.embedding_service.embed_texts(questions)
            
            # 准备插入数据
            records = []
            current_time = int(time.time())
            
            for example, embedding in zip(examples, embeddings):
                record = example.to_dict()
                record["vector"] = embedding
                record["created_at"] = current_time
                records.append(record)
            
            # 批量插入
            result = self.milvus_client.insert(
                collection_name=self.few_shot_collection,
                data=records
            )
            
            logger.info(f"成功插入{len(records)}个Few-Shot示例到Milvus")
            return True
            
        except Exception as e:
            logger.error(f"插入Few-Shot示例失败: {e}")
            return False

    def insert_column_descriptions(self, columns: List[ColumnDescription]) -> bool:
        """批量插入字段描述"""
        try:
            # 准备搜索文本
            search_texts = [col.get_searchable_text() for col in columns]

            # 生成向量嵌入
            logger.info(f"开始为{len(search_texts)}个字段描述生成向量嵌入...")
            embeddings = self.embedding_service.embed_texts(search_texts)

            # 准备插入数据
            records = []
            current_time = int(time.time())

            for col, embedding in zip(columns, embeddings):
                record = col.to_dict()
                record["vector"] = embedding
                record["created_at"] = current_time
                records.append(record)

            # 批量插入
            result = self.milvus_client.insert(
                collection_name=self.column_collection,
                data=records
            )

            logger.info(f"成功插入{len(records)}个字段描述到Milvus")
            return True

        except Exception as e:
            logger.error(f"插入字段描述失败: {e}")
            return False

    def insert_schema_info(self, schemas: List[SchemaInfo]) -> bool:
        """批量插入数据库Schema信息"""
        try:
            # 准备搜索文本
            search_texts = [schema.get_searchable_text() for schema in schemas]
            
            # 生成向量嵌入
            logger.info(f"开始为{len(search_texts)}个Schema生成向量嵌入...")
            embeddings = self.embedding_service.embed_texts(search_texts)
            
            # 准备插入数据
            records = []
            current_time = int(time.time())
            
            for schema, embedding in zip(schemas, embeddings):
                record = schema.to_dict()
                record["vector"] = embedding
                record["created_at"] = current_time
                records.append(record)
            
            # 批量插入
            result = self.milvus_client.insert(
                collection_name=self.schema_collection,
                data=records
            )
            
            logger.info(f"成功插入{len(records)}个Schema信息到Milvus")
            return True
            
        except Exception as e:
            logger.error(f"插入Schema信息失败: {e}")
            return False

    def search_ddl_schemas(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """检索相关的DDL Schema"""
        try:
            # 生成查询向量
            query_embedding = self.embedding_service.embed_text(query)

            # 执行搜索
            self.milvus_client.load_collection(self.ddl_collection)

            search_results = self.milvus_client.search(
                collection_name=self.ddl_collection,
                data=[query_embedding],
                limit=top_k,
                output_fields=["table_name", "ddl_statement"]
            )

            # 处理结果
            ddl_schemas = []
            if search_results and len(search_results) > 0:
                for hit in search_results[0]:
                    ddl_schema = {
                        "table_name": hit.get("table_name", ""),
                        "ddl_statement": hit.get("ddl_statement", ""),
                        "similarity_score": hit.get("distance", 0.0)
                    }
                    ddl_schemas.append(ddl_schema)

            logger.info(f"检索到{len(ddl_schemas)}个相关DDL Schema")
            return ddl_schemas

        except Exception as e:
            logger.error(f"检索DDL Schema失败: {e}")
            return []

    def search_few_shot_examples(self, query: str, top_k: int = 5,
                                category: Optional[str] = None) -> List[Dict[str, Any]]:
        """检索相关的Few-Shot示例"""
        try:
            # 生成查询向量
            query_embedding = self.embedding_service.embed_text(query)
            
            # 构建过滤条件
            filter_expr = None
            if category:
                filter_expr = f'category == "{category}"'
            
            # 执行搜索
            self.milvus_client.load_collection(self.few_shot_collection)
            
            search_results = self.milvus_client.search(
                collection_name=self.few_shot_collection,
                data=[query_embedding],
                limit=top_k,
                output_fields=["question", "sql", "explanation", "category", "difficulty"],
                filter=filter_expr
            )
            
            # 处理结果
            examples = []
            if search_results and len(search_results) > 0:
                for hit in search_results[0]:
                    example = {
                        "question": hit.get("question", ""),
                        "sql": hit.get("sql", ""),
                        "explanation": hit.get("explanation", ""),
                        "category": hit.get("category", ""),
                        "difficulty": hit.get("difficulty", ""),
                        "similarity_score": hit.get("distance", 0.0)
                    }
                    examples.append(example)
            
            logger.info(f"检索到{len(examples)}个相关Few-Shot示例")
            return examples
            
        except Exception as e:
            logger.error(f"检索Few-Shot示例失败: {e}")
            return []

    def search_column_descriptions(self, query: str, top_k: int = 5,
                                  table_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """检索相关的字段描述"""
        try:
            # 生成查询向量
            query_embedding = self.embedding_service.embed_text(query)

            # 构建过滤条件
            filter_expr = None
            if table_name:
                filter_expr = f'table_name == "{table_name}"'

            # 执行搜索
            self.milvus_client.load_collection(self.column_collection)

            search_results = self.milvus_client.search(
                collection_name=self.column_collection,
                data=[query_embedding],
                limit=top_k,
                output_fields=["table_name", "column_name", "data_type", "description", "constraints"],
                filter=filter_expr
            )

            # 处理结果
            columns = []
            if search_results and len(search_results) > 0:
                for hit in search_results[0]:
                    column = {
                        "table_name": hit.get("table_name", ""),
                        "column_name": hit.get("column_name", ""),
                        "data_type": hit.get("data_type", ""),
                        "description": hit.get("description", ""),
                        "constraints": hit.get("constraints", ""),
                        "similarity_score": hit.get("distance", 0.0)
                    }
                    columns.append(column)

            logger.info(f"检索到{len(columns)}个相关字段描述")
            return columns

        except Exception as e:
            logger.error(f"检索字段描述失败: {e}")
            return []

    def search_schema_info(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """检索相关的数据库Schema信息"""
        try:
            # 生成查询向量
            query_embedding = self.embedding_service.embed_text(query)
            
            # 执行搜索
            self.milvus_client.load_collection(self.schema_collection)
            
            search_results = self.milvus_client.search(
                collection_name=self.schema_collection,
                data=[query_embedding],
                limit=top_k,
                output_fields=["table_name", "description", "ddl", "columns"]
            )
            
            # 处理结果
            schemas = []
            if search_results and len(search_results) > 0:
                for hit in search_results[0]:
                    schema = {
                        "table_name": hit.get("table_name", ""),
                        "description": hit.get("description", ""),
                        "ddl": hit.get("ddl", ""),
                        "columns": json.loads(hit.get("columns", "{}")),
                        "similarity_score": hit.get("distance", 0.0)
                    }
                    schemas.append(schema)
            
            logger.info(f"检索到{len(schemas)}个相关Schema信息")
            return schemas
            
        except Exception as e:
            logger.error(f"检索Schema信息失败: {e}")
            return []
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """获取三个集合的统计信息"""
        try:
            ddl_stats = self.milvus_client.get_collection_stats(self.ddl_collection)
            few_shot_stats = self.milvus_client.get_collection_stats(self.few_shot_collection)
            column_stats = self.milvus_client.get_collection_stats(self.column_collection)

            return {
                "ddl_schemas": ddl_stats,
                "few_shot_examples": few_shot_stats,
                "column_descriptions": column_stats
            }
        except Exception as e:
            logger.error(f"获取集合统计信息失败: {e}")
            return {}


# 单例模式
_text_to_sql_vectorstore = None

def get_text_to_sql_vectorstore() -> TextToSQLVectorStore:
    """获取Text-to-SQL向量存储实例"""
    global _text_to_sql_vectorstore
    if _text_to_sql_vectorstore is None:
        _text_to_sql_vectorstore = TextToSQLVectorStore()
    return _text_to_sql_vectorstore
