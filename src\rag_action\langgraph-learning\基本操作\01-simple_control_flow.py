"""
01 - 简单控制流
最简单的 LangGraph 应用，展示基础的线性控制流
"""

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from typing import Dict, List, Any, TypedDict, Annotated

# 创建 LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0
)

# 定义状态类型
class SimpleState(TypedDict):
    messages: Annotated[List, add_messages]  # 消息历史
    user_input: str  # 用户输入
    result: str  # 处理结果

def simple_linear_flow():
    """简单线性控制流：输入 -> 处理 -> 输出"""
    
    print("=== 简单线性控制流 ===")
    print("流程：用户输入 -> 处理 -> 输出结果")
    print()
    
    def process_node(state: SimpleState) -> SimpleState:
        """处理节点：处理用户输入并生成回答"""
        user_input = state["user_input"]
        
        # 使用 LLM 处理输入
        response = llm.invoke([
            HumanMessage(content=f"""
请回答以下问题：

问题：{user_input}

请提供简洁明了的回答：
""")
        ])
        
        answer = response.content
        
        return {
            **state,
            "result": answer,
            "messages": [AIMessage(content=answer)]
        }
    
    # 创建图
    workflow = StateGraph(SimpleState)
    
    # 添加节点
    workflow.add_node("process", process_node)
    
    # 设置入口点
    workflow.set_entry_point("process")
    
    # 添加边：process -> END
    workflow.add_edge("process", END)
    
    # 编译图
    app = workflow.compile()
    
    # 测试
    test_inputs = [
        "什么是Python？",
        "今天天气怎么样？",
        "请介绍一下机器学习"
    ]
    
    for i, user_input in enumerate(test_inputs, 1):
        print(f"测试 {i}: {user_input}")
        
        # 运行图
        result = app.invoke({
            "user_input": user_input,
            "messages": [],
            "result": ""
        })
        
        print(f"回答: {result['result']}")
        print("-" * 50)

def main():
    """主函数"""
    simple_linear_flow()

if __name__ == "__main__":
    main() 