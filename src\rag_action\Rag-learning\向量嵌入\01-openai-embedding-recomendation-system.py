# 下面是一个基于向量嵌入的推荐系统示例代码，并配有详细注释，适合Python初学者理解

import os
from llama_index.embeddings.openai import OpenAIEmbedding  # 用于获取OpenAI的文本嵌入
import openai
import pandas as pd  # 用于数据处理
import numpy as np    # 用于数值计算
from sklearn.metrics.pairwise import cosine_similarity  # 用于计算余弦相似度
import json  # 用于读取json文件

# 步骤1：读取用户评价数据集
# 假设每一行包含用户ID、游戏名称等信息
df = pd.read_csv("C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/用户评价.csv")

# 步骤2：读取游戏描述文件
# 这个json文件包含每个游戏的详细描述，key为游戏名，value为描述文本
with open("C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/游戏说明.json", "r", encoding="utf-8") as f:
    game_descriptions = json.load(f)

# 步骤3：定义一个函数，用于获取文本的嵌入向量
def get_embedding(text, model="text-embedding-3-small"):
    # 创建OpenAIEmbedding对象，传入API密钥、API地址和模型名
    embedding = OpenAIEmbedding(
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
        api_base="https://api.zhizengzeng.com/v1",
        model=model
    )
    # 获取文本的嵌入向量
    return embedding.get_query_embedding(text)

# 步骤4：获取所有游戏的嵌入向量
# 首先获取所有出现过的游戏名
unique_games = df['game_title'].unique().tolist()
target_game = "Killing God: Hu Sun"  # 设定目标游戏名
if target_game not in unique_games:
    unique_games.append(target_game)  # 如果目标游戏不在列表中，手动添加

game_embeddings = {}  # 用于存储每个游戏的嵌入向量

# 遍历每个游戏，获取其描述的嵌入向量
for game in unique_games:
    description = game_descriptions[game]  # 获取游戏描述
    game_embeddings[game] = np.array(get_embedding(description))  # 获取嵌入并转为numpy数组

# 步骤5：计算每个用户的嵌入向量
# 方法：对该用户评价过的所有游戏的嵌入向量取平均
user_vector = {}  # 用于存储每个用户的嵌入向量

# 按用户分组
for user_id, group in df.groupby('user_id'):
    user_game_vecs = []
    for idx, row in group.iterrows():
        g_title = row['game_title']  # 该用户评价的游戏名
        g_vec = game_embeddings[g_title]  # 获取该游戏的嵌入向量
        user_game_vecs.append(g_vec)
    # 对该用户所有游戏的嵌入向量取平均，得到用户的兴趣向量
    user_vector[user_id] = np.mean(user_game_vecs, axis=0)

# 步骤6：获取目标游戏的嵌入向量
target_vector = game_embeddings[target_game]

# 步骤7：计算每个用户与目标游戏的相似度
# 使用余弦相似度衡量
results = []
for user_id, u_vec in user_vector.items():
    u_vec_reshaped = u_vec.reshape(1, -1)  # 变成二维数组，便于计算
    v_vec = target_vector.reshape(1, -1)
    similarity = cosine_similarity(u_vec_reshaped, v_vec)[0][0]  # 计算余弦相似度
    results.append((user_id, similarity))  # 保存用户ID和相似度

# 步骤8：将结果整理成DataFrame，并按相似度排序
result_df = pd.DataFrame(results, columns=["user_id", f"similarity_to_{target_game}"])
result_df = result_df.sort_values(by=f"similarity_to_{target_game}", ascending=False)

# 步骤9：输出最可能喜欢目标游戏的前5位用户
print(f"\n最可能喜欢{target_game}的前5位用户：")
print(result_df.head())

# 总结：
# 1. 读取用户评价和游戏描述数据
# 2. 用OpenAI模型将游戏描述转为向量
# 3. 计算每个用户的兴趣向量（对其评价过的游戏向量取平均）
# 4. 计算每个用户与目标游戏的相似度
# 5. 输出最有可能喜欢目标游戏的用户
