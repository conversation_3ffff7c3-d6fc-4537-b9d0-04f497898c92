import camelot
import pandas as pd

# Camelot 是一个用于从 PDF 文件中提取表格数据的 Python 库。它能够自动检测和解析 PDF 中的表格，并将其转换为 pandas DataFrame，方便后续的数据分析和处理。
# 
# Camelot 支持两种解析模式：'lattice'（基于线条的表格检测，适用于有明确表格边框的 PDF）和 'stream'（基于文本流的表格检测，适用于无边框的表格）。
# 
# Ghostscript 是一个用于处理 PDF 和 PostScript 文件的第三方软件。Camelot 在解析某些 PDF 文件时，尤其是使用 'lattice' 模式时，需要依赖 Ghostscript 来将 PDF 页面渲染为图片，从而更好地检测表格的边界和结构。
# 
# 简单来说，Camelot 负责表格的识别和数据提取，而 Ghostscript 作为底层工具，辅助 Camelot 进行 PDF 的渲染和处理。没有安装 Ghostscript 时，Camelot 的部分功能（如 'lattice' 模式）可能无法正常使用。
# 
# 总结：
# - Camelot：Python 库，负责 PDF 表格的检测与提取。
# - Ghostscript：外部依赖，辅助 Camelot 进行 PDF 渲染，提升表格检测的准确性。

# Camelot 检测和提取 PDF 表格的原理如下：
# 1. 预处理：Camelot 首先对 PDF 页面进行解析，将页面内容（文本、线条、图片等）提取出来。对于 'lattice' 模式，会将页面渲染为图片，检测出所有的直线（水平和垂直线），用于后续的表格结构分析。
# 2. 表格结构检测：
#    - 'lattice' 模式：通过检测页面中的线条，推断出表格的网格结构（单元格的边界），适用于有明显边框的表格。
#    - 'stream' 模式：通过分析文本块的相对位置（如行间距、列间距），推断出表格的行和列，适用于无边框的表格。
# 3. 单元格分割：根据检测到的表格结构，将表格区域划分为若干单元格，并提取每个单元格中的文本内容。
# 4. 数据整理：将提取到的表格数据组织成 pandas DataFrame，方便后续的数据处理和分析。
# 5. 多表格支持：Camelot 可以在同一页或多页中检测和提取多个表格。
# 
# 总结：Camelot 通过分析 PDF 页面中的线条或文本布局，自动检测表格结构，并将表格内容提取为结构化数据。'lattice' 模式依赖于边框线条，'stream' 模式依赖于文本对齐和间距。

pdf_path = "C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/billionaires_page-1-5.pdf"
import time
start_time = time.time()
tables = camelot.read_pdf(pdf_path, pages = "all")
end_time = time.time()
print(f"提取表格时间: {end_time - start_time}秒")

# 转换所有表格为DataFrame
if tables:
    # 遍历所有表格
    for i, table in enumerate(tables, 1):
        # 将表格转化为 DataFrame
        df = table.df

        # 打印当前表格数据
        print(f"表格{i}数据:")
        print(df)

        # 显示基本信息
        print(f"表格{i}基本信息:")
        print(df.info())


        # 保存到csv文件中
        csv_path = f"C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/billionaires_page-{i}.csv"
        df.to_csv(csv_path, index=False)
        print(f"表格{i}已保存到{csv_path}")