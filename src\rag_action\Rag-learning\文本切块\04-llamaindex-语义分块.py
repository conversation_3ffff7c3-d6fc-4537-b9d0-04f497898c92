from llama_index.core import SimpleDirectoryReader
from llama_index.core.node_parser import (
    SentenceSplitter,
    SemanticSplitterNodeParser,
)
from llama_index.embeddings.openai import OpenAIEmbedding 
# from llama_index.embeddings.huggingface import HuggingFaceEmbedding 
# embed_model = HuggingFaceEmbedding(model_name="BAAI/bge-small-zh")


# 配置 OpenAI 嵌入模型（用于文本向量化）
embed_model = OpenAIEmbedding(
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    api_base="https://api.zhizengzeng.com/v1",
    model="text-embedding-3-small"
)

documents = SimpleDirectoryReader(input_files=["C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/黑悟空wiki.txt"]).load_data()

# 创建语义分块器
splitter = SemanticSplitterNodeParser(
    buffer_size=3,  # 缓冲区大小
    breakpoint_percentile_threshold=90, # 断点百分位阈值
    embed_model=embed_model # 使用的嵌入模型
)
# 创建基础句子分块器（作为对照）
base_splitter = SentenceSplitter(
    # chunk_size=512
)


'''
简单来说：

buffer_size 就是“每几句话合成一组”来判断内容是不是变了。比如设成3，就是每3句话看成一组，组和组之间比一比，看看内容差别大不大。

breakpoint_percentile_threshold 就是“分段的敏感度”。数值小，分得细（容易切分，块多）；数值大，分得粗（不容易切分，块少）。

举例：
- buffer_size=2，breakpoint_percentile_threshold=90：每2句话一组，只要组和组之间有90%以上的不一样，就切开，块会比较多。
- buffer_size=3，breakpoint_percentile_threshold=98：每3句话一组，只有组和组之间非常不一样（98%），才切开，块会比较少。

你可以理解为：
- buffer_size 决定“每次比较多大一段”
- breakpoint_percentile_threshold 决定“多不一样才算要分段”
'''


# 使用语义分块器对文档进行分块
semantic_nodes = splitter.get_nodes_from_documents(documents)
print("\n=== 语义分块结果 ===")
print(f"语义分块器生成的块数：{len(semantic_nodes)}")
for i, node in enumerate(semantic_nodes, 1):
    print(f"\n--- 第 {i} 个语义块 ---")
    print(f"内容:\n{node.text}")
    print("-" * 50)

# 使用基础句子分块器对文档进行分块
base_nodes = base_splitter.get_nodes_from_documents(documents)
print("\n=== 基础句子分块结果 ===")
print(f"基础句子分块器生成的块数：{len(base_nodes)}")
for i, node in enumerate(base_nodes, 1):
    print(f"\n--- 第 {i} 个句子块 ---")
    print(f"内容:\n{node.text}")
    print("-" * 50)