<template>
  <div class="notes-app">
    <!-- 顶部导航栏 -->
    <header class="app-header">
      <div class="header-left">
        <h1 class="app-title">
          <el-icon><Document /></el-icon>
          AI笔记系统
        </h1>
      </div>

      <div class="header-center">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索笔记内容..."
          class="search-input"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建笔记
        </el-button>
        <el-button @click="showUploadDialog = true">
          <el-icon><Upload /></el-icon>
          上传文件
        </el-button>
        <el-button @click="handleQueryClick">
          <el-icon><ChatDotRound /></el-icon>
          智能问答
        </el-button>
      </div>
    </header>

    <!-- 主内容区域 -->
    <div class="app-main">
      <!-- 左侧边栏 -->
      <aside class="app-sidebar">
        <!-- 标签筛选 -->
        <div class="sidebar-section">
          <h3 class="section-title">标签筛选</h3>
          <div class="tags-filter">
            <el-tag
              v-for="tag in availableTags"
              :key="tag.id"
              :type="selectedTags.includes(tag.name) ? 'primary' : 'info'"
              :effect="selectedTags.includes(tag.name) ? 'dark' : 'plain'"
              class="tag-item"
              @click="toggleTag(tag.name)"
            >
              {{ tag.name }}
            </el-tag>
          </div>
        </div>

        <!-- 分类导航 -->
        <div class="sidebar-section">
          <h3 class="section-title">文档类型</h3>
          <el-menu
            v-model="selectedSourceType"
            class="source-menu"
            @select="handleSourceTypeChange"
          >
            <el-menu-item index="">
              <el-icon><FolderOpened /></el-icon>
              全部文档
            </el-menu-item>
            <el-menu-item index="manual">
              <el-icon><Edit /></el-icon>
              手动创建
            </el-menu-item>
            <el-menu-item index="pdf">
              <el-icon><Document /></el-icon>
              PDF文档
            </el-menu-item>
            <el-menu-item index="markdown">
              <el-icon><Document /></el-icon>
              Markdown
            </el-menu-item>
          </el-menu>
        </div>

        <!-- 笔记列表 -->
        <div class="sidebar-section notes-list-section">
          <h3 class="section-title">
            笔记列表 ({{ filteredNotes.length }})
            <el-button
              size="small"
              text
              @click="refreshNotes"
              :loading="loading"
            >
              <el-icon><Refresh /></el-icon>
            </el-button>
          </h3>

          <div class="notes-list" v-loading="loading">
            <div
              v-for="note in filteredNotes"
              :key="note.id"
              class="note-item"
              :class="{ active: selectedNote?.id === note.id }"
              @click="selectNote(note)"
            >
              <div class="note-header">
                <h4 class="note-title">{{ note.title }}</h4>
                <span class="note-source">{{ getSourceTypeLabel(note.source_type) }}</span>
              </div>
              <p class="note-preview">{{ getContentPreview(note.content_preview || note.content) }}</p>
              <div class="note-meta">
                <span class="note-date">{{ formatDate(note.created_at) }}</span>
                <div class="note-tags">
                  <el-tag
                    v-for="tag in note.tags"
                    :key="tag.id"
                    size="small"
                    type="info"
                  >
                    {{ tag.name }}
                  </el-tag>
                </div>
              </div>
            </div>

            <el-empty v-if="!loading && filteredNotes.length === 0" description="暂无笔记" />
          </div>
        </div>
      </aside>
        <div class="stat-icon size">
          <el-icon><FolderOpened /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatFileSize(stats?.total_size || 0) }}</div>
          <div class="stat-label">存储空间</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon system">
          <el-icon><Monitor /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value" :class="systemStatusClass">{{ systemStatusText }}</div>
          <div class="stat-label">系统状态</div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-grid">
      <!-- 笔记来源分布 -->
      <div class="chart-card">
        <div class="card-header">
          <h3>笔记来源分布</h3>
        </div>
        <div class="chart-content">
          <div v-if="sourceTypeData.length > 0" class="source-chart">
            <div
              v-for="item in sourceTypeData"
              :key="item.type"
              class="source-item"
            >
              <div class="source-info">
                <span class="source-type">{{ getSourceTypeLabel(item.type) }}</span>
                <span class="source-count">{{ item.count }}</span>
              </div>
              <div class="source-bar">
                <div
                  class="source-progress"
                  :style="{ width: `${(item.count / maxSourceCount) * 100}%` }"
                ></div>
              </div>
            </div>
          </div>
          <el-empty v-else description="暂无数据" />
        </div>
      </div>

      <!-- 月度趋势 -->
      <div class="chart-card">
        <div class="card-header">
          <h3>月度新增趋势</h3>
        </div>
        <div class="chart-content">
          <div v-if="monthlyData.length > 0" class="monthly-chart">
            <div
              v-for="item in monthlyData"
              :key="item.month"
              class="monthly-item"
            >
              <div class="monthly-bar">
                <div
                  class="monthly-progress"
                  :style="{ height: `${(item.count / maxMonthlyCount) * 100}%` }"
                ></div>
              </div>
              <div class="monthly-label">{{ item.month }}</div>
              <div class="monthly-count">{{ item.count }}</div>
            </div>
          </div>
          <el-empty v-else description="暂无数据" />
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <div class="action-card">
        <div class="action-icon">
          <el-icon><Upload /></el-icon>
        </div>
        <div class="action-content">
          <h4>上传文档</h4>
          <p>快速上传PDF或Markdown文档</p>
          <el-button type="primary" @click="$router.push('/upload')">
            立即上传
          </el-button>
        </div>
      </div>

      <div class="action-card">
        <div class="action-icon">
          <el-icon><ChatDotRound /></el-icon>
        </div>
        <div class="action-content">
          <h4>智能问答</h4>
          <p>基于已有笔记进行智能问答</p>
          <el-button type="primary" @click="handleQueryClick">
            开始问答
          </el-button>
        </div>
      </div>

      <div class="action-card">
        <div class="action-icon">
          <el-icon><Search /></el-icon>
        </div>
        <div class="action-content">
          <h4>搜索笔记</h4>
          <p>快速查找和管理您的笔记</p>
          <el-button type="primary" @click="$router.push('/notes')">
            浏览笔记
          </el-button>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity">
      <div class="activity-header">
        <h3>最近活动</h3>
        <el-button type="text" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      
      <div class="activity-list">
        <div
          v-for="activity in recentActivities"
          :key="activity.id"
          class="activity-item"
        >
          <div class="activity-icon" :class="activity.type">
            <el-icon>
              <component :is="getActivityIcon(activity.type)" />
            </el-icon>
          </div>
          <div class="activity-content">
            <div class="activity-title">{{ activity.title }}</div>
            <div class="activity-time">{{ timeAgo(activity.timestamp) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useNotesStore, useSystemStore, useAppStore } from '@/stores'
import { formatFileSize, timeAgo } from '@/utils'
import {
  Document,
  PriceTag,
  FolderOpened,
  Monitor,
  Upload,
  ChatDotRound,
  Search,
  Refresh,
  Plus,
  Edit,
  Delete
} from '@element-plus/icons-vue'

const notesStore = useNotesStore()
const systemStore = useSystemStore()
const appStore = useAppStore()

const loading = ref(false)

// 计算属性
const stats = computed(() => notesStore.stats)
const systemStatus = computed(() => systemStore.systemStatus)

const systemStatusText = computed(() => {
  const statusMap = {
    healthy: '正常',
    degraded: '降级',
    unhealthy: '异常',
    unknown: '未知'
  }
  return statusMap[systemStatus.value] || '未知'
})

const systemStatusClass = computed(() => {
  const classMap = {
    healthy: 'text-success',
    degraded: 'text-warning',
    unhealthy: 'text-danger',
    unknown: 'text-info'
  }
  return classMap[systemStatus.value] || 'text-info'
})

// 来源类型数据
const sourceTypeData = computed(() => {
  if (!stats.value?.by_source_type) return []
  
  return Object.entries(stats.value.by_source_type).map(([type, count]) => ({
    type,
    count
  }))
})

const maxSourceCount = computed(() => {
  return Math.max(...sourceTypeData.value.map(item => item.count), 1)
})

// 月度数据
const monthlyData = computed(() => {
  return stats.value?.by_month?.slice(-6) || []
})

const maxMonthlyCount = computed(() => {
  return Math.max(...monthlyData.value.map(item => item.count), 1)
})

// 最近活动（模拟数据）
const recentActivities = ref([
  {
    id: 1,
    type: 'upload',
    title: '上传了新文档 "AI技术发展报告.pdf"',
    timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString()
  },
  {
    id: 2,
    type: 'query',
    title: '进行了智能问答查询',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString()
  },
  {
    id: 3,
    type: 'edit',
    title: '编辑了笔记 "机器学习基础"',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString()
  }
])

// 方法
const getSourceTypeLabel = (type: string) => {
  const labelMap = {
    pdf: 'PDF文档',
    markdown: 'Markdown',
    manual: '手动创建'
  }
  return labelMap[type] || type
}

const getActivityIcon = (type: string) => {
  const iconMap = {
    upload: Upload,
    query: ChatDotRound,
    edit: Edit,
    delete: Delete,
    create: Plus
  }
  return iconMap[type] || Document
}

// 智能问答点击处理
const handleQueryClick = () => {
  console.log('🔥 智能问答按钮被点击 - 使用新的嵌入模式')
  appStore.enterQueryMode()
}

const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      notesStore.fetchStats(),
      systemStore.fetchHealth()
    ])
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.dashboard {
  padding: var(--spacing-lg);
  
  .dashboard-header {
    margin-bottom: var(--spacing-xl);
    
    .page-title {
      font-size: 28px;
      font-weight: var(--font-weight-bold);
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
    }
    
    .page-subtitle {
      color: var(--text-secondary);
      font-size: var(--font-size-lg);
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    
    .stat-card {
      background: var(--bg-color);
      border-radius: var(--border-radius-base);
      padding: var(--spacing-lg);
      box-shadow: var(--shadow-base);
      display: flex;
      align-items: center;
      transition: var(--transition-base);
      
      &:hover {
        box-shadow: var(--shadow-light);
        transform: translateY(-2px);
      }
      
      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        margin-right: var(--spacing-md);
        
        &.notes {
          background: rgba(64, 158, 255, 0.1);
          color: var(--primary-color);
        }
        
        &.tags {
          background: rgba(103, 194, 58, 0.1);
          color: var(--success-color);
        }
        
        &.size {
          background: rgba(230, 162, 60, 0.1);
          color: var(--warning-color);
        }
        
        &.system {
          background: rgba(245, 108, 108, 0.1);
          color: var(--danger-color);
        }
      }
      
      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: var(--font-weight-bold);
          color: var(--text-primary);
          margin-bottom: 4px;
        }
        
        .stat-label {
          color: var(--text-secondary);
          font-size: var(--font-size-sm);
        }
      }
    }
  }
  
  .charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    
    .chart-card {
      background: var(--bg-color);
      border-radius: var(--border-radius-base);
      box-shadow: var(--shadow-base);
      overflow: hidden;
      
      .card-header {
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--border-lighter);
        
        h3 {
          font-size: var(--font-size-lg);
          font-weight: var(--font-weight-medium);
          color: var(--text-primary);
        }
      }
      
      .chart-content {
        padding: var(--spacing-lg);
        height: 300px;
        
        .source-chart {
          .source-item {
            margin-bottom: var(--spacing-md);
            
            .source-info {
              display: flex;
              justify-content: space-between;
              margin-bottom: var(--spacing-sm);
              
              .source-type {
                color: var(--text-primary);
                font-weight: var(--font-weight-medium);
              }
              
              .source-count {
                color: var(--text-secondary);
              }
            }
            
            .source-bar {
              height: 8px;
              background: var(--border-extra-light);
              border-radius: 4px;
              overflow: hidden;
              
              .source-progress {
                height: 100%;
                background: var(--primary-color);
                transition: width 0.3s ease;
              }
            }
          }
        }
        
        .monthly-chart {
          display: flex;
          align-items: end;
          justify-content: space-around;
          height: 200px;
          
          .monthly-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            
            .monthly-bar {
              width: 40px;
              height: 150px;
              background: var(--border-extra-light);
              border-radius: 4px;
              display: flex;
              align-items: end;
              overflow: hidden;
              margin-bottom: var(--spacing-sm);
              
              .monthly-progress {
                width: 100%;
                background: var(--success-color);
                transition: height 0.3s ease;
                border-radius: 4px;
              }
            }
            
            .monthly-label {
              font-size: var(--font-size-sm);
              color: var(--text-secondary);
              margin-bottom: 2px;
            }
            
            .monthly-count {
              font-size: var(--font-size-sm);
              font-weight: var(--font-weight-medium);
              color: var(--text-primary);
            }
          }
        }
      }
    }
  }
  
  .quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    
    .action-card {
      background: var(--bg-color);
      border-radius: var(--border-radius-base);
      padding: var(--spacing-lg);
      box-shadow: var(--shadow-base);
      display: flex;
      align-items: center;
      transition: var(--transition-base);
      
      &:hover {
        box-shadow: var(--shadow-light);
      }
      
      .action-icon {
        width: 50px;
        height: 50px;
        border-radius: var(--border-radius-base);
        background: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        margin-right: var(--spacing-md);
      }
      
      .action-content {
        flex: 1;
        
        h4 {
          font-size: var(--font-size-lg);
          font-weight: var(--font-weight-medium);
          color: var(--text-primary);
          margin-bottom: var(--spacing-sm);
        }
        
        p {
          color: var(--text-secondary);
          margin-bottom: var(--spacing-md);
        }
      }
    }
  }
  
  .recent-activity {
    background: var(--bg-color);
    border-radius: var(--border-radius-base);
    box-shadow: var(--shadow-base);
    overflow: hidden;
    
    .activity-header {
      padding: var(--spacing-lg);
      border-bottom: 1px solid var(--border-lighter);
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h3 {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-medium);
        color: var(--text-primary);
      }
    }
    
    .activity-list {
      padding: var(--spacing-lg);
      
      .activity-item {
        display: flex;
        align-items: center;
        padding: var(--spacing-md) 0;
        border-bottom: 1px solid var(--border-extra-light);
        
        &:last-child {
          border-bottom: none;
        }
        
        .activity-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: var(--spacing-md);
          
          &.upload {
            background: rgba(64, 158, 255, 0.1);
            color: var(--primary-color);
          }
          
          &.query {
            background: rgba(103, 194, 58, 0.1);
            color: var(--success-color);
          }
          
          &.edit {
            background: rgba(230, 162, 60, 0.1);
            color: var(--warning-color);
          }
        }
        
        .activity-content {
          .activity-title {
            color: var(--text-primary);
            margin-bottom: 2px;
          }
          
          .activity-time {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
          }
        }
      }
    }
  }
}

@include respond-below(md) {
  .dashboard {
    padding: var(--spacing-md);
    
    .stats-grid {
      grid-template-columns: 1fr;
    }
    
    .charts-grid {
      grid-template-columns: 1fr;
    }
    
    .quick-actions {
      grid-template-columns: 1fr;
    }
  }
}
</style>
