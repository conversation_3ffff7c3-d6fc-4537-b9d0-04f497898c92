#!/usr/bin/env python3
"""
LangChain LCEL集成项目完整验证脚本
验证所有已实现的核心模块功能

运行方式：
python test_all_modules.py
"""
import asyncio
import time
import logging
from typing import Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_langchain_rag_service():
    """测试LangChain RAG服务核心功能"""
    print("🧪 测试LangChain RAG服务")
    print("-" * 50)
    
    try:
        from src.rag_action.service.langchain_rag_service import LangChainRAGService
        
        # 创建服务实例
        service = LangChainRAGService()
        print("✅ LangChain RAG服务实例创建成功")
        
        # 检查LCEL组件
        if hasattr(service, 'rag_chain') and service.rag_chain:
            print("✅ LCEL RAG链初始化成功")
        else:
            print("⚠️ LCEL RAG链未初始化")
        
        # 检查流式组件
        if hasattr(service, 'streaming_chain') and service.streaming_chain:
            print("✅ 流式处理链初始化成功")
        else:
            print("⚠️ 流式处理链未初始化")
        
        return True
        
    except Exception as e:
        print(f"❌ LangChain RAG服务测试失败: {e}")
        return False

async def test_retriever_components():
    """测试Retriever组件"""
    print("\n🔍 测试Retriever组件")
    print("-" * 50)
    
    try:
        from src.rag_action.service.langchain_rag_service import LangChainRAGService
        
        service = LangChainRAGService()
        
        # 获取Retriever状态
        retriever_info = service.get_retriever_info()
        
        print(f"📊 Retriever状态:")
        print(f"  - LangChain可用: {retriever_info['langchain_retrievers_available']}")
        print(f"  - 使用LangChain: {retriever_info['use_langchain_retrievers']}")
        print(f"  - Milvus: {'✅' if retriever_info['retrievers']['milvus'] else '❌'}")
        print(f"  - BM25: {'✅' if retriever_info['retrievers']['bm25'] else '❌'}")
        print(f"  - 混合检索: {'✅' if retriever_info['retrievers']['ensemble'] else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Retriever组件测试失败: {e}")
        return False

async def test_memory_components():
    """测试Memory组件"""
    print("\n🧠 测试Memory组件")
    print("-" * 50)
    
    try:
        from src.rag_action.service.langchain_rag_service import LangChainRAGService
        
        service = LangChainRAGService()
        
        # 获取Memory状态
        memory_info = service.get_memory_info()
        
        print(f"📊 Memory状态:")
        print(f"  - LangChain可用: {memory_info['langchain_memory_available']}")
        print(f"  - 使用LangChain: {memory_info['use_langchain_memory']}")
        print(f"  - 摘要记忆: {'✅' if memory_info['memory_components']['summary_memory'] else '❌'}")
        print(f"  - 向量记忆: {'✅' if memory_info['memory_components']['vector_memory'] else '❌'}")
        print(f"  - 对话缓冲: {'✅' if memory_info['memory_components']['conversation_memory'] else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Memory组件测试失败: {e}")
        return False

async def test_agent_components():
    """测试Agent组件"""
    print("\n🤖 测试Agent组件")
    print("-" * 50)
    
    try:
        from src.rag_action.service.langchain_rag_service import LangChainRAGService
        
        service = LangChainRAGService()
        
        # 获取Agent状态
        agent_info = service.get_agent_info()
        
        print(f"📊 Agent状态:")
        print(f"  - LangChain可用: {agent_info['langchain_agent_available']}")
        print(f"  - 使用LangChain: {agent_info['use_langchain_agent']}")
        print(f"  - Agent执行器: {'✅' if agent_info['agent_components']['agent_executor'] else '❌'}")
        print(f"  - 工具数量: {agent_info['agent_components']['tools_count']}")
        print(f"  - 可用工具: {', '.join(agent_info['agent_components']['tools_list'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent组件测试失败: {e}")
        return False

async def test_compatibility_adapter():
    """测试兼容性适配器"""
    print("\n🔄 测试兼容性适配器")
    print("-" * 50)
    
    try:
        from src.rag_action.service.rag_service_adapter import get_rag_service
        
        # 获取RAG服务实例
        rag_service = get_rag_service()
        print("✅ 兼容性适配器创建成功")
        
        # 检查服务类型
        service_type = type(rag_service).__name__
        print(f"📋 服务类型: {service_type}")
        
        # 检查关键方法是否存在
        required_methods = ['answer_question', 'answer_question_stream']
        for method in required_methods:
            if hasattr(rag_service, method):
                print(f"✅ 方法 {method} 可用")
            else:
                print(f"❌ 方法 {method} 不可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 兼容性适配器测试失败: {e}")
        return False

async def test_documentation_completeness():
    """测试文档完整性"""
    print("\n📚 测试文档完整性")
    print("-" * 50)
    
    import os
    
    required_docs = [
        "docs/langchain_lcel_integration_guide.md",
        "docs/migration_guide.md", 
        "docs/project_summary.md",
        "docs/complete_implementation_summary.md"
    ]
    
    doc_count = 0
    for doc_path in required_docs:
        if os.path.exists(doc_path):
            print(f"✅ {doc_path}")
            doc_count += 1
        else:
            print(f"❌ {doc_path} 缺失")
    
    print(f"📊 文档完整性: {doc_count}/{len(required_docs)} ({doc_count/len(required_docs)*100:.0f}%)")
    
    return doc_count == len(required_docs)

async def test_test_files_completeness():
    """测试测试文件完整性"""
    print("\n🧪 测试测试文件完整性")
    print("-" * 50)
    
    import os
    
    required_tests = [
        "tests/test_langchain_lcel_integration.py",
        "tests/test_langchain_retriever_module.py",
        "tests/test_langchain_memory_module.py",
        "tests/test_langchain_agent_module.py"
    ]
    
    test_count = 0
    for test_path in required_tests:
        if os.path.exists(test_path):
            print(f"✅ {test_path}")
            test_count += 1
        else:
            print(f"❌ {test_path} 缺失")
    
    print(f"📊 测试文件完整性: {test_count}/{len(required_tests)} ({test_count/len(required_tests)*100:.0f}%)")
    
    return test_count == len(required_tests)

async def test_example_files():
    """测试示例文件"""
    print("\n📝 测试示例文件")
    print("-" * 50)
    
    import os
    
    example_files = [
        "examples/langchain_lcel_usage_example.py"
    ]
    
    example_count = 0
    for example_path in example_files:
        if os.path.exists(example_path):
            print(f"✅ {example_path}")
            example_count += 1
        else:
            print(f"❌ {example_path} 缺失")
    
    print(f"📊 示例文件完整性: {example_count}/{len(example_files)} ({example_count/len(example_files)*100:.0f}%)")
    
    return example_count == len(example_files)

async def main():
    """主测试函数"""
    print("🎉 LangChain LCEL集成项目完整验证")
    print("=" * 80)
    
    # 运行所有测试
    tests = [
        ("LangChain RAG服务", test_langchain_rag_service),
        ("Retriever组件", test_retriever_components),
        ("Memory组件", test_memory_components),
        ("Agent组件", test_agent_components),
        ("兼容性适配器", test_compatibility_adapter),
        ("文档完整性", test_documentation_completeness),
        ("测试文件完整性", test_test_files_completeness),
        ("示例文件", test_example_files)
    ]
    
    results = []
    start_time = time.time()
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 80)
    print("📊 完整验证结果总结")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name:<20}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.0f}%)")
    print(f"⏱️ 总验证时间: {time.time() - start_time:.2f}s")
    
    if passed == total:
        print("\n🎊 恭喜！所有模块验证通过！")
        print("🚀 LangChain LCEL集成项目实施成功！")
        print("\n核心成就:")
        print("  ✅ 7个核心模块100%完成")
        print("  ✅ 完整的测试和文档体系")
        print("  ✅ 向后兼容性保证")
        print("  ✅ 现代化的AI应用架构")
    else:
        print(f"\n⚠️ {total-passed}个模块需要检查")
        print("请查看上述详细信息进行修复")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    asyncio.run(main())
