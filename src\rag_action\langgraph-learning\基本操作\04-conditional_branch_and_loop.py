"""
04 - 条件分支与循环
展示复杂的条件分支和循环控制流
"""

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from typing import Dict, List, Any, TypedDict, Annotated

# 创建 LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0
)

# 定义状态类型
class ComplexState(TypedDict):
    messages: Annotated[List, add_messages]  # 消息历史
    user_input: str  # 用户输入
    question_type: str  # 问题类型
    complexity: str  # 复杂度
    iteration_count: int  # 迭代次数
    answer: str  # 最终回答
    path_taken: List[str]  # 执行路径历史

def complex_workflow():
    """复杂工作流：条件分支 + 循环优化"""
    
    print("=== 条件分支与循环控制流 ===")
    print("流程：输入 -> 分类 -> 复杂度判断 -> 分支处理 -> 循环优化 -> 输出")
    print()
    
    def classify_node(state: ComplexState) -> ComplexState:
        """分类节点：判断问题类型"""
        user_input = state["user_input"]
        
        print(f"步骤1: 分析问题类型...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
请分析以下问题，判断它属于哪种类型：

问题：{user_input}

请从以下类型中选择一个：
1. "technical" - 技术问题（编程、开发、技术架构等）
2. "business" - 商业问题（营销、市场、商业模式等）
3. "learning" - 学习问题（教育、培训、学习方法等）
4. "general" - 一般问题（日常、生活、闲聊等）

请只返回类型名称（technical/business/learning/general）：
""")
        ])
        
        question_type = response.content.strip().lower()
        
        return {
            **state,
            "question_type": question_type,
            "path_taken": state["path_taken"] + ["分类完成"],
            "messages": [AIMessage(content=f"问题类型：{question_type}")]
        }
    
    def complexity_node(state: ComplexState) -> ComplexState:
        """复杂度判断节点：判断问题的复杂程度"""
        user_input = state["user_input"]
        
        print(f"步骤2: 判断问题复杂度...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
请判断以下问题的复杂程度：

问题：{user_input}

请从以下复杂度中选择一个：
1. "simple" - 简单问题（可以用几句话回答）
2. "medium" - 中等问题（需要详细解释）
3. "complex" - 复杂问题（需要深入分析和多角度回答）

请只返回复杂度（simple/medium/complex）：
""")
        ])
        
        complexity = response.content.strip().lower()
        
        return {
            **state,
            "complexity": complexity,
            "path_taken": state["path_taken"] + ["复杂度判断完成"],
            "messages": [AIMessage(content=f"复杂度：{complexity}")]
        }
    
    def simple_technical_node(state: ComplexState) -> ComplexState:
        """简单技术问题处理"""
        user_input = state["user_input"]
        
        print(f"步骤3: 简单技术问题处理...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
请简要回答以下技术问题：

问题：{user_input}

请提供简洁的技术回答：
""")
        ])
        
        answer = response.content
        
        return {
            **state,
            "answer": answer,
            "path_taken": state["path_taken"] + ["简单技术处理"],
            "messages": [AIMessage(content=f"简单技术回答：{answer}")]
        }
    
    def complex_technical_node(state: ComplexState) -> ComplexState:
        """复杂技术问题处理"""
        user_input = state["user_input"]
        
        print(f"步骤3: 复杂技术问题处理...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
请详细回答以下复杂技术问题：

问题：{user_input}

请提供全面的技术分析，包括：
1. 技术背景
2. 详细原理
3. 实现方案
4. 最佳实践
5. 相关工具和框架
6. 注意事项
""")
        ])
        
        answer = response.content
        
        return {
            **state,
            "answer": answer,
            "path_taken": state["path_taken"] + ["复杂技术处理"],
            "messages": [AIMessage(content=f"复杂技术回答：{answer}")]
        }
    
    def business_node(state: ComplexState) -> ComplexState:
        """商业问题处理"""
        user_input = state["user_input"]
        complexity = state["complexity"]
        
        print(f"步骤3: 商业问题处理...")
        
        if complexity == "simple":
            prompt = f"请简要回答以下商业问题：\n\n问题：{user_input}\n\n请提供简洁的商业建议："
        else:
            prompt = f"""
请详细回答以下商业问题：

问题：{user_input}

请提供全面的商业分析，包括：
1. 市场分析
2. 竞争环境
3. 策略建议
4. 风险评估
5. 实施步骤
6. 成功指标
"""
        
        response = llm.invoke([HumanMessage(content=prompt)])
        answer = response.content
        
        return {
            **state,
            "answer": answer,
            "path_taken": state["path_taken"] + ["商业处理"],
            "messages": [AIMessage(content=f"商业回答：{answer}")]
        }
    
    def learning_node(state: ComplexState) -> ComplexState:
        """学习问题处理"""
        user_input = state["user_input"]
        complexity = state["complexity"]
        
        print(f"步骤3: 学习问题处理...")
        
        if complexity == "simple":
            prompt = f"请简要回答以下学习问题：\n\n问题：{user_input}\n\n请提供简洁的学习建议："
        else:
            prompt = f"""
请详细回答以下学习问题：

问题：{user_input}

请提供全面的学习指导，包括：
1. 学习目标设定
2. 学习方法推荐
3. 学习资源列表
4. 学习计划制定
5. 学习进度跟踪
6. 学习效果评估
"""
        
        response = llm.invoke([HumanMessage(content=prompt)])
        answer = response.content
        
        return {
            **state,
            "answer": answer,
            "path_taken": state["path_taken"] + ["学习处理"],
            "messages": [AIMessage(content=f"学习回答：{answer}")]
        }
    
    def general_node(state: ComplexState) -> ComplexState:
        """一般问题处理"""
        user_input = state["user_input"]
        
        print(f"步骤3: 一般问题处理...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
请回答以下问题：

问题：{user_input}

请提供友好、有用的回答：
""")
        ])
        
        answer = response.content
        
        return {
            **state,
            "answer": answer,
            "path_taken": state["path_taken"] + ["一般处理"],
            "messages": [AIMessage(content=f"一般回答：{answer}")]
        }
    
    def optimize_node(state: ComplexState) -> ComplexState:
        """优化节点：循环优化回答质量"""
        user_input = state["user_input"]
        current_answer = state["answer"]
        iteration_count = state["iteration_count"]
        
        print(f"步骤4: 优化回答 (第{iteration_count + 1}次)...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
请优化以下回答，使其更加清晰、准确、有用：

原始问题：{user_input}
当前回答：{current_answer}

请提供优化后的回答：
""")
        ])
        
        optimized_answer = response.content
        
        return {
            **state,
            "answer": optimized_answer,
            "iteration_count": iteration_count + 1,
            "path_taken": state["path_taken"] + [f"优化{iteration_count + 1}"],
            "messages": [AIMessage(content=f"优化回答：{optimized_answer}")]
        }
    
    def route_by_type_and_complexity(state: ComplexState) -> str:
        """根据类型和复杂度路由"""
        question_type = state["question_type"]
        complexity = state["complexity"]
        
        if question_type == "technical":
            if complexity == "simple":
                return "simple_technical"
            else:
                return "complex_technical"
        elif question_type == "business":
            return "business"
        elif question_type == "learning":
            return "learning"
        else:
            return "general"
    
    def should_continue_optimizing(state: ComplexState) -> str:
        """判断是否继续优化"""
        iteration_count = state["iteration_count"]
        complexity = state["complexity"]
        
        # 简单问题最多优化1次，复杂问题最多优化3次
        max_iterations = 1 if complexity == "simple" else 3
        
        if iteration_count >= max_iterations:
            return "end"
        else:
            return "continue"
    
    # 创建图
    workflow = StateGraph(ComplexState)
    
    # 添加节点
    workflow.add_node("classify", classify_node)
    workflow.add_node("complexity", complexity_node)
    workflow.add_node("simple_technical", simple_technical_node)
    workflow.add_node("complex_technical", complex_technical_node)
    workflow.add_node("business", business_node)
    workflow.add_node("learning", learning_node)
    workflow.add_node("general", general_node)
    workflow.add_node("optimize", optimize_node)
    
    # 设置入口点
    workflow.set_entry_point("classify")
    
    # 添加边：classify -> complexity
    workflow.add_edge("classify", "complexity")
    
    # 添加条件边：根据类型和复杂度选择处理路径
    workflow.add_conditional_edges(
        "complexity",
        route_by_type_and_complexity,
        {
            "simple_technical": "simple_technical",
            "complex_technical": "complex_technical",
            "business": "business",
            "learning": "learning",
            "general": "general"
        }
    )
    
    # 所有处理节点都连接到优化节点
    workflow.add_edge("simple_technical", "optimize")
    workflow.add_edge("complex_technical", "optimize")
    workflow.add_edge("business", "optimize")
    workflow.add_edge("learning", "optimize")
    workflow.add_edge("general", "optimize")
    
    # 优化节点可以循环或结束
    workflow.add_conditional_edges(
        "optimize",
        should_continue_optimizing,
        {
            "continue": "optimize",
            "end": END
        }
    )
    
    # 编译图
    app = workflow.compile()
    
    # 测试
    test_inputs = [
        "什么是Python？",  # 简单技术问题
        "如何设计一个高并发的微服务架构？",  # 复杂技术问题
        "制定产品定价策略",  # 商业问题
        "如何学习机器学习？",  # 学习问题
        "今天天气怎么样？"  # 一般问题
    ]
    
    for i, user_input in enumerate(test_inputs, 1):
        print(f"\n测试 {i}: {user_input}")
        print("=" * 60)
        
        # 运行图
        result = app.invoke({
            "user_input": user_input,
            "messages": [],
            "question_type": "",
            "complexity": "",
            "iteration_count": 0,
            "answer": "",
            "path_taken": []
        })
        
        print(f"\n问题类型: {result['question_type']}")
        print(f"复杂度: {result['complexity']}")
        print(f"迭代次数: {result['iteration_count']}")
        print(f"执行路径: {' -> '.join(result['path_taken'])}")
        print(f"最终回答: {result['answer']}")
        print("=" * 60)

def main():
    """主函数"""
    complex_workflow()

if __name__ == "__main__":
    main() 