"""
Milvus 混合检索简化示例 - 使用内置模型避免网络依赖

本示例使用 Milvus 内置的嵌入功能，避免网络下载模型的问题。
适用于网络受限或需要快速验证功能的场景。

作者: RAG Action Team
日期: 2025-08-05
"""

import logging
import time
import tempfile
import os
from typing import List, Dict, Any
import numpy as np
from pymilvus import (
    MilvusClient,
    DataType,
    Function,
    FunctionType,
    AnnSearchRequest,
    WeightedRanker,
    RRFRanker
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SimpleMilvusHybridSearcher:
    """
    简化的 Milvus 混合检索器
    
    使用简单的向量生成方法，避免依赖外部模型下载
    """
    
    def __init__(self,
                 uri: str = "http://localhost:19530",
                 collection_name: str = "simple_hybrid_demo"):
        """
        初始化简化的混合检索器
        
        Args:
            uri: Milvus 数据库文件路径
            collection_name: 集合名称
        """
        self.uri = uri
        self.collection_name = collection_name
        self.client = None
        self.dense_dim = 128  # 使用较小的向量维度
        
        logger.info(f"初始化简化 MilvusHybridSearcher，URI: {uri}")
    
    def connect(self):
        """连接到 Milvus 数据库"""
        try:
            logger.info(f"正在连接到 Milvus 数据库: {self.uri}")
            self.client = MilvusClient(uri=self.uri)
            logger.info("Milvus 数据库连接成功")
        except Exception as e:
            logger.error(f"连接 Milvus 数据库失败: {e}")
            raise
    
    def _simple_text_to_vector(self, text: str) -> List[float]:
        """
        简单的文本向量化方法（仅用于演示）
        
        在实际应用中，应该使用专业的嵌入模型
        """
        # 基于文本内容生成简单的向量表示
        vector = []
        for i in range(self.dense_dim):
            # 使用文本的哈希值和位置生成向量分量
            hash_val = hash(text + str(i)) % 1000000
            vector.append((hash_val / 1000000.0 - 0.5) * 2)  # 归一化到 [-1, 1]
        
        # 归一化向量
        norm = np.linalg.norm(vector)
        if norm > 0:
            vector = [x / norm for x in vector]
        
        return vector
    
    def create_collection(self, drop_existing: bool = True):
        """
        创建支持混合检索的集合
        
        Args:
            drop_existing: 是否删除已存在的同名集合
        """
        try:
            # 检查并删除已存在的集合
            if drop_existing and self.client.has_collection(self.collection_name):
                logger.info(f"删除已存在的集合: {self.collection_name}")
                self.client.drop_collection(self.collection_name)
            
            # 创建集合模式
            schema = self.client.create_schema()
            
            # 添加字段
            schema.add_field(
                field_name="id", 
                datatype=DataType.INT64, 
                is_primary=True, 
                auto_id=True
            )
            schema.add_field(
                field_name="text", 
                datatype=DataType.VARCHAR, 
                max_length=2000, 
                enable_analyzer=True  # 启用文本分析器
            )
            schema.add_field(
                field_name="dense_vector", 
                datatype=DataType.FLOAT_VECTOR, 
                dim=self.dense_dim
            )
            schema.add_field(
                field_name="sparse_vector", 
                datatype=DataType.SPARSE_FLOAT_VECTOR
            )
            
            # 添加 BM25 函数
            bm25_function = Function(
                name="text_bm25_embedding",
                input_field_names=["text"],
                output_field_names=["sparse_vector"],
                function_type=FunctionType.BM25,
            )
            schema.add_function(bm25_function)
            
            # 准备索引参数
            index_params = self.client.prepare_index_params()
            
            # 为密集向量添加索引
            index_params.add_index(
                field_name="dense_vector",
                index_type="AUTOINDEX",
                metric_type="COSINE"
            )
            
            # 为稀疏向量添加索引
            index_params.add_index(
                field_name="sparse_vector",
                index_type="SPARSE_INVERTED_INDEX",
                metric_type="BM25"
            )
            
            # 创建集合
            logger.info(f"正在创建集合: {self.collection_name}")
            self.client.create_collection(
                collection_name=self.collection_name,
                schema=schema,
                index_params=index_params
            )
            
            logger.info(f"集合 {self.collection_name} 创建成功")
            
        except Exception as e:
            logger.error(f"创建集合失败: {e}")
            raise
    
    def insert_data(self, texts: List[str], batch_size: int = 50):
        """
        插入数据
        
        Args:
            texts: 要插入的文本列表
            batch_size: 批处理大小
        """
        try:
            logger.info(f"正在插入 {len(texts)} 条数据...")
            
            # 分批插入数据
            total_inserted = 0
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                
                # 生成密集向量
                batch_dense_vectors = [
                    self._simple_text_to_vector(text) for text in batch_texts
                ]
                
                # 准备批次数据
                batch_data = [
                    {
                        "text": text,
                        "dense_vector": dense_vec
                    }
                    for text, dense_vec in zip(batch_texts, batch_dense_vectors)
                ]
                
                # 插入数据
                result = self.client.insert(
                    collection_name=self.collection_name,
                    data=batch_data
                )
                
                total_inserted += result["insert_count"]
                logger.info(f"已插入 {total_inserted}/{len(texts)} 条数据")
            
            logger.info(f"数据插入完成，总计: {total_inserted} 条")
            
        except Exception as e:
            logger.error(f"插入数据失败: {e}")
            raise
    
    def dense_search(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """执行密集向量检索"""
        try:
            logger.info(f"执行密集向量检索: {query}")
            
            # 生成查询向量
            query_vector = self._simple_text_to_vector(query)
            
            # 执行检索
            results = self.client.search(
                collection_name=self.collection_name,
                data=[query_vector],
                anns_field="dense_vector",
                limit=limit,
                output_fields=["text"],
                search_params={"metric_type": "COSINE"}
            )
            
            return [{"text": hit["entity"]["text"], "score": hit["distance"]} 
                   for hit in results[0]]
            
        except Exception as e:
            logger.error(f"密集向量检索失败: {e}")
            raise
    
    def sparse_search(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """执行稀疏向量检索"""
        try:
            logger.info(f"执行稀疏向量检索: {query}")
            
            # 执行检索
            results = self.client.search(
                collection_name=self.collection_name,
                data=[query],
                anns_field="sparse_vector",
                limit=limit,
                output_fields=["text"],
                search_params={
                    "metric_type": "BM25",
                    "params": {"drop_ratio_search": 0.2}
                }
            )
            
            return [{"text": hit["entity"]["text"], "score": hit["distance"]} 
                   for hit in results[0]]
            
        except Exception as e:
            logger.error(f"稀疏向量检索失败: {e}")
            raise
    
    def hybrid_search(self, 
                     query: str, 
                     limit: int = 10,
                     sparse_weight: float = 0.7,
                     dense_weight: float = 1.0) -> List[Dict[str, Any]]:
        """执行混合检索"""
        try:
            logger.info(f"执行混合检索: {query}")
            
            # 生成查询向量
            query_vector = self._simple_text_to_vector(query)
            
            # 创建密集向量检索请求
            dense_req = AnnSearchRequest(
                data=[query_vector],
                anns_field="dense_vector",
                param={"metric_type": "COSINE"},
                limit=limit
            )
            
            # 创建稀疏向量检索请求
            sparse_req = AnnSearchRequest(
                data=[query],
                anns_field="sparse_vector",
                param={
                    "metric_type": "BM25",
                    "params": {"drop_ratio_search": 0.2}
                },
                limit=limit
            )
            
            # 使用加权重排序器
            reranker = WeightedRanker(sparse_weight, dense_weight)
            
            # 执行混合检索
            results = self.client.hybrid_search(
                collection_name=self.collection_name,
                reqs=[sparse_req, dense_req],
                ranker=reranker,
                limit=limit,
                output_fields=["text"]
            )
            
            return [{"text": hit["entity"]["text"], "score": hit["distance"]} 
                   for hit in results[0]]
            
        except Exception as e:
            logger.error(f"混合检索失败: {e}")
            raise
    
    def get_collection_info(self) -> Dict[str, Any]:
        """获取集合信息"""
        try:
            collections = self.client.list_collections()
            if self.collection_name in collections:
                # 获取集合统计信息
                stats = self.client.get_collection_stats(self.collection_name)
                return {
                    "collection_name": self.collection_name,
                    "exists": True,
                    "stats": stats
                }
            else:
                return {
                    "collection_name": self.collection_name,
                    "exists": False
                }
        except Exception as e:
            logger.error(f"获取集合信息失败: {e}")
            return {"error": str(e)}
    
    def close(self):
        """关闭连接"""
        try:
            logger.info("连接已关闭")
        except Exception as e:
            logger.warning(f"关闭连接时出现警告: {e}")


def main():
    """主函数 - 演示简化的 Milvus 混合检索"""

    try:
        logger.info("=== Milvus 混合检索简化示例开始 ===")
        logger.info("注意：此示例需要运行 Milvus 服务器")
        logger.info("请参考 docker_setup_guide.md 了解如何启动 Milvus")

        # 初始化检索器（连接到本地 Milvus 服务器）
        searcher = SimpleMilvusHybridSearcher(
            uri="http://localhost:19530",
            collection_name="demo_hybrid_search"
        )
        
        # 连接数据库
        searcher.connect()
        
        # 创建集合
        searcher.create_collection()
        
        # 准备示例数据
        sample_data = [
            "人工智能是计算机科学的一个分支，致力于创建智能系统。",
            "机器学习是人工智能的子集，让计算机能够自动学习。",
            "深度学习使用神经网络来模拟人脑的工作方式。",
            "自然语言处理帮助计算机理解和生成人类语言。",
            "计算机视觉使机器能够理解和解释视觉信息。",
            "向量数据库专门用于存储和检索高维向量数据。",
            "Milvus 是一个开源的向量数据库，专为 AI 应用设计。",
            "语义搜索使用向量嵌入来理解查询的含义。",
            "混合检索结合了密集向量和稀疏向量的优势。",
            "RAG 技术结合了信息检索和文本生成。"
        ]
        
        # 插入数据
        searcher.insert_data(sample_data)
        
        # 等待索引完成
        time.sleep(1)
        
        # 获取集合信息
        collection_info = searcher.get_collection_info()
        logger.info(f"集合信息: {collection_info}")
        
        # 执行检索测试
        query = "什么是机器学习？"
        logger.info(f"\n=== 检索查询: {query} ===")
        
        # 1. 密集向量检索
        logger.info("\n--- 密集向量检索结果 ---")
        dense_results = searcher.dense_search(query, limit=3)
        for i, result in enumerate(dense_results, 1):
            logger.info(f"{i}. [分数: {result['score']:.4f}] {result['text']}")
        
        # 2. 稀疏向量检索
        logger.info("\n--- 稀疏向量检索结果 ---")
        sparse_results = searcher.sparse_search(query, limit=3)
        for i, result in enumerate(sparse_results, 1):
            logger.info(f"{i}. [分数: {result['score']:.4f}] {result['text']}")
        
        # 3. 混合检索
        logger.info("\n--- 混合检索结果 ---")
        hybrid_results = searcher.hybrid_search(query, limit=3)
        for i, result in enumerate(hybrid_results, 1):
            logger.info(f"{i}. [分数: {result['score']:.4f}] {result['text']}")
        
        logger.info("\n=== 示例执行完成 ===")
        logger.info("✅ Milvus 混合检索功能验证成功！")
        
    except Exception as e:
        logger.error(f"示例执行失败: {e}")
        raise
    except ConnectionError as e:
        logger.error("❌ 无法连接到 Milvus 服务器")
        logger.error("请确保 Milvus 服务器正在运行在 localhost:19530")
        logger.error("参考 docker_setup_guide.md 了解如何启动 Milvus")
        logger.error(f"错误详情: {e}")
    finally:
        # 清理资源
        if 'searcher' in locals():
            searcher.close()


if __name__ == "__main__":
    main()
