from pymilvus import MilvusClient, DataType, Function, FunctionType
import json

# 1. 设置 Milvus 客户端
client = MilvusClient(uri="http://localhost:19530")
COLLECTION_NAME = "full_text_search_demo_fixed"

# 如果集合已存在，则删除
if client.has_collection(COLLECTION_NAME):
    print(f"删除已存在的集合: {COLLECTION_NAME}")
    client.drop_collection(COLLECTION_NAME)

# 2. 创建 schema
print("\n创建 schema...")
schema = client.create_schema()

# 添加必要的字段
schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True, auto_id=True)
schema.add_field(field_name="text", datatype=DataType.VARCHAR, max_length=1000, enable_analyzer=True)
schema.add_field(field_name="sparse", datatype=DataType.SPARSE_FLOAT_VECTOR)

# 3. 定义 BM25 函数
print("定义 BM25 函数...")
bm25_function = Function(
    name="text_bm25_emb",
    input_field_names=["text"],
    output_field_names=["sparse"],
    function_type=FunctionType.BM25,
)

# 将函数添加到 schema
schema.add_function(bm25_function)

# 4. 配置索引参数
print("配置索引参数...")
index_params = client.prepare_index_params()

index_params.add_index(
    field_name="sparse",
    index_name="sparse_inverted_index",
    index_type="SPARSE_INVERTED_INDEX",
    metric_type="BM25",
    params={
        "inverted_index_algo": "DAAT_MAXSCORE",
        "bm25_k1": 1.2,
        "bm25_b": 0.75
    },
)

# 5. 创建集合
print(f"创建集合: {COLLECTION_NAME}")
client.create_collection(
    collection_name=COLLECTION_NAME,
    schema=schema,
    index_params=index_params
)

# 6. 插入文本数据
print("\n插入文本数据...")
documents = [
    {'text': 'Information retrieval is a field of study.'},
    {'text': 'Information retrieval focuses on finding relevant information in large datasets.'},
    {'text': 'Data mining and information retrieval overlap in research.'},
    {'text': 'Search engines are a typical example of information retrieval systems.'},
    {'text': 'Natural language processing plays an important role in information retrieval.'},
]

insert_result = client.insert(COLLECTION_NAME, documents)
client.flush(collection_name=COLLECTION_NAME)
print(f"插入结果: {insert_result}")

# 7. 加载集合
print("\n加载集合...")
client.load_collection(collection_name=COLLECTION_NAME)

# 8. 执行全文搜索
print("\n=== 全文搜索示例 ===")

# 修复搜索参数
search_params = {
    "metric_type": "BM25",  # 添加度量类型
    "params": {
        "drop_ratio_search": 0.0,  # 不丢弃结果
        "min_score": 0.0  # 最小分数阈值
    }
}

query_text = "information retrieval"
print(f"\n执行搜索，查询文本: {query_text}")
results = client.search(
    collection_name=COLLECTION_NAME,
    data=[query_text],
    anns_field='sparse',
    limit=5,
    search_params=search_params,
    output_fields=["text"]
)

# 修复JSON序列化问题
def convert_hit_to_dict(hit):
    """将Hit对象转换为可序列化的字典"""
    result = {}
    # 复制基本属性
    for attr in ['id', 'distance']:
        if hasattr(hit, attr):
            result[attr] = getattr(hit, attr)
    
    # 处理entity字段
    if hasattr(hit, 'entity') and hit.entity:
        result['entity'] = {}
        for key, value in hit.entity.items():
            result['entity'][key] = value
    
    return result

print("\n搜索结果结构:")
# 安全地序列化结果
try:
    serializable_results = []
    for hits in results:
        serializable_hits = [convert_hit_to_dict(hit) for hit in hits]
        serializable_results.append(serializable_hits)
    
    print(json.dumps(serializable_results, indent=2, ensure_ascii=False))
except Exception as e:
    print(f"序列化失败: {e}")
    print("原始结果类型:", type(results))
    if results:
        print("第一个元素类型:", type(results[0]) if results[0] else "None")

print("\n搜索结果:")
if results and len(results) > 0 and len(results[0]) > 0:
    for i, hits in enumerate(results):
        print(f"\n查询 {i+1} 的结果:")
        for hit in hits:
            # 安全地访问Hit对象属性
            hit_id = getattr(hit, 'id', 'N/A')
            hit_distance = getattr(hit, 'distance', 'N/A')
            print(f"ID: {hit_id}, 分数: {hit_distance:.4f}")
            
            # 安全地访问entity
            if hasattr(hit, 'entity') and hit.entity:
                if hasattr(hit.entity, 'text'):
                    print(f"文本: {hit.entity.text}")
                elif isinstance(hit.entity, dict) and 'text' in hit.entity:
                    print(f"文本: {hit.entity['text']}")
            print("-" * 50)
else:
    print("没有找到匹配的结果")
    
    # 尝试不同的搜索策略
    print("\n尝试不同的搜索策略...")
    
    # 策略1：尝试单个词搜索
    print("\n策略1：尝试单个词搜索")
    results_single = client.search(
        collection_name=COLLECTION_NAME,
        data=["information"],  # 只搜索单个词
        anns_field='sparse',
        limit=5,
        search_params=search_params,
        output_fields=["text"]
    )
    
    if results_single and len(results_single) > 0 and len(results_single[0]) > 0:
        print("单个词搜索结果:")
        for hit in results_single[0]:
            hit_id = getattr(hit, 'id', 'N/A')
            hit_distance = getattr(hit, 'distance', 'N/A')
            print(f"ID: {hit_id}, 分数: {hit_distance:.4f}")
            
            if hasattr(hit, 'entity') and hit.entity:
                if hasattr(hit.entity, 'text'):
                    print(f"文本: {hit.entity.text}")
            print("-" * 30)
    else:
        print("单个词搜索也没有结果")

# 9. 清理
print("\n清理资源...")
client.release_collection(collection_name=COLLECTION_NAME) 