"""
AI笔记系统主应用
基于FastAPI的智能笔记管理系统
"""
import logging
import os
from contextlib import asynccontextmanager
from datetime import datetime
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse

from rag_action.core.config import get_settings
from rag_action.core.database import db_manager
from rag_action.core.service_manager import service_manager
from rag_action.router.rag import router as rag_router
from rag_action.models.schemas import HealthResponse, ErrorResponse

# 配置日志
settings = get_settings()

# 确保日志目录存在
log_file_path = settings.logging.file
if not os.path.isabs(log_file_path):
    # 如果是相对路径，相对于项目根目录
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    log_file_path = os.path.join(project_root, log_file_path)

os.makedirs(os.path.dirname(log_file_path), exist_ok=True)

logging.basicConfig(
    level=getattr(logging, settings.logging.level),
    format=settings.logging.format,
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(log_file_path, encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("正在启动AI笔记系统...")

    try:
        # 初始化数据库
        if not db_manager.init_all():
            logger.error("数据库初始化失败")
            raise Exception("数据库初始化失败")

        # 预加载所有AI服务
        logger.info("开始预加载AI服务...")
        await service_manager.preload_all_services()
        logger.info("AI服务预加载完成")

        logger.info("AI笔记系统启动成功")
        yield

    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        raise
    finally:
        # 关闭时清理
        logger.info("正在关闭AI笔记系统...")


# 创建FastAPI应用
app = FastAPI(
    title=settings.app.title,
    description=settings.app.description,
    version=settings.app.version,
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(rag_router)


@app.get("/", summary="根路径")
async def root():
    """根路径欢迎信息"""
    return {
        "message": "欢迎使用AI笔记系统",
        "title": settings.app.title,
        "version": settings.app.version,
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.get("/health", response_model=HealthResponse, summary="健康检查")
async def health_check():
    """系统健康检查"""
    try:
        # 检查MySQL
        mysql_status = "healthy" if db_manager.check_mysql_health() else "unhealthy"

        # 检查Milvus
        milvus_status = "healthy" if db_manager.check_milvus_health() else "unhealthy"

        # 检查嵌入服务
        embedding_status = "healthy"
        try:
            from rag_action.service.embedding_service import get_embedding_service
            embedding_service = get_embedding_service()
            # 简单测试
            embedding_service.embed_text("test")
        except Exception:
            embedding_status = "unhealthy"

        return HealthResponse(
            status="healthy" if all([
                mysql_status == "healthy",
                milvus_status == "healthy",
                embedding_status == "healthy"
            ]) else "unhealthy",
            timestamp=datetime.now(),
            database=mysql_status,
            milvus=milvus_status,
            embedding_service=embedding_status
        )

    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail="健康检查失败")


@app.get("/stats", summary="系统统计")
async def get_stats():
    """获取系统统计信息"""
    try:
        mysql_stats = db_manager.get_mysql_stats()
        milvus_stats = db_manager.get_milvus_stats()

        return {
            "mysql": mysql_stats,
            "milvus": milvus_stats,
            "system": {
                "app_title": settings.app.title,
                "app_version": settings.app.version,
                "embedding_model": settings.embedding.model,
                "llm_model": settings.llm.model_name
            }
        }

    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取统计信息失败")


@app.get("/api/service/status", summary="服务状态")
async def service_status():
    """获取预加载服务状态"""
    try:
        status = service_manager.get_service_status()
        return status
    except Exception as e:
        logger.error(f"获取服务状态失败: {e}")
        return {"error": str(e)}


# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {exc}", exc_info=True)

    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="InternalServerError",
            message="服务器内部错误",
            detail=str(exc) if settings.app.debug else "请联系管理员"
        ).dict()
    )


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error="HTTPException",
            message=exc.detail,
            detail=None
        ).dict()
    )


def main():
    import uvicorn

    # 直接传递app对象，避免模块导入问题
    uvicorn.run(
        app,
        host=settings.app.host,
        port=settings.app.port,
        reload=False,  # 暂时禁用reload以避免导入问题
        log_level=settings.logging.level.lower()
    )


if __name__ == "__main__":
    main()

