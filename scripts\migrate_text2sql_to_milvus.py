"""
Text-to-SQL数据迁移脚本
将硬编码的Few-Shot示例和数据库Schema迁移到Milvus向量数据库
"""
import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置PYTHONPATH环境变量
import os
os.environ['PYTHONPATH'] = str(project_root)

from src.rag_action.service.text_to_sql_vectorstore import (
    TextToSQLVectorStore, FewShotExample, SchemaInfo, get_text_to_sql_vectorstore
)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def get_few_shot_examples() -> list[FewShotExample]:
    """获取Few-Shot示例数据（从原有硬编码数据迁移）"""
    examples = [
        FewShotExample(
            question="有多少个笔记？",
            sql="SELECT COUNT(*) as note_count FROM notes;",
            explanation="统计笔记总数",
            category="aggregate",
            difficulty="easy"
        ),
        FewShotExample(
            question="最近创建的5个笔记是什么？",
            sql="SELECT id, title, created_at FROM notes ORDER BY created_at DESC LIMIT 5;",
            explanation="按创建时间倒序获取最新的5个笔记",
            category="filter",
            difficulty="easy"
        ),
        FewShotExample(
            question="标题包含'AI'的笔记有哪些？",
            sql="SELECT id, title, created_at FROM notes WHERE title LIKE '%AI%';",
            explanation="模糊匹配标题包含AI的笔记",
            category="filter",
            difficulty="easy"
        ),
        FewShotExample(
            question="今天创建的笔记数量",
            sql="SELECT COUNT(*) as today_notes FROM notes WHERE DATE(created_at) = CURDATE();",
            explanation="统计今天创建的笔记数量",
            category="aggregate",
            difficulty="medium"
        ),
        FewShotExample(
            question="每个标签有多少个笔记？",
            sql="SELECT t.name as tag_name, COUNT(nt.note_id) as note_count FROM tags t LEFT JOIN note_tags nt ON t.id = nt.tag_id GROUP BY t.id, t.name ORDER BY note_count DESC;",
            explanation="通过JOIN查询每个标签的笔记数量",
            category="join",
            difficulty="medium"
        ),
        FewShotExample(
            question="文件大小超过1MB的笔记",
            sql="SELECT id, title, file_size FROM notes WHERE file_size > 1048576;",
            explanation="查找文件大小超过1MB的笔记",
            category="filter",
            difficulty="easy"
        ),
        FewShotExample(
            question="PDF类型的笔记有多少个？",
            sql="SELECT COUNT(*) as pdf_count FROM notes WHERE source_type = 'pdf';",
            explanation="统计PDF类型的笔记数量",
            category="aggregate",
            difficulty="easy"
        ),
        FewShotExample(
            question="包含'机器学习'标签的笔记内容",
            sql="""SELECT n.id, n.title, n.content
FROM notes n
JOIN note_tags nt ON n.id = nt.note_id
JOIN tags t ON nt.tag_id = t.id
WHERE t.name = '机器学习';""",
            explanation="通过三表关联查询包含特定标签的笔记",
            category="join",
            difficulty="hard"
        ),
        FewShotExample(
            question="笔记总数是多少？",
            sql="SELECT COUNT(*) as total FROM notes;",
            explanation="统计笔记总数的另一种表达",
            category="aggregate",
            difficulty="easy"
        ),
        FewShotExample(
            question="一共有几个笔记？",
            sql="SELECT COUNT(*) as count FROM notes;",
            explanation="计算笔记总数",
            category="aggregate",
            difficulty="easy"
        ),
        FewShotExample(
            question="最新的3个笔记标题",
            sql="SELECT title FROM notes ORDER BY created_at DESC LIMIT 3;",
            explanation="获取最新3个笔记的标题",
            category="filter",
            difficulty="easy"
        ),
        FewShotExample(
            question="标题包含'测试'的笔记数量",
            sql="SELECT COUNT(*) as count FROM notes WHERE title LIKE '%测试%';",
            explanation="统计标题包含测试的笔记数量",
            category="aggregate",
            difficulty="easy"
        ),
        FewShotExample(
            question="每种文件类型有多少笔记？",
            sql="SELECT source_type, COUNT(*) as count FROM notes GROUP BY source_type;",
            explanation="按文件类型分组统计笔记数量",
            category="aggregate",
            difficulty="medium"
        ),
        FewShotExample(
            question="有标签的笔记数量",
            sql="SELECT COUNT(DISTINCT n.id) as tagged_notes FROM notes n JOIN note_tags nt ON n.id = nt.note_id;",
            explanation="统计有标签的笔记数量",
            category="join",
            difficulty="medium"
        ),
        FewShotExample(
            question="按标题排序的所有笔记",
            sql="SELECT id, title FROM notes ORDER BY title ASC;",
            explanation="按标题字母顺序排序所有笔记",
            category="general",
            difficulty="easy"
        ),
        FewShotExample(
            question="最早创建的笔记",
            sql="SELECT id, title, created_at FROM notes ORDER BY created_at ASC LIMIT 1;",
            explanation="获取最早创建的笔记",
            category="filter",
            difficulty="easy"
        ),
        FewShotExample(
            question="有多少个父文档块？",
            sql="SELECT COUNT(*) as parent_count FROM note_chunks WHERE chunk_type = 'parent';",
            explanation="统计父文档块的数量",
            category="aggregate",
            difficulty="medium"
        ),
        FewShotExample(
            question="有多少个子文档块？",
            sql="SELECT COUNT(*) as child_count FROM note_chunks WHERE chunk_type = 'child';",
            explanation="统计子文档块的数量",
            category="aggregate",
            difficulty="medium"
        ),
        FewShotExample(
            question="每个笔记有多少个文档块？",
            sql="SELECT note_id, COUNT(*) as chunk_count FROM note_chunks GROUP BY note_id ORDER BY chunk_count DESC;",
            explanation="按笔记分组统计文档块数量",
            category="aggregate",
            difficulty="medium"
        ),
        FewShotExample(
            question="父文档块和对应的子文档块数量",
            sql="SELECT p.id as parent_id, p.content as parent_content, COUNT(c.id) as child_count FROM note_chunks p LEFT JOIN note_chunks c ON p.id = c.parent_chunk_id WHERE p.chunk_type = 'parent' GROUP BY p.id;",
            explanation="查询每个父文档块及其对应的子文档块数量",
            category="join",
            difficulty="hard"
        ),
        FewShotExample(
            question="某个笔记的所有父文档块",
            sql="SELECT id, content, chunk_index FROM note_chunks WHERE note_id = ? AND chunk_type = 'parent' ORDER BY chunk_index;",
            explanation="查询指定笔记的所有父文档块",
            category="filter",
            difficulty="medium"
        ),
        FewShotExample(
            question="有上下文窗口的文档块数量",
            sql="SELECT COUNT(*) as context_chunks FROM note_chunks WHERE context_window IS NOT NULL;",
            explanation="统计包含上下文窗口的文档块数量",
            category="aggregate",
            difficulty="medium"
        ),
        FewShotExample(
            question="每种文档块类型的数量分布",
            sql="SELECT chunk_type, COUNT(*) as count FROM note_chunks GROUP BY chunk_type;",
            explanation="按文档块类型分组统计数量",
            category="aggregate",
            difficulty="medium"
        ),
        FewShotExample(
            question="最大的笔记文件是哪个？",
            sql="SELECT id, title, file_size FROM notes WHERE file_size IS NOT NULL ORDER BY file_size DESC LIMIT 1;",
            explanation="查找文件大小最大的笔记",
            category="filter",
            difficulty="medium"
        ),
        FewShotExample(
            question="本月创建的笔记数量",
            sql="SELECT COUNT(*) as monthly_notes FROM notes WHERE YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE());",
            explanation="统计本月创建的笔记数量",
            category="aggregate",
            difficulty="medium"
        )
    ]
    
    return examples


def get_schema_info() -> list[SchemaInfo]:
    """获取数据库Schema信息（从原有硬编码数据迁移）"""
    schemas = [
        SchemaInfo(
            table_name="notes",
            description="笔记表，存储用户创建的笔记信息",
            ddl="""CREATE TABLE notes (
    id INTEGER PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    source_type VARCHAR(50) NOT NULL,
    source_file VARCHAR(255) NULL,
    file_size INTEGER NULL,
    total_pages INTEGER NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);""",
            columns={
                "id": {"type": "INTEGER", "description": "笔记唯一标识符", "primary_key": True},
                "title": {"type": "VARCHAR(255)", "description": "笔记标题"},
                "content": {"type": "TEXT", "description": "笔记内容"},
                "source_type": {"type": "VARCHAR(50)", "description": "来源类型：pdf, manual等"},
                "source_file": {"type": "VARCHAR(255)", "description": "源文件名"},
                "file_size": {"type": "INTEGER", "description": "文件大小（字节）"},
                "total_pages": {"type": "INTEGER", "description": "总页数"},
                "created_at": {"type": "DATETIME", "description": "创建时间"},
                "updated_at": {"type": "DATETIME", "description": "更新时间"}
            }
        ),
        SchemaInfo(
            table_name="tags",
            description="标签表，存储所有标签信息",
            ddl="""CREATE TABLE tags (
    id INTEGER PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);""",
            columns={
                "id": {"type": "INTEGER", "description": "标签唯一标识", "primary_key": True},
                "name": {"type": "VARCHAR(100)", "description": "标签名称"},
                "created_at": {"type": "DATETIME", "description": "创建时间"}
            }
        ),
        SchemaInfo(
            table_name="note_tags",
            description="笔记标签关联表，多对多关系",
            ddl="""CREATE TABLE note_tags (
    note_id INTEGER NOT NULL,
    tag_id INTEGER NOT NULL,
    FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
);""",
            columns={
                "note_id": {"type": "INTEGER", "description": "笔记ID，外键关联notes表"},
                "tag_id": {"type": "INTEGER", "description": "标签ID，外键关联tags表"}
            }
        ),
        SchemaInfo(
            table_name="note_chunks",
            description="笔记分块表，存储文档分块信息，支持父子文档架构",
            ddl="""CREATE TABLE note_chunks (
    id INTEGER PRIMARY KEY AUTO_INCREMENT,
    note_id INTEGER NOT NULL,
    content TEXT NOT NULL,
    chunk_type VARCHAR(20) NOT NULL DEFAULT 'child',
    parent_chunk_id INTEGER NULL,
    context_window TEXT NULL,
    chunk_metadata TEXT NULL,
    page_number INTEGER DEFAULT 1,
    chunk_index INTEGER DEFAULT 0,
    vector_id VARCHAR(100) NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_chunk_id) REFERENCES note_chunks(id) ON DELETE SET NULL,
    INDEX idx_chunk_type (chunk_type),
    INDEX idx_parent_chunk_id (parent_chunk_id)
);""",
            columns={
                "id": {"type": "INTEGER", "description": "分块ID", "primary_key": True},
                "note_id": {"type": "INTEGER", "description": "所属笔记ID"},
                "content": {"type": "TEXT", "description": "分块内容"},
                "chunk_type": {"type": "VARCHAR(20)", "description": "分块类型：parent（父文档）或child（子文档）"},
                "parent_chunk_id": {"type": "INTEGER", "description": "父文档块ID，用于子文档关联父文档"},
                "context_window": {"type": "TEXT", "description": "上下文窗口数据（JSON格式）"},
                "chunk_metadata": {"type": "TEXT", "description": "元数据信息（JSON格式）"},
                "page_number": {"type": "INTEGER", "description": "页码"},
                "chunk_index": {"type": "INTEGER", "description": "分块索引"},
                "vector_id": {"type": "VARCHAR(100)", "description": "向量数据库中的ID"},
                "created_at": {"type": "DATETIME", "description": "创建时间"}
            }
        )
    ]
    
    return schemas


def main():
    """主函数：执行数据迁移"""
    logger.info("开始Text-to-SQL数据迁移到Milvus...")
    
    try:
        # 获取向量存储实例
        vectorstore = get_text_to_sql_vectorstore()
        
        # 初始化集合
        logger.info("初始化Milvus集合...")
        vectorstore.initialize_collections()
        
        # 迁移Few-Shot示例
        logger.info("迁移Few-Shot示例...")
        few_shot_examples = get_few_shot_examples()
        success = vectorstore.insert_few_shot_examples(few_shot_examples)
        if success:
            logger.info(f"成功迁移{len(few_shot_examples)}个Few-Shot示例")
        else:
            logger.error("Few-Shot示例迁移失败")
            return False
        
        # 迁移Schema信息
        logger.info("迁移数据库Schema信息...")
        schema_info = get_schema_info()
        success = vectorstore.insert_schema_info(schema_info)
        if success:
            logger.info(f"成功迁移{len(schema_info)}个Schema信息")
        else:
            logger.error("Schema信息迁移失败")
            return False
        
        # 获取统计信息
        stats = vectorstore.get_collection_stats()
        logger.info(f"迁移完成，集合统计信息: {stats}")
        
        logger.info("Text-to-SQL数据迁移完成！")
        return True
        
    except Exception as e:
        logger.error(f"数据迁移失败: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
