"""
Text-to-SQL向量存储测试脚本
测试向量检索功能和SQL生成效果
"""
import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.rag_action.service.text_to_sql_service import get_text_to_sql_service
from src.rag_action.service.text_to_sql_vectorstore import get_text_to_sql_vectorstore

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_vectorstore_retrieval():
    """测试向量存储检索功能"""
    logger.info("=== 测试向量存储检索功能 ===")
    
    vectorstore = get_text_to_sql_vectorstore()
    
    # 测试查询列表
    test_queries = [
        "有多少个笔记？",
        "最新的笔记有哪些？",
        "包含机器学习的笔记",
        "每个标签的笔记数量",
        "父文档块和子文档块"
    ]
    
    for query in test_queries:
        logger.info(f"\n--- 测试查询: {query} ---")
        
        # 检索Few-Shot示例
        examples = vectorstore.search_few_shot_examples(query, top_k=3)
        logger.info(f"检索到{len(examples)}个相关Few-Shot示例:")
        for i, example in enumerate(examples, 1):
            logger.info(f"  {i}. 问题: {example['question']}")
            logger.info(f"     SQL: {example['sql']}")
            logger.info(f"     相似度: {example.get('similarity_score', 0.0):.3f}")
        
        # 检索Schema信息
        schemas = vectorstore.search_schema_info(query, top_k=2)
        logger.info(f"检索到{len(schemas)}个相关Schema:")
        for i, schema in enumerate(schemas, 1):
            logger.info(f"  {i}. 表名: {schema['table_name']}")
            logger.info(f"     描述: {schema['description']}")
            logger.info(f"     相似度: {schema.get('similarity_score', 0.0):.3f}")


def test_sql_generation():
    """测试SQL生成功能"""
    logger.info("\n=== 测试SQL生成功能 ===")
    
    text_to_sql_service = get_text_to_sql_service()
    
    # 测试查询列表
    test_queries = [
        "有多少个笔记？",
        "最近创建的5个笔记标题是什么？",
        "标题包含'AI'的笔记有哪些？",
        "每个标签有多少个笔记？",
        "PDF类型的笔记数量",
        "有多少个父文档块？",
        "每种文档块类型的数量分布"
    ]
    
    for query in test_queries:
        logger.info(f"\n--- 测试查询: {query} ---")
        
        try:
            result = text_to_sql_service.generate_sql(query)
            
            if result.get("success", False):
                logger.info(f"✅ SQL生成成功:")
                logger.info(f"   SQL: {result.get('sql', '')}")
                logger.info(f"   说明: {result.get('explanation', '')}")
                
                # 显示验证结果
                if result.get("warnings"):
                    logger.info(f"   警告: {result.get('warnings', [])}")
            else:
                logger.error(f"❌ SQL生成失败:")
                logger.error(f"   错误: {result.get('error', '')}")
                
        except Exception as e:
            logger.error(f"❌ SQL生成异常: {e}")


def test_compatibility():
    """测试兼容性接口"""
    logger.info("\n=== 测试兼容性接口 ===")
    
    text_to_sql_service = get_text_to_sql_service()
    
    # 测试get_schema_info方法
    logger.info("测试get_schema_info方法:")
    schema_info = text_to_sql_service.get_schema_info()
    logger.info(f"获取到{len(schema_info)}个表的Schema信息:")
    for table_name in schema_info.keys():
        logger.info(f"  - {table_name}")
    
    # 测试get_few_shot_examples方法
    logger.info("\n测试get_few_shot_examples方法:")
    examples = text_to_sql_service.get_few_shot_examples()
    logger.info(f"获取到{len(examples)}个Few-Shot示例:")
    for i, example in enumerate(examples[:3], 1):  # 只显示前3个
        logger.info(f"  {i}. {example['question']} -> {example['sql'][:50]}...")


def test_collection_stats():
    """测试集合统计信息"""
    logger.info("\n=== 测试集合统计信息 ===")
    
    vectorstore = get_text_to_sql_vectorstore()
    
    try:
        stats = vectorstore.get_collection_stats()
        logger.info("集合统计信息:")
        logger.info(f"  Few-Shot示例: {stats.get('few_shot_examples', {})}")
        logger.info(f"  Schema信息: {stats.get('schema_info', {})}")
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")


def main():
    """主函数"""
    logger.info("开始Text-to-SQL向量存储测试...")
    
    try:
        # 1. 测试向量存储检索
        test_vectorstore_retrieval()
        
        # 2. 测试SQL生成
        test_sql_generation()
        
        # 3. 测试兼容性接口
        test_compatibility()
        
        # 4. 测试集合统计
        test_collection_stats()
        
        logger.info("\n✅ 所有测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
