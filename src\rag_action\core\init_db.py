"""
数据库初始化脚本
用于首次部署时初始化MySQL和Milvus数据库
"""
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from rag_action.core.config import get_settings
from rag_action.core.database import db_manager

logger = logging.getLogger(__name__)


def init_databases():
    """初始化所有数据库"""
    print("开始初始化AI笔记系统数据库...")
    
    try:
        # 加载配置
        settings = get_settings()
        print(f"配置加载成功，MySQL: {settings.mysql.host}:{settings.mysql.port}")
        print(f"Milvus: {settings.milvus.host}:{settings.milvus.port}")
        
        # 初始化MySQL
        print("\n正在初始化MySQL数据库...")
        if db_manager.init_mysql():
            print("[OK] MySQL数据库初始化成功")
        else:
            print("[ERROR] MySQL数据库初始化失败")
            return False

        # 初始化Milvus
        print("\n正在初始化Milvus向量数据库...")
        if db_manager.init_milvus():
            print("[OK] Milvus向量数据库初始化成功")
        else:
            print("[ERROR] Milvus向量数据库初始化失败")
            return False

        print("\n[SUCCESS] 所有数据库初始化完成！")
        
        # 显示统计信息
        print("\n数据库统计信息：")
        mysql_stats = db_manager.get_mysql_stats()
        milvus_stats = db_manager.get_milvus_stats()
        
        print(f"MySQL - 笔记数: {mysql_stats.get('notes_count', 0)}, "
              f"标签数: {mysql_stats.get('tags_count', 0)}, "
              f"分块数: {mysql_stats.get('chunks_count', 0)}")
        
        print(f"Milvus - 集合: {milvus_stats.get('collection_name', 'N/A')}, "
              f"向量数: {milvus_stats.get('num_entities', 0)}")
        
        return True
        
    except Exception as e:
        print(f"\n[ERROR] 数据库初始化失败: {e}")
        logger.error(f"数据库初始化失败: {e}", exc_info=True)
        return False


def check_databases():
    """检查数据库连接状态"""
    print("检查数据库连接状态...")
    
    try:
        # 检查MySQL
        mysql_ok = db_manager.check_mysql_health()
        print(f"MySQL连接: {'[OK] 正常' if mysql_ok else '[ERROR] 异常'}")

        # 检查Milvus
        milvus_ok = db_manager.check_milvus_health()
        print(f"Milvus连接: {'[OK] 正常' if milvus_ok else '[ERROR] 异常'}")

        if mysql_ok and milvus_ok:
            print("\n[SUCCESS] 所有数据库连接正常！")
            return True
        else:
            print("\n[ERROR] 部分数据库连接异常")
            return False
            
    except Exception as e:
        print(f"\n[ERROR] 数据库检查失败: {e}")
        return False


def reset_databases():
    """重置数据库（危险操作）"""
    print("[WARNING] 警告：此操作将删除所有数据！")
    confirm = input("请输入 'YES' 确认重置数据库: ")
    
    if confirm != "YES":
        print("操作已取消")
        return False
    
    try:
        from sqlalchemy import text
        from rag_action.core.database import engine
        from pymilvus import utility
        
        # 重置MySQL
        print("正在重置MySQL数据库...")
        with engine.connect() as conn:
            # 删除所有表
            conn.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
            
            # 获取所有表名
            result = conn.execute(text("SHOW TABLES"))
            tables = [row[0] for row in result]
            
            # 删除所有表
            for table in tables:
                conn.execute(text(f"DROP TABLE IF EXISTS {table}"))
            
            conn.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
            conn.commit()
        
        print("[OK] MySQL数据库重置完成")

        # 重置Milvus
        print("正在重置Milvus数据库...")
        settings = get_settings()
        collection_name = settings.milvus.collection_name

        if utility.has_collection(collection_name):
            utility.drop_collection(collection_name)
            print(f"[OK] 删除Milvus集合: {collection_name}")
        
        # 重新初始化
        print("\n重新初始化数据库...")
        return init_databases()
        
    except Exception as e:
        print(f"\n[ERROR] 数据库重置失败: {e}")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AI笔记系统数据库管理工具")
    parser.add_argument("action", choices=["init", "check", "reset"], 
                       help="操作类型: init(初始化), check(检查), reset(重置)")
    
    args = parser.parse_args()
    
    if args.action == "init":
        success = init_databases()
    elif args.action == "check":
        success = check_databases()
    elif args.action == "reset":
        success = reset_databases()
    else:
        print("未知操作")
        success = False
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
