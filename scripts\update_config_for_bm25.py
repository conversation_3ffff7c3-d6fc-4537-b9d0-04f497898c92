#!/usr/bin/env python3
"""
配置更新脚本
为Milvus BM25功能更新配置文件
"""
import os
import yaml
import logging
from typing import Dict, Any

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ConfigUpdater:
    """配置更新器"""
    
    def __init__(self):
        self.config_file = "config.yaml"
        self.backup_file = "config.yaml.backup"
    
    def backup_config(self):
        """备份现有配置"""
        if os.path.exists(self.config_file):
            import shutil
            shutil.copy2(self.config_file, self.backup_file)
            logger.info(f"配置文件已备份到 {self.backup_file}")
    
    def load_config(self) -> Dict[str, Any]:
        """加载现有配置"""
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        else:
            return {}
    
    def update_milvus_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """更新Milvus配置"""
        if 'milvus' not in config:
            config['milvus'] = {}
        
        milvus_config = config['milvus']
        
        # 添加BM25相关配置
        bm25_config = {
            'enable_bm25': True,
            'bm25_language': 'chinese',
            'bm25_k1': 1.2,
            'bm25_b': 0.75,
            'bm25_drop_ratio': 0.2,
            'dense_weight': 0.6,
            'sparse_weight': 0.4
        }
        
        # 更新配置，保留现有值
        for key, value in bm25_config.items():
            if key not in milvus_config:
                milvus_config[key] = value
        
        logger.info("Milvus BM25配置已更新")
        return config
    
    def update_ensemble_retriever_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """更新混合检索器配置"""
        if 'ensemble_retriever' not in config:
            config['ensemble_retriever'] = {}
        
        ensemble_config = config['ensemble_retriever']
        
        # 添加新的配置选项
        new_config = {
            'use_milvus_bm25': True,  # 启用Milvus BM25
            'fallback_to_traditional': True,  # 允许回退到传统BM25
            'dense_top_k': 10,
            'sparse_top_k': 10,
            'final_top_k': 5,
            'hybrid_method': 'native'  # 'native' 或 'manual'
        }
        
        # 更新配置
        for key, value in new_config.items():
            if key not in ensemble_config:
                ensemble_config[key] = value
        
        logger.info("混合检索器配置已更新")
        return config
    
    def add_migration_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """添加迁移相关配置"""
        if 'migration' not in config:
            config['migration'] = {}
        
        migration_config = {
            'enable_gradual_migration': True,
            'migration_batch_size': 100,
            'backup_collection_suffix': '_backup',
            'new_collection_suffix': '_bm25'
        }
        
        config['migration'].update(migration_config)
        logger.info("迁移配置已添加")
        return config
    
    def save_config(self, config: Dict[str, Any]):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
        logger.info(f"配置文件已保存到 {self.config_file}")
    
    def create_example_config(self):
        """创建示例配置文件"""
        example_config = {
            'milvus': {
                'host': 'localhost',
                'port': 19530,
                'collection_name': 'note_embeddings',
                'embedding_dim': 1536,
                'index_type': 'HNSW',
                'metric_type': 'COSINE',
                # BM25配置
                'enable_bm25': True,
                'bm25_language': 'chinese',
                'bm25_k1': 1.2,
                'bm25_b': 0.75,
                'bm25_drop_ratio': 0.2,
                'dense_weight': 0.6,
                'sparse_weight': 0.4
            },
            'ensemble_retriever': {
                'use_milvus_bm25': True,
                'fallback_to_traditional': True,
                'dense_top_k': 10,
                'sparse_top_k': 10,
                'final_top_k': 5,
                'hybrid_method': 'native'
            },
            'migration': {
                'enable_gradual_migration': True,
                'migration_batch_size': 100,
                'backup_collection_suffix': '_backup',
                'new_collection_suffix': '_bm25'
            }
        }
        
        example_file = "config.yaml.example"
        with open(example_file, 'w', encoding='utf-8') as f:
            yaml.dump(example_config, f, default_flow_style=False, allow_unicode=True, indent=2)
        
        logger.info(f"示例配置文件已创建: {example_file}")
    
    def update_config(self):
        """执行配置更新"""
        logger.info("开始更新配置文件...")
        
        # 1. 备份现有配置
        self.backup_config()
        
        # 2. 加载现有配置
        config = self.load_config()
        
        # 3. 更新各部分配置
        config = self.update_milvus_config(config)
        config = self.update_ensemble_retriever_config(config)
        config = self.add_migration_config(config)
        
        # 4. 保存更新后的配置
        self.save_config(config)
        
        # 5. 创建示例配置
        self.create_example_config()
        
        logger.info("✅ 配置更新完成")
    
    def validate_config(self) -> bool:
        """验证配置文件"""
        try:
            config = self.load_config()
            
            # 检查必需的配置项
            required_sections = ['milvus', 'ensemble_retriever']
            for section in required_sections:
                if section not in config:
                    logger.error(f"缺少必需的配置节: {section}")
                    return False
            
            # 检查Milvus BM25配置
            milvus_config = config.get('milvus', {})
            required_milvus_keys = ['enable_bm25', 'bm25_language']
            for key in required_milvus_keys:
                if key not in milvus_config:
                    logger.error(f"缺少Milvus配置项: {key}")
                    return False
            
            logger.info("✅ 配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False


def main():
    """主函数"""
    logger.info("=== Milvus BM25配置更新 ===")
    
    updater = ConfigUpdater()
    
    try:
        # 更新配置
        updater.update_config()
        
        # 验证配置
        if updater.validate_config():
            logger.info("配置更新和验证完成")
        else:
            logger.error("配置验证失败")
            return
        
        # 提供后续步骤指导
        logger.info("\n后续步骤:")
        logger.info("1. 检查并调整 config.yaml 中的配置参数")
        logger.info("2. 运行依赖升级脚本: python scripts/upgrade_dependencies.py")
        logger.info("3. 运行数据迁移脚本: python scripts/migrate_to_milvus_bm25.py")
        logger.info("4. 运行功能测试脚本: python scripts/test_milvus_bm25.py")
        logger.info("5. 重启应用服务")
        
    except Exception as e:
        logger.error(f"配置更新失败: {e}")


if __name__ == "__main__":
    main()
