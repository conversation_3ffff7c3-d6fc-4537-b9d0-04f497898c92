"""
会话管理服务
"""
import uuid
import json
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from rag_action.models.conversation import (
    ConversationContext, ConversationMessage, ConversationRequest,
    ConversationResponse, ConversationSummary, MessageRole
)
from rag_action.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class ConversationManager:
    """会话管理器"""
    
    def __init__(self):
        # 内存存储会话（生产环境应使用Redis或数据库）
        self._conversations: Dict[str, ConversationContext] = {}
        self._cleanup_interval = 3600  # 1小时清理一次过期会话
        self._max_conversation_age = 24 * 3600  # 24小时过期
        self._last_cleanup = time.time()
    
    def create_conversation(self, initial_message: Optional[str] = None) -> str:
        """创建新会话"""
        conversation_id = str(uuid.uuid4())
        context = ConversationContext(conversation_id=conversation_id)
        
        # 添加系统消息
        system_message = ConversationMessage(
            id=str(uuid.uuid4()),
            role=MessageRole.SYSTEM,
            content="你是一个智能笔记助手，可以帮助用户查询和分析笔记内容。请基于提供的上下文信息回答用户问题。",
            metadata={"type": "system_init"}
        )
        context.add_message(system_message)
        
        # 如果有初始消息，添加到会话
        if initial_message:
            user_message = ConversationMessage(
                id=str(uuid.uuid4()),
                role=MessageRole.USER,
                content=initial_message
            )
            context.add_message(user_message)
        
        self._conversations[conversation_id] = context
        logger.info(f"创建新会话: {conversation_id}, 当前会话总数: {len(self._conversations)}")

        # 定期清理过期会话（暂时禁用以调试）
        # self._cleanup_expired_conversations()

        return conversation_id
    
    def get_conversation(self, conversation_id: str) -> Optional[ConversationContext]:
        """获取会话上下文"""
        return self._conversations.get(conversation_id)
    
    def add_message(self, conversation_id: str, role: MessageRole, content: str, 
                   metadata: Optional[Dict[str, Any]] = None) -> str:
        """添加消息到会话"""
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            raise ValueError(f"会话不存在: {conversation_id}")
        
        message_id = str(uuid.uuid4())
        message = ConversationMessage(
            id=message_id,
            role=role,
            content=content,
            metadata=metadata or {}
        )
        
        conversation.add_message(message)
        role_str = role.value if hasattr(role, 'value') else str(role)
        logger.debug(f"添加消息到会话 {conversation_id}: {role_str}")

        return message_id
    
    def get_conversation_context(self, conversation_id: str, max_tokens: int = 4000) -> List[Dict[str, str]]:
        """获取会话上下文用于LLM"""
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            return []
        
        return conversation.get_context_for_llm(max_tokens)
    
    def list_conversations(self, limit: int = 50) -> List[ConversationSummary]:
        """列出会话摘要"""
        summaries = []
        
        # 按更新时间排序
        sorted_conversations = sorted(
            self._conversations.values(),
            key=lambda x: x.updated_at,
            reverse=True
        )
        
        for conv in sorted_conversations[:limit]:
            # 生成会话标题（使用第一个用户消息）
            title = "新会话"
            user_messages = [msg for msg in conv.messages if msg.role == MessageRole.USER]
            if user_messages:
                title = user_messages[0].content[:50] + ("..." if len(user_messages[0].content) > 50 else "")
            
            summary = ConversationSummary(
                conversation_id=conv.conversation_id,
                title=title,
                summary=f"包含 {len(conv.messages)} 条消息",
                message_count=len(conv.messages),
                created_at=conv.created_at,
                updated_at=conv.updated_at
            )
            summaries.append(summary)
        
        return summaries
    
    def delete_conversation(self, conversation_id: str) -> bool:
        """删除会话"""
        if conversation_id in self._conversations:
            del self._conversations[conversation_id]
            logger.info(f"删除会话: {conversation_id}")
            return True
        return False
    
    def clear_all_conversations(self) -> int:
        """清空所有会话"""
        count = len(self._conversations)
        self._conversations.clear()
        logger.info(f"清空所有会话: {count} 个")
        return count
    
    def _cleanup_expired_conversations(self) -> None:
        """清理过期会话"""
        current_time = time.time()
        
        # 检查是否需要清理
        if current_time - self._last_cleanup < self._cleanup_interval:
            return
        
        expired_ids = []
        cutoff_time = datetime.now() - timedelta(seconds=self._max_conversation_age)
        
        for conv_id, conv in self._conversations.items():
            if conv.updated_at < cutoff_time:
                expired_ids.append(conv_id)
        
        # 删除过期会话
        for conv_id in expired_ids:
            del self._conversations[conv_id]
        
        if expired_ids:
            logger.info(f"清理过期会话: {len(expired_ids)} 个")
        
        self._last_cleanup = current_time
    
    def get_stats(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        total_conversations = len(self._conversations)
        total_messages = sum(len(conv.messages) for conv in self._conversations.values())
        
        return {
            "total_conversations": total_conversations,
            "total_messages": total_messages,
            "average_messages_per_conversation": total_messages / total_conversations if total_conversations > 0 else 0
        }


# 全局会话管理器实例
conversation_manager = ConversationManager()


def get_conversation_manager() -> ConversationManager:
    """获取会话管理器实例"""
    return conversation_manager
