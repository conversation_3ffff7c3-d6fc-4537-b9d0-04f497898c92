#!/usr/bin/env python3
"""
Milvus BM25迁移脚本
将现有数据迁移到支持BM25的新Milvus集合
"""
import sys
import os
import logging
import asyncio
from typing import List, Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from pymilvus import connections, utility, Collection

from src.rag_action.core.config import get_settings
from src.rag_action.core.database import SessionLocal
from src.rag_action.models.database import NoteChunk, MilvusSchema
from src.rag_action.service.vector_service import VectorService
from src.rag_action.service.embedding_service import EmbeddingServiceFactory

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

settings = get_settings()


class MilvusBM25Migrator:
    """Milvus BM25迁移器"""
    
    def __init__(self):
        self.old_collection_name = settings.milvus.collection_name
        self.new_collection_name = f"{self.old_collection_name}_bm25"
        self.backup_collection_name = f"{self.old_collection_name}_backup"
        
        # 连接Milvus
        self._connect_milvus()
        
        # 初始化服务
        self.embedding_service = EmbeddingServiceFactory.create_service()
        
    def _connect_milvus(self):
        """连接Milvus"""
        try:
            connections.connect(
                alias="default",
                host=settings.milvus.host,
                port=settings.milvus.port
            )
            logger.info("Milvus连接成功")
        except Exception as e:
            logger.error(f"Milvus连接失败: {e}")
            raise
    
    def check_prerequisites(self) -> bool:
        """检查迁移前提条件"""
        logger.info("检查迁移前提条件...")
        
        # 1. 检查Milvus版本是否支持BM25
        try:
            from pymilvus import Function, FunctionType
            if not hasattr(FunctionType, 'BM25'):
                logger.error("Milvus版本不支持BM25功能")
                return False
        except ImportError:
            logger.error("无法导入BM25相关功能")
            return False
        
        # 2. 检查原集合是否存在
        if not utility.has_collection(self.old_collection_name):
            logger.error(f"原集合 {self.old_collection_name} 不存在")
            return False
        
        # 3. 检查新集合是否已存在
        if utility.has_collection(self.new_collection_name):
            logger.warning(f"新集合 {self.new_collection_name} 已存在")
            response = input("是否删除现有集合并重新创建？(y/N): ")
            if response.lower() == 'y':
                utility.drop_collection(self.new_collection_name)
                logger.info(f"已删除现有集合 {self.new_collection_name}")
            else:
                return False
        
        logger.info("✅ 前提条件检查通过")
        return True
    
    def create_new_collection(self):
        """创建支持BM25的新集合"""
        logger.info("创建支持BM25的新集合...")
        
        try:
            # 获取新的Schema（启用BM25）
            schema = MilvusSchema.get_collection_schema(enable_bm25=True)
            
            # 添加BM25函数
            bm25_function = MilvusSchema.get_bm25_function()
            schema.add_function(bm25_function)
            
            # 创建集合
            collection = Collection(self.new_collection_name, schema)
            
            # 创建索引
            index_params = MilvusSchema.get_index_params(enable_bm25=True)
            for index_param in index_params:
                collection.create_index(
                    field_name=index_param["field_name"],
                    index_params=index_param
                )
            
            logger.info(f"✅ 新集合 {self.new_collection_name} 创建成功")
            return collection
            
        except Exception as e:
            logger.error(f"创建新集合失败: {e}")
            raise
    
    def backup_old_collection(self):
        """备份原集合"""
        logger.info("备份原集合...")
        
        try:
            # 检查备份集合是否存在
            if utility.has_collection(self.backup_collection_name):
                utility.drop_collection(self.backup_collection_name)
            
            # 创建备份（通过复制数据实现）
            old_collection = Collection(self.old_collection_name)
            old_collection.load()
            
            # 分批获取所有数据（Milvus限制单次查询最大16384条）
            batch_size = 10000
            offset = 0
            all_results = []

            while True:
                batch_results = old_collection.query(
                    expr="",
                    output_fields=["*"],
                    limit=batch_size,
                    offset=offset
                )

                if not batch_results:
                    break

                all_results.extend(batch_results)
                offset += batch_size

                logger.info(f"已获取 {len(all_results)} 条记录")

                # 如果返回的记录数少于batch_size，说明已经到末尾
                if len(batch_results) < batch_size:
                    break

            results = all_results
            
            if results:
                # 创建备份集合（使用原Schema）
                old_schema = old_collection.schema
                backup_collection = Collection(self.backup_collection_name, old_schema)
                
                # 插入数据
                backup_collection.insert(results)
                backup_collection.flush()
                
                logger.info(f"✅ 原集合已备份到 {self.backup_collection_name}")
            else:
                logger.info("原集合无数据，跳过备份")
                
        except Exception as e:
            logger.error(f"备份原集合失败: {e}")
            raise
    
    def migrate_data(self, batch_size: int = 100):
        """迁移数据到新集合"""
        logger.info("开始数据迁移...")
        
        try:
            # 从数据库获取所有chunk数据
            db = SessionLocal()
            chunks = db.query(NoteChunk).all()
            
            if not chunks:
                logger.info("没有数据需要迁移")
                return
            
            logger.info(f"找到 {len(chunks)} 个文档块需要迁移")
            
            # 获取新集合
            new_collection = Collection(self.new_collection_name)
            
            # 批量处理数据
            for i in range(0, len(chunks), batch_size):
                batch_chunks = chunks[i:i + batch_size]
                batch_data = []
                
                for chunk in batch_chunks:
                    try:
                        # 截断过长的文本内容（最大5000字符）
                        content = chunk.content[:5000] if chunk.content else ""

                        # 生成向量嵌入
                        embedding = self.embedding_service.embed_text(content)

                        # 准备数据
                        data = {
                            "id": f"chunk_{chunk.id}",
                            "note_id": chunk.note_id,
                            "chunk_id": chunk.id,
                            "content": content,  # 截断后的文本，Milvus会自动生成稀疏向量
                            "embedding": embedding,
                            "chunk_type": chunk.chunk_type or "child",
                            "parent_chunk_id": chunk.parent_chunk_id or 0,
                            "page_number": chunk.page_number or 0,
                            "created_at": int(chunk.created_at.timestamp()) if chunk.created_at else 0
                        }
                        batch_data.append(data)
                        
                    except Exception as e:
                        logger.error(f"处理chunk {chunk.id} 失败: {e}")
                        continue
                
                # 插入批次数据
                if batch_data:
                    new_collection.insert(batch_data)
                    logger.info(f"已迁移 {i + len(batch_data)} / {len(chunks)} 个文档块")
            
            # 刷新集合
            new_collection.flush()
            
            # 加载集合
            new_collection.load()
            
            logger.info("✅ 数据迁移完成")
            
        except Exception as e:
            logger.error(f"数据迁移失败: {e}")
            raise
        finally:
            db.close()
    
    def verify_migration(self):
        """验证迁移结果"""
        logger.info("验证迁移结果...")
        
        try:
            old_collection = Collection(self.old_collection_name)
            new_collection = Collection(self.new_collection_name)
            
            old_collection.load()
            new_collection.load()
            
            # 获取数据量
            old_count = old_collection.num_entities
            new_count = new_collection.num_entities
            
            logger.info(f"原集合数据量: {old_count}")
            logger.info(f"新集合数据量: {new_count}")
            
            if old_count == new_count:
                logger.info("✅ 数据量验证通过")
            else:
                logger.warning(f"⚠️ 数据量不匹配，可能存在迁移问题")
            
            # 测试BM25搜索
            test_query = "测试查询"
            search_results = new_collection.search(
                data=[test_query],
                anns_field="sparse_embedding",
                param={"drop_ratio_search": 0.2},
                limit=5,
                output_fields=["content"]
            )
            
            if search_results and len(search_results[0]) > 0:
                logger.info("✅ BM25搜索功能验证通过")
            else:
                logger.warning("⚠️ BM25搜索功能可能存在问题")
                
        except Exception as e:
            logger.error(f"验证迁移结果失败: {e}")
            raise
    
    def switch_collection(self):
        """切换到新集合"""
        logger.info("切换到新集合...")
        
        try:
            # 重命名集合
            old_temp_name = f"{self.old_collection_name}_old"
            
            # 1. 将原集合重命名为临时名称
            if utility.has_collection(old_temp_name):
                utility.drop_collection(old_temp_name)
            
            # 注意：Milvus可能不支持直接重命名，需要通过配置更新
            logger.info(f"请手动更新配置文件，将collection_name从 {self.old_collection_name} 改为 {self.new_collection_name}")
            logger.info("或者删除原集合并将新集合重命名")
            
        except Exception as e:
            logger.error(f"切换集合失败: {e}")
            raise


def main():
    """主函数"""
    logger.info("=== Milvus BM25迁移开始 ===")
    
    migrator = MilvusBM25Migrator()
    
    try:
        # 1. 检查前提条件
        if not migrator.check_prerequisites():
            logger.error("前提条件检查失败，迁移终止")
            return
        
        # 2. 备份原集合
        migrator.backup_old_collection()
        
        # 3. 创建新集合
        migrator.create_new_collection()
        
        # 4. 迁移数据
        migrator.migrate_data()
        
        # 5. 验证迁移
        migrator.verify_migration()
        
        # 6. 提示切换
        migrator.switch_collection()
        
        logger.info("✅ Milvus BM25迁移完成")
        logger.info("请更新配置文件中的collection_name以使用新集合")
        
    except Exception as e:
        logger.error(f"迁移失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
