# Milvus 混合检索示例

本示例演示如何使用 Milvus 2.5+ 进行稀疏向量和密集向量的混合检索。

## 功能特性

- ✅ 支持 Milvus 2.5+ 的最新混合检索功能
- ✅ 使用 BGE-M3 模型生成密集和稀疏向量
- ✅ 支持三种检索模式：
  - 密集向量检索（语义搜索）
  - 稀疏向量检索（关键词/全文搜索）
  - 混合检索（结合语义和关键词）
- ✅ 完整的错误处理和日志记录
- ✅ 支持本地 Milvus Lite 和远程 Milvus 服务器

## 环境要求

- Python 3.8+
- Milvus 2.5+
- 足够的内存运行 BGE-M3 模型（建议 4GB+）

## 安装依赖

### 使用 uv（推荐）

```bash
# 在项目根目录下
cd C:\StudySpace\rag-action

# 激活虚拟环境
.venv\Scripts\activate

# 安装依赖
uv add pymilvus[model]
uv add FlagEmbedding
uv add sentence-transformers
uv add transformers
uv add torch
uv add pandas
uv add numpy
```

### 使用 pip

```bash
pip install -r src/rag_action/milvus/requirements.txt
```

## 快速开始

### 1. 运行完整示例

```bash
# 在项目根目录下
python src/rag_action/milvus/hybrid_search_example.py
```

### 2. 在代码中使用

```python
from src.rag_action.milvus.hybrid_search_example import MilvusHybridSearcher

# 初始化检索器
searcher = MilvusHybridSearcher(
    uri="./milvus_demo.db",  # 本地文件数据库
    collection_name="my_collection"
)

# 连接并创建集合
searcher.connect()
searcher.create_collection()

# 插入数据
texts = ["你的文本数据1", "你的文本数据2", ...]
searcher.insert_data(texts)

# 执行混合检索
results = searcher.hybrid_search("查询文本", limit=5)
for result in results:
    print(f"文本: {result['text']}, 分数: {result['score']}")

# 清理资源
searcher.close()
```

## 配置选项

### 数据库连接

```python
# 本地 Milvus Lite（推荐用于开发测试）
uri = "./milvus_demo.db"

# 远程 Milvus 服务器
uri = "http://localhost:19530"

# Zilliz Cloud（托管服务）
uri = "https://your-cluster.zillizcloud.com"
```

### 检索参数调优

```python
# 混合检索权重调整
hybrid_results = searcher.hybrid_search(
    query="查询文本",
    sparse_weight=0.7,  # 稀疏向量权重
    dense_weight=1.0,   # 密集向量权重
    reranker_type="weighted"  # 或 "rrf"
)
```

## 检索模式对比

| 检索模式 | 优势 | 适用场景 |
|---------|------|----------|
| 密集向量检索 | 语义理解强，能处理同义词 | 概念性查询，语义相似性搜索 |
| 稀疏向量检索 | 关键词匹配精确，速度快 | 精确关键词搜索，专业术语查找 |
| 混合检索 | 结合两者优势，召回率高 | 综合性查询，生产环境推荐 |

## 性能优化建议

1. **硬件配置**
   - 使用 SSD 存储提升 I/O 性能
   - 增加内存以支持更大的数据集
   - 使用 GPU 加速向量计算（可选）

2. **参数调优**
   - 根据数据特点调整 `sparse_weight` 和 `dense_weight`
   - 适当设置 `batch_size` 以平衡内存使用和性能
   - 调整 `drop_ratio_search` 参数优化稀疏检索

3. **索引优化**
   - 对于大规模数据，考虑使用 IVF 或 HNSW 索引
   - 根据查询模式选择合适的相似度度量

## 故障排除

### 常见问题

1. **内存不足**
   ```
   解决方案：减少 batch_size 或使用更小的模型
   ```

2. **连接失败**
   ```
   解决方案：检查 Milvus 服务是否正常运行，确认 URI 配置正确
   ```

3. **模型下载慢**
   ```
   解决方案：配置 HuggingFace 镜像或手动下载模型
   ```

### 日志调试

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 扩展功能

- 支持多模态检索（文本+图像）
- 自定义嵌入模型
- 批量检索和异步处理
- 结果缓存和性能监控

## 新增：BGE-M3 原生稀疏向量支持

### BGE-M3 原生稀疏向量 vs BM25 对比

我们新增了 BGE-M3 原生稀疏向量的完整实现，展示了两种不同的稀疏向量方法：

| 特性 | BGE-M3 原生稀疏向量 | BM25 稀疏向量 |
|------|-------------------|---------------|
| **生成方式** | 神经网络学习 | 统计算法 |
| **词汇扩展** | ✅ 语义扩展 | ❌ 仅原文词汇 |
| **语义理解** | ✅ 深度理解 | ❌ 关键词匹配 |
| **计算成本** | 高 | 低 |
| **检索质量** | 高 | 中等 |

### 新增文件说明

```
src/rag_action/milvus/
├── bge_m3_native_sparse_example.py    # BGE-M3 原生稀疏向量示例
├── bge_m3_vs_bm25_comparison.py       # 两种方法对比分析
├── BGE_M3_TECHNICAL_DETAILS.md        # 技术实现详解
└── ... (其他文件)
```

### 运行 BGE-M3 原生稀疏向量示例

```bash
# 运行 BGE-M3 原生稀疏向量示例
python src/rag_action/milvus/bge_m3_native_sparse_example.py

# 运行对比分析
python src/rag_action/milvus/bge_m3_vs_bm25_comparison.py
```

### 技术优势

1. **学习的词汇重要性**: BGE-M3 通过神经网络学习词汇权重
2. **语义词汇扩展**: 可以激活语义相关但原文未出现的词汇
3. **上下文感知**: 同一词汇在不同上下文中权重不同
4. **多语言支持**: 支持 100+ 种语言

## 参考资料

- [Milvus 官方文档](https://milvus.io/docs)
- [BGE-M3 模型](https://github.com/FlagOpen/FlagEmbedding)
- [BGE-M3 技术论文](https://arxiv.org/abs/2402.03216)
- [混合检索最佳实践](https://milvus.io/blog/hybrid-search-with-milvus.md)
- [BGE-M3 稀疏向量详解](https://medium.com/@zilliz_learn/exploring-bge-m3-and-splade-two-machine-learning-models-for-generating-sparse-embeddings-0772de2c52a7)
