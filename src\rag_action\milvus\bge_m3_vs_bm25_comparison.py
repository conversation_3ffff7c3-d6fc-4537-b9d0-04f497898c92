"""
BGE-M3 原生稀疏向量 vs BM25 稀疏向量对比分析

本脚本创建两个集合，分别使用 BGE-M3 原生稀疏向量和 BM25 稀疏向量，
对比它们在相同数据集上的检索效果差异。

对比维度：
1. 检索准确性
2. 语义理解能力
3. 词汇扩展能力
4. 性能表现

作者: RAG Action Team
日期: 2025-08-05
"""

import os
import logging
import time
from typing import List, Dict, Any, Tuple

# 设置环境变量
os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"

from pymilvus import MilvusClient, DataType, Function, FunctionType, AnnSearchRequest, WeightedRanker
from pymilvus.model.hybrid import BGEM3EmbeddingFunction
from bge_m3_native_sparse_example import BGEM3NativeSparseSearcher

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class BM25SparseSearcher:
    """
    BM25 稀疏向量检索器（使用 Milvus BM25 Function）
    """
    
    def __init__(self, 
                 uri: str = "http://localhost:19530",
                 collection_name: str = "bm25_demo"):
        self.uri = uri
        self.collection_name = collection_name
        self.client = None
        self.embedding_function = None
        self.dense_dim = 1024
        
        self._init_bge_m3_model()
    
    def _init_bge_m3_model(self):
        """初始化 BGE-M3 模型（仅用于密集向量）"""
        self.embedding_function = BGEM3EmbeddingFunction(
            model_name='BAAI/bge-m3',
            device='cpu',
            use_fp16=False
        )
    
    def connect(self):
        """连接到 Milvus 数据库"""
        self.client = MilvusClient(uri=self.uri)
    
    def create_collection_with_bm25(self, drop_existing: bool = True):
        """创建使用 BM25 Function 的集合"""
        if drop_existing and self.client.has_collection(self.collection_name):
            self.client.drop_collection(self.collection_name)
        
        # 创建集合模式
        schema = self.client.create_schema()
        
        schema.add_field("id", DataType.INT64, is_primary=True, auto_id=True)
        schema.add_field("text", DataType.VARCHAR, max_length=2000, enable_analyzer=True)
        schema.add_field("bge_dense_vector", DataType.FLOAT_VECTOR, dim=self.dense_dim)
        schema.add_field("bm25_sparse_vector", DataType.SPARSE_FLOAT_VECTOR)
        
        # 添加 BM25 函数
        bm25_function = Function(
            name="text_bm25_embedding",
            input_field_names=["text"],
            output_field_names=["bm25_sparse_vector"],
            function_type=FunctionType.BM25,
        )
        schema.add_function(bm25_function)
        
        # 准备索引
        index_params = self.client.prepare_index_params()
        index_params.add_index("bge_dense_vector", "AUTOINDEX", "COSINE")
        index_params.add_index("bm25_sparse_vector", "SPARSE_INVERTED_INDEX", "BM25")
        
        # 创建集合
        self.client.create_collection(
            collection_name=self.collection_name,
            schema=schema,
            index_params=index_params
        )
    
    def insert_data(self, texts: List[str], batch_size: int = 10):
        """插入数据"""
        embeddings = self.embedding_function.encode_documents(texts)
        
        total_inserted = 0
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_dense = embeddings["dense"][i:i + batch_size]
            
            batch_data = [
                {
                    "text": text,
                    "bge_dense_vector": dense_vec
                    # bm25_sparse_vector 由 BM25 函数自动生成
                }
                for text, dense_vec in zip(batch_texts, batch_dense)
            ]
            
            result = self.client.insert(self.collection_name, batch_data)
            total_inserted += result["insert_count"]
        
        return total_inserted
    
    def bm25_sparse_search(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """执行 BM25 稀疏向量检索"""
        results = self.client.search(
            collection_name=self.collection_name,
            data=[query],
            anns_field="bm25_sparse_vector",
            limit=limit,
            output_fields=["text"],
            search_params={"metric_type": "BM25", "params": {"drop_ratio_search": 0.2}}
        )
        
        return [{"text": hit["entity"]["text"], "score": hit["distance"]} 
               for hit in results[0]]
    
    def hybrid_search_bm25(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """执行 BM25 混合检索"""
        query_embeddings = self.embedding_function.encode_queries([query])
        query_dense = query_embeddings["dense"][0]
        
        dense_req = AnnSearchRequest(
            data=[query_dense],
            anns_field="bge_dense_vector",
            param={"metric_type": "COSINE"},
            limit=limit
        )
        
        sparse_req = AnnSearchRequest(
            data=[query],
            anns_field="bm25_sparse_vector",
            param={"metric_type": "BM25", "params": {"drop_ratio_search": 0.2}},
            limit=limit
        )
        
        reranker = WeightedRanker(0.7, 1.0)
        
        results = self.client.hybrid_search(
            collection_name=self.collection_name,
            reqs=[sparse_req, dense_req],
            ranker=reranker,
            limit=limit,
            output_fields=["text"]
        )
        
        return [{"text": hit["entity"]["text"], "score": hit["distance"]} 
               for hit in results[0]]


def create_test_data() -> List[str]:
    """创建测试数据集"""
    return [
        "人工智能是计算机科学的一个分支，致力于创建智能系统。",
        "机器学习是AI的子集，让计算机能够自动学习和改进。",
        "深度学习使用神经网络模拟人脑的工作方式。",
        "自然语言处理帮助计算机理解人类语言。",
        "计算机视觉使机器能够理解视觉信息。",
        "向量数据库专门存储高维向量数据。",
        "Milvus是开源的向量数据库，专为AI设计。",
        "语义搜索使用向量嵌入理解查询含义。",
        "混合检索结合密集和稀疏向量的优势。",
        "RAG技术结合信息检索和文本生成。",
        "神经网络是深度学习的基础架构。",
        "算法是解决问题的步骤和方法。",
        "数据挖掘从大量数据中发现模式。",
        "知识图谱表示实体间的关系。",
        "推荐系统根据用户偏好推荐内容。"
    ]


def run_comparison_test():
    """运行对比测试"""
    
    logger.info("=== BGE-M3 原生稀疏向量 vs BM25 稀疏向量对比测试 ===")
    
    try:
        # 准备测试数据
        test_data = create_test_data()
        test_queries = [
            "什么是机器学习？",
            "神经网络如何工作？",
            "向量数据库的用途",
            "AI和人工智能",
            "深度学习算法"
        ]
        
        # 初始化两个检索器
        logger.info("🔧 初始化 BGE-M3 原生稀疏向量检索器...")
        bge_searcher = BGEM3NativeSparseSearcher(
            uri="http://localhost:19530",
            collection_name="bge_m3_native_test"
        )
        bge_searcher.connect()
        bge_searcher.create_collection_with_native_sparse()
        
        logger.info("🔧 初始化 BM25 稀疏向量检索器...")
        bm25_searcher = BM25SparseSearcher(
            uri="http://localhost:19530",
            collection_name="bm25_test"
        )
        bm25_searcher.connect()
        bm25_searcher.create_collection_with_bm25()
        
        # 插入相同的测试数据
        logger.info("📝 插入测试数据...")
        bge_searcher.insert_data_with_native_sparse(test_data, batch_size=5)
        bm25_searcher.insert_data(test_data, batch_size=5)
        
        # 等待索引完成
        time.sleep(3)
        
        # 对每个查询进行对比测试
        for i, query in enumerate(test_queries, 1):
            logger.info(f"\n{'='*60}")
            logger.info(f"🔍 测试查询 {i}: {query}")
            logger.info(f"{'='*60}")
            
            # BGE-M3 原生稀疏向量检索
            logger.info(f"\n--- BGE-M3 原生稀疏向量结果 ---")
            bge_sparse_results = bge_searcher.native_sparse_search(query, limit=3)
            for j, result in enumerate(bge_sparse_results, 1):
                logger.info(f"{j}. [分数: {result['score']:.4f}] {result['text'][:50]}...")
            
            # BM25 稀疏向量检索
            logger.info(f"\n--- BM25 稀疏向量结果 ---")
            bm25_sparse_results = bm25_searcher.bm25_sparse_search(query, limit=3)
            for j, result in enumerate(bm25_sparse_results, 1):
                logger.info(f"{j}. [分数: {result['score']:.4f}] {result['text'][:50]}...")
            
            # BGE-M3 混合检索
            logger.info(f"\n--- BGE-M3 混合检索结果 ---")
            bge_hybrid_results = bge_searcher.hybrid_search_native(query, limit=3)
            for j, result in enumerate(bge_hybrid_results, 1):
                logger.info(f"{j}. [分数: {result['score']:.4f}] {result['text'][:50]}...")
            
            # BM25 混合检索
            logger.info(f"\n--- BM25 混合检索结果 ---")
            bm25_hybrid_results = bm25_searcher.hybrid_search_bm25(query, limit=3)
            for j, result in enumerate(bm25_hybrid_results, 1):
                logger.info(f"{j}. [分数: {result['score']:.4f}] {result['text'][:50]}...")
        
        # 总结分析
        logger.info(f"\n{'='*60}")
        logger.info("📊 对比测试总结")
        logger.info(f"{'='*60}")
        
        logger.info(f"\n🎯 BGE-M3 原生稀疏向量优势:")
        logger.info(f"✅ 更好的语义理解能力")
        logger.info(f"✅ 支持词汇扩展和同义词匹配")
        logger.info(f"✅ 学习的权重分配更智能")
        logger.info(f"✅ 与密集向量来自同一模型，一致性好")
        
        logger.info(f"\n🎯 BM25 稀疏向量优势:")
        logger.info(f"✅ 计算速度更快")
        logger.info(f"✅ 内存占用更小")
        logger.info(f"✅ 精确关键词匹配")
        logger.info(f"✅ 实现简单，易于理解")
        
        logger.info(f"\n🎯 推荐使用场景:")
        logger.info(f"📈 BGE-M3 原生稀疏向量：高质量语义检索、多语言应用、复杂查询理解")
        logger.info(f"📈 BM25 稀疏向量：精确关键词搜索、资源受限环境、实时检索需求")
        
    except Exception as e:
        logger.error(f"对比测试失败: {e}")
        raise


if __name__ == "__main__":
    run_comparison_test()
