<template>
  <el-dialog
    v-model="visible"
    title="上传文件"
    width="600px"
    @close="handleClose"
  >
    <div class="upload-container">
      <el-upload
        ref="uploadRef"
        class="upload-dragger"
        drag
        :auto-upload="false"
        :accept="acceptedTypes"
        :on-change="handleFileChange"
        :file-list="fileList"
        :limit="1"
        :on-exceed="handleExceed"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 PDF 和 Markdown 文件，文件大小不超过 50MB
          </div>
        </template>
      </el-upload>

      <el-form
        v-if="fileList.length > 0"
        :model="form"
        label-width="80px"
        style="margin-top: 20px"
      >
        <el-form-item label="标签">
          <el-select
            v-model="form.tags"
            multiple
            filterable
            allow-create
            placeholder="选择或创建标签"
            style="width: 100%"
          >
            <el-option
              v-for="tag in availableTags"
              :key="tag.id"
              :label="tag.name"
              :value="tag.name"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <div v-if="uploadProgress > 0" class="upload-progress">
        <el-progress
          :percentage="uploadProgress"
          :status="uploadStatus"
        />
        <p class="progress-text">{{ progressText }}</p>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleUpload"
          :loading="uploading"
          :disabled="fileList.length === 0"
        >
          上传
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { UploadInstance, UploadFile, UploadFiles } from 'element-plus'
import { notesApi } from '@/api'
import type { Note, Tag } from '@/types'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'uploaded', note: Note): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const uploadRef = ref<UploadInstance>()
const uploading = ref(false)
const uploadProgress = ref(0)
const uploadStatus = ref<'success' | 'exception' | 'warning' | ''>('')
const progressText = ref('')
const availableTags = ref<Tag[]>([])
const fileList = ref<UploadFiles>([])

const form = ref({
  tags: [] as string[]
})

const acceptedTypes = '.pdf,.md,.markdown'

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loadTags = async () => {
  try {
    availableTags.value = await notesApi.getTags()
  } catch (error) {
    console.error('加载标签失败:', error)
  }
}

const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  fileList.value = files
  
  // 验证文件类型
  const allowedTypes = ['application/pdf', 'text/markdown', 'text/plain']
  const fileExtension = file.name.split('.').pop()?.toLowerCase()
  
  if (!allowedTypes.includes(file.raw?.type || '') && 
      !['pdf', 'md', 'markdown'].includes(fileExtension || '')) {
    ElMessage.error('只支持 PDF 和 Markdown 文件')
    fileList.value = []
    return
  }
  
  // 验证文件大小 (50MB)
  if (file.size && file.size > 50 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过 50MB')
    fileList.value = []
    return
  }
}

const handleExceed = () => {
  ElMessage.warning('只能上传一个文件')
}

const handleUpload = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请选择要上传的文件')
    return
  }

  const file = fileList.value[0].raw
  if (!file) return

  uploading.value = true
  uploadProgress.value = 0
  uploadStatus.value = ''
  progressText.value = '正在上传文件...'

  try {
    let result: Note

    if (file.name.endsWith('.pdf')) {
      // 上传PDF文件
      progressText.value = '正在处理PDF文件...'
      result = await notesApi.uploadPdf(file, form.value.tags)
    } else {
      // 上传Markdown文件
      progressText.value = '正在处理Markdown文件...'
      result = await notesApi.uploadMarkdown(file, form.value.tags)
    }

    uploadProgress.value = 100
    uploadStatus.value = 'success'
    progressText.value = '上传完成！'

    emit('uploaded', result)
    
    setTimeout(() => {
      handleClose()
    }, 1000)

  } catch (error) {
    console.error('文件上传失败:', error)
    uploadStatus.value = 'exception'
    progressText.value = '上传失败'
    ElMessage.error('文件上传失败')
  } finally {
    uploading.value = false
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  fileList.value = []
  form.value.tags = []
  uploadProgress.value = 0
  uploadStatus.value = ''
  progressText.value = ''
  uploadRef.value?.clearFiles()
}

watch(visible, (newVal) => {
  if (newVal) {
    loadTags()
  }
})
</script>

<style lang="scss" scoped>
.upload-container {
  .upload-dragger {
    width: 100%;
  }

  .upload-progress {
    margin-top: 20px;
    
    .progress-text {
      margin-top: 8px;
      text-align: center;
      color: #606266;
      font-size: 14px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

:deep(.el-upload-dragger) {
  width: 100%;
}
</style>
