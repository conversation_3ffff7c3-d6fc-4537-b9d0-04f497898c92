<template>
  <div class="file-upload">
    <!-- 拖拽上传区域 -->
    <div
      class="upload-dragger"
      :class="{
        'is-dragover': isDragOver,
        'is-disabled': disabled
      }"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      @click="triggerFileInput"
    >
      <input
        ref="fileInputRef"
        type="file"
        :multiple="multiple"
        :accept="accept"
        @change="handleFileSelect"
        style="display: none"
      />
      
      <div class="upload-icon">
        <el-icon><UploadFilled /></el-icon>
      </div>
      
      <div class="upload-text">
        <p class="upload-title">{{ title }}</p>
        <p class="upload-hint">{{ hint }}</p>
      </div>
    </div>

    <!-- 文件列表 -->
    <div v-if="fileList.length > 0" class="file-list">
      <div class="list-header">
        <span>文件列表 ({{ fileList.length }})</span>
        <div class="list-actions">
          <el-button
            v-if="hasWaiting"
            type="primary"
            size="small"
            @click="startUpload"
            :loading="uploading"
          >
            开始上传
          </el-button>
          <el-button
            size="small"
            @click="clearCompleted"
            :disabled="!hasCompleted"
          >
            清空已完成
          </el-button>
          <el-button
            size="small"
            @click="clearAll"
          >
            清空全部
          </el-button>
        </div>
      </div>
      
      <div class="file-items">
        <div
          v-for="file in fileList"
          :key="file.id"
          class="file-item"
          :class="`status-${file.status}`"
        >
          <div class="file-info">
            <div class="file-icon">
              <el-icon>
                <Document v-if="file.name.endsWith('.pdf')" />
                <EditPen v-else-if="file.name.endsWith('.md')" />
                <DocumentCopy v-else />
              </el-icon>
            </div>
            
            <div class="file-details">
              <div class="file-name" :title="file.name">{{ file.name }}</div>
              <div class="file-meta">
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
                <span class="file-status">{{ getStatusText(file.status) }}</span>
              </div>
            </div>
          </div>
          
          <div class="file-progress">
            <el-progress
              v-if="file.status === 'uploading'"
              :percentage="file.progress"
              :stroke-width="4"
              :show-text="false"
            />
            <div v-else-if="file.status === 'success'" class="success-icon">
              <el-icon color="var(--success-color)"><CircleCheck /></el-icon>
            </div>
            <div v-else-if="file.status === 'error'" class="error-icon">
              <el-icon color="var(--danger-color)"><CircleClose /></el-icon>
            </div>
          </div>
          
          <div class="file-actions">
            <el-button
              v-if="file.status === 'error'"
              type="text"
              size="small"
              @click="retryUpload(file.id)"
            >
              重试
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="removeFile(file.id)"
            >
              移除
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 总体进度 -->
      <div v-if="uploading" class="total-progress">
        <div class="progress-info">
          <span>总进度: {{ totalProgress }}%</span>
          <span>{{ successCount }}/{{ fileList.length }} 已完成</span>
        </div>
        <el-progress
          :percentage="totalProgress"
          :stroke-width="6"
          :show-text="false"
        />
      </div>
    </div>

    <!-- 上传配置 -->
    <div class="upload-config">
      <el-collapse>
        <el-collapse-item title="上传设置" name="config">
          <div class="config-form">
            <el-form label-width="100px" size="small">
              <el-form-item label="自动上传">
                <el-switch
                  v-model="config.autoUpload"
                  @change="updateConfig"
                />
              </el-form-item>
              
              <el-form-item label="最大文件大小">
                <el-input-number
                  v-model="config.maxFileSize"
                  :min="1"
                  :max="500"
                  @change="updateConfig"
                />
                <span class="config-unit">MB</span>
              </el-form-item>
              
              <el-form-item label="并发上传数">
                <el-input-number
                  v-model="config.maxConcurrent"
                  :min="1"
                  :max="10"
                  @change="updateConfig"
                />
              </el-form-item>
            </el-form>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUploadStore } from '@/stores'
import { formatFileSize } from '@/utils'
import {
  UploadFilled,
  Document,
  EditPen,
  DocumentCopy,
  CircleCheck,
  CircleClose
} from '@element-plus/icons-vue'

interface Props {
  multiple?: boolean
  accept?: string
  disabled?: boolean
  title?: string
  hint?: string
}

const props = withDefaults(defineProps<Props>(), {
  multiple: true,
  accept: '.pdf,.md,.txt',
  disabled: false,
  title: '拖拽文件到此处或点击上传',
  hint: '支持 PDF、Markdown 等格式，单个文件不超过 100MB'
})

const uploadStore = useUploadStore()
const fileInputRef = ref<HTMLInputElement>()
const isDragOver = ref(false)

// 计算属性
const fileList = computed(() => uploadStore.files)
const uploading = computed(() => uploadStore.hasUploading)
const totalProgress = computed(() => uploadStore.totalProgress)
const config = computed(() => uploadStore.config)

const hasWaiting = computed(() => uploadStore.pendingFiles.length > 0)
const hasCompleted = computed(() => 
  uploadStore.successFiles.length > 0 || uploadStore.failedFiles.length > 0
)

const successCount = computed(() => uploadStore.successFiles.length)

// 拖拽处理
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  e.stopPropagation()
}

const handleDragEnter = (e: DragEvent) => {
  e.preventDefault()
  e.stopPropagation()
  isDragOver.value = true
}

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  e.stopPropagation()
  isDragOver.value = false
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  e.stopPropagation()
  isDragOver.value = false
  
  if (props.disabled) return
  
  const files = e.dataTransfer?.files
  if (files && files.length > 0) {
    uploadStore.addFiles(files)
  }
}

// 文件选择
const triggerFileInput = () => {
  if (props.disabled) return
  fileInputRef.value?.click()
}

const handleFileSelect = (e: Event) => {
  const target = e.target as HTMLInputElement
  const files = target.files
  
  if (files && files.length > 0) {
    uploadStore.addFiles(files)
  }
  
  // 清空input值，允许重复选择同一文件
  target.value = ''
}

// 上传控制
const startUpload = () => {
  uploadStore.startUpload()
}

const retryUpload = (fileId: string) => {
  uploadStore.retryUpload(fileId)
}

const removeFile = (fileId: string) => {
  uploadStore.removeFile(fileId)
}

const clearCompleted = () => {
  uploadStore.clearCompleted()
}

const clearAll = () => {
  uploadStore.clearFiles()
}

// 配置更新
const updateConfig = () => {
  uploadStore.updateConfig(config.value)
}

// 状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    waiting: '等待上传',
    uploading: '上传中',
    success: '上传成功',
    error: '上传失败'
  }
  return statusMap[status] || status
}

// 组件挂载
onMounted(() => {
  // 可以在这里初始化一些配置
})
</script>

<style lang="scss" scoped>
.file-upload {
  .upload-dragger {
    border: 2px dashed var(--border-base);
    border-radius: var(--border-radius-base);
    padding: var(--spacing-xl);
    text-align: center;
    cursor: pointer;
    transition: var(--transition-base);
    background-color: var(--bg-color);
    
    &:hover:not(.is-disabled) {
      border-color: var(--primary-color);
      background-color: var(--primary-color);
      background-opacity: 0.05;
    }
    
    &.is-dragover {
      border-color: var(--primary-color);
      background-color: rgba(64, 158, 255, 0.1);
    }
    
    &.is-disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }
    
    .upload-icon {
      font-size: 48px;
      color: var(--text-placeholder);
      margin-bottom: var(--spacing-md);
    }
    
    .upload-text {
      .upload-title {
        font-size: var(--font-size-lg);
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
      }
      
      .upload-hint {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
      }
    }
  }
  
  .file-list {
    margin-top: var(--spacing-lg);
    
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-md);
      padding-bottom: var(--spacing-sm);
      border-bottom: 1px solid var(--border-lighter);
      
      .list-actions {
        display: flex;
        gap: var(--spacing-sm);
      }
    }
    
    .file-items {
      max-height: 400px;
      overflow-y: auto;
      @include scrollbar;
    }
    
    .file-item {
      display: flex;
      align-items: center;
      padding: var(--spacing-md);
      border: 1px solid var(--border-lighter);
      border-radius: var(--border-radius-base);
      margin-bottom: var(--spacing-sm);
      background-color: var(--bg-color);
      transition: var(--transition-base);
      
      &:hover {
        box-shadow: var(--shadow-base);
      }
      
      &.status-success {
        border-color: var(--success-color);
        background-color: rgba(103, 194, 58, 0.05);
      }
      
      &.status-error {
        border-color: var(--danger-color);
        background-color: rgba(245, 108, 108, 0.05);
      }
      
      .file-info {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
        
        .file-icon {
          font-size: 24px;
          color: var(--primary-color);
          margin-right: var(--spacing-md);
        }
        
        .file-details {
          flex: 1;
          min-width: 0;
          
          .file-name {
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            @include text-ellipsis(1);
            margin-bottom: 2px;
          }
          
          .file-meta {
            display: flex;
            gap: var(--spacing-md);
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            
            .file-status {
              &.status-success {
                color: var(--success-color);
              }
              
              &.status-error {
                color: var(--danger-color);
              }
            }
          }
        }
      }
      
      .file-progress {
        width: 120px;
        margin: 0 var(--spacing-md);
        
        .success-icon,
        .error-icon {
          text-align: center;
          font-size: 20px;
        }
      }
      
      .file-actions {
        display: flex;
        gap: var(--spacing-sm);
      }
    }
    
    .total-progress {
      margin-top: var(--spacing-lg);
      padding: var(--spacing-md);
      background-color: var(--bg-color);
      border-radius: var(--border-radius-base);
      border: 1px solid var(--border-lighter);
      
      .progress-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: var(--spacing-sm);
        font-size: var(--font-size-sm);
        color: var(--text-regular);
      }
    }
  }
  
  .upload-config {
    margin-top: var(--spacing-lg);
    
    .config-form {
      .config-unit {
        margin-left: var(--spacing-sm);
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
      }
    }
  }
}
</style>
