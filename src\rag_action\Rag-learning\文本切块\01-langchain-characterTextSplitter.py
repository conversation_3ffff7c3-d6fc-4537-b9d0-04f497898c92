from langchain_community.document_loaders import TextLoader
from langchain_text_splitters import CharacterTextSplitter
loader = TextLoader("C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/云冈石窟.txt", encoding="utf-8")
documents = loader.load()
# 设置分块器，指定块的大小为50个字符，无重叠
# 下面这段代码用于将加载的文档进行文本切块，便于后续的向量化、检索等操作。
# 整个过程如下：
# 1. 首先实例化一个 CharacterTextSplitter 分块器。
# 2. 通过设置 chunk_size=100，表示每个文本块的最大长度为100个字符。
# 3. 设置 chunk_overlap=10，表示相邻文本块之间有10个字符的重叠，这样可以减少信息割裂，提升检索时的上下文连贯性。
# 4. 分块器会自动遍历输入的文档，将其切分为若干个长度不超过100字符、且相邻块有10字符重叠的小块。
# 5. 这些小块后续可以用于向量化、相似度检索等任务，提升大模型处理长文档的能力。
text_splitter = CharacterTextSplitter(
    chunk_size=100,      # 每个文本块的最大长度为100个字符
    chunk_overlap=10,    # 相邻文本块之间有10个字符的重叠
)
chunks = text_splitter.split_documents(documents)
print("\n=== 文档分块结果 ===")
for i, chunk in enumerate(chunks, 1):
    print(f"\n--- 第 {i} 个文档块 ---")
    print(f"内容: {chunk.page_content}")
    print(f"元数据: {chunk.metadata}")
    print("-" * 50)