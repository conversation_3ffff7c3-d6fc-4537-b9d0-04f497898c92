<template>
  <div class="notes-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">笔记管理</h1>
        <p class="page-subtitle">管理和搜索您的所有笔记</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="createNote">
          <el-icon><Plus /></el-icon>
          新建笔记
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索笔记标题或内容..."
          clearable
          @input="handleSearch"
          @clear="handleSearchClear"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      
      <div class="filter-bar">
        <el-select
          v-model="selectedSourceType"
          placeholder="文档类型"
          clearable
          @change="handleFilter"
        >
          <el-option label="PDF文档" value="pdf" />
          <el-option label="Markdown" value="markdown" />
          <el-option label="手动创建" value="manual" />
        </el-select>
        
        <el-select
          v-model="selectedTags"
          placeholder="选择标签"
          multiple
          clearable
          @change="handleFilter"
        >
          <el-option
            v-for="tag in tags"
            :key="tag.id"
            :label="tag.name"
            :value="tag.name"
          />
        </el-select>
        
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="handleFilter"
        />
        
        <el-button @click="clearFilters">
          <el-icon><RefreshLeft /></el-icon>
          清空筛选
        </el-button>
      </div>
    </div>

    <!-- 笔记列表 -->
    <div class="notes-content">
      <!-- 视图切换 -->
      <div class="view-controls">
        <div class="view-modes">
          <el-radio-group v-model="viewMode" @change="handleViewModeChange">
            <el-radio-button label="grid">
              <el-icon><Grid /></el-icon>
              网格视图
            </el-radio-button>
            <el-radio-button label="list">
              <el-icon><List /></el-icon>
              列表视图
            </el-radio-button>
          </el-radio-group>
        </div>
        
        <div class="sort-controls">
          <el-select v-model="sortBy" @change="handleSort">
            <el-option label="创建时间" value="created_at" />
            <el-option label="更新时间" value="updated_at" />
            <el-option label="标题" value="title" />
            <el-option label="文件大小" value="file_size" />
          </el-select>
          
          <el-button
            :type="sortOrder === 'desc' ? 'primary' : 'default'"
            @click="toggleSortOrder"
          >
            <el-icon><Sort /></el-icon>
            {{ sortOrder === 'desc' ? '降序' : '升序' }}
          </el-button>
        </div>
      </div>

      <!-- 笔记网格视图 -->
      <div v-if="viewMode === 'grid'" class="notes-grid" v-loading="loading">
        <div
          v-for="note in notes"
          :key="note.id"
          class="note-card"
          @click="viewNote(note.id)"
        >
          <div class="card-header">
            <div class="note-type">
              <el-icon>
                <Document v-if="note.source_type === 'pdf'" />
                <EditPen v-else-if="note.source_type === 'markdown'" />
                <DocumentCopy v-else />
              </el-icon>
              <span>{{ getSourceTypeLabel(note.source_type) }}</span>
            </div>
            <el-dropdown @command="handleNoteAction">
              <el-button type="text" size="small">
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`view-${note.id}`">查看</el-dropdown-item>
                  <el-dropdown-item :command="`edit-${note.id}`">编辑</el-dropdown-item>
                  <el-dropdown-item :command="`delete-${note.id}`" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          
          <div class="card-content">
            <h3 class="note-title" :title="note.title">{{ note.title }}</h3>
            <p class="note-excerpt">{{ getExcerpt(note.content) }}</p>
          </div>
          
          <div class="card-footer">
            <div class="note-tags">
              <el-tag
                v-for="tag in note.tags.slice(0, 2)"
                :key="tag.id"
                size="small"
                type="info"
              >
                {{ tag.name }}
              </el-tag>
              <span v-if="note.tags.length > 2" class="more-tags">
                +{{ note.tags.length - 2 }}
              </span>
            </div>
            
            <div class="note-meta">
              <span class="note-time">{{ timeAgo(note.updated_at) }}</span>
              <span v-if="note.file_size" class="note-size">{{ formatFileSize(note.file_size) }}</span>
            </div>
          </div>
        </div>
        
        <el-empty v-if="notes.length === 0 && !loading" description="暂无笔记" />
      </div>

      <!-- 笔记列表视图 -->
      <div v-else class="notes-list" v-loading="loading">
        <el-table :data="notes" @row-click="viewNote">
          <el-table-column prop="title" label="标题" min-width="200">
            <template #default="{ row }">
              <div class="title-cell">
                <el-icon class="type-icon">
                  <Document v-if="row.source_type === 'pdf'" />
                  <EditPen v-else-if="row.source_type === 'markdown'" />
                  <DocumentCopy v-else />
                </el-icon>
                <span class="title-text">{{ row.title }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="source_type" label="类型" width="100">
            <template #default="{ row }">
              {{ getSourceTypeLabel(row.source_type) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="tags" label="标签" width="200">
            <template #default="{ row }">
              <div class="tags-cell">
                <el-tag
                  v-for="tag in row.tags.slice(0, 3)"
                  :key="tag.id"
                  size="small"
                  type="info"
                >
                  {{ tag.name }}
                </el-tag>
                <span v-if="row.tags.length > 3" class="more-tags">
                  +{{ row.tags.length - 3 }}
                </span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="file_size" label="大小" width="100">
            <template #default="{ row }">
              {{ row.file_size ? formatFileSize(row.file_size) : '-' }}
            </template>
          </el-table-column>
          
          <el-table-column prop="updated_at" label="更新时间" width="150">
            <template #default="{ row }">
              {{ timeAgo(row.updated_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click.stop="viewNote(row.id)">
                查看
              </el-button>
              <el-button type="text" size="small" @click.stop="editNote(row.id)">
                编辑
              </el-button>
              <el-button type="text" size="small" @click.stop="deleteNote(row.id)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useNotesStore } from '@/stores'
import { formatFileSize, timeAgo, debounce } from '@/utils'
import {
  Plus,
  Search,
  RefreshLeft,
  Grid,
  List,
  Sort,
  Document,
  EditPen,
  DocumentCopy,
  MoreFilled
} from '@element-plus/icons-vue'

const router = useRouter()
const notesStore = useNotesStore()

// 响应式数据
const searchKeyword = ref('')
const selectedSourceType = ref('')
const selectedTags = ref([])
const dateRange = ref([])
const viewMode = ref('grid')
const sortBy = ref('updated_at')
const sortOrder = ref('desc')
const currentPage = ref(1)
const pageSize = ref(20)

// 计算属性
const notes = computed(() => notesStore.notes)
const tags = computed(() => notesStore.tags)
const total = computed(() => notesStore.totalNotes)
const loading = computed(() => notesStore.loading)

// 防抖搜索
const handleSearch = debounce(() => {
  currentPage.value = 1
  fetchNotes()
}, 500)

// 方法
const getSourceTypeLabel = (type: string) => {
  const labelMap = {
    pdf: 'PDF',
    markdown: 'MD',
    manual: '手动'
  }
  return labelMap[type] || type
}

const getExcerpt = (content: string) => {
  return content.length > 100 ? content.substring(0, 100) + '...' : content
}

const fetchNotes = async () => {
  const params = {
    page: currentPage.value,
    page_size: pageSize.value,
    keyword: searchKeyword.value,
    source_type: selectedSourceType.value,
    tags: selectedTags.value,
    // date_range: dateRange.value
  }
  
  await notesStore.fetchNotes(params)
}

const handleSearchClear = () => {
  searchKeyword.value = ''
  fetchNotes()
}

const handleFilter = () => {
  currentPage.value = 1
  fetchNotes()
}

const clearFilters = () => {
  searchKeyword.value = ''
  selectedSourceType.value = ''
  selectedTags.value = []
  dateRange.value = []
  currentPage.value = 1
  fetchNotes()
}

const handleViewModeChange = () => {
  // 保存视图模式到本地存储
  localStorage.setItem('notesViewMode', viewMode.value)
}

const handleSort = () => {
  fetchNotes()
}

const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'desc' ? 'asc' : 'desc'
  fetchNotes()
}

const handleSizeChange = () => {
  currentPage.value = 1
  fetchNotes()
}

const handleCurrentChange = () => {
  fetchNotes()
}

const viewNote = (noteId: number) => {
  router.push(`/notes/${noteId}`)
}

const editNote = (noteId: number) => {
  router.push(`/notes/${noteId}/edit`)
}

const createNote = () => {
  router.push('/notes/create')
}

const deleteNote = async (noteId: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这篇笔记吗？', '确认删除', {
      type: 'warning'
    })
    
    await notesStore.deleteNote(noteId)
    ElMessage.success('删除成功')
    fetchNotes()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleNoteAction = (command: string) => {
  const [action, noteId] = command.split('-')
  const id = parseInt(noteId)
  
  switch (action) {
    case 'view':
      viewNote(id)
      break
    case 'edit':
      editNote(id)
      break
    case 'delete':
      deleteNote(id)
      break
  }
}

// 生命周期
onMounted(() => {
  // 恢复视图模式
  const savedViewMode = localStorage.getItem('notesViewMode')
  if (savedViewMode) {
    viewMode.value = savedViewMode
  }
  
  // 获取数据
  fetchNotes()
  notesStore.fetchTags()
})

// 监听路由变化
watch(() => router.currentRoute.value, () => {
  if (router.currentRoute.value.name === 'Notes') {
    fetchNotes()
  }
})
</script>

<style lang="scss" scoped>
.notes-page {
  padding: var(--spacing-lg);
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-xl);
    
    .header-left {
      .page-title {
        font-size: 28px;
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
      }
      
      .page-subtitle {
        color: var(--text-secondary);
        font-size: var(--font-size-lg);
      }
    }
  }
  
  .search-section {
    margin-bottom: var(--spacing-lg);
    
    .search-bar {
      margin-bottom: var(--spacing-md);
      
      .el-input {
        max-width: 400px;
      }
    }
    
    .filter-bar {
      display: flex;
      gap: var(--spacing-md);
      flex-wrap: wrap;
      align-items: center;
      
      .el-select {
        min-width: 120px;
      }
      
      .el-date-picker {
        min-width: 240px;
      }
    }
  }
  
  .notes-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .view-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      
      .sort-controls {
        display: flex;
        gap: var(--spacing-sm);
        align-items: center;
      }
    }
    
    .notes-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: var(--spacing-lg);
      flex: 1;
      
      .note-card {
        background: var(--bg-color);
        border-radius: var(--border-radius-base);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-lg);
        cursor: pointer;
        transition: var(--transition-base);
        display: flex;
        flex-direction: column;
        height: 240px;
        
        &:hover {
          box-shadow: var(--shadow-light);
          transform: translateY(-2px);
        }
        
        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: var(--spacing-md);
          
          .note-type {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
          }
        }
        
        .card-content {
          flex: 1;
          
          .note-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            @include text-ellipsis(2);
          }
          
          .note-excerpt {
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
            line-height: 1.6;
            @include text-ellipsis(3);
          }
        }
        
        .card-footer {
          .note-tags {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
            align-items: center;
            
            .more-tags {
              font-size: var(--font-size-xs);
              color: var(--text-secondary);
            }
          }
          
          .note-meta {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
          }
        }
      }
    }
    
    .notes-list {
      flex: 1;
      
      .title-cell {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        
        .type-icon {
          color: var(--primary-color);
        }
        
        .title-text {
          @include text-ellipsis(1);
        }
      }
      
      .tags-cell {
        display: flex;
        gap: var(--spacing-sm);
        align-items: center;
        flex-wrap: wrap;
        
        .more-tags {
          font-size: var(--font-size-xs);
          color: var(--text-secondary);
        }
      }
    }
    
    .pagination-wrapper {
      margin-top: var(--spacing-lg);
      display: flex;
      justify-content: center;
    }
  }
}

@include respond-below(md) {
  .notes-page {
    padding: var(--spacing-md);
    
    .page-header {
      flex-direction: column;
      gap: var(--spacing-md);
    }
    
    .filter-bar {
      flex-direction: column;
      align-items: stretch;
      
      .el-select,
      .el-date-picker {
        width: 100%;
      }
    }
    
    .view-controls {
      flex-direction: column;
      gap: var(--spacing-md);
      align-items: stretch;
    }
    
    .notes-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
