import { http } from '../request'
import type { Note, NoteCreate, NotesListResponse, Tag } from '@/types'

/**
 * 笔记相关API
 */
export const notesApi = {
  /**
   * 获取笔记列表
   */
  getNotes(params: {
    page?: number
    page_size?: number
    keyword?: string
    tags?: string[]
    source_type?: string
  } = {}): Promise<NotesListResponse> {
    return http.get('/notes', { params })
  },

  /**
   * 获取笔记详情
   */
  getNoteById(id: number): Promise<Note> {
    return http.get(`/note/${id}`)
  },

  /**
   * 创建笔记
   */
  createNote(data: NoteCreate): Promise<Note> {
    return http.post('/note', data)
  },

  /**
   * 更新笔记
   */
  updateNote(id: number, data: Partial<NoteCreate>): Promise<Note> {
    return http.put(`/note/${id}`, data)
  },

  /**
   * 删除笔记
   */
  deleteNote(id: number): Promise<void> {
    return http.delete(`/note/${id}`)
  },

  /**
   * 上传PDF文档（同步）
   */
  uploadPdf(file: File, tags?: string[]): Promise<Note> {
    const formData = new FormData()
    formData.append('file', file)
    if (tags && tags.length > 0) {
      tags.forEach(tag => formData.append('tags', tag))
    }
    return http.upload('/upload/pdf', formData)
  },

  /**
   * 上传PDF文档（异步）
   */
  uploadPdfAsync(file: File, tags?: string[]): Promise<{ task_id: string }> {
    const formData = new FormData()
    formData.append('file', file)
    if (tags && tags.length > 0) {
      tags.forEach(tag => formData.append('tags', tag))
    }
    return http.upload('/upload/pdf/async', formData)
  },

  /**
   * 上传Markdown文件
   */
  uploadMarkdown(file: File, tags?: string[]): Promise<Note> {
    const formData = new FormData()
    formData.append('file', file)
    if (tags && tags.length > 0) {
      tags.forEach(tag => formData.append('tags', tag))
    }
    return http.upload('/upload/markdown', formData)
  },

  /**
   * 获取标签列表
   */
  getTags(): Promise<Tag[]> {
    return http.get('/tags')
  },

  /**
   * 创建标签
   */
  createTag(name: string, color?: string): Promise<Tag> {
    return http.post('/tags', { name, color })
  },

  /**
   * 删除标签
   */
  deleteTag(id: number): Promise<void> {
    return http.delete(`/tags/${id}`)
  },

  /**
   * 搜索笔记
   */
  searchNotes(params: {
    keyword: string
    page?: number
    page_size?: number
    tags?: string[]
    source_type?: string
  }): Promise<NotesListResponse> {
    return http.get('/notes/search', { params })
  },

  /**
   * 获取笔记统计信息
   */
  getNotesStats(): Promise<{
    total_notes: number
    total_tags: number
    total_size: number
    by_source_type: Record<string, number>
    by_month: Array<{ month: string; count: number }>
  }> {
    return http.get('/notes/stats')
  }
}
