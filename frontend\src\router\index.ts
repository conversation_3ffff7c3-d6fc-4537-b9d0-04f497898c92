import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAppStore } from '@/stores'
import NProgress from 'nprogress'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard/DashboardMain.vue'),
    meta: {
      title: 'AI笔记系统',
      icon: 'Dashboard',
      keepAlive: true
    }
  },
  {
    path: '/notes',
    name: 'Notes',
    component: () => import('@/views/Notes/index.vue'),
    meta: {
      title: '笔记管理',
      icon: 'Document',
      keepAlive: true
    }
  },
  {
    path: '/notes/:id',
    name: 'NoteDetail',
    component: () => import('@/views/Notes/Detail.vue'),
    meta: {
      title: '笔记详情',
      hidden: true
    }
  },
  {
    path: '/notes/:id/edit',
    name: 'NoteEdit',
    component: () => import('@/views/Notes/Edit.vue'),
    meta: {
      title: '编辑笔记',
      hidden: true
    }
  },
  {
    path: '/upload',
    name: 'Upload',
    component: () => import('@/views/Upload/index.vue'),
    meta: {
      title: '文档上传',
      icon: 'Upload'
    }
  },
  {
    path: '/query',
    name: 'Query',
    component: () => import('@/views/Query/index.vue'),
    meta: {
      title: '智能问答',
      icon: 'ChatDotRound',
      keepAlive: true
    }
  },
  {
    path: '/streaming-test',
    name: 'StreamingTest',
    component: () => import('@/views/StreamingTest.vue'),
    meta: {
      title: '流式问答测试',
      icon: 'Lightning',
      keepAlive: true
    }
  },
  {
    path: '/system',
    name: 'System',
    component: () => import('@/views/System/index.vue'),
    meta: {
      title: '系统监控',
      icon: 'Monitor'
    },
    children: [
      {
        path: '',
        redirect: '/system/overview'
      },
      {
        path: 'overview',
        name: 'SystemOverview',
        component: () => import('@/views/System/Overview.vue'),
        meta: {
          title: '系统概览'
        }
      },
      {
        path: 'health',
        name: 'SystemHealth',
        component: () => import('@/views/System/Health.vue'),
        meta: {
          title: '健康检查'
        }
      },
      {
        path: 'logs',
        name: 'SystemLogs',
        component: () => import('@/views/System/Logs.vue'),
        meta: {
          title: '系统日志'
        }
      },
      {
        path: 'backup',
        name: 'SystemBackup',
        component: () => import('@/views/System/Backup.vue'),
        meta: {
          title: '数据备份'
        }
      }
    ]
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/Settings/index.vue'),
    meta: {
      title: '系统设置',
      icon: 'Setting',
      hidden: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/Error/404.vue'),
    meta: {
      title: '页面不存在',
      hidden: true
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 开始进度条
  NProgress.start()
  
  // 设置页面标题
  const title = to.meta?.title as string
  if (title) {
    document.title = `${title} - AI笔记系统`
  } else {
    document.title = 'AI笔记系统'
  }
  
  // 设置激活菜单
  const appStore = useAppStore()
  appStore.setActiveMenu(to.path)
  
  // 页面加载状态
  appStore.setPageLoading(true)
  
  next()
})

// 全局后置守卫
router.afterEach((to, from) => {
  // 结束进度条
  NProgress.done()
  
  // 清除页面加载状态
  const appStore = useAppStore()
  appStore.setPageLoading(false)
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  NProgress.done()
  
  const appStore = useAppStore()
  appStore.setPageLoading(false)
})

export default router
