#!/usr/bin/env python3
"""
基础PDF转Markdown转换脚本
参考 minerU_pdf2markdown.py 的实现方式
适合新手使用，最简单的配置
"""
import os
from pathlib import Path
from loguru import logger
import json

# 导入mineru相关模块
from mineru.cli.common import convert_pdf_bytes_to_bytes_by_pypdfium2, prepare_env, read_fn
from mineru.data.data_reader_writer import FileBasedDataWriter
from mineru.utils.draw_bbox import draw_layout_bbox, draw_span_bbox
from mineru.utils.enum_class import MakeMode
from mineru.backend.pipeline.pipeline_analyze import doc_analyze as pipeline_doc_analyze
from mineru.backend.pipeline.pipeline_middle_json_mkcontent import union_make as pipeline_union_make
from mineru.backend.pipeline.model_json_to_middle_json import result_to_middle_json as pipeline_result_to_middle_json

def main():
    """基础转换主函数 - 使用pipeline模式"""
    print("🚀 开始PDF转Markdown转换...")
    
    # 设置路径
    __dir__ = os.path.dirname(os.path.abspath(__file__))
    pdf_files_dir = os.path.join(__dir__, "pdfs")
    output_dir = os.path.join(__dir__, "output")
    
    # 确保目录存在
    os.makedirs(pdf_files_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)
    
    # 收集PDF文件
    pdf_suffixes = [".pdf"]
    doc_path_list = []
    
    for doc_path in Path(pdf_files_dir).glob('*'):
        if doc_path.suffix.lower() in pdf_suffixes:
            doc_path_list.append(doc_path)
    
    print(f"📁 找到 {len(doc_path_list)} 个PDF文件:")
    for path in doc_path_list:
        print(f"   - {path.name}")
    
    if not doc_path_list:
        print("❌ 未找到PDF文件！请将PDF文件放入 'pdfs' 目录")
        return
    
    # 开始转换
    try:
        print("\n🔄 开始转换...")
        
        # 准备数据
        file_name_list = []
        pdf_bytes_list = []
        lang_list = []
        
        for path in doc_path_list:
            file_name = str(Path(path).stem)
            pdf_bytes = read_fn(path)
            file_name_list.append(file_name)
            pdf_bytes_list.append(pdf_bytes)
            lang_list.append("ch")  # 中文文档
        
        # 使用pipeline模式进行转换
        parse_with_pipeline(
            output_dir=output_dir,
            pdf_file_names=file_name_list,
            pdf_bytes_list=pdf_bytes_list,
            p_lang_list=lang_list
        )
        
        print("✅ 转换完成！")
        print(f"📂 输出目录: {output_dir}")
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        logger.exception(e)

def parse_with_pipeline(
    output_dir,
    pdf_file_names: list[str],
    pdf_bytes_list: list[bytes],
    p_lang_list: list[str],
    parse_method="auto",
    formula_enable=True,
    table_enable=True,
    start_page_id=0,
    end_page_id=None
):
    """使用pipeline模式解析PDF"""
    
    # 处理页面范围
    for idx, pdf_bytes in enumerate(pdf_bytes_list):
        new_pdf_bytes = convert_pdf_bytes_to_bytes_by_pypdfium2(pdf_bytes, start_page_id, end_page_id)
        pdf_bytes_list[idx] = new_pdf_bytes

    # 使用pipeline进行分析
    infer_results, all_image_lists, all_pdf_docs, lang_list, ocr_enabled_list = pipeline_doc_analyze(
        pdf_bytes_list, 
        p_lang_list, 
        parse_method=parse_method, 
        formula_enable=formula_enable,
        table_enable=table_enable
    )

    # 处理每个PDF文件
    for idx, model_list in enumerate(infer_results):
        model_json = model_list.copy()
        pdf_file_name = pdf_file_names[idx]
        
        # 准备环境
        local_image_dir, local_md_dir = prepare_env(output_dir, pdf_file_name, parse_method)
        image_writer, md_writer = FileBasedDataWriter(local_image_dir), FileBasedDataWriter(local_md_dir)

        images_list = all_image_lists[idx]
        pdf_doc = all_pdf_docs[idx]
        _lang = lang_list[idx]
        _ocr_enable = ocr_enabled_list[idx]
        
        # 转换为中间JSON格式
        middle_json = pipeline_result_to_middle_json(
            model_list, images_list, pdf_doc, image_writer, _lang, _ocr_enable, formula_enable
        )

        pdf_info = middle_json["pdf_info"]
        pdf_bytes = pdf_bytes_list[idx]

        # 生成Markdown文件
        image_dir = str(os.path.basename(local_image_dir))
        md_content_str = pipeline_union_make(pdf_info, MakeMode.MM_MD, image_dir)
        md_writer.write_string(f"{pdf_file_name}.md", md_content_str)

        # 生成内容列表
        content_list = pipeline_union_make(pdf_info, MakeMode.CONTENT_LIST, image_dir)
        md_writer.write_string(
            f"{pdf_file_name}_content_list.json",
            json.dumps(content_list, ensure_ascii=False, indent=4)
        )

        # 生成中间JSON
        md_writer.write_string(
            f"{pdf_file_name}_middle.json",
            json.dumps(middle_json, ensure_ascii=False, indent=4)
        )

        logger.info(f"输出目录: {local_md_dir}")

if __name__ == "__main__":
    main() 