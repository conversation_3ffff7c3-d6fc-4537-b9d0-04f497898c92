"""
服务管理器 - 实现单例模式和服务预加载
"""
import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime
import threading

logger = logging.getLogger(__name__)

class ServiceManager:
    """服务管理器 - 单例模式管理所有AI服务"""
    
    _instance = None
    _lock = threading.Lock()
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(ServiceManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._services: Dict[str, Any] = {}
            self._service_status: Dict[str, Dict[str, Any]] = {}
            self._initialization_lock = threading.Lock()
            ServiceManager._initialized = True
    
    def register_service(self, name: str, service_instance: Any, health_check_func: Optional[callable] = None):
        """注册服务实例"""
        with self._initialization_lock:
            self._services[name] = service_instance
            self._service_status[name] = {
                "status": "active",
                "last_check": datetime.now(),
                "health_check": health_check_func,
                "error_count": 0
            }
            logger.info(f"服务已注册: {name}")
    
    def get_service(self, name: str) -> Optional[Any]:
        """获取服务实例"""
        if name in self._services:
            # 检查服务健康状态
            self._check_service_health(name)
            return self._services[name]
        else:
            logger.warning(f"服务未找到: {name}")
            return None
    
    def _check_service_health(self, name: str):
        """检查服务健康状态"""
        if name not in self._service_status:
            return
        
        status_info = self._service_status[name]
        health_check = status_info.get("health_check")
        
        if health_check:
            try:
                is_healthy = health_check(self._services[name])
                if is_healthy:
                    status_info["status"] = "active"
                    status_info["error_count"] = 0
                else:
                    status_info["status"] = "unhealthy"
                    status_info["error_count"] += 1
            except Exception as e:
                logger.warning(f"服务健康检查失败 {name}: {e}")
                status_info["status"] = "error"
                status_info["error_count"] += 1
        
        status_info["last_check"] = datetime.now()
    
    def get_service_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有服务状态"""
        return self._service_status.copy()
    
    def is_service_healthy(self, name: str) -> bool:
        """检查服务是否健康"""
        if name not in self._service_status:
            return False
        return self._service_status[name]["status"] == "active"
    
    async def preload_all_services(self):
        """预加载所有服务"""
        logger.info("开始预加载所有AI服务...")

        try:
            # 预加载嵌入服务
            await self._preload_embedding_service()

            # 预加载LLM服务
            await self._preload_llm_service()

            # 预加载重排序服务
            await self._preload_rerank_service()

            # 预加载向量服务
            await self._preload_vector_service()

            # 预加载文档解析服务
            await self._preload_document_parser()

            # 预加载业务服务
            await self._preload_business_services()

            logger.info("✅ 所有AI服务预加载完成")

        except Exception as e:
            logger.error(f"❌ 服务预加载失败: {e}")
            raise
    
    async def _preload_embedding_service(self):
        """预加载嵌入服务"""
        try:
            from rag_action.service.embedding_service import EmbeddingServiceFactory

            logger.info("预加载嵌入服务...")
            embedding_service = EmbeddingServiceFactory.create_service()

            # 测试嵌入服务
            test_text = "测试文本"
            test_embedding = embedding_service.embed_text(test_text)
            logger.info(f"嵌入服务测试成功，向量维度: {len(test_embedding)}")

            # 注册服务
            self.register_service(
                "embedding_service",
                embedding_service,
                lambda service: hasattr(service, 'embed_text')
            )

            logger.info("✅ 嵌入服务预加载完成")

        except Exception as e:
            logger.error(f"❌ 嵌入服务预加载失败: {e}")
            raise
    
    async def _preload_llm_service(self):
        """预加载LLM服务"""
        try:
            from rag_action.service.rag_service import LLMService

            logger.info("预加载LLM服务...")
            llm_service = LLMService()

            # 测试LLM服务
            test_prompt = "测试"
            test_response, tokens = llm_service.generate_response(test_prompt)
            logger.info(f"LLM服务测试成功，响应长度: {len(test_response)}, 使用tokens: {tokens}")

            # 注册服务
            self.register_service(
                "llm_service",
                llm_service,
                lambda service: hasattr(service, 'client') and service.client is not None
            )

            logger.info("✅ LLM服务预加载完成")

        except Exception as e:
            logger.error(f"❌ LLM服务预加载失败: {e}")
            raise
    
    async def _preload_rerank_service(self):
        """预加载重排序服务"""
        try:
            from rag_action.service.reranker import Reranker
            from rag_action.core.config import get_settings

            logger.info("预加载重排序服务...")
            settings = get_settings()
            rerank_config = {
                "enabled": True,
                "model_name": settings.rerank.model_name if hasattr(settings, 'rerank') else "BAAI/bge-reranker-base",
                "top_k": 10
            }
            rerank_service = Reranker(rerank_config)

            # 注册服务
            self.register_service(
                "rerank_service",
                rerank_service,
                lambda service: hasattr(service, 'enabled')
            )

            logger.info("✅ 重排序服务预加载完成")

        except Exception as e:
            logger.error(f"❌ 重排序服务预加载失败: {e}")
            # 重排序服务失败不应该阻止启动
            logger.warning("重排序服务预加载失败，将在运行时创建")
    
    async def _preload_vector_service(self):
        """预加载向量服务"""
        try:
            from rag_action.service.enhanced_vector_service import EnhancedVectorService
            
            logger.info("预加载向量服务...")
            vector_service = EnhancedVectorService()
            
            # 注册服务
            self.register_service(
                "vector_service",
                vector_service,
                lambda service: hasattr(service, 'collection') and service.collection is not None
            )
            
            logger.info("✅ 向量服务预加载完成")
            
        except Exception as e:
            logger.error(f"❌ 向量服务预加载失败: {e}")
            raise
    
    async def _preload_document_parser(self):
        """预加载文档解析服务"""
        try:
            from rag_action.service.enhanced_document_parser import EnhancedDocumentParser
            from rag_action.core.config import get_settings

            logger.info("预加载文档解析服务...")
            settings = get_settings()

            # 创建优化的配置
            parser_config = {
                "use_mineru": True,
                "chunk_size": settings.embedding.chunk_size,
                "chunk_overlap": settings.embedding.chunk_overlap,
                "context_window_size": 3,
                "enable_parent_child": True,
                "parent_chunk_size": 3000,  # 增加父文档块大小
                "min_chunk_size": 100,      # 最小块大小
                "filter_meaningless_chunks": True  # 启用智能过滤
            }

            document_parser = EnhancedDocumentParser(parser_config)

            # 注册服务
            self.register_service(
                "document_parser",
                document_parser,
                lambda service: service is not None
            )

            logger.info("✅ 文档解析服务预加载完成")

        except Exception as e:
            logger.error(f"❌ 文档解析服务预加载失败: {e}")
            # 文档解析服务失败不应该阻止启动
            logger.warning("文档解析服务预加载失败，将在运行时创建")

    async def _preload_business_services(self):
        """预加载业务服务"""
        try:
            logger.info("预加载业务服务...")

            # 注意：业务服务依赖于AI服务，所以在AI服务预加载完成后再创建
            # 这里只是标记业务服务可以使用预加载的AI服务

            logger.info("✅ 业务服务预加载配置完成（将在首次使用时创建）")

        except Exception as e:
            logger.error(f"❌ 业务服务预加载失败: {e}")
            # 业务服务失败不应该阻止启动
            logger.warning("业务服务预加载失败，将在运行时创建")

# 全局服务管理器实例
service_manager = ServiceManager()
