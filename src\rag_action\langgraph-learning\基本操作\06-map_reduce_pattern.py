"""
06 - Map-Reduce 模式
展示如何使用 LangGraph 实现 Map-Reduce 模式处理大量数据
"""

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from typing import Dict, List, Any, TypedDict, Annotated
import json
import asyncio
from datetime import datetime

# 创建 LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0.7  # 提高创造性，生成更有趣的笑话
)

# 定义状态类型
class AnimalJokeState(TypedDict):
    messages: Annotated[List, add_messages]  # 消息历史
    user_input: str  # 用户输入的动物
    animal_list: List[str]  # 动物列表
    joke_results: List[Dict[str, Any]]  # Map 结果（笑话）
    best_joke: str  # Reduce 结果（最好笑的笑话）
    execution_path: List[str]  # 执行路径

def animal_joke_map_reduce():
    """动物笑话 Map-Reduce 示例：输入动物 -> 生成多个动物 -> 并行生成笑话 -> 评选最佳笑话"""
    
    print("=== 动物笑话 Map-Reduce 示例 ===")
    print("流程：输入动物 -> 生成动物列表 -> 并行生成笑话 -> 评选最佳笑话")
    print()
    
    def generate_animals_node(state: AnimalJokeState) -> AnimalJokeState:
        """生成动物列表节点：基于用户输入生成相关动物列表"""
        user_input = state["user_input"]
        
        print(f"步骤1: 生成动物列表...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
用户输入了一个动物：{user_input}

请生成一个包含10个动物的列表，这些动物应该与"{user_input}"相关或者有趣。
可以是：
1. 同类型的动物（如猫、狗、兔子等宠物）
2. 有趣的动物组合
3. 不同环境的动物

请只返回一个JSON数组，格式如下：
["动物1", "动物2", "动物3", ...]

请只返回JSON数组：
""")
        ])
        
        try:
            animal_list = json.loads(response.content)
            if not isinstance(animal_list, list):
                animal_list = [user_input, "猫", "狗", "兔子", "熊猫", "老虎", "狮子", "大象", "长颈鹿", "企鹅", "考拉"]
        except:
            animal_list = [user_input, "猫", "狗", "兔子", "熊猫", "老虎", "狮子", "大象", "长颈鹿", "企鹅", "考拉"]
        
        return {
            **state,
            "animal_list": animal_list,
            "execution_path": state["execution_path"] + [f"生成动物列表({len(animal_list)}个动物)"],
            "messages": [AIMessage(content=f"生成了{len(animal_list)}个动物：{', '.join(animal_list)}")]
        }
    
    async def generate_jokes_map_node(state: AnimalJokeState) -> AnimalJokeState:
        """Map 节点：并行为每个动物生成笑话"""
        animal_list = state["animal_list"]
        
        print(f"步骤2: 并行生成笑话 ({len(animal_list)}个动物)...")
        
        async def generate_joke_for_animal(animal: str, animal_id: int) -> Dict[str, Any]:
            """为单个动物生成笑话"""
            print(f"  为 {animal} 生成笑话...")
            
            response = await llm.ainvoke([
                HumanMessage(content=f"""
请为动物"{animal}"创作一个有趣的笑话。

要求：
1. 笑话要幽默、有趣
2. 要围绕这个动物的特点
3. 长度适中，不要太长
4. 要有创意

请返回JSON格式的笑话：
{{
    "animal": "{animal}",
    "joke": "笑话内容",
    "humor_score": 1-10,
    "creativity_score": 1-10
}}

请只返回JSON：
""")
            ])
            
            try:
                result = json.loads(response.content)
                return result
            except:
                return {
                    "animal": animal,
                    "joke": f"为什么{animal}总是很开心？因为它有{animal}的快乐！",
                    "humor_score": 5,
                    "creativity_score": 5
                }
        
        # 并行为所有动物生成笑话
        tasks = [generate_joke_for_animal(animal, i) for i, animal in enumerate(animal_list)]
        joke_results = await asyncio.gather(*tasks)
        
        return {
            **state,
            "joke_results": joke_results,
            "execution_path": state["execution_path"] + ["并行生成笑话完成"],
            "messages": [AIMessage(content=f"为{len(joke_results)}个动物生成了笑话")]
        }
    
    async def select_best_joke_reduce_node(state: AnimalJokeState) -> AnimalJokeState:
        """Reduce 节点：评选最佳笑话"""
        joke_results = state["joke_results"]
        user_input = state["user_input"]
        
        print(f"步骤3: 评选最佳笑话...")
        
        # 将所有笑话转换为文本
        jokes_text = json.dumps(joke_results, ensure_ascii=False, indent=2)
        
        response = await llm.ainvoke([
            HumanMessage(content=f"""
用户最初输入的动物是：{user_input}

以下是所有动物的笑话：

{jokes_text}

请从这些笑话中评选出最好笑的一个，并给出评选理由。

请返回JSON格式的评选结果：
{{
    "best_joke": "最好笑的笑话内容",
    "best_animal": "最好笑的动物",
    "selection_reason": "为什么这个笑话最好笑",
    "overall_rating": "整体评价"
}}

请只返回JSON：
""")
        ])
        
        try:
            selection_result = json.loads(response.content)
            best_joke = selection_result.get("best_joke", "无法评选最佳笑话")
        except:
            best_joke = "由于技术原因，无法评选最佳笑话"
        
        return {
            **state,
            "best_joke": best_joke,
            "execution_path": state["execution_path"] + ["评选最佳笑话完成"],
            "messages": [AIMessage(content=f"评选完成，最佳笑话：{best_joke}")]
        }
    
    # 创建图
    workflow = StateGraph(AnimalJokeState)
    
    # 添加节点
    workflow.add_node("generate_animals", generate_animals_node)
    workflow.add_node("generate_jokes", generate_jokes_map_node)
    workflow.add_node("select_best", select_best_joke_reduce_node)
    
    # 设置入口点和边
    workflow.set_entry_point("generate_animals")
    workflow.add_edge("generate_animals", "generate_jokes")
    workflow.add_edge("generate_jokes", "select_best")
    workflow.add_edge("select_best", END)
    
    # 编译图
    app = workflow.compile()
    
    # 测试不同的动物输入
    test_animals = ["熊猫", "猫咪", "大象"]
    
    for animal in test_animals:
        print(f"\n测试动物: {animal}")
        print("=" * 60)
        
        # 运行异步图
        async def run_animal_joke_workflow():
            result = await app.ainvoke({
                "user_input": animal,
                "messages": [],
                "animal_list": [],
                "joke_results": [],
                "best_joke": "",
                "execution_path": []
            })
            
            print(f"\n执行路径: {' -> '.join(result['execution_path'])}")
            print(f"\n生成的动物列表: {result['animal_list']}")
            print(f"\n所有笑话:")
            for joke_data in result['joke_results']:
                print(f"  {joke_data['animal']}: {joke_data['joke']}")
            print(f"\n🎉 最佳笑话: {result['best_joke']}")
            print("=" * 60)
        
        # 运行异步工作流
        asyncio.run(run_animal_joke_workflow())

def main():
    """主函数"""
    animal_joke_map_reduce()

if __name__ == "__main__":
    main() 