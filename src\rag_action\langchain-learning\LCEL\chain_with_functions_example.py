"""
链中使用函数的场景。
"""

from langchain_core.output_parsers import StrOutputParser
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableLambda
import json
import re

# 创建 LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0
)

# 定义各种函数
def extract_keywords(text: str) -> list:
    """提取关键词"""
    # 简单的关键词提取逻辑
    keywords = re.findall(r'\b[A-Za-z]{3,}\b', text)
    return list(set(keywords[:10]))  # 返回前10个不重复的关键词

def count_words(text: str) -> int:
    """统计字数"""
    return len(text.split())

def format_output(text: str) -> str:
    """格式化输出"""
    return f"=== 格式化结果 ===\n{text}\n=================="

def validate_topic(topic: str) -> str:
    """验证主题"""
    if len(topic) < 2:
        return "主题太短，请提供更详细的主题"
    return topic

def create_summary(data: dict) -> str:
    """创建总结"""
    summary = f"""
主题: {data.get('topic', '未知')}
字数: {data.get('word_count', 0)}
关键词: {', '.join(data.get('keywords', []))}
内容: {data.get('content', '')[:100]}...
    """
    return summary.strip()

def basic_chain_with_function():
    """基础链中使用函数"""
    
    print("=== 基础链中使用函数 ===")
    
    # 创建链：主题验证 -> LLM -> 字数统计 -> 格式化
    chain = (
        RunnableLambda(validate_topic) |
        ChatPromptTemplate.from_template("请详细解释：{topic}") |
        llm |
        StrOutputParser() |
        RunnableLambda(count_words) |
        RunnableLambda(format_output)
    )
    
    result = chain.invoke("人工智能")
    print(result)

def advanced_chain_with_functions():
    """高级链中使用多个函数"""
    
    print("\n=== 高级链中使用多个函数 ===")
    
    # 创建复杂的链
    chain = (
        {"topic": RunnableLambda(validate_topic)} |
        ChatPromptTemplate.from_template("请详细解释：{topic}") |
        llm |
        StrOutputParser() |
        {
            "content": RunnableLambda(lambda x: x),
            "word_count": RunnableLambda(count_words),
            "keywords": RunnableLambda(extract_keywords)
        } |
        RunnableLambda(create_summary)
    )
    
    result = chain.invoke("机器学习")
    print(result)

def chain_with_conditional_function():
    """链中使用条件函数"""
    
    print("\n=== 链中使用条件函数 ===")
    
    def conditional_processing(data: dict) -> str:
        """条件处理函数"""
        word_count = data.get('word_count', 0)
        if word_count > 100:
            return f"内容较长 ({word_count} 字)，建议分段阅读"
        elif word_count > 50:
            return f"内容适中 ({word_count} 字)，适合快速浏览"
        else:
            return f"内容较短 ({word_count} 字)，需要补充更多信息"
    
    chain = (
        {"topic": RunnableLambda(validate_topic)} |
        ChatPromptTemplate.from_template("请简要介绍：{topic}") |
        llm |
        StrOutputParser() |
        {
            "content": RunnableLambda(lambda x: x),
            "word_count": RunnableLambda(count_words)
        } |
        RunnableLambda(conditional_processing)
    )
    
    result = chain.invoke("区块链")
    print(result)

def chain_with_error_handling_function():
    """链中使用错误处理函数"""
    
    print("\n=== 链中使用错误处理函数 ===")
    
    def safe_json_parse(text: str) -> dict:
        """安全的JSON解析函数"""
        try:
            # 尝试提取JSON部分
            json_match = re.search(r'\{.*\}', text, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(0))
            else:
                return {"error": "未找到JSON格式", "text": text[:100]}
        except Exception as e:
            return {"error": f"JSON解析失败: {e}", "text": text[:100]}
    
    def format_json_result(data: dict) -> str:
        """格式化JSON结果"""
        if "error" in data:
            return f"❌ {data['error']}"
        else:
            return f"✅ 解析成功: {json.dumps(data, ensure_ascii=False, indent=2)}"
    
    chain = (
        ChatPromptTemplate.from_template("请以JSON格式回答：{topic} 的主要特点") |
        llm |
        StrOutputParser() |
        RunnableLambda(safe_json_parse) |
        RunnableLambda(format_json_result)
    )
    
    result = chain.invoke("深度学习")
    print(result)

def chain_with_data_transformation():
    """链中使用数据转换函数"""
    
    print("\n=== 链中使用数据转换函数 ===")
    
    def split_into_sections(text: str) -> list:
        """将文本分割成段落"""
        sections = text.split('\n\n')
        return [s.strip() for s in sections if s.strip()]
    
    def add_section_numbers(sections: list) -> str:
        """为段落添加编号"""
        numbered_sections = []
        for i, section in enumerate(sections, 1):
            numbered_sections.append(f"第{i}段：{section}")
        return '\n\n'.join(numbered_sections)
    
    def calculate_readability(sections: list) -> str:
        """计算可读性"""
        total_words = sum(len(section.split()) for section in sections)
        avg_words_per_section = total_words / len(sections) if sections else 0
        
        if avg_words_per_section > 50:
            readability = "较难阅读"
        elif avg_words_per_section > 30:
            readability = "适中"
        else:
            readability = "易于阅读"
        
        return f"可读性评估：{readability} (平均每段 {avg_words_per_section:.1f} 字)"
    
    chain = (
        ChatPromptTemplate.from_template("请详细解释：{topic}") |
        llm |
        StrOutputParser() |
        RunnableLambda(split_into_sections) |
        {
            "numbered_sections": RunnableLambda(add_section_numbers),
            "readability": RunnableLambda(calculate_readability)
        }
    )
    
    result = chain.invoke("云计算")
    print("【分段结果】")
    print(result["numbered_sections"])
    print(f"\n【{result['readability']}】")

def main():
    """主函数"""
    basic_chain_with_function()
    advanced_chain_with_functions()
    chain_with_conditional_function()
    chain_with_error_handling_function()
    chain_with_data_transformation()

if __name__ == "__main__":
    main() 