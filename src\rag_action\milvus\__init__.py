"""
Milvus 混合检索模块

本模块提供了 Milvus 2.5+ 稀疏向量和密集向量混合检索的完整实现。

主要功能:
- 支持 BGE-M3 模型的向量嵌入
- 密集向量检索（语义搜索）
- 稀疏向量检索（关键词搜索）
- 混合检索（结合语义和关键词）
- 完整的错误处理和日志记录

使用示例:
    from rag_action.milvus import MilvusHybridSearcher
    
    searcher = MilvusHybridSearcher()
    searcher.connect()
    searcher.create_collection()
    searcher.insert_data(["文本1", "文本2"])
    results = searcher.hybrid_search("查询文本")
"""

from .hybrid_search_example import MilvusHybridSearcher

__version__ = "1.0.0"
__author__ = "RAG Action Team"

__all__ = [
    "MilvusHybridSearcher"
]
