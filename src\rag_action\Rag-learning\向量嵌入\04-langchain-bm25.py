from langchain_community.retrievers import BM25Retriever
from langchain_community.vectorstores import Chroma
from langchain_core.documents import Document
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI, OpenAIEmbeddings

battle_logs = [
    "猢狲身披锁子甲。",
    "猢狲在无回谷遭遇了妖怪，妖怪开始攻击，猢狲使用铜云棒抵挡。",
    "猢狲施展烈焰拳击退妖怪随后开启金刚体抵挡神兵攻击。",
    "妖怪使用寒冰箭攻击猢狲但被烈焰拳反击击溃。",
    "猢狲召唤烈焰拳与毁灭咆哮击败妖怪随后收集妖怪精华。"
]
request = "猢狲有什么装备和招数？"

bm25_retriever = BM25Retriever.from_texts(battle_logs)
bm25_response = bm25_retriever.invoke(request)
print(f"bm25检索结果：{bm25_response}")

docs = [Document(page_content=log) for log in battle_logs]

chroma_vs = Chroma.from_documents(
    documents=docs,
    embedding=OpenAIEmbeddings(
        model="text-embedding-3-small",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
        base_url="https://api.zhizengzeng.com/v1"
    )
)

chroma_retriever = chroma_vs.as_retriever()
chroma_response = chroma_retriever.invoke(request)
print(f"chroma检索结果：{chroma_response}")

hybrid_response = list({doc.page_content for doc in bm25_response + chroma_response})
print(f"混合检索结果：{hybrid_response}")

prompt = ChatPromptTemplate.from_template(
                """
                基于以下上下文，回答问题。如果上下文中没有相关信息，
                请说"我无法从提供的上下文中找到相关信息"。
                上下文: {context}
                问题: {question}
                回答:
                """
)

llm = ChatOpenAI(
    model="gpt-4o-mini",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    base_url="https://api.zhizengzeng.com/v1"
)

# 直接使用检索到的文档
chain = prompt | llm | StrOutputParser()
response = chain.invoke({"context": hybrid_response, "question": request})
print(f"最终回答：{response}")








