from pymilvus import MilvusClient, DataType
import random

# ANN（Approximate Nearest Neighbor，近似最近邻）是一种用于高效检索高维向量空间中与查询向量最相似（距离最近）的若干个向量的技术。
# 在大规模向量检索场景下，精确最近邻搜索的计算和存储成本非常高，因此通常采用ANN算法以牺牲少量精度为代价，极大提升检索速度。
# 常见的ANN算法包括 FLAT、IVF、HNSW、DISKANN 等，Milvus 支持多种ANN索引类型，适用于不同的应用需求。
# 本示例将演示如何在 Milvus 中使用 ANN 索引进行向量的高效相似性搜索。

# 1. 设置 Milvus 客户端
# 首先，创建一个 MilvusClient 实例，连接到本地的 Milvus 服务（默认端口19530）。
client = MilvusClient(uri="http://localhost:19530")
COLLECTION_NAME = "ann_search_demo"

# 2. 检查并删除已存在的集合
# 如果集合已存在，为了保证流程的可重复性，先删除同名集合。
if client.has_collection(COLLECTION_NAME):
    client.drop_collection(COLLECTION_NAME)

# 3. 创建集合的 schema（模式）
# schema 定义了集合中每个字段的类型和属性。
# - id: 主键，类型为 INT64
# - vector: 向量字段，类型为 128 维的 FLOAT_VECTOR
# - color: 额外的字符串字段，类型为 VARCHAR，最大长度100
schema = MilvusClient.create_schema(auto_id=False, enable_dynamic_field=True)
schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True)
schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=128)
schema.add_field(field_name="color", datatype=DataType.VARCHAR, max_length=100)

# 4. 创建集合
# 使用上面定义的 schema 创建集合。
client.create_collection(collection_name=COLLECTION_NAME, schema=schema)

# 5. 插入随机向量数据
# 生成1000条数据，每条数据包含：
# - id: 唯一编号
# - vector: 128维随机向量
# - color: 随机颜色字符串
num_vectors = 1000
vectors = [[random.random() for _ in range(128)] for _ in range(num_vectors)]
ids = list(range(num_vectors))
colors = [f"color_{random.randint(1, 1000)}" for _ in range(num_vectors)]
entities = [{"id": ids[i], "vector": vectors[i], "color": colors[i]} for i in range(num_vectors)]

# 插入数据到集合中
client.insert(collection_name=COLLECTION_NAME, data=entities)
# flush 保证数据写入磁盘
client.flush(collection_name=COLLECTION_NAME)

# 6. 创建向量索引
# 为 vector 字段创建 FLAT 索引，使用 L2 距离度量方式。
index_params = MilvusClient.prepare_index_params()
index_params.add_index(
    field_name="vector",
    metric_type="L2",
    index_type="FLAT",
    index_name="vector_index",
    params={}
)
client.create_index(
    collection_name=COLLECTION_NAME,
    index_params=index_params,
    sync=True
)

# 7. 加载集合到内存
# 搜索前需要将集合加载到内存中。
client.load_collection(collection_name=COLLECTION_NAME)

# 8. 单向量搜索示例
# 随机生成一个查询向量，查找与其最相似的3个向量。
print("\n=== 单向量搜索 ===")
query_vector = [random.random() for _ in range(128)]
results = client.search(
    collection_name=COLLECTION_NAME,
    data=[query_vector],
    anns_field="vector",
    limit=3,
    search_params={"metric_type": "L2"}
)

print("搜索结果:")
for hits in results:
    for hit in hits:
        print(f"ID: {hit['id']}, 距离: {hit['distance']}")

# 9. 批量向量搜索示例
# 一次性查询2个向量，每个向量都返回最相似的3个结果。
print("\n=== 批量向量搜索 ===")
query_vectors = [[random.random() for _ in range(128)] for _ in range(2)]
results = client.search(
    collection_name=COLLECTION_NAME,
    data=query_vectors,
    anns_field="vector",
    limit=3,
    search_params={"metric_type": "L2"}
)

print("批量搜索结果:")
for i, hits in enumerate(results):
    print(f"\n查询向量 {i+1} 的结果:")
    for hit in hits:
        print(f"ID: {hit['id']}, 距离: {hit['distance']}")

# 10. 带输出字段的搜索示例
# 查询时除了返回 id 和距离，还返回 color 字段的内容。
print("\n=== 带输出字段的搜索 ===")
results = client.search(
    collection_name=COLLECTION_NAME,
    data=[query_vector],
    anns_field="vector",
    limit=3,
    search_params={"metric_type": "L2"},
    output_fields=["color"]
)

print("带输出字段的搜索结果:")
for hits in results:
    for hit in hits:
        print(f"ID: {hit['id']}, 距离: {hit['distance']}, 颜色: {hit['entity']['color']}")

# 11. 释放集合资源
# 搜索结束后，释放集合占用的内存资源。
client.release_collection(collection_name=COLLECTION_NAME)
