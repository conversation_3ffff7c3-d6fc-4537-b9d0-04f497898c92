from turtle import st
from unstructured.partition.pdf import partition_pdf

pdf_path = "C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/billionaires_page-1-5.pdf"

elements = partition_pdf(pdf_path, strategy="hi_res",infer_table_structure=True)

# 创建一个元素ID到元素的映射
element_map = {element.id: element for element in elements if hasattr(element, "id")}

for element in elements:
    if element.category == "Table":
        #print("\n表格数据:")
        #print("表格元素据:", vars(element.metadata))
        print("表格内容:")
        print(element.text)

        ##获取并打印父节点信息
        parent_id = getattr(element.metadata, "parent_id", None)
        if parent_id and parent_id in element_map:
            parent_element = element_map[parent_id]
            print("\n父节点信息:")
            print(f"类型: {parent_element.category}")
            print(f"文本: {parent_element.text}")
            if hasattr(parent_element, "metadata"):
                print(f"父节点元数据: {parent_element.metadata}")
        else:
            print(f"\n没有找到父节点 (ID:{parent_id})")
        print("-"*50)







