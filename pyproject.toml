[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "rag-action"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "camelot-py>=0.11.0",
    "chromadb>=1.0.15",
    "cohere==5.12.0",
    "deepeval>=3.2.6",
    "docx2txt>=0.9",
    "faiss-cpu>=1.11.0",
    "fastapi~=0.115.0",
    "flagembedding>=1.3.5",
    "ghostscript>=0.8.1",
    "httpx>=0.28.1",
    "jieba>=0.42.1",
    "langchain>=0.3.26",
    "langchain-cohere==0.4.4",
    "langchain-community>=0.3.27",
    "langchain-deepseek>=0.1.3",
    "langchain-huggingface>=0.3.0",
    "langchain-openai>=0.3.27",
    "langchain-unstructured>=0.1.6",
    "langgraph>=0.5.3",
    "lark>=1.2.2",
    "llama-index>=0.12.48",
    "llama-index-embeddings-huggingface>=0.5.5",
    "llama-index-postprocessor-flag-embedding-reranker>=0.3.0",
    "llama-index-readers-database>=0.4.0",
    "llama-parse>=0.6.46",
    "llmlingua>=0.2.2",
    "markdown>=3.8.2",
    "mcp>=1.12.0",
    "mineru[core]>=2.1.5",
    "mysqlclient>=2.2.7",
    "nltk>=3.9.1",
    "openai>=1.95.1",
    "pdfplumber>=0.11.7",
    "pi-heif>=1.0.0",
    "pydantic>=2.11.7",
    "pymilvus[model]>=2.5.0",
    "rank-bm25>=0.2.2", # 保留作为备选方案
    "pymupdf>=1.26.3",
    "pymysql>=1.1.1",
    "pytesseract>=0.3.13",
    "python-multipart>=0.0.20",
    "pytube==12.1.3",
    "pyyaml>=6.0.2",
    "ragas>=0.2.15",
    "rank-bm25>=0.2.2",
    "rank-llm>=0.21.0",
    "sentence-transformers>=5.0.0",
    "sqlalchemy>=2.0.41",
    "trulens>=1.5.3",
    "unstructured[local-inference,pdf]>=0.18.5",
    "uvicorn[standard]~=0.29.0",
    "youtube-transcript-api>=1.1.1",
    "fastmcp>=2.10.6",
    "langchain-mcp-adapters>=0.1.9",
    "langchain-qdrant>=0.2.0",
    "dingtalk-stream>=0.24.2",
    "redis>=6.2.0",
    "google-search-results>=2.4.2",
    # 多智能体系统依赖
    "langgraph-supervisor>=0.0.8",
    "websockets>=15.0.1",
    "rizaio>=0.8.0",
    # 语音和数字人相关依赖
    "azure-cognitiveservices-speech>=1.40.0",
    "aiofiles>=24.1.0",
    "python-a2a>=0.5.9",
]

[tool.setuptools.packages.find]
where = ["src"]
include = ["rag_action*"]

[tool.uv]
index-url = "https://pypi.tuna.tsinghua.edu.cn/simple"

[[tool.uv.index]]
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple/"
default = true
