from typing import Literal
from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI

# 数据模型
class RouteQuery(BaseModel):
    """将用户查询路由到最相关的数据源"""
    datasource: Literal["python_docs", "js_docs", "golang_docs"] = Field(
        ...,
        description="根据用户问题，选择最适合回答问题的数据源",
    )

def create_router():
    # 带函数调用的大模型
    llm = ChatOpenAI(
        model="gpt-4o-mini",
        temperature=0,
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
        base_url="https://api.zhizengzeng.com/v1"
    )
    # 这里的传参是指 with_structured_output 需要传入一个用于结构化输出的 schema，这里传的是 RouteQuery（即定义好的 Pydantic 数据模型），
    # 这样大模型输出会自动解析为 RouteQuery 对象，方便后续直接获取 datasource 字段。
    structured_llm = llm.with_structured_output(RouteQuery)
    
    # 提示模板
    system = """你是将用户问题路由到合适数据源的专家。
根据问题所涉及的编程语言，将其路由到相关的数据源。"""
    prompt = ChatPromptTemplate.from_messages([
        ("system", system),
        ("human", "{question}"),
    ])
    
    # 定义路由器
    return prompt | structured_llm

def route_question(question: str) -> str:
    """路由用户问题到合适的数据源"""
    router = create_router()
    result = router.invoke({"question": question})
    return result.datasource

# 使用示例
if __name__ == "__main__":
    # 测试问题
    test_question = "go语言有什么特点"
    result = route_question(test_question)
    print(f"问题: {test_question}")
    print(f"路由结果: {result}")

