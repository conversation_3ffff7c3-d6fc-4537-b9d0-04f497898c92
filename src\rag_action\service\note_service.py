"""
笔记服务
处理笔记的创建、查询、管理等核心业务逻辑
"""
import os
import logging
import re
from typing import List, Optional, Dict, Any
from datetime import datetime
from fastapi import UploadFile
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from rag_action.core.config import get_settings
from rag_action.core.database import get_db, SessionLocal
from rag_action.core.service_manager import service_manager
from rag_action.models.database import Note, Tag, NoteChunk, note_tags
from rag_action.models.schemas import (
    NoteCreate, NoteResponse, NoteSummary, NotesListResponse, TagResponse
)
from rag_action.service.loading_service import LoadingService
from rag_action.service.chuking_service import ChunkingService

logger = logging.getLogger(__name__)
settings = get_settings()


class NoteService:
    """笔记服务类"""
    
    def __init__(self):
        self.loading_service = LoadingService()
        self.chunking_service = ChunkingService()

        # 确保上传目录存在
        os.makedirs(settings.file.upload_dir, exist_ok=True)
        os.makedirs(settings.file.temp_dir, exist_ok=True)

    def _get_embedding_service(self):
        """获取嵌入服务实例"""
        service = service_manager.get_service("embedding_service")
        if service is None:
            # 如果服务管理器中没有，创建新实例（向后兼容）
            from rag_action.service.embedding_service import EmbeddingService
            service = EmbeddingService()
        return service

    def _get_vector_service(self):
        """获取向量服务实例"""
        service = service_manager.get_service("vector_service")
        if service is None:
            # 如果服务管理器中没有，创建新实例（向后兼容）
            from rag_action.service.enhanced_vector_service import EnhancedVectorService
            service = EnhancedVectorService()
        return service

    def _get_document_parser(self):
        """获取文档解析器实例"""
        service = service_manager.get_service("document_parser")
        if service is None:
            # 如果服务管理器中没有，创建新实例（向后兼容）
            from rag_action.service.enhanced_document_parser import EnhancedDocumentParser
            service = EnhancedDocumentParser()
        return service

    def _clean_text(self, text: str) -> str:
        """清理文本，移除代理字符和其他问题字符"""
        if not text:
            return ""

        try:
            # 使用更强力的清理方法
            # 1. 首先移除代理字符（surrogate characters）
            # 代理字符的Unicode范围是 U+D800 到 U+DFFF
            cleaned_chars = []
            for char in text:
                char_code = ord(char)
                # 跳过代理字符
                if 0xD800 <= char_code <= 0xDFFF:
                    continue
                # 跳过其他问题字符
                if char_code < 32 and char not in '\n\t\r':
                    continue
                cleaned_chars.append(char)

            text = ''.join(cleaned_chars)

            # 2. 使用encode/decode进一步清理
            text = text.encode('utf-8', 'ignore').decode('utf-8')

            # 3. 移除零宽字符
            text = re.sub(r'[\u200B-\u200D\uFEFF]', '', text)

            # 4. 规范化空白字符
            text = re.sub(r'\s+', ' ', text)
            text = text.strip()

            # 5. 最终验证：尝试编码为UTF-8
            test_bytes = text.encode('utf-8')
            test_decode = test_bytes.decode('utf-8')

            return text

        except Exception as e:
            logger.warning(f"文本清理失败: {e}")
            # 如果清理失败，使用最激进的清理方法
            try:
                # 只保留安全的字符
                safe_chars = []
                for char in text:
                    try:
                        char.encode('utf-8')
                        char_code = ord(char)
                        # 只保留基本的Unicode字符，排除代理字符
                        if char_code < 0xD800 or char_code > 0xDFFF:
                            safe_chars.append(char)
                    except:
                        continue

                cleaned = ''.join(safe_chars)
                return cleaned.strip()

            except:
                # 最后的备选方案：只保留ASCII字符
                return text.encode('ascii', 'ignore').decode('ascii').strip()
    
    async def create_note_from_pdf(self, file: UploadFile, tags: List[str] = None) -> NoteResponse:
        """从PDF文件创建笔记"""
        db = SessionLocal()
        note = None
        try:
            # 保存上传的文件
            file_path = await self._save_uploaded_file(file)
            
            # 优先使用增强文档解析器（MinierU + Markdown + 父子文档 + 上下文窗口）
            try:
                enhanced_parser = self._get_document_parser()
                parse_result = enhanced_parser.parse_pdf(file_path, file.filename)
                content = parse_result["content"]
                markdown_content = parse_result.get("markdown_content", content)
                html_content = parse_result.get("html_content", "")
                toc = parse_result.get("toc", [])
                parent_chunks = parse_result.get("parent_chunks", [])
                child_chunks = parse_result.get("child_chunks", [])
                chunks = child_chunks  # 默认使用子文档块
                metadata = parse_result["metadata"]
                logger.info(f"使用增强解析器成功解析PDF: {file.filename}, 父文档: {len(parent_chunks)}, 子文档: {len(child_chunks)}")
            except Exception as e:
                logger.warning(f"增强解析器失败，使用LangChain备选解析器: {e}")
                try:
                    # 备选方案1：使用LangChain解析器
                    from rag_action.service.langchain_document_parser import get_langchain_document_parser
                    langchain_config = {
                        "use_langchain": True,
                        "chunk_size": settings.embedding.chunk_size,
                        "chunk_overlap": 200
                    }
                    langchain_parser = get_langchain_document_parser(langchain_config)
                    parse_result = langchain_parser.parse_pdf(file_path, file.filename)
                    content = parse_result["content"]
                    chunks = parse_result.get("chunks", [])
                    parent_chunks = []
                    child_chunks = chunks
                    metadata = parse_result["metadata"]
                    logger.info(f"使用LangChain解析器成功解析PDF: {file.filename}")
                except Exception as e2:
                    logger.warning(f"LangChain解析器也失败，使用原始解析器: {e2}")
                    # 备选方案2：使用原始解析器
                    from rag_action.service.document_parser import get_document_parser
                    default_config = {
                        "use_mineru": False,
                        "mineru_config": {},
                        "fallback_parser": "pymupdf"
                    }
                    document_parser = get_document_parser(default_config)
                    parse_result = document_parser.parse_pdf(file_path, file.filename)
                    content = parse_result["content"]
                    chunks = None
                    parent_chunks = []
                    child_chunks = []
                    metadata = parse_result["metadata"]

            # 清理文本内容
            clean_content = self._clean_text(content)

            # 从内容中提取标题
            title = self._extract_title_from_content(clean_content, file.filename)
            clean_title = self._clean_text(title)

            # 创建笔记记录
            note = Note(
                title=clean_title,
                content=clean_content,
                source_type="pdf",
                source_file=file.filename,
                file_size=file.size,
                total_pages=metadata.get("total_pages", 0)
            )

            db.add(note)
            db.flush()  # 获取note.id

            # 立即提交note记录，确保后续操作能找到note_id
            db.commit()
            logger.info(f"笔记记录已创建并提交: note_id={note.id}")

            # 保存note_id用于后续操作
            note_id = note.id

            # 重新查询note对象，确保在新的事务中使用
            note = db.query(Note).filter(Note.id == note_id).first()
            if not note:
                raise Exception(f"无法找到刚创建的笔记记录: note_id={note_id}")

            # 处理标签
            if tags:
                note_tags_list = self._process_tags(db, tags)
                note.tags.extend(note_tags_list)
                db.commit()  # 提交标签关联
            
            # 文本分块处理
            if chunks is not None and len(chunks) > 0:
                # 使用增强解析器提供的chunks（包含父子文档和上下文窗口）
                logger.info(f"使用增强解析器预分块结果，共{len(chunks)}个子文档块")
                processed_chunks = self._process_enhanced_chunks(chunks, note.id)

                # 处理父文档块（如果有）
                if parent_chunks:
                    processed_parent_chunks = self._process_enhanced_chunks(parent_chunks, note.id, chunk_type="parent")
                    logger.info(f"处理{len(processed_parent_chunks)}个父文档块")
                else:
                    processed_parent_chunks = []
            else:
                # 使用传统分块方法
                logger.info("使用传统分块方法")
                processed_chunks = self._chunk_text_with_page_map(content, note.id, [])
                processed_parent_chunks = []
            
            # 生成向量嵌入并存储（支持父子文档）
            try:
                await self._process_parent_child_chunks(db, note, processed_parent_chunks, processed_chunks)
                db.commit()
                logger.info(f"向量嵌入处理完成并提交: note_id={note.id}")
            except Exception as e:
                logger.error(f"向量嵌入处理失败: {e}")
                # 不要回滚，因为note记录已经提交
                # 只记录错误，让note记录保持存在
                logger.warning(f"笔记记录已保存(ID: {note.id})，但向量处理失败，可稍后重试")
            
            # 清理临时文件
            if os.path.exists(file_path):
                os.remove(file_path)
            
            return self._note_to_response(note)
            
        except Exception as e:
            # 只有在note还没有提交时才回滚
            if note is None:
                db.rollback()
                logger.error(f"从PDF创建笔记失败(未提交): {e}")
            elif hasattr(locals(), 'note_id') and note_id:
                logger.error(f"从PDF创建笔记部分失败(笔记已保存ID: {note_id}): {e}")
            else:
                db.rollback()
                logger.error(f"从PDF创建笔记失败: {e}")
            raise
        finally:
            db.close()
    
    async def create_note(self, note_data: NoteCreate) -> NoteResponse:
        """创建手动笔记"""
        db = SessionLocal()
        try:
            # 创建笔记记录
            note = Note(
                title=note_data.title,
                content=note_data.content,
                source_type="manual"
            )
            
            db.add(note)
            db.flush()
            
            # 处理标签
            if note_data.tags:
                note_tags_list = self._process_tags(db, note_data.tags)
                note.tags.extend(note_tags_list)
            
            # 使用增强解析器处理文本（支持父子文档和上下文窗口）
            try:
                # 对于手动创建的笔记，使用增强解析器的文本处理功能
                metadata = {
                    "filename": note.title,
                    "source": note.title,
                    "parser": "enhanced_manual"
                }

                # 创建父子文档块
                parent_chunks, child_chunks = self.enhanced_parser.create_parent_child_chunks(note_data.content, metadata)

                # 为子文档添加上下文窗口
                child_chunks_with_context = self.enhanced_parser.create_context_windows(child_chunks)

                # 处理增强的文档块
                processed_parent_chunks = self._process_enhanced_chunks(parent_chunks, note.id, chunk_type="parent") if parent_chunks else []
                processed_child_chunks = self._process_enhanced_chunks(child_chunks_with_context, note.id, chunk_type="child")

                # 生成向量嵌入并存储（支持父子文档）
                await self._process_parent_child_chunks(db, note, processed_parent_chunks, processed_child_chunks)

                logger.info(f"手动笔记使用增强解析器: 父文档{len(processed_parent_chunks)}个, 子文档{len(processed_child_chunks)}个")

            except Exception as e:
                logger.warning(f"增强解析器处理失败，使用传统方法: {e}")
                # 降级到传统分块方法
                chunks = self._chunk_text(note_data.content, note.id)
                await self._process_chunks(db, note, chunks)
            
            db.commit()
            return self._note_to_response(note)
            
        except Exception as e:
            db.rollback()
            logger.error(f"创建笔记失败: {e}")
            raise
        finally:
            db.close()
    
    async def get_notes(self, page: int = 1, page_size: int = 10, tag: str = None) -> NotesListResponse:
        """获取笔记列表"""
        db = SessionLocal()
        try:
            query = db.query(Note)
            
            # 按标签筛选
            if tag:
                query = query.join(Note.tags).filter(Tag.name == tag)
            
            # 计算总数
            total = query.count()
            
            # 分页查询
            offset = (page - 1) * page_size
            notes = query.order_by(Note.created_at.desc()).offset(offset).limit(page_size).all()
            
            # 转换为响应模型
            note_summaries = [self._note_to_summary(note) for note in notes]
            
            return NotesListResponse(
                notes=note_summaries,
                total=total,
                page=page,
                page_size=page_size,
                total_pages=(total + page_size - 1) // page_size
            )
            
        except Exception as e:
            logger.error(f"获取笔记列表失败: {e}")
            raise
        finally:
            db.close()
    
    async def get_note_by_id(self, note_id: int) -> Optional[NoteResponse]:
        """根据ID获取笔记详情"""
        db = SessionLocal()
        try:
            note = db.query(Note).filter(Note.id == note_id).first()
            if not note:
                return None
            
            return self._note_to_response(note)
            
        except Exception as e:
            logger.error(f"获取笔记详情失败: {e}")
            raise
        finally:
            db.close()
    
    async def get_all_tags(self) -> List[TagResponse]:
        """获取所有标签"""
        db = SessionLocal()
        try:
            # 查询标签及其使用次数
            tags_with_count = db.query(
                Tag,
                func.count(note_tags.c.note_id).label('note_count')
            ).outerjoin(note_tags).group_by(Tag.id).order_by(
                func.count(note_tags.c.note_id).desc()
            ).all()
            
            return [
                TagResponse(
                    id=tag.id,
                    name=tag.name,
                    created_at=tag.created_at,
                    note_count=count
                )
                for tag, count in tags_with_count
            ]
            
        except Exception as e:
            logger.error(f"获取标签列表失败: {e}")
            raise
        finally:
            db.close()
    
    async def _save_uploaded_file(self, file: UploadFile) -> str:
        """保存上传的文件"""
        # 生成唯一文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{file.filename}"
        file_path = os.path.join(settings.file.temp_dir, filename)
        
        # 保存文件
        with open(file_path, "wb") as f:
            content = await file.read()
            f.write(content)
        
        return file_path
    
    def _extract_title_from_content(self, content: str, filename: str) -> str:
        """从内容中提取标题"""
        if not content:
            return os.path.splitext(filename)[0]
        
        # 取前几行作为标题候选
        lines = content.split('\n')[:5]
        for line in lines:
            line = line.strip()
            if line and len(line) < 200:
                return line
        
        # 如果没有找到合适的标题，使用文件名
        return os.path.splitext(filename)[0]
    
    def _process_tags(self, db: Session, tag_names: List[str]) -> List[Tag]:
        """处理标签，不存在则创建"""
        tags = []
        for tag_name in tag_names:
            tag_name = tag_name.strip()
            if not tag_name:
                continue
            
            # 查找或创建标签
            tag = db.query(Tag).filter(Tag.name == tag_name).first()
            if not tag:
                tag = Tag(name=tag_name)
                db.add(tag)
                db.flush()
            
            tags.append(tag)
        
        return tags
    
    def _chunk_text(self, content: str, note_id: int) -> List[Dict[str, Any]]:
        """文本分块"""
        # 使用现有的分块服务
        metadata = {"note_id": note_id}

        # 对于手动创建的笔记，创建一个简单的page_map（列表格式）
        page_map = [{"page": 1, "text": content}]

        chunks = self.chunking_service.chunk_text(
            content,
            "by_pages",
            metadata,
            page_map,
            settings.embedding.chunk_size,
            return_metadata=False  # 直接返回chunks列表
        )

        return chunks

    def _chunk_text_with_page_map(self, content: str, note_id: int, page_map: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """使用page_map进行文本分块"""
        metadata = {"note_id": note_id}

        chunks = self.chunking_service.chunk_text(
            content,
            "by_pages",
            metadata,
            page_map,
            settings.embedding.chunk_size,
            return_metadata=False  # 直接返回chunks列表
        )

        return chunks

    def _process_langchain_chunks(self, chunks: List[Dict[str, Any]], note_id: int) -> List[Dict[str, Any]]:
        """处理LangChain解析器提供的chunks"""
        processed_chunks = []

        for chunk in chunks:
            # 清理chunk内容
            clean_content = self._clean_text(chunk["content"])

            processed_chunk = {
                "content": clean_content,
                "metadata": {
                    "note_id": note_id,
                    "chunk_index": chunk.get("chunk_index", 0),
                    "source": chunk.get("metadata", {}).get("source", ""),
                    "chunk_type": chunk.get("metadata", {}).get("chunk_type", "langchain"),
                    "page_number": 1  # LangChain chunks可能不包含页码信息
                }
            }
            processed_chunks.append(processed_chunk)

        return processed_chunks

    def _process_enhanced_chunks(self, chunks: List[Dict[str, Any]], note_id: int, chunk_type: str = "child") -> List[Dict[str, Any]]:
        """处理增强解析器提供的chunks（支持父子文档和上下文窗口）"""
        processed_chunks = []

        for chunk in chunks:
            # 清理chunk内容
            clean_content = self._clean_text(chunk["content"])

            processed_chunk = {
                "content": clean_content,
                "metadata": {
                    "note_id": note_id,
                    "chunk_index": chunk.get("chunk_index", 0),
                    "chunk_type": chunk_type,
                    "source": chunk.get("metadata", {}).get("source", ""),
                    "page_number": chunk.get("metadata", {}).get("page_number", 1),
                    "parent_id": chunk.get("parent_id", ""),
                    "has_context": "context" in chunk
                }
            }

            # 添加上下文信息（如果有）
            if "context" in chunk:
                processed_chunk["context"] = chunk["context"]
                processed_chunk["content_with_context"] = chunk.get("content_with_context", chunk["content"])

            processed_chunks.append(processed_chunk)

        return processed_chunks

    async def _process_parent_child_chunks(self, db: Session, note: Note,
                                         parent_chunks: List[Dict[str, Any]],
                                         child_chunks: List[Dict[str, Any]]):
        """处理父子文档块的向量嵌入和存储"""
        try:
            # 获取服务实例
            embedding_service = self._get_embedding_service()
            vector_service = self._get_vector_service()

            # 生成父文档嵌入
            parent_embeddings = []
            if parent_chunks:
                parent_texts = [chunk.get('content', '') for chunk in parent_chunks]
                parent_embeddings = embedding_service.embed_texts(parent_texts)
                logger.info(f"生成{len(parent_embeddings)}个父文档嵌入")

            # 生成子文档嵌入
            child_embeddings = []
            if child_chunks:
                # 使用带上下文的内容进行嵌入（如果有）
                child_texts = []
                for chunk in child_chunks:
                    if chunk.get("content_with_context"):
                        child_texts.append(chunk["content_with_context"])
                    else:
                        child_texts.append(chunk.get('content', ''))

                child_embeddings = embedding_service.embed_texts(child_texts)
                logger.info(f"生成{len(child_embeddings)}个子文档嵌入")

            # 存储到增强向量服务
            if hasattr(vector_service, 'store_parent_child_embeddings'):
                vector_service.store_parent_child_embeddings(
                    parent_chunks, child_chunks, parent_embeddings, child_embeddings
                )

            # 同时存储到数据库（使用子文档块）
            if child_chunks and child_embeddings:
                await self._store_chunks_to_database(db, note, child_chunks, child_embeddings)

        except Exception as e:
            logger.error(f"处理父子文档块失败: {e}")
            # 降级处理：只处理子文档块
            if child_chunks:
                await self._process_chunks(db, note, child_chunks)

    async def _store_chunks_to_database(self, db: Session, note: Note,
                                      chunks: List[Dict[str, Any]],
                                      embeddings: List[List[float]]):
        """将文档块存储到数据库（支持父子文档结构）"""
        try:
            # 首先存储父文档块（如果有）
            parent_chunk_map = {}  # 用于映射parent_id到数据库ID

            for chunk, embedding in zip(chunks, embeddings):
                chunk_metadata = chunk.get('metadata', {})
                chunk_type = chunk_metadata.get('chunk_type', 'child')

                # 准备上下文窗口数据
                context_window_json = None
                if 'context' in chunk:
                    import json
                    context_window_json = json.dumps(chunk['context'], ensure_ascii=False)

                # 准备元数据
                chunk_metadata_json = None
                if chunk_metadata:
                    import json
                    chunk_metadata_json = json.dumps(chunk_metadata, ensure_ascii=False)

                # 查找父文档块ID（如果是子文档）
                parent_chunk_id = None
                if chunk_type == 'child':
                    parent_id = chunk_metadata.get('parent_id', '')
                    if parent_id and parent_id in parent_chunk_map:
                        parent_chunk_id = parent_chunk_map[parent_id]

                # 创建数据库记录
                note_chunk = NoteChunk(
                    note_id=note.id,
                    content=chunk.get('content', ''),
                    chunk_type=chunk_type,
                    parent_chunk_id=parent_chunk_id,
                    context_window=context_window_json,
                    chunk_metadata=chunk_metadata_json,
                    page_number=chunk_metadata.get('page_number', 1),
                    chunk_index=chunk_metadata.get('chunk_index', 0)
                )
                db.add(note_chunk)
                db.flush()  # 获取ID

                # 如果是父文档，记录映射关系
                if chunk_type == 'parent':
                    chunk_id = chunk.get('id', '')
                    if chunk_id:
                        parent_chunk_map[chunk_id] = note_chunk.id

                # 存储向量 - 支持父子文档结构
                vector_metadata = {
                    "id": str(note_chunk.id),
                    "note_id": note.id,
                    "chunk_id": note_chunk.id,
                    "content": chunk.get('content', '')[:500],  # 截断内容
                    "page_number": chunk_metadata.get('page_number', 1),
                    "chunk_type": chunk_type,
                    "parent_chunk_id": parent_chunk_id or 0,  # Milvus需要非空值
                    "has_context": 'context' in chunk
                }

                # 准备向量数据
                vector_data = {
                    "id": str(note_chunk.id),
                    "embedding": embedding,
                    "metadata": vector_metadata
                }

                # 暂存向量数据，稍后批量存储
                if not hasattr(self, '_temp_vector_data'):
                    self._temp_vector_data = []
                self._temp_vector_data.append(vector_data)

            # 批量存储向量数据
            if hasattr(self, '_temp_vector_data') and self._temp_vector_data:
                try:
                    vector_service = self._get_vector_service()
                    vector_service.store_embedding_batch(self._temp_vector_data)
                    logger.info(f"批量存储{len(self._temp_vector_data)}个向量到Milvus")
                    # 清理临时数据
                    self._temp_vector_data = []
                except Exception as ve:
                    logger.error(f"向量存储失败: {ve}")
                    # 向量存储失败不应该影响数据库事务

            db.commit()
            logger.info(f"存储{len(chunks)}个文档块到数据库")

        except Exception as e:
            logger.error(f"存储文档块到数据库失败: {e}")
            db.rollback()
            # 清理临时向量数据
            if hasattr(self, '_temp_vector_data'):
                self._temp_vector_data = []
            raise

    async def _process_chunks(self, db: Session, note: Note, chunks: List[Dict[str, Any]]):
        """处理分块：生成嵌入并存储"""
        logger.info(f"处理分块，chunks类型: {type(chunks)}, chunks内容: {chunks}")
        chunk_texts = [chunk.get('content', '') for chunk in chunks]
        logger.info(f"提取的文本: {chunk_texts}")

        # 获取嵌入服务
        embedding_service = self._get_embedding_service()

        # 生成向量嵌入
        embeddings = embedding_service.embed_texts(chunk_texts)
        
        # 获取向量服务
        vector_service = self._get_vector_service()

        # 存储到数据库和向量数据库
        for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
            # 创建分块记录
            note_chunk = NoteChunk(
                note_id=note.id,
                chunk_index=i,
                content=chunk.get('content', ''),
                page_number=chunk.get('metadata', {}).get('page_number')
            )

            db.add(note_chunk)
            db.flush()

            # 存储到向量数据库
            vector_id = f"note_{note.id}_chunk_{note_chunk.id}"
            # 注意：vector_service的insert_vector可能不是异步的
            vector_service.insert_vector(
                vector_id=vector_id,
                embedding=embedding,
                metadata={
                    "note_id": note.id,
                    "chunk_id": note_chunk.id,
                    "content": chunk.get('content', ''),
                    "page_number": chunk.get('metadata', {}).get('page_number', 0),
                    "created_at": int(datetime.now().timestamp())
                }
            )

            # 更新分块的向量ID
            note_chunk.vector_id = vector_id
    
    def _note_to_response(self, note: Note) -> NoteResponse:
        """将Note模型转换为响应模型"""
        return NoteResponse(
            id=note.id,
            title=note.title,
            content=note.content,
            source_type=note.source_type,
            source_file=note.source_file,
            file_size=note.file_size,
            total_pages=note.total_pages,
            created_at=note.created_at,
            updated_at=note.updated_at,
            tags=[TagResponse(id=tag.id, name=tag.name, created_at=tag.created_at) for tag in note.tags]
        )
    
    def _note_to_summary(self, note: Note) -> NoteSummary:
        """将Note模型转换为摘要模型"""
        content_preview = note.content[:200] + "..." if len(note.content) > 200 else note.content
        
        return NoteSummary(
            id=note.id,
            title=note.title,
            source_type=note.source_type,
            source_file=note.source_file,
            total_pages=note.total_pages,
            created_at=note.created_at,
            tags=[TagResponse(id=tag.id, name=tag.name, created_at=tag.created_at) for tag in note.tags],
            content_preview=content_preview
        )
