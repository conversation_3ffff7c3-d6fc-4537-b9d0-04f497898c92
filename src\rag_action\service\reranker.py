"""
重排序服务
使用ColBERT等模型对检索结果进行重排序
"""
import logging
from typing import List, Dict, Any, Tuple
import numpy as np

logger = logging.getLogger(__name__)

# 尝试导入重排序相关库
try:
    from sentence_transformers import SentenceTransformer, CrossEncoder
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    logger.warning("sentence-transformers不可用，重排序功能将被禁用")

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    logger.warning("PyTorch不可用，重排序功能将被禁用")


class Reranker:
    """重排序器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.enabled = config.get("enabled", True)
        self.model_name = config.get("model", "cross-encoder/ms-marco-MiniLM-L-12-v2")  # 使用更强的L-12模型
        self.top_k = config.get("top_k", 3)
        self.batch_size = config.get("batch_size", 32)

        self.model = None
        self.use_advanced_reranking = False

        if self.enabled and SENTENCE_TRANSFORMERS_AVAILABLE and TORCH_AVAILABLE:
            self._load_model()
        else:
            logger.warning(f"重排序功能降级到简单模式 - SENTENCE_TRANSFORMERS: {SENTENCE_TRANSFORMERS_AVAILABLE}, TORCH: {TORCH_AVAILABLE}")
    
    def _load_model(self):
        """加载重排序模型"""
        try:
            # 尝试加载CrossEncoder模型（更适合重排序任务）
            logger.info(f"正在加载重排序模型: {self.model_name}")
            self.model = CrossEncoder(self.model_name)
            self.use_advanced_reranking = True
            logger.info(f"🚀 高级重排序模型加载成功: {self.model_name}")

        except Exception as e:
            logger.warning(f"加载重排序模型失败，将使用简单重排序: {e}")
            self.model = None
            self.use_advanced_reranking = False
    
    def rerank(self, query: str, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        对文档进行重排序

        Args:
            query: 查询文本
            documents: 文档列表

        Returns:
            重排序后的文档列表
        """
        if not self.enabled or not documents:
            return documents

        try:
            if self.use_advanced_reranking and self.model:
                # 使用高级重排序模型
                return self._advanced_rerank(query, documents)
            else:
                # 使用简单重排序（基于现有分数）
                return self._simple_rerank(query, documents)

        except Exception as e:
            logger.error(f"重排序失败: {e}")
            return documents

    def _advanced_rerank(self, query: str, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """使用CrossEncoder模型进行高级重排序"""
        try:
            # 准备输入数据
            query_doc_pairs = []
            for doc in documents:
                content = doc.get("content", "")
                query_doc_pairs.append([query, content])

            # 批量计算相关性分数
            scores = self.model.predict(query_doc_pairs)

            # 将分数添加到文档中并排序
            for i, doc in enumerate(documents):
                doc["rerank_score"] = float(scores[i])
            
            # 按重排序分数排序
            reranked_docs = sorted(documents, key=lambda x: x["rerank_score"], reverse=True)

            # 返回top_k结果
            final_results = reranked_docs[:self.top_k]

            logger.info(f"高级重排序完成: {len(documents)} -> {len(final_results)}")
            return final_results

        except Exception as e:
            logger.error(f"高级重排序失败: {e}")
            return documents[:self.top_k]  # 降级处理

    def _simple_rerank(self, query: str, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """使用简单策略进行重排序（基于现有分数和文本相似度）"""
        try:
            import re
            from collections import Counter

            # 查询词分词（简单空格分割）
            query_words = set(re.findall(r'\w+', query.lower()))

            for doc in documents:
                content = doc.get("content", "").lower()

                # 计算词汇重叠度
                content_words = set(re.findall(r'\w+', content))
                overlap = len(query_words.intersection(content_words))
                overlap_ratio = overlap / len(query_words) if query_words else 0

                # 结合原有分数和词汇重叠度
                original_score = doc.get("similarity_score", doc.get("score", 0.0))
                simple_score = original_score * 0.7 + overlap_ratio * 0.3

                doc["rerank_score"] = simple_score

            # 按重排序分数排序
            reranked_docs = sorted(documents, key=lambda x: x["rerank_score"], reverse=True)

            # 返回top_k结果
            final_results = reranked_docs[:self.top_k]

            logger.info(f"简单重排序完成: {len(documents)} -> {len(final_results)}")
            return final_results

        except Exception as e:
            logger.error(f"简单重排序失败: {e}")
            return documents[:self.top_k]
    
    def rerank_with_scores(self, query: str, documents: List[Dict[str, Any]]) -> List[Tuple[Dict[str, Any], float]]:
        """
        重排序并返回分数
        
        Args:
            query: 查询文本
            documents: 文档列表
            
        Returns:
            (文档, 重排序分数) 的列表
        """
        reranked_docs = self.rerank(query, documents)
        return [(doc, doc.get("rerank_score", 0.0)) for doc in reranked_docs]


class ContextCompressor:
    """上下文压缩器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.enabled = config.get("enabled", True)
        self.max_tokens = config.get("max_tokens", 4000)  # 增加token限制
    
    def compress_context(self, documents: List[Dict[str, Any]], query: str = "") -> str:
        """
        压缩上下文，确保不超过token限制
        
        Args:
            documents: 文档列表
            query: 查询文本（用于相关性判断）
            
        Returns:
            压缩后的上下文文本
        """
        if not self.enabled or not documents:
            return ""
        
        try:
            # 简单的压缩策略：按重要性截断
            context_parts = []
            current_tokens = 0
            
            for doc in documents:
                content = doc.get("content", "")
                # 简单的token估算（中文按字符数，英文按单词数）
                estimated_tokens = len(content) if self._is_chinese(content) else len(content.split())
                
                if current_tokens + estimated_tokens <= self.max_tokens:
                    context_parts.append(content)
                    current_tokens += estimated_tokens
                else:
                    # 如果剩余空间不足，截断当前文档
                    remaining_tokens = self.max_tokens - current_tokens
                    if remaining_tokens > 50:  # 至少保留50个token
                        if self._is_chinese(content):
                            truncated_content = content[:remaining_tokens]
                        else:
                            words = content.split()
                            truncated_content = " ".join(words[:remaining_tokens])
                        context_parts.append(truncated_content + "...")
                    break
            
            compressed_context = "\n\n".join(context_parts)
            logger.info(f"上下文压缩: 估算token数 {current_tokens}")
            
            return compressed_context
            
        except Exception as e:
            logger.error(f"上下文压缩失败: {e}")
            # 降级处理：简单拼接
            return "\n\n".join([doc.get("content", "") for doc in documents[:3]])
    
    def _is_chinese(self, text: str) -> bool:
        """判断文本是否主要为中文"""
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        return chinese_chars > len(text) * 0.3


class RetrievalPostProcessor:
    """检索后处理器，整合重排序和上下文压缩"""
    
    def __init__(self, config: Dict[str, Any]):
        self.reranker = Reranker(config.get("reranking", {}))
        self.compressor = ContextCompressor(config.get("context_compression", {}))
    
    def process(self, query: str, documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        处理检索结果
        
        Args:
            query: 查询文本
            documents: 检索到的文档列表
            
        Returns:
            处理结果字典
        """
        try:
            # 1. 重排序
            reranked_docs = self.reranker.rerank(query, documents)
            
            # 2. 上下文压缩
            compressed_context = self.compressor.compress_context(reranked_docs, query)
            
            return {
                "documents": reranked_docs,
                "context": compressed_context,
                "processing_info": {
                    "original_count": len(documents),
                    "reranked_count": len(reranked_docs),
                    "reranking_enabled": self.reranker.enabled,
                    "compression_enabled": self.compressor.enabled
                }
            }
            
        except Exception as e:
            logger.error(f"检索后处理失败: {e}")
            # 降级处理
            return {
                "documents": documents[:3],
                "context": "\n\n".join([doc.get("content", "") for doc in documents[:3]]),
                "processing_info": {
                    "original_count": len(documents),
                    "reranked_count": len(documents[:3]),
                    "reranking_enabled": False,
                    "compression_enabled": False,
                    "error": str(e)
                }
            }


def get_reranker(config: Dict[str, Any]) -> Reranker:
    """获取重排序器实例"""
    return Reranker(config)


def get_retrieval_post_processor(config: Dict[str, Any]) -> RetrievalPostProcessor:
    """获取检索后处理器实例"""
    # 尝试从服务管理器获取已预加载的重排序服务
    try:
        from rag_action.core.service_manager import service_manager
        rerank_service = service_manager.get_service("rerank_service")
        if rerank_service is not None:
            # 使用已预加载的重排序服务创建后处理器
            processor = RetrievalPostProcessor(config)
            processor.reranker = rerank_service
            return processor
    except Exception:
        pass

    # 如果无法获取预加载的服务，则创建新的实例
    return RetrievalPostProcessor(config)
