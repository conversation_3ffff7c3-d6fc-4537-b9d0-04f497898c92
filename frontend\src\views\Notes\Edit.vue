<template>
  <div class="note-edit">
    <div class="edit-header">
      <el-button @click="$router.back()" type="text">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <div class="header-actions">
        <el-button @click="saveNote" type="primary" :loading="saving">
          <el-icon><Check /></el-icon>
          保存
        </el-button>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <div v-else class="edit-form">
      <el-form :model="form" label-width="100px">
        <el-form-item label="标题" required>
          <el-input
            v-model="form.title"
            placeholder="请输入笔记标题"
            size="large"
          />
        </el-form-item>

        <el-form-item label="标签">
          <el-select
            v-model="form.tags"
            multiple
            filterable
            allow-create
            placeholder="选择或创建标签"
            style="width: 100%"
          >
            <el-option
              v-for="tag in availableTags"
              :key="tag.id"
              :label="tag.name"
              :value="tag.name"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="内容" required>
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="20"
            placeholder="请输入笔记内容"
            style="font-family: 'Monaco', 'Consolas', monospace;"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useNotesStore } from '@/stores'
import { ArrowLeft, Check } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const notesStore = useNotesStore()

const loading = ref(false)
const saving = ref(false)

const form = reactive({
  title: '',
  content: '',
  tags: []
})

const note = computed(() => notesStore.currentNote)
const availableTags = computed(() => notesStore.tags)

const saveNote = async () => {
  if (!form.title.trim() || !form.content.trim()) {
    ElMessage.warning('请填写标题和内容')
    return
  }

  saving.value = true
  try {
    const noteId = parseInt(route.params.id as string)
    await notesStore.updateNote(noteId, {
      title: form.title,
      content: form.content,
      tags: form.tags
    })
    
    ElMessage.success('保存成功')
    router.push(`/notes/${noteId}`)
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const loadNoteData = async () => {
  const noteId = parseInt(route.params.id as string)
  if (!noteId) return

  loading.value = true
  try {
    await notesStore.fetchNoteById(noteId)
    
    if (note.value) {
      form.title = note.value.title
      form.content = note.value.content
      form.tags = note.value.tags.map(tag => tag.name)
    }
  } catch (error) {
    ElMessage.error('获取笔记数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadNoteData()
  notesStore.fetchTags()
})
</script>

<style lang="scss" scoped>
.note-edit {
  padding: var(--spacing-lg);
  max-width: 1000px;
  margin: 0 auto;
  
  .edit-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-lighter);
  }
  
  .loading-container {
    padding: var(--spacing-xl);
  }
  
  .edit-form {
    background: var(--bg-color);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-base);
    box-shadow: var(--shadow-base);
  }
}

@include respond-below(md) {
  .note-edit {
    padding: var(--spacing-md);
    
    .edit-form {
      padding: var(--spacing-lg);
    }
  }
}
</style>
