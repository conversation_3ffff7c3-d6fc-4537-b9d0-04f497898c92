#!/usr/bin/env python3
"""
Milvus BM25完整迁移脚本
一键执行完整的迁移流程
"""
import sys
import os
import logging
import subprocess
import time
from typing import Dict, Any

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CompleteMigrator:
    """完整迁移器"""
    
    def __init__(self):
        self.scripts_dir = os.path.dirname(os.path.abspath(__file__))
        self.project_root = os.path.dirname(self.scripts_dir)
        
        # 迁移步骤状态
        self.steps = {
            "upgrade_dependencies": False,
            "update_config": False,
            "migrate_data": False,
            "test_functionality": False,
            "validate_migration": False
        }
    
    def run_script(self, script_name: str, description: str) -> bool:
        """运行脚本"""
        script_path = os.path.join(self.scripts_dir, script_name)
        
        if not os.path.exists(script_path):
            logger.error(f"脚本不存在: {script_path}")
            return False
        
        logger.info(f"开始执行: {description}")
        logger.info(f"运行脚本: {script_path}")
        
        try:
            # 运行脚本
            result = subprocess.run(
                [sys.executable, script_path],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=1800  # 30分钟超时
            )
            
            if result.returncode == 0:
                logger.info(f"✅ {description} 完成")
                if result.stdout:
                    logger.info(f"输出: {result.stdout}")
                return True
            else:
                logger.error(f"❌ {description} 失败")
                logger.error(f"错误输出: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"❌ {description} 超时")
            return False
        except Exception as e:
            logger.error(f"❌ {description} 执行异常: {e}")
            return False
    
    def check_prerequisites(self) -> bool:
        """检查前提条件"""
        logger.info("=== 检查前提条件 ===")
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            logger.error("Python版本需要3.8+")
            return False
        
        # 检查必要的脚本文件
        required_scripts = [
            "upgrade_dependencies.py",
            "update_config_for_bm25.py",
            "migrate_to_milvus_bm25.py",
            "test_milvus_bm25.py"
        ]
        
        for script in required_scripts:
            script_path = os.path.join(self.scripts_dir, script)
            if not os.path.exists(script_path):
                logger.error(f"缺少必要脚本: {script}")
                return False
        
        # 检查项目结构
        required_dirs = [
            "src/rag_action/service",
            "src/rag_action/models",
            "src/rag_action/core"
        ]
        
        for dir_path in required_dirs:
            full_path = os.path.join(self.project_root, dir_path)
            if not os.path.exists(full_path):
                logger.error(f"缺少必要目录: {dir_path}")
                return False
        
        logger.info("✅ 前提条件检查通过")
        return True
    
    def step1_upgrade_dependencies(self) -> bool:
        """步骤1: 升级依赖"""
        logger.info("\n=== 步骤1: 升级依赖 ===")
        
        success = self.run_script(
            "upgrade_dependencies.py",
            "升级依赖包到支持BM25的版本"
        )
        
        if success:
            self.steps["upgrade_dependencies"] = True
            
            # 额外验证
            try:
                import pymilvus
                from pymilvus import Function, FunctionType
                
                if hasattr(FunctionType, 'BM25'):
                    logger.info("✅ BM25功能验证通过")
                else:
                    logger.warning("⚠️ BM25功能验证失败，但继续执行")
                    
            except ImportError as e:
                logger.warning(f"⚠️ 依赖验证失败: {e}")
        
        return success
    
    def step2_update_config(self) -> bool:
        """步骤2: 更新配置"""
        logger.info("\n=== 步骤2: 更新配置 ===")
        
        success = self.run_script(
            "update_config_for_bm25.py",
            "更新配置文件以支持BM25"
        )
        
        if success:
            self.steps["update_config"] = True
            
            # 检查配置文件是否存在
            config_file = os.path.join(self.project_root, "config.yaml")
            if os.path.exists(config_file):
                logger.info("✅ 配置文件更新完成")
            else:
                logger.warning("⚠️ 配置文件不存在，请手动创建")
        
        return success
    
    def step3_migrate_data(self) -> bool:
        """步骤3: 迁移数据"""
        logger.info("\n=== 步骤3: 迁移数据 ===")
        
        # 询问用户是否继续
        response = input("数据迁移将创建新的Milvus集合，是否继续？(y/N): ")
        if response.lower() != 'y':
            logger.info("用户取消数据迁移")
            return False
        
        success = self.run_script(
            "migrate_to_milvus_bm25.py",
            "迁移数据到支持BM25的新集合"
        )
        
        if success:
            self.steps["migrate_data"] = True
        
        return success
    
    def step4_test_functionality(self) -> bool:
        """步骤4: 测试功能"""
        logger.info("\n=== 步骤4: 测试功能 ===")
        
        success = self.run_script(
            "test_milvus_bm25.py",
            "测试BM25和混合检索功能"
        )
        
        if success:
            self.steps["test_functionality"] = True
        
        return success
    
    def step5_validate_migration(self) -> bool:
        """步骤5: 验证迁移"""
        logger.info("\n=== 步骤5: 验证迁移 ===")
        
        try:
            # 简单的验证逻辑
            logger.info("执行迁移验证...")
            
            # 这里可以添加更多验证逻辑
            # 例如：检查数据一致性、性能对比等
            
            time.sleep(2)  # 模拟验证过程
            
            logger.info("✅ 迁移验证完成")
            self.steps["validate_migration"] = True
            return True
            
        except Exception as e:
            logger.error(f"❌ 迁移验证失败: {e}")
            return False
    
    def print_migration_summary(self):
        """打印迁移总结"""
        logger.info("\n=== 迁移总结 ===")
        
        total_steps = len(self.steps)
        completed_steps = sum(self.steps.values())
        
        logger.info(f"总步骤数: {total_steps}")
        logger.info(f"完成步骤: {completed_steps}")
        logger.info(f"成功率: {completed_steps/total_steps*100:.1f}%")
        
        logger.info("\n步骤详情:")
        step_names = {
            "upgrade_dependencies": "1. 升级依赖",
            "update_config": "2. 更新配置",
            "migrate_data": "3. 迁移数据",
            "test_functionality": "4. 测试功能",
            "validate_migration": "5. 验证迁移"
        }
        
        for step_key, completed in self.steps.items():
            step_name = step_names.get(step_key, step_key)
            status = "✅ 完成" if completed else "❌ 失败"
            logger.info(f"  {step_name}: {status}")
        
        if completed_steps == total_steps:
            logger.info("\n🎉 迁移完全成功！")
            logger.info("\n后续步骤:")
            logger.info("1. 检查应用配置文件")
            logger.info("2. 重启应用服务")
            logger.info("3. 监控系统运行状态")
            logger.info("4. 如有问题，可回退到原系统")
        else:
            logger.info("\n⚠️ 迁移未完全成功，请检查失败的步骤")
            logger.info("可以单独运行失败的脚本进行修复")
    
    def run_complete_migration(self):
        """运行完整迁移流程"""
        logger.info("=== Milvus BM25完整迁移开始 ===")
        
        start_time = time.time()
        
        try:
            # 检查前提条件
            if not self.check_prerequisites():
                logger.error("前提条件检查失败，迁移终止")
                return
            
            # 执行迁移步骤
            steps = [
                self.step1_upgrade_dependencies,
                self.step2_update_config,
                self.step3_migrate_data,
                self.step4_test_functionality,
                self.step5_validate_migration
            ]
            
            for i, step_func in enumerate(steps, 1):
                logger.info(f"\n{'='*50}")
                logger.info(f"执行步骤 {i}/{len(steps)}")
                
                if not step_func():
                    logger.error(f"步骤 {i} 失败，迁移终止")
                    break
                
                logger.info(f"步骤 {i} 完成")
            
            # 打印总结
            end_time = time.time()
            duration = end_time - start_time
            
            logger.info(f"\n迁移总耗时: {duration:.1f} 秒")
            self.print_migration_summary()
            
        except KeyboardInterrupt:
            logger.info("\n用户中断迁移")
        except Exception as e:
            logger.error(f"迁移过程中出现异常: {e}")
        finally:
            logger.info("迁移流程结束")


def main():
    """主函数"""
    print("=" * 60)
    print("Milvus 2.5 BM25全文检索迁移工具")
    print("=" * 60)
    print()
    print("此工具将执行以下步骤:")
    print("1. 升级依赖包到支持BM25的版本")
    print("2. 更新配置文件以支持BM25")
    print("3. 迁移数据到新的Milvus集合")
    print("4. 测试BM25和混合检索功能")
    print("5. 验证迁移结果")
    print()
    print("⚠️ 注意事项:")
    print("- 迁移过程中会创建新的Milvus集合")
    print("- 建议在测试环境先验证迁移流程")
    print("- 迁移过程可能需要较长时间")
    print("- 请确保有足够的磁盘空间")
    print()
    
    response = input("是否继续执行迁移？(y/N): ")
    if response.lower() != 'y':
        print("迁移已取消")
        return
    
    migrator = CompleteMigrator()
    migrator.run_complete_migration()


if __name__ == "__main__":
    main()
