# ingest_ddl.py
import logging
from pymilvus import MilvusClient, DataType, FieldSchema, CollectionSchema
from langchain_community.embeddings import OpenAIEmbeddings
import torch
import yaml

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 1. 初始化嵌入函数
embedding_function = OpenAIEmbeddings(
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",  # API密钥
    base_url="https://api.zhizengzeng.com/v1", 
    model='text-embedding-3-large')

# 2. 读取 DDL 列表（假设 ddl_statements.yaml 中存放 {table_name: "CREATE TABLE ..."}）
with open("ddl_map.yaml","r") as f:
    ddl_map = yaml.safe_load(f)
    logging.info(f"[DDL] 从YAML文件加载了 {len(ddl_map)} 个表/视图定义")

# 3. 连接 Milvus
client = MilvusClient(host="localhost",port=19530)

# 4. 定义 Collection Schema
#    字段：id, vector, table_name, ddl_text
# 这行代码的意思是：用 embedding_function 对 "dummy" 这个字符串做一次嵌入，得到一个向量，然后用 len() 得到这个向量的维度（即向量的长度），赋值给 vector_dim。
vector_dim = len(embedding_function.embed_query("dummy"))
fields = [
    FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
    FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=vector_dim),
    FieldSchema(name="table_name", dtype=DataType.VARCHAR, max_length=100),
    FieldSchema(name="ddl_text", dtype=DataType.VARCHAR, max_length=2000),
]
schema = CollectionSchema(fields, description="DDL Knowledge Base", enable_dynamic_field=False)

# 5. 创建 Collection（如不存在）
collection_name = "ddl_knowledge"
if not client.has_collection(collection_name):
    client.create_collection(collection_name=collection_name, schema=schema)
    logging.info(f"[DDL] 创建了新的集合 {collection_name}")
else:
    logging.info(f"[DDL] 集合 {collection_name} 已存在")
    client.drop_collection(collection_name=collection_name)
    client.create_collection(collection_name=collection_name, schema=schema)

# 6. 为向量字段添加索引
index_params = client.prepare_index_params()
index_params.add_index(field_name="vector", index_type="IVF_FLAT", metric_type="COSINE", params={"nlist": 1024})
client.create_index(collection_name=collection_name, index_params=index_params)

# 7. 批量插入 DDL
data = []
texts = []
for tbl, ddl in ddl_map.items():
    texts.append(ddl)
    data.append({"table_name": tbl, "ddl_text": ddl})

logging.info(f"[DDL] 准备处理 {len(data)} 个表/视图的DDL语句")

# 生成全部嵌入
embeddings = embedding_function.embed_documents(texts)
logging.info(f"[DDL] 成功生成了 {len(embeddings)} 个向量嵌入")

# 组织为 Milvus insert 格式
records = []
# 这段代码的意思是：遍历每个表/视图的向量嵌入（emb）和对应的表信息（rec），
# 把向量嵌入作为"vector"字段加入到rec字典中，然后把rec添加到records列表里，准备后续批量插入Milvus。
# 这段代码的作用是将每个表/视图的向量嵌入（emb）添加到对应的记录（rec）中，然后将更新后的记录加入到records列表中，便于后续批量插入Milvus。
for emb, rec in zip(embeddings, data):
    rec["vector"] = emb
    records.append(rec)


res = client.insert(collection_name=collection_name, data=records)
logging.info(f"[DDL] 成功插入了 {len(records)} 条记录到Milvus")
logging.info(f"[DDL] 插入结果: {res}")

logging.info("[DDL] 知识库构建完成")
