from langchain.utils.math import cosine_similarity
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import RunnableLambda, RunnablePassthrough
from langchain_openai import ChatOpenAI, OpenAIEmbeddings

# 定义两个提示模板
combat_template = """你是一位精通黑悟空战斗技巧的专家。
你擅长以简洁易懂的方式回答关于黑悟空战斗的问题。
当你不知道问题的答案时，你会坦诚相告。

以下是一个问题：
{query}"""

story_template = """你是一位熟悉黑悟空故事情节的专家。
你擅长将复杂的情节分解并详细解释。
当你不知道问题的答案时，你会坦诚相告。

以下是一个问题：
{query}"""

# 初始化嵌入模型
embeddings = OpenAIEmbeddings(
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    base_url="https://api.zhizengzeng.com/v1")
prompt_templates = [combat_template, story_template]
prompt_embeddings = embeddings.embed_documents(prompt_templates)

# 定义路由函数
def prompt_router(input):
    # 对用户问题进行嵌入
    query_embedding = embeddings.embed_query(input["query"])
    # 计算相似度
    # 计算用户问题与每个模板的相似度，选出最相似的模板
    similarity = cosine_similarity([query_embedding], prompt_embeddings)[0]
    max_index = similarity.argmax()  # 找到最大相似度的索引
    most_similar = prompt_templates[max_index]  # 取出最相似的模板
    # 选择最相似的提示模板
    print("使用战斗技巧模板" if most_similar == combat_template else "使用故事情节模板")
    return PromptTemplate.from_template(most_similar)

# 创建处理链
# 这个chain的作用是：根据用户输入的问题内容，自动选择最合适的提示模板（如战斗技巧或故事情节），然后将用户问题和选定的模板组合，交给大模型（ChatOpenAI）进行回答，最后用StrOutputParser解析输出结果。
# 具体流程如下：
# 1. {"query": RunnablePassthrough()}：将用户输入原样传递，构造成字典格式，键为"query"。
# 2. | RunnableLambda(prompt_router)：调用prompt_router函数，根据用户问题的语义嵌入与预设模板的相似度，自动路由到最合适的提示模板。
# 3. | ChatOpenAI()：将选定的提示模板和用户问题交给大模型进行生成式问答。
# 4. | StrOutputParser()：解析大模型的输出，得到最终的字符串答案。

llm = ChatOpenAI(model="gpt-4o-mini", temperature=0,api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",base_url="https://api.zhizengzeng.com/v1")
chain = (
    # RunnablePassthrough 是 LangChain 中的一个可运行对象（Runnable），它的作用是“原样传递”输入，不做任何处理，常用于 chain 的输入端，保证用户输入能被后续步骤以指定格式（如字典）接收。
    # RunnableLambda 是 LangChain 的另一个可运行对象，它允许你将任意 Python 函数（如 prompt_router）包装成 chain 的一个步骤，实现自定义的处理逻辑。
    # 下面是带注释的 chain 组合：
    {
        "query": RunnablePassthrough()  # 直接将用户输入作为 "query" 字段传递下去
    }
    | RunnableLambda(prompt_router)      # 用 prompt_router 函数根据语义路由选择合适的提示模板
    | llm                     # 选定模板后，交给大模型生成回答
    | StrOutputParser()                 # 解析大模型输出，得到最终字符串答案
)

# 示例问题
print(chain.invoke("黑悟空是如何打败敌人的？"))
