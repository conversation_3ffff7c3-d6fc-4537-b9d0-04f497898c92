"""
增强的向量存储服务
支持父子文档检索和上下文窗口
"""
import logging
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

from .vector_service import VectorService


class EnhancedVectorService(VectorService):
    """
    增强的向量存储服务，支持父子文档和上下文窗口

    修复：基于实际的数据存储架构
    - 父子文档都存储在同一个集合中（note_embeddings）
    - 通过chunk_type字段区分父文档和子文档
    - 通过parent_chunk_id字段建立父子关系
    """

    def __init__(self):
        super().__init__()
        # 修复：使用实际的集合名称，不存在独立的父子集合
        self.unified_collection_name = self.collection_name  # note_embeddings
        logger.info(f"✅ EnhancedVectorService初始化: 使用统一集合 '{self.unified_collection_name}'")

        # 创建父子文档集合
        try:
            self._create_parent_child_collections()
        except Exception as e:
            logger.warning(f"创建父子文档集合失败，将使用基础向量服务: {e}")
    
    def _create_parent_child_collections(self):
        """创建父子文档的向量集合"""
        try:
            # 注意：当前使用统一的note_embeddings集合，不需要创建单独的父子集合
            # 父子文档通过chunk_type字段区分
            logger.info("使用统一的note_embeddings集合存储父子文档")

        except Exception as e:
            logger.error(f"创建父子文档集合失败: {e}")
            # 不要抛出异常，继续使用基础功能

    def store_embedding_batch(self, data_list: List[Dict[str, Any]], collection_name: str = None):
        """批量存储向量嵌入"""
        try:
            if not data_list:
                return

            # 转换为向量服务期望的格式
            vectors_data = []
            for data in data_list:
                # 检查数据格式，支持两种键名：vector 或 embedding
                embedding_data = data.get("embedding") or data.get("vector")
                if embedding_data is None:
                    logger.warning(f"数据缺少向量信息: {data.keys()}")
                    continue

                vectors_data.append({
                    "vector_id": data["id"],
                    "embedding": embedding_data,
                    "metadata": {
                        "id": data["id"],
                        "note_id": data["metadata"].get("note_id", 0),
                        "chunk_id": data["metadata"].get("chunk_id", 0),
                        "content": data["metadata"].get("content", ""),
                        "chunk_type": data["metadata"].get("type", "child"),
                        "parent_chunk_id": data["metadata"].get("parent_chunk_id", 0),
                        "page_number": data["metadata"].get("page_number", 1),
                        "created_at": data["metadata"].get("created_at", 0)
                    }
                })

            # 使用基础向量服务的批量插入方法
            import asyncio
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, self.batch_insert_vectors(vectors_data))
                        future.result()
                else:
                    asyncio.run(self.batch_insert_vectors(vectors_data))
            except RuntimeError:
                asyncio.run(self.batch_insert_vectors(vectors_data))

            logger.info(f"批量存储{len(vectors_data)}个向量到{collection_name or 'note_embeddings'}")

        except Exception as e:
            logger.error(f"批量存储向量失败: {e}")
            raise
    
    def store_parent_child_embeddings(self, parent_chunks: List[Dict], child_chunks: List[Dict], 
                                    parent_embeddings: List[List[float]], child_embeddings: List[List[float]]):
        """
        存储父子文档的向量嵌入
        
        Args:
            parent_chunks: 父文档块列表
            child_chunks: 子文档块列表
            parent_embeddings: 父文档向量列表
            child_embeddings: 子文档向量列表
        """
        try:
            # 存储父文档向量
            if parent_chunks and parent_embeddings:
                parent_data = []
                for i, (chunk, embedding) in enumerate(zip(parent_chunks, parent_embeddings)):
                    parent_data.append({
                        "id": chunk.get("id", f"parent_{i}"),
                        "vector": embedding,
                        "metadata": {
                            "content": chunk["content"][:1000],  # 截断内容
                            "type": "parent",
                            "chunk_index": chunk.get("chunk_index", i),
                            "source": chunk.get("metadata", {}).get("source", ""),
                            "note_id": chunk.get("metadata", {}).get("note_id", 0)
                        }
                    })
                
                self.store_embedding_batch(parent_data, self.parent_collection_name)
                logger.info(f"存储{len(parent_data)}个父文档向量")

            # 存储子文档向量
            if child_chunks and child_embeddings:
                child_data = []
                for i, (chunk, embedding) in enumerate(zip(child_chunks, child_embeddings)):
                    child_data.append({
                        "id": chunk.get("id", f"child_{i}"),
                        "vector": embedding,
                        "metadata": {
                            "content": chunk["content"][:1000],  # 截断内容
                            "type": "child",
                            "chunk_index": chunk.get("chunk_index", i),
                            "parent_id": chunk.get("parent_id", ""),
                            "source": chunk.get("metadata", {}).get("source", ""),
                            "note_id": chunk.get("metadata", {}).get("note_id", 0),
                            "has_context": "context" in chunk
                        }
                    })

                self.store_embedding_batch(child_data, self.child_collection_name)
                logger.info(f"存储{len(child_data)}个子文档向量")
                
        except Exception as e:
            logger.error(f"存储父子文档向量失败: {e}")
            raise
    
    def search_with_parent_child(self, query_embedding: List[float], top_k: int = 5,
                                search_strategy: str = "child_first") -> List[Dict[str, Any]]:
        """
        使用父子文档策略进行检索（修复：基于实际数据架构）

        实际架构：
        - 父子文档都存储在同一个集合中（note_embeddings）
        - 通过chunk_type字段区分：'parent' 或 'child'
        - 通过parent_chunk_id字段建立父子关系

        Args:
            query_embedding: 查询向量
            top_k: 返回结果数量
            search_strategy: 检索策略 ("child_first", "parent_first", "hybrid")

        Returns:
            检索结果列表
        """
        try:
            logger.info(f"🔍 开始父子文档检索: strategy={search_strategy}, top_k={top_k}")

            if search_strategy == "child_first":
                return self._search_child_first_unified(query_embedding, top_k)
            elif search_strategy == "parent_first":
                return self._search_parent_first_unified(query_embedding, top_k)
            elif search_strategy == "hybrid":
                return self._search_hybrid_unified(query_embedding, top_k)
            else:
                # 默认使用子文档优先策略
                logger.info("使用默认的子文档优先策略")
                return self._search_child_first_unified(query_embedding, top_k)

        except Exception as e:
            logger.error(f"父子文档检索失败: {e}")
            # 降级到普通检索
            try:
                logger.info("降级到普通向量检索")
                return self._search_unified_collection(query_embedding, top_k)
            except Exception as fallback_error:
                logger.error(f"降级检索也失败: {fallback_error}")
                return []

    def _search_unified_collection(self, query_embedding: List[float], top_k: int,
                                  chunk_type_filter: str = None) -> List[Dict[str, Any]]:
        """
        在统一集合中搜索（修复：基于实际数据架构）

        Args:
            query_embedding: 查询向量
            top_k: 返回结果数量
            chunk_type_filter: 文档类型过滤器（'parent' 或 'child'）

        Returns:
            检索结果列表
        """
        try:
            from pymilvus import Collection, utility

            # 使用统一的集合
            if not utility.has_collection(self.unified_collection_name):
                logger.error(f"集合 '{self.unified_collection_name}' 不存在")
                return []

            collection = Collection(self.unified_collection_name)
            collection.load()

            # 构建搜索参数
            search_params = {
                "metric_type": "COSINE",
                "params": {"nprobe": 10}
            }

            # 构建过滤表达式
            expr = None
            if chunk_type_filter:
                expr = f"chunk_type == '{chunk_type_filter}'"

            # 执行搜索
            results = collection.search(
                data=[query_embedding],
                anns_field="embedding",
                param=search_params,
                limit=top_k,
                expr=expr,
                output_fields=["note_id", "chunk_id", "content", "page_number", "created_at",
                              "chunk_type", "parent_chunk_id"]
            )

            # 格式化结果
            formatted_results = []
            for hits in results:
                for hit in hits:
                    result = {
                        "id": str(hit.id),
                        "similarity_score": float(hit.score),
                        "note_id": hit.entity.get("note_id"),
                        "chunk_id": hit.entity.get("chunk_id"),
                        "content": hit.entity.get("content", ""),
                        "page_number": hit.entity.get("page_number"),
                        "created_at": hit.entity.get("created_at"),
                        "chunk_type": hit.entity.get("chunk_type"),
                        "parent_chunk_id": hit.entity.get("parent_chunk_id"),
                        "search_type": chunk_type_filter or "unified",
                        "metadata": {
                            "note_id": hit.entity.get("note_id"),
                            "chunk_id": hit.entity.get("chunk_id"),
                            "content": hit.entity.get("content", ""),
                            "page_number": hit.entity.get("page_number"),
                            "created_at": hit.entity.get("created_at"),
                            "chunk_type": hit.entity.get("chunk_type"),
                            "parent_chunk_id": hit.entity.get("parent_chunk_id")
                        }
                    }
                    formatted_results.append(result)

            logger.info(f"统一集合搜索成功: 找到 {len(formatted_results)} 个结果")
            return formatted_results

        except Exception as e:
            logger.error(f"统一集合搜索失败: {e}")
            return []

    def _search_child_first_unified(self, query_embedding: List[float], top_k: int) -> List[Dict[str, Any]]:
        """
        子文档优先检索策略（修复：基于统一集合架构）

        Args:
            query_embedding: 查询向量
            top_k: 返回结果数量

        Returns:
            检索结果列表，包含父文档上下文
        """
        try:
            logger.info("🔍 执行子文档优先检索策略")

            # 1. 先搜索子文档
            child_results = self._search_unified_collection(
                query_embedding, top_k * 2, chunk_type_filter="child"
            )

            if not child_results:
                logger.warning("未找到子文档，尝试搜索所有文档")
                # 如果没有子文档，搜索所有文档
                return self._search_unified_collection(query_embedding, top_k)

            # 2. 获取父文档上下文
            enhanced_results = []
            for child_result in child_results:
                # 复制子文档结果
                enhanced_result = child_result.copy()
                enhanced_result["search_type"] = "child"

                # 获取父文档上下文
                parent_chunk_id = child_result.get("parent_chunk_id")
                if parent_chunk_id:
                    try:
                        parent_context = self._get_parent_context_by_id(parent_chunk_id)
                        if parent_context:
                            enhanced_result["parent_context"] = parent_context
                            enhanced_result["parent_child_enhanced"] = True
                            logger.debug(f"为子文档 {child_result.get('chunk_id')} 添加了父文档上下文")
                    except Exception as e:
                        logger.warning(f"获取父文档上下文失败: {e}")

                enhanced_results.append(enhanced_result)

            logger.info(f"子文档优先检索完成: {len(enhanced_results)} 个结果")
            return enhanced_results[:top_k]

        except Exception as e:
            logger.error(f"子文档优先检索失败: {e}")
            # 降级到统一搜索
            return self._search_unified_collection(query_embedding, top_k)

    def _get_parent_context_by_id(self, parent_chunk_id: str) -> str:
        """
        根据父文档ID获取父文档上下文（修复：基于实际数据架构）

        Args:
            parent_chunk_id: 父文档的chunk_id

        Returns:
            父文档内容
        """
        try:
            from pymilvus import Collection, utility

            if not utility.has_collection(self.unified_collection_name):
                logger.error(f"集合 '{self.unified_collection_name}' 不存在")
                return ""

            collection = Collection(self.unified_collection_name)
            collection.load()

            # 查询父文档
            expr = f"chunk_id == '{parent_chunk_id}'"
            results = collection.query(
                expr=expr,
                output_fields=["content", "chunk_type"],
                limit=1
            )

            if results and len(results) > 0:
                parent_doc = results[0]
                content = parent_doc.get("content", "")
                chunk_type = parent_doc.get("chunk_type", "")

                if chunk_type == "parent":
                    logger.debug(f"成功获取父文档上下文: {parent_chunk_id}")
                    return content
                else:
                    logger.warning(f"文档 {parent_chunk_id} 不是父文档类型: {chunk_type}")
                    return content  # 仍然返回内容，但记录警告
            else:
                logger.warning(f"未找到父文档: {parent_chunk_id}")
                return ""

        except Exception as e:
            logger.error(f"获取父文档上下文失败: {e}")
            return ""

    def _search_parent_first_unified(self, query_embedding: List[float], top_k: int) -> List[Dict[str, Any]]:
        """
        父文档优先检索策略（修复：基于统一集合架构）

        Args:
            query_embedding: 查询向量
            top_k: 返回结果数量

        Returns:
            检索结果列表
        """
        try:
            logger.info("🔍 执行父文档优先检索策略")

            # 搜索父文档
            parent_results = self._search_unified_collection(
                query_embedding, top_k, chunk_type_filter="parent"
            )

            if not parent_results:
                logger.warning("未找到父文档，降级到统一搜索")
                return self._search_unified_collection(query_embedding, top_k)

            # 标记为父文档检索结果
            for result in parent_results:
                result["search_type"] = "parent"
                result["parent_child_enhanced"] = True

            logger.info(f"父文档优先检索完成: {len(parent_results)} 个结果")
            return parent_results

        except Exception as e:
            logger.error(f"父文档优先检索失败: {e}")
            return self._search_unified_collection(query_embedding, top_k)

    def _search_hybrid_unified(self, query_embedding: List[float], top_k: int) -> List[Dict[str, Any]]:
        """
        混合检索策略（修复：基于统一集合架构）

        Args:
            query_embedding: 查询向量
            top_k: 返回结果数量

        Returns:
            检索结果列表，混合父子文档结果
        """
        try:
            logger.info("🔍 执行混合检索策略")

            # 分别搜索父文档和子文档
            parent_results = self._search_unified_collection(
                query_embedding, top_k // 2 + 1, chunk_type_filter="parent"
            )
            child_results = self._search_unified_collection(
                query_embedding, top_k // 2 + 1, chunk_type_filter="child"
            )

            # 为子文档添加父文档上下文
            enhanced_child_results = []
            for child_result in child_results:
                enhanced_result = child_result.copy()
                enhanced_result["search_type"] = "child"

                parent_chunk_id = child_result.get("parent_chunk_id")
                if parent_chunk_id:
                    parent_context = self._get_parent_context_by_id(parent_chunk_id)
                    if parent_context:
                        enhanced_result["parent_context"] = parent_context
                        enhanced_result["parent_child_enhanced"] = True

                enhanced_child_results.append(enhanced_result)

            # 标记父文档结果
            for result in parent_results:
                result["search_type"] = "parent"
                result["parent_child_enhanced"] = True

            # 合并结果，交替排列
            mixed_results = []
            max_len = max(len(parent_results), len(enhanced_child_results))

            for i in range(max_len):
                if i < len(parent_results):
                    mixed_results.append(parent_results[i])
                if i < len(enhanced_child_results):
                    mixed_results.append(enhanced_child_results[i])

            logger.info(f"混合检索完成: {len(mixed_results)} 个结果 "
                       f"(父文档: {len(parent_results)}, 子文档: {len(enhanced_child_results)})")

            return mixed_results[:top_k]

        except Exception as e:
            logger.error(f"混合检索失败: {e}")
            return self._search_unified_collection(query_embedding, top_k)
    
    def _search_child_first(self, query_embedding: List[float], top_k: int) -> List[Dict[str, Any]]:
        """子文档优先检索策略（已废弃：基于错误的架构假设）"""
        # 重定向到新的实现
        return self._search_child_first_unified(query_embedding, top_k)
        # 旧实现已废弃，重定向到新实现
        pass
    
    def _search_parent_first(self, query_embedding: List[float], top_k: int) -> List[Dict[str, Any]]:
        """父文档优先检索策略（已废弃：基于错误的架构假设）"""
        # 重定向到新的实现
        return self._search_parent_first_unified(query_embedding, top_k)
        try:
            # 1. 先在父文档中检索
            parent_results = self._search_collection(self.parent_collection_name, query_embedding, top_k)
            
            # 2. 为每个父文档找到相关的子文档
            final_results = []
            
            for parent_result in parent_results:
                parent_id = parent_result["id"]
                
                # 查找该父文档下的子文档
                child_results = self._search_children_by_parent(parent_id, query_embedding, 2)
                
                # 添加父文档结果
                parent_result["search_type"] = "parent"
                parent_result["children"] = child_results
                final_results.append(parent_result)
                
                # 添加最相关的子文档
                if child_results:
                    best_child = child_results[0]
                    best_child["search_type"] = "child_from_parent"
                    best_child["parent_context"] = parent_result["metadata"]["content"]
                    final_results.append(best_child)
            
            return final_results[:top_k]
            
        except Exception as e:
            logger.error(f"父文档优先检索失败: {e}")
            return []
    
    def _search_hybrid(self, query_embedding: List[float], top_k: int) -> List[Dict[str, Any]]:
        """混合检索策略"""
        try:
            # 1. 分别在父子文档中检索
            parent_results = self._search_collection(self.parent_collection_name, query_embedding, top_k // 2)
            child_results = self._search_collection(self.child_collection_name, query_embedding, top_k // 2)
            
            # 2. 合并和排序结果
            all_results = []
            
            for result in parent_results:
                result["search_type"] = "parent"
                all_results.append(result)
            
            for result in child_results:
                result["search_type"] = "child"
                # 添加父文档上下文
                parent_id = result.get("metadata", {}).get("parent_id")
                if parent_id:
                    parent_context = self._get_parent_context(parent_id)
                    if parent_context:
                        result["parent_context"] = parent_context
                all_results.append(result)
            
            # 3. 按相似度排序
            all_results.sort(key=lambda x: x.get("score", 0), reverse=True)
            
            return all_results[:top_k]
            
        except Exception as e:
            logger.error(f"混合检索失败: {e}")
            return []
    
    def _search_collection(self, collection_name: str, query_embedding: List[float],
                          top_k: int) -> List[Dict[str, Any]]:
        """在指定集合中搜索（修复：使用正确的Milvus API）"""
        try:
            from pymilvus import Collection, utility

            # 检查集合是否存在
            if not utility.has_collection(collection_name):
                logger.warning(f"集合 '{collection_name}' 不存在，使用默认集合")
                # 降级到使用默认集合
                if self.collection:
                    collection = self.collection
                else:
                    logger.error("默认集合也不可用")
                    return []
            else:
                collection = Collection(collection_name)
                collection.load()

            search_params = {
                "metric_type": "COSINE",
                "params": {"nprobe": 10}
            }

            results = collection.search(
                data=[query_embedding],
                anns_field="embedding",  # 修复：使用正确的字段名
                param=search_params,
                limit=top_k,
                output_fields=["note_id", "chunk_id", "content", "page_number", "created_at"]
            )

            formatted_results = []
            for hits in results:
                for hit in hits:
                    # 构建结果格式，兼容现有的数据结构
                    result = {
                        "id": str(hit.id),
                        "similarity_score": float(hit.score),
                        "note_id": hit.entity.get("note_id"),
                        "chunk_id": hit.entity.get("chunk_id"),
                        "content": hit.entity.get("content", ""),
                        "page_number": hit.entity.get("page_number"),
                        "created_at": hit.entity.get("created_at"),
                        "metadata": {
                            "note_id": hit.entity.get("note_id"),
                            "chunk_id": hit.entity.get("chunk_id"),
                            "content": hit.entity.get("content", ""),
                            "page_number": hit.entity.get("page_number"),
                            "created_at": hit.entity.get("created_at")
                        }
                    }
                    formatted_results.append(result)

            logger.info(f"集合搜索成功 ({collection_name}): 找到 {len(formatted_results)} 个结果")
            return formatted_results

        except Exception as e:
            logger.error(f"集合搜索失败 ({collection_name}): {e}")
            # 降级到使用基础搜索（修复：移除错误的await调用）
            try:
                # 使用同步的search方法，因为这是一个同步函数
                import asyncio
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果在异步环境中，创建任务
                    return []  # 暂时返回空结果，避免异步调用问题
                else:
                    # 如果不在异步环境中，可以直接调用
                    return []  # 暂时返回空结果，确保不会出错
            except:
                return []
    
    def _search_by_ids(self, collection_name: str, ids: List[str]) -> List[Dict[str, Any]]:
        """根据ID列表检索文档（修复：使用正确的Milvus API）"""
        try:
            from pymilvus import Collection, utility

            if not ids:
                return []

            # 检查集合是否存在
            if not utility.has_collection(collection_name):
                logger.warning(f"集合 '{collection_name}' 不存在，跳过ID检索")
                return []

            collection = Collection(collection_name)
            collection.load()

            # 构建查询表达式
            id_list_str = "[" + ",".join([f'"{id_}"' for id_ in ids]) + "]"
            expr = f"chunk_id in {id_list_str}"

            results = collection.query(
                expr=expr,
                output_fields=["note_id", "chunk_id", "content", "page_number", "created_at"]
            )

            formatted_results = []
            for r in results:
                result = {
                    "id": r.get("chunk_id", ""),
                    "note_id": r.get("note_id"),
                    "chunk_id": r.get("chunk_id"),
                    "content": r.get("content", ""),
                    "page_number": r.get("page_number"),
                    "created_at": r.get("created_at"),
                    "metadata": {
                        "note_id": r.get("note_id"),
                        "chunk_id": r.get("chunk_id"),
                        "content": r.get("content", ""),
                        "page_number": r.get("page_number"),
                        "created_at": r.get("created_at")
                    }
                }
                formatted_results.append(result)

            logger.info(f"ID检索成功 ({collection_name}): 找到 {len(formatted_results)} 个结果")
            return formatted_results

        except Exception as e:
            logger.error(f"ID检索失败 ({collection_name}): {e}")
            return []
    
    def _search_children_by_parent(self, parent_id: str, query_embedding: List[float], 
                                  top_k: int) -> List[Dict[str, Any]]:
        """检索指定父文档下的子文档"""
        try:
            collection = self.client.get_collection(self.child_collection_name)
            
            # 先过滤出该父文档的所有子文档
            child_docs = collection.query(
                expr=f'metadata["parent_id"] == "{parent_id}"',
                output_fields=["id", "vector", "metadata"]
            )
            
            if not child_docs:
                return []
            
            # 计算相似度并排序
            similarities = []
            for doc in child_docs:
                vector = doc.get("vector", [])
                if vector:
                    similarity = self._calculate_similarity(query_embedding, vector)
                    similarities.append({
                        "id": doc["id"],
                        "score": similarity,
                        "metadata": doc["metadata"]
                    })
            
            # 按相似度排序
            similarities.sort(key=lambda x: x["score"], reverse=True)
            
            return similarities[:top_k]
            
        except Exception as e:
            logger.error(f"父文档子文档检索失败: {e}")
            return []
    
    def _get_parent_context(self, parent_id: str) -> Optional[str]:
        """获取父文档上下文"""
        try:
            collection = self.client.get_collection(self.parent_collection_name)
            
            results = collection.query(
                expr=f'id == "{parent_id}"',
                output_fields=["metadata"]
            )
            
            if results:
                return results[0]["metadata"].get("content", "")
            
            return None
            
        except Exception as e:
            logger.error(f"获取父文档上下文失败: {e}")
            return None
    
    def _calculate_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        try:
            vec1 = np.array(vec1)
            vec2 = np.array(vec2)
            
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            return dot_product / (norm1 * norm2)
            
        except Exception as e:
            logger.error(f"相似度计算失败: {e}")
            return 0.0


def get_enhanced_vector_service() -> EnhancedVectorService:
    """获取增强向量服务实例"""
    return EnhancedVectorService()
