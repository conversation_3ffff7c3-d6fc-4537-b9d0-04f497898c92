"""
07 - LangGraph 并行处理
展示 LangGraph 支持的多种并行处理模式
"""

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from typing import Dict, List, Any, TypedDict, Annotated
import json
import asyncio
from datetime import datetime

# 创建 LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0.7
)

# 定义状态类型
class ParallelState(TypedDict):
    messages: Annotated[List, add_messages]  # 消息历史
    user_input: str  # 用户输入
    parallel_results: Dict[str, Any]  # 并行处理结果
    final_result: str  # 最终结果
    execution_path: List[str]  # 执行路径

def parallel_nodes_example():
    """并行节点示例：多个节点同时执行"""
    
    print("=== LangGraph 并行节点示例 ===")
    print("多个节点同时执行，然后合并结果")
    print()
    
    def analyze_sentiment_node(state: ParallelState) -> ParallelState:
        """情感分析节点"""
        user_input = state["user_input"]
        
        print(f"  执行情感分析...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
请分析以下文本的情感倾向：

文本：{user_input}

请返回JSON格式的分析结果：
{{
    "sentiment": "positive|negative|neutral",
    "confidence": 0.0-1.0,
    "reason": "分析理由"
}}

请只返回JSON：
""")
        ])
        
        try:
            result = json.loads(response.content)
        except:
            result = {"sentiment": "neutral", "confidence": 0.5, "reason": "分析失败"}
        
        return {
            **state,
            "parallel_results": {**state.get("parallel_results", {}), "sentiment": result},
            "execution_path": state["execution_path"] + ["情感分析完成"]
        }
    
    def extract_keywords_node(state: ParallelState) -> ParallelState:
        """关键词提取节点"""
        user_input = state["user_input"]
        
        print(f"  执行关键词提取...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
请从以下文本中提取关键词：

文本：{user_input}

请返回JSON格式的关键词列表：
{{
    "keywords": ["关键词1", "关键词2", "关键词3"],
    "importance": [0.9, 0.7, 0.5]
}}

请只返回JSON：
""")
        ])
        
        try:
            result = json.loads(response.content)
        except:
            result = {"keywords": ["提取失败"], "importance": [0.5]}
        
        return {
            **state,
            "parallel_results": {**state.get("parallel_results", {}), "keywords": result},
            "execution_path": state["execution_path"] + ["关键词提取完成"]
        }
    
    def summarize_node(state: ParallelState) -> ParallelState:
        """文本摘要节点"""
        user_input = state["user_input"]
        
        print(f"  执行文本摘要...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
请为以下文本生成摘要：

文本：{user_input}

请返回JSON格式的摘要：
{{
    "summary": "文本摘要",
    "length": "short|medium|long",
    "key_points": ["要点1", "要点2"]
}}

请只返回JSON：
""")
        ])
        
        try:
            result = json.loads(response.content)
        except:
            result = {"summary": "摘要生成失败", "length": "short", "key_points": []}
        
        return {
            **state,
            "parallel_results": {**state.get("parallel_results", {}), "summary": result},
            "execution_path": state["execution_path"] + ["文本摘要完成"]
        }
    
    def merge_results_node(state: ParallelState) -> ParallelState:
        """合并结果节点"""
        parallel_results = state["parallel_results"]
        user_input = state["user_input"]
        
        print(f"步骤2: 合并并行处理结果...")
        
        results_text = json.dumps(parallel_results, ensure_ascii=False, indent=2)
        
        response = llm.invoke([
            HumanMessage(content=f"""
请基于以下并行处理结果，生成一个综合的分析报告：

原始输入：{user_input}

并行处理结果：
{results_text}

请生成一个综合的分析报告，包括：
1. 整体情感倾向
2. 关键信息提取
3. 文本摘要
4. 综合建议

请提供结构化的分析报告：
""")
        ])
        
        final_result = response.content
        
        return {
            **state,
            "final_result": final_result,
            "execution_path": state["execution_path"] + ["结果合并完成"],
            "messages": [AIMessage(content=f"并行处理完成，生成了综合报告")]
        }
    
    # 创建图
    workflow = StateGraph(ParallelState)
    
    # 添加节点
    workflow.add_node("sentiment", analyze_sentiment_node)
    workflow.add_node("keywords", extract_keywords_node)
    workflow.add_node("summary", summarize_node)
    workflow.add_node("merge", merge_results_node)
    
    # 设置入口点和边
    workflow.set_entry_point("sentiment")
    workflow.add_edge("sentiment", "merge")
    workflow.add_edge("keywords", "merge")
    workflow.add_edge("summary", "merge")
    workflow.add_edge("merge", END)
    
    # 编译图
    app = workflow.compile()
    
    # 测试数据
    test_input = "人工智能技术正在快速发展，为我们的生活带来了很多便利，但也带来了一些担忧。"
    
    print(f"测试输入: {test_input}")
    print("=" * 60)
    
    # 运行图
    result = app.invoke({
        "user_input": test_input,
        "messages": [],
        "parallel_results": {},
        "final_result": "",
        "execution_path": []
    })
    
    print(f"\n执行路径: {' -> '.join(result['execution_path'])}")
    print(f"\n并行处理结果:")
    for key, value in result['parallel_results'].items():
        print(f"  {key}: {value}")
    print(f"\n最终综合报告:\n{result['final_result']}")

def async_parallel_example():
    """异步并行处理示例"""
    
    print("\n=== 异步并行处理示例 ===")
    print("使用异步函数实现真正的并行处理")
    print()
    
    async def async_sentiment_node(state: ParallelState) -> ParallelState:
        """异步情感分析节点"""
        user_input = state["user_input"]
        
        print(f"  异步执行情感分析...")
        
        response = await llm.ainvoke([
            HumanMessage(content=f"""
请分析以下文本的情感倾向：

文本：{user_input}

请返回JSON格式的分析结果：
{{
    "sentiment": "positive|negative|neutral",
    "confidence": 0.0-1.0,
    "reason": "分析理由",
    "processing_time": "{datetime.now().strftime('%H:%M:%S')}"
}}

请只返回JSON：
""")
        ])
        
        try:
            result = json.loads(response.content)
        except:
            result = {"sentiment": "neutral", "confidence": 0.5, "reason": "分析失败", "processing_time": datetime.now().strftime('%H:%M:%S')}
        
        return {
            **state,
            "parallel_results": {**state.get("parallel_results", {}), "sentiment": result},
            "execution_path": state["execution_path"] + ["异步情感分析完成"]
        }
    
    async def async_keywords_node(state: ParallelState) -> ParallelState:
        """异步关键词提取节点"""
        user_input = state["user_input"]
        
        print(f"  异步执行关键词提取...")
        
        response = await llm.ainvoke([
            HumanMessage(content=f"""
请从以下文本中提取关键词：

文本：{user_input}

请返回JSON格式的关键词列表：
{{
    "keywords": ["关键词1", "关键词2", "关键词3"],
    "importance": [0.9, 0.7, 0.5],
    "processing_time": "{datetime.now().strftime('%H:%M:%S')}"
}}

请只返回JSON：
""")
        ])
        
        try:
            result = json.loads(response.content)
        except:
            result = {"keywords": ["提取失败"], "importance": [0.5], "processing_time": datetime.now().strftime('%H:%M:%S')}
        
        return {
            **state,
            "parallel_results": {**state.get("parallel_results", {}), "keywords": result},
            "execution_path": state["execution_path"] + ["异步关键词提取完成"]
        }
    
    async def async_summary_node(state: ParallelState) -> ParallelState:
        """异步文本摘要节点"""
        user_input = state["user_input"]
        
        print(f"  异步执行文本摘要...")
        
        response = await llm.ainvoke([
            HumanMessage(content=f"""
请为以下文本生成摘要：

文本：{user_input}

请返回JSON格式的摘要：
{{
    "summary": "文本摘要",
    "length": "short|medium|long",
    "key_points": ["要点1", "要点2"],
    "processing_time": "{datetime.now().strftime('%H:%M:%S')}"
}}

请只返回JSON：
""")
        ])
        
        try:
            result = json.loads(response.content)
        except:
            result = {"summary": "摘要生成失败", "length": "short", "key_points": [], "processing_time": datetime.now().strftime('%H:%M:%S')}
        
        return {
            **state,
            "parallel_results": {**state.get("parallel_results", {}), "summary": result},
            "execution_path": state["execution_path"] + ["异步文本摘要完成"]
        }
    
    async def async_merge_results_node(state: ParallelState) -> ParallelState:
        """异步合并结果节点"""
        parallel_results = state["parallel_results"]
        user_input = state["user_input"]
        
        print(f"步骤2: 异步合并并行处理结果...")
        
        results_text = json.dumps(parallel_results, ensure_ascii=False, indent=2)
        
        response = await llm.ainvoke([
            HumanMessage(content=f"""
请基于以下异步并行处理结果，生成一个综合的分析报告：

原始输入：{user_input}

异步并行处理结果：
{results_text}

请生成一个综合的分析报告，包括：
1. 整体情感倾向
2. 关键信息提取
3. 文本摘要
4. 处理性能分析
5. 综合建议

请提供结构化的分析报告：
""")
        ])
        
        final_result = response.content
        
        return {
            **state,
            "final_result": final_result,
            "execution_path": state["execution_path"] + ["异步结果合并完成"],
            "messages": [AIMessage(content=f"异步并行处理完成，生成了综合报告")]
        }
    
    # 创建图
    workflow = StateGraph(ParallelState)
    
    # 添加节点
    workflow.add_node("async_sentiment", async_sentiment_node)
    workflow.add_node("async_keywords", async_keywords_node)
    workflow.add_node("async_summary", async_summary_node)
    workflow.add_node("async_merge", async_merge_results_node)
    
    # 设置入口点和边
    workflow.set_entry_point("async_sentiment")
    workflow.add_edge("async_sentiment", "async_merge")
    workflow.add_edge("async_keywords", "async_merge")
    workflow.add_edge("async_summary", "async_merge")
    workflow.add_edge("async_merge", END)
    
    # 编译图
    app = workflow.compile()
    
    # 测试数据
    test_input = "机器学习算法在图像识别领域取得了突破性进展，准确率已经超过了人类水平。"
    
    print(f"测试输入: {test_input}")
    print("=" * 60)
    
    # 运行异步图
    async def run_async_workflow():
        result = await app.ainvoke({
            "user_input": test_input,
            "messages": [],
            "parallel_results": {},
            "final_result": "",
            "execution_path": []
        })
        
        print(f"\n执行路径: {' -> '.join(result['execution_path'])}")
        print(f"\n异步并行处理结果:")
        for key, value in result['parallel_results'].items():
            print(f"  {key}: {value}")
        print(f"\n最终综合报告:\n{result['final_result']}")
    
    # 运行异步工作流
    asyncio.run(run_async_workflow())

def conditional_parallel_example():
    """条件并行处理示例"""
    
    print("\n=== 条件并行处理示例 ===")
    print("根据条件决定是否执行并行处理")
    print()
    
    def analyze_complexity_node(state: ParallelState) -> ParallelState:
        """分析复杂度节点"""
        user_input = state["user_input"]
        
        print(f"步骤1: 分析文本复杂度...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
请分析以下文本的复杂度：

文本：{user_input}

请返回JSON格式的复杂度分析：
{{
    "complexity": "simple|medium|complex",
    "word_count": 单词数量,
    "needs_parallel": true/false,
    "reason": "分析理由"
}}

请只返回JSON：
""")
        ])
        
        try:
            result = json.loads(response.content)
        except:
            result = {"complexity": "medium", "word_count": len(user_input.split()), "needs_parallel": True, "reason": "分析失败"}
        
        return {
            **state,
            "parallel_results": {**state.get("parallel_results", {}), "complexity": result},
            "execution_path": state["execution_path"] + ["复杂度分析完成"]
        }
    
    def simple_analysis_node(state: ParallelState) -> ParallelState:
        """简单分析节点（非并行）"""
        user_input = state["user_input"]
        
        print(f"步骤2: 执行简单分析...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
请对以下文本进行简单分析：

文本：{user_input}

请提供简单的分析结果：
""")
        ])
        
        return {
            **state,
            "final_result": response.content,
            "execution_path": state["execution_path"] + ["简单分析完成"],
            "messages": [AIMessage(content=f"简单分析完成")]
        }
    
    def parallel_analysis_node(state: ParallelState) -> ParallelState:
        """并行分析节点"""
        user_input = state["user_input"]
        
        print(f"步骤2: 执行并行分析...")
        
        # 这里可以调用之前的并行处理逻辑
        # 为了简化，直接生成一个并行分析结果
        response = llm.invoke([
            HumanMessage(content=f"""
请对以下文本进行深度并行分析：

文本：{user_input}

请提供深度的并行分析结果：
""")
        ])
        
        return {
            **state,
            "final_result": response.content,
            "execution_path": state["execution_path"] + ["并行分析完成"],
            "messages": [AIMessage(content=f"并行分析完成")]
        }
    
    def route_by_complexity(state: ParallelState) -> str:
        """根据复杂度路由"""
        complexity_result = state["parallel_results"].get("complexity", {})
        needs_parallel = complexity_result.get("needs_parallel", False)
        
        if needs_parallel:
            return "parallel"
        else:
            return "simple"
    
    # 创建图
    workflow = StateGraph(ParallelState)
    
    # 添加节点
    workflow.add_node("analyze_complexity", analyze_complexity_node)
    workflow.add_node("simple_analysis", simple_analysis_node)
    workflow.add_node("parallel_analysis", parallel_analysis_node)
    
    # 设置入口点和边
    workflow.set_entry_point("analyze_complexity")
    workflow.add_conditional_edges("analyze_complexity", route_by_complexity, {
        "simple": "simple_analysis",
        "parallel": "parallel_analysis"
    })
    workflow.add_edge("simple_analysis", END)
    workflow.add_edge("parallel_analysis", END)
    
    # 编译图
    app = workflow.compile()
    
    # 测试不同的输入
    test_inputs = [
        "你好",  # 简单文本
        "人工智能技术正在快速发展，为我们的生活带来了很多便利，但也带来了一些担忧。机器学习算法在图像识别领域取得了突破性进展，准确率已经超过了人类水平。"  # 复杂文本
    ]
    
    for i, test_input in enumerate(test_inputs):
        print(f"\n测试输入 {i+1}: {test_input}")
        print("=" * 60)
        
        result = app.invoke({
            "user_input": test_input,
            "messages": [],
            "parallel_results": {},
            "final_result": "",
            "execution_path": []
        })
        
        print(f"\n执行路径: {' -> '.join(result['execution_path'])}")
        if "complexity" in result['parallel_results']:
            complexity = result['parallel_results']['complexity']
            print(f"复杂度分析: {complexity}")
        print(f"\n最终结果:\n{result['final_result']}")

def main():
    """主函数"""
    parallel_nodes_example()
    async_parallel_example()
    conditional_parallel_example()

if __name__ == "__main__":
    main() 