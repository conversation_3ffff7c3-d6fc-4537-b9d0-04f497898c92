"""
LangChain LCEL集成测试
验证新实现的功能完整性和向后兼容性
"""
import pytest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock
from typing import List, Dict, Any

# 测试导入
try:
    from src.rag_action.service.langchain_rag_service import (
        LangChainRAGService,
        LangChainRAGCompatibilityAdapter,
        StreamingCallbackHandler,
        get_enhanced_rag_service
    )
    from src.rag_action.service.rag_service_adapter import RAGService
    from src.rag_action.models.schemas import QAResponse, RetrievedDocument
    
    LANGCHAIN_AVAILABLE = True
except ImportError as e:
    LANGCHAIN_AVAILABLE = False
    pytest.skip(f"LangChain组件不可用: {e}", allow_module_level=True)


class TestLangChainRAGService:
    """LangChain RAG服务测试"""
    
    @pytest.fixture
    def mock_settings(self):
        """模拟配置"""
        with patch('src.rag_action.service.langchain_rag_service.settings') as mock:
            mock.llm.api_key = "test-key"
            mock.llm.base_url = "https://api.test.com/v1"
            mock.llm.model_name = "gpt-4o-mini"
            mock.llm.temperature = 0.7
            mock.llm.max_tokens = 2000
            mock.rag.max_context_length = 4000
            mock.rag.prompt_template = "基于以下文档：{context}\n\n问题：{question}\n\n回答："
            yield mock
    
    @pytest.fixture
    def mock_services(self):
        """模拟依赖服务"""
        with patch.multiple(
            'src.rag_action.service.langchain_rag_service',
            get_embedding_service=Mock(),
            get_ensemble_retriever=Mock(),
            get_query_processor=Mock(),
            get_retrieval_post_processor=Mock(),
            get_intelligent_query_router=Mock(),
            get_database_query_service=Mock(),
            get_text_to_sql_agent=Mock()
        ) as mocks:
            yield mocks
    
    @pytest.mark.asyncio
    async def test_service_initialization(self, mock_settings, mock_services):
        """测试服务初始化"""
        service = LangChainRAGService()
        
        # 验证基本属性
        assert service.embedding_service is not None
        assert service.vector_service is not None
        assert service.ensemble_retriever is not None
        
        # 验证LangChain组件初始化
        if hasattr(service, 'llm') and service.llm:
            assert service.rag_chain is not None
            assert service.streaming_chain is not None
    
    @pytest.mark.asyncio
    async def test_async_retrieve_documents(self, mock_settings, mock_services):
        """测试异步文档检索"""
        service = LangChainRAGService()
        
        # 模拟检索结果
        mock_documents = [
            {
                "content": "测试文档内容1",
                "note_id": 1,
                "chunk_id": 1,
                "page_number": 1,
                "similarity_score": 0.9
            },
            {
                "content": "测试文档内容2", 
                "note_id": 2,
                "chunk_id": 2,
                "page_number": 1,
                "similarity_score": 0.8
            }
        ]
        
        # 模拟查询处理器
        service.query_processor.process_query.return_value = {
            "refined_query": "处理后的查询",
            "metadata_filters": {}
        }
        
        # 模拟检索器
        service.ensemble_retriever.ensemble_search_unified = AsyncMock(return_value=mock_documents)
        
        # 模拟后处理器
        service.post_processor.process.return_value = {
            "documents": mock_documents,
            "context": "处理后的上下文"
        }
        
        # 执行测试
        inputs = {"question": "测试问题", "top_k": 5}
        result = await service._async_retrieve_documents(inputs)
        
        # 验证结果
        assert len(result) == 2
        assert result[0]["content"] == "测试文档内容1"
        assert result[1]["note_id"] == 2
    
    def test_format_context(self, mock_settings, mock_services):
        """测试上下文格式化"""
        service = LangChainRAGService()
        
        documents = [
            {
                "content": "文档1内容",
                "note_id": 1,
                "page_number": 1
            },
            {
                "content": "文档2内容",
                "note_id": 2,
                "page_number": 2
            }
        ]
        
        context = service._format_context(documents)
        
        # 验证格式化结果
        assert "文档1" in context
        assert "文档2" in context
        assert "文档1内容" in context
        assert "文档2内容" in context
        assert "笔记ID: 1" in context
        assert "页码: 1" in context
    
    def test_format_context_empty(self, mock_settings, mock_services):
        """测试空文档列表的上下文格式化"""
        service = LangChainRAGService()
        
        context = service._format_context([])
        assert context == "未找到相关文档内容。"
    
    @pytest.mark.asyncio
    async def test_build_retrieved_documents(self, mock_settings, mock_services):
        """测试构建检索文档对象"""
        service = LangChainRAGService()
        
        # 模拟数据库查询
        with patch('src.rag_action.service.langchain_rag_service.get_db') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value.__next__.return_value = mock_db
            
            # 模拟笔记查询结果
            mock_note = Mock()
            mock_note.id = 1
            mock_note.title = "测试笔记标题"
            mock_db.query.return_value.filter.return_value.all.return_value = [mock_note]
            
            documents = [
                {
                    "content": "测试内容",
                    "note_id": 1,
                    "chunk_id": 1,
                    "page_number": 1,
                    "similarity_score": 0.9
                }
            ]
            
            result = await service._build_retrieved_documents(documents)
            
            # 验证结果
            assert len(result) == 1
            assert isinstance(result[0], RetrievedDocument)
            assert result[0].content == "测试内容"
            assert result[0].note_id == 1
            assert result[0].note_title == "测试笔记标题"


class TestStreamingCallbackHandler:
    """流式回调处理器测试"""
    
    @pytest.mark.asyncio
    async def test_callback_initialization(self):
        """测试回调处理器初始化"""
        queue = asyncio.Queue()
        handler = StreamingCallbackHandler(queue)
        
        assert handler.queue == queue
        assert handler.full_answer == ""
        assert handler.token_count == 0
    
    @pytest.mark.asyncio
    async def test_on_llm_start(self):
        """测试LLM开始回调"""
        queue = asyncio.Queue()
        handler = StreamingCallbackHandler(queue)
        
        await handler.on_llm_start({}, ["test prompt"])
        
        # 检查队列中的消息
        message = await queue.get()
        assert message["type"] == "generation_start"
        assert message["content"] == "正在生成回答..."
        assert message["finished"] == False
    
    @pytest.mark.asyncio
    async def test_on_llm_new_token(self):
        """测试新token回调"""
        queue = asyncio.Queue()
        handler = StreamingCallbackHandler(queue)
        
        await handler.on_llm_new_token("测试")
        await handler.on_llm_new_token("token")
        
        # 检查第一个token
        message1 = await queue.get()
        assert message1["type"] == "answer_chunk"
        assert message1["content"] == "测试"
        assert message1["finished"] == False
        
        # 检查第二个token
        message2 = await queue.get()
        assert message2["type"] == "answer_chunk"
        assert message2["content"] == "token"
        
        # 检查累积状态
        assert handler.full_answer == "测试token"
        assert handler.token_count == 2
    
    @pytest.mark.asyncio
    async def test_on_llm_end(self):
        """测试LLM结束回调"""
        queue = asyncio.Queue()
        handler = StreamingCallbackHandler(queue)
        
        # 先添加一些token
        await handler.on_llm_new_token("完整")
        await handler.on_llm_new_token("答案")
        
        # 清空队列
        while not queue.empty():
            await queue.get()
        
        # 测试结束回调
        await handler.on_llm_end(None)
        
        message = await queue.get()
        assert message["type"] == "generation_complete"
        assert message["content"] == "完整答案"
        assert message["token_count"] == 2
        assert message["finished"] == False


class TestRAGServiceAdapter:
    """RAG服务适配器测试"""
    
    @pytest.mark.asyncio
    async def test_adapter_initialization(self):
        """测试适配器初始化"""
        with patch('src.rag_action.service.rag_service_adapter.LangChainRAGCompatibilityAdapter'):
            adapter = RAGService()
            assert adapter is not None
    
    def test_get_service_info(self):
        """测试获取服务信息"""
        with patch('src.rag_action.service.rag_service_adapter.LangChainRAGCompatibilityAdapter'):
            adapter = RAGService()
            
            # 模拟get_service_info方法
            adapter.get_service_info = Mock(return_value={
                "service_type": "enhanced",
                "langchain_available": True,
                "version": "2.0.0",
                "features": {
                    "lcel_pipeline": True,
                    "native_streaming": True,
                    "auto_fallback": True,
                    "compatibility_mode": True
                }
            })
            
            info = adapter.get_service_info()
            
            assert info["service_type"] == "enhanced"
            assert info["langchain_available"] == True
            assert info["features"]["lcel_pipeline"] == True


class TestIntegration:
    """集成测试"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_compatibility(self):
        """端到端兼容性测试"""
        # 这个测试验证新实现与原有API的完全兼容性
        
        # 模拟原有调用方式
        with patch('src.rag_action.service.rag_service_adapter.LangChainRAGCompatibilityAdapter') as mock_adapter:
            # 模拟适配器实例
            mock_instance = Mock()
            mock_adapter.return_value = mock_instance
            
            # 模拟answer_question方法
            expected_response = QAResponse(
                question="测试问题",
                answer="测试答案",
                sources=[],
                processing_time=1.0,
                metadata={"implementation": "langchain_lcel"}
            )
            mock_instance.answer_question = AsyncMock(return_value=expected_response)
            
            # 创建服务实例
            service = RAGService()
            
            # 调用方法（与原有方式完全相同）
            response = await service.answer_question("测试问题")
            
            # 验证响应格式
            assert isinstance(response, QAResponse)
            assert response.question == "测试问题"
            assert response.answer == "测试答案"
            assert "implementation" in response.metadata
    
    @pytest.mark.asyncio
    async def test_streaming_compatibility(self):
        """流式输出兼容性测试"""
        with patch('src.rag_action.service.rag_service_adapter.LangChainRAGCompatibilityAdapter') as mock_adapter:
            # 模拟流式输出
            async def mock_stream():
                yield {"type": "retrieval_start", "content": "开始检索"}
                yield {"type": "answer_chunk", "content": "测试"}
                yield {"type": "answer_chunk", "content": "回答"}
                yield {"type": "answer_complete", "content": "测试回答", "finished": True}
            
            mock_instance = Mock()
            mock_adapter.return_value = mock_instance
            mock_instance.answer_question_stream = mock_stream
            
            service = RAGService()
            
            # 收集流式输出
            chunks = []
            async for chunk in service.answer_question_stream("测试问题"):
                chunks.append(chunk)
            
            # 验证流式输出格式
            assert len(chunks) == 4
            assert chunks[0]["type"] == "retrieval_start"
            assert chunks[1]["type"] == "answer_chunk"
            assert chunks[1]["content"] == "测试"
            assert chunks[-1]["finished"] == True


class TestLangChainRetrieverIntegration:
    """LangChain标准化检索器集成测试（2.2模块）"""

    @pytest.fixture
    def mock_langchain_retrievers(self):
        """模拟LangChain检索器组件"""
        with patch.multiple(
            'src.rag_action.service.langchain_rag_service',
            LANGCHAIN_RETRIEVER_AVAILABLE=True,
            EnsembleRetriever=Mock(),
            Milvus=Mock(),
            BM25Retriever=Mock()
        ) as mocks:
            yield mocks

    @pytest.mark.asyncio
    async def test_langchain_retriever_initialization(self, mock_settings, mock_services, mock_langchain_retrievers):
        """测试LangChain检索器初始化"""
        # 模拟Milvus检索器
        mock_milvus_instance = Mock()
        mock_langchain_retrievers['Milvus'].return_value.as_retriever.return_value = mock_milvus_instance

        # 模拟BM25检索器
        mock_bm25_instance = Mock()
        mock_langchain_retrievers['BM25Retriever'].from_texts.return_value = mock_bm25_instance

        # 模拟混合检索器
        mock_ensemble_instance = Mock()
        mock_langchain_retrievers['EnsembleRetriever'].return_value = mock_ensemble_instance

        service = LangChainRAGService()

        # 验证检索器初始化
        assert service.use_langchain_retrievers == True
        assert service.langchain_ensemble_retriever is not None

    @pytest.mark.asyncio
    async def test_langchain_retrieve_documents(self, mock_settings, mock_services, mock_langchain_retrievers):
        """测试LangChain标准化文档检索"""
        service = LangChainRAGService()

        # 模拟LangChain Document对象
        from langchain_core.documents import Document
        mock_docs = [
            Document(
                page_content="测试文档内容1",
                metadata={
                    "note_id": 1,
                    "chunk_id": 1,
                    "page_number": 1,
                    "score": 0.9,
                    "source": "milvus"
                }
            ),
            Document(
                page_content="测试文档内容2",
                metadata={
                    "note_id": 2,
                    "chunk_id": 2,
                    "page_number": 1,
                    "score": 0.8,
                    "source": "bm25"
                }
            )
        ]

        # 模拟检索器返回结果
        service.langchain_ensemble_retriever = AsyncMock()
        service.langchain_ensemble_retriever.ainvoke.return_value = mock_docs
        service.use_langchain_retrievers = True

        # 执行检索
        result = await service._langchain_retrieve_documents("测试查询", 5)

        # 验证结果
        assert len(result) == 2
        assert result[0]["content"] == "测试文档内容1"
        assert result[0]["note_id"] == 1
        assert result[0]["langchain_retriever"] == True
        assert result[1]["source"] == "bm25"

    def test_convert_langchain_documents(self, mock_settings, mock_services):
        """测试LangChain文档格式转换"""
        service = LangChainRAGService()

        # 创建模拟的LangChain Document
        from langchain_core.documents import Document
        langchain_docs = [
            Document(
                page_content="转换测试内容",
                metadata={
                    "note_id": 123,
                    "chunk_id": 456,
                    "page_number": 2,
                    "score": 0.95,
                    "source": "test_source"
                }
            )
        ]

        # 执行转换
        converted = service._convert_langchain_documents(langchain_docs)

        # 验证转换结果
        assert len(converted) == 1
        doc = converted[0]
        assert doc["content"] == "转换测试内容"
        assert doc["note_id"] == 123
        assert doc["chunk_id"] == 456
        assert doc["page_number"] == 2
        assert doc["similarity_score"] == 0.95
        assert doc["source"] == "test_source"
        assert doc["retriever_type"] == "langchain_standard"
        assert "original_metadata" in doc

    def test_get_retriever_info(self, mock_settings, mock_services):
        """测试获取检索器状态信息"""
        service = LangChainRAGService()

        # 模拟检索器状态
        service.use_langchain_retrievers = True
        service.langchain_milvus_retriever = Mock()
        service.langchain_bm25_retriever = Mock()
        service.langchain_ensemble_retriever = Mock()

        info = service.get_retriever_info()

        # 验证状态信息
        assert info["use_langchain_retrievers"] == True
        assert info["retrievers"]["milvus"] == True
        assert info["retrievers"]["bm25"] == True
        assert info["retrievers"]["ensemble"] == True
        assert info["fallback_available"] == True
        assert "retriever_weights" in info

    @pytest.mark.asyncio
    async def test_retriever_fallback_mechanism(self, mock_settings, mock_services):
        """测试检索器降级机制"""
        service = LangChainRAGService()

        # 模拟LangChain检索器失败
        service.use_langchain_retrievers = True
        service.langchain_ensemble_retriever = AsyncMock()
        service.langchain_ensemble_retriever.ainvoke.side_effect = Exception("LangChain检索失败")

        # 模拟原有检索器成功
        service.ensemble_retriever.ensemble_search_unified = AsyncMock(return_value=[
            {"content": "降级检索结果", "note_id": 1, "similarity_score": 0.7}
        ])

        # 执行检索（应该自动降级）
        inputs = {"question": "测试问题", "top_k": 5}
        result = await service._async_retrieve_documents(inputs)

        # 验证降级成功
        assert len(result) == 1
        assert result[0]["content"] == "降级检索结果"


class TestLangChainMemoryIntegration:
    """LangChain Memory组件集成测试（2.3模块）"""

    @pytest.fixture
    def mock_langchain_memory(self):
        """模拟LangChain Memory组件"""
        with patch.multiple(
            'src.rag_action.service.langchain_rag_service',
            LANGCHAIN_MEMORY_AVAILABLE=True,
            ConversationSummaryBufferMemory=Mock(),
            VectorStoreRetrieverMemory=Mock(),
            ConversationBufferWindowMemory=Mock()
        ) as mocks:
            yield mocks

    @pytest.mark.asyncio
    async def test_memory_initialization(self, mock_settings, mock_services, mock_langchain_memory):
        """测试Memory组件初始化"""
        # 模拟Memory实例
        mock_summary_memory = Mock()
        mock_langchain_memory['ConversationSummaryBufferMemory'].return_value = mock_summary_memory

        mock_vector_memory = Mock()
        mock_langchain_memory['VectorStoreRetrieverMemory'].return_value = mock_vector_memory

        mock_conversation_memory = Mock()
        mock_langchain_memory['ConversationBufferWindowMemory'].return_value = mock_conversation_memory

        service = LangChainRAGService()

        # 验证Memory组件初始化
        assert service.use_langchain_memory == True
        assert service.summary_memory is not None
        assert service.conversation_memory is not None

    def test_conversation_context_enhancement(self, mock_settings, mock_services):
        """测试对话上下文增强"""
        service = LangChainRAGService()

        # 模拟Memory组件
        service.use_langchain_memory = True
        service.summary_memory = Mock()
        service.vector_memory = Mock()
        service.conversation_memory = Mock()

        # 模拟对话上下文
        conversation_context = [
            {"role": "user", "content": "什么是人工智能？"},
            {"role": "assistant", "content": "人工智能是计算机科学的一个分支..."},
            {"role": "user", "content": "它有哪些应用？"}
        ]

        # 执行上下文转换
        messages = service._convert_conversation_context(conversation_context)

        # 验证基础转换
        assert len(messages) >= 3  # 至少包含原始消息

        # 验证消息类型
        from langchain_core.messages import HumanMessage, AIMessage
        assert isinstance(messages[-1], HumanMessage)  # 最后一条是用户消息

    def test_summary_memory_context(self, mock_settings, mock_services):
        """测试摘要记忆上下文获取"""
        service = LangChainRAGService()

        # 模拟摘要记忆
        mock_summary_memory = Mock()
        mock_summary_memory.load_memory_variables.return_value = {
            "chat_history": "用户询问了关于AI的基础知识，助手提供了详细解释。"
        }
        service.summary_memory = mock_summary_memory

        # 测试获取摘要上下文
        conversation_context = [
            {"role": "user", "content": "什么是机器学习？"},
            {"role": "assistant", "content": "机器学习是AI的一个子领域..."},
            {"role": "user", "content": "深度学习呢？"},
            {"role": "assistant", "content": "深度学习是机器学习的一种方法..."},
            {"role": "user", "content": "它们有什么区别？"}
        ]

        summary = service._get_summary_memory_context(conversation_context)

        # 验证摘要获取
        assert summary == "用户询问了关于AI的基础知识，助手提供了详细解释。"
        assert mock_summary_memory.save_context.called

    def test_vector_memory_context(self, mock_settings, mock_services):
        """测试向量记忆上下文获取"""
        service = LangChainRAGService()

        # 模拟向量记忆
        from langchain_core.documents import Document
        mock_vector_memory = Mock()
        mock_vector_memory.load_memory_variables.return_value = {
            "relevant_history": [
                Document(page_content="之前讨论过AI的基本概念"),
                Document(page_content="用户对机器学习很感兴趣")
            ]
        }
        service.vector_memory = mock_vector_memory

        # 测试获取向量记忆上下文
        query = "深度学习的应用"
        context = service._get_vector_memory_context(query)

        # 验证上下文获取
        assert "之前讨论过AI的基本概念" in context
        assert "用户对机器学习很感兴趣" in context
        assert mock_vector_memory.load_memory_variables.called

    def test_conversation_buffer_update(self, mock_settings, mock_services):
        """测试对话缓冲记忆更新"""
        service = LangChainRAGService()

        # 模拟对话缓冲记忆
        mock_conversation_memory = Mock()
        service.conversation_memory = mock_conversation_memory

        # 测试更新对话缓冲
        conversation_context = [
            {"role": "user", "content": "测试问题1"},
            {"role": "assistant", "content": "测试回答1"},
            {"role": "user", "content": "测试问题2"},
            {"role": "assistant", "content": "测试回答2"}
        ]

        service._update_conversation_buffer(conversation_context)

        # 验证缓冲记忆更新
        assert mock_conversation_memory.save_context.called
        call_args = mock_conversation_memory.save_context.call_args_list
        assert len(call_args) >= 1  # 至少调用一次

    def test_get_memory_info(self, mock_settings, mock_services):
        """测试获取Memory状态信息"""
        service = LangChainRAGService()

        # 模拟Memory组件状态
        service.use_langchain_memory = True
        service.summary_memory = Mock()
        service.vector_memory = Mock()
        service.conversation_memory = Mock()

        info = service.get_memory_info()

        # 验证状态信息
        assert info["use_langchain_memory"] == True
        assert info["memory_components"]["summary_memory"] == True
        assert info["memory_components"]["vector_memory"] == True
        assert info["memory_components"]["conversation_memory"] == True
        assert "memory_config" in info
        assert "max_tokens" in info["memory_config"]
        assert "window_size" in info["memory_config"]

    def test_memory_fallback_mechanism(self, mock_settings, mock_services):
        """测试Memory组件降级机制"""
        service = LangChainRAGService()

        # 模拟Memory组件失败
        service.use_langchain_memory = True
        service.summary_memory = Mock()
        service.summary_memory.load_memory_variables.side_effect = Exception("Memory失败")

        # 测试降级处理
        conversation_context = [
            {"role": "user", "content": "测试问题"},
            {"role": "assistant", "content": "测试回答"}
        ]

        # 应该不会抛出异常，而是优雅降级
        messages = service._convert_conversation_context(conversation_context)

        # 验证基础功能仍然可用
        assert len(messages) == 2
        from langchain_core.messages import HumanMessage, AIMessage
        assert isinstance(messages[0], HumanMessage)
        assert isinstance(messages[1], AIMessage)


class TestLangChainAgentIntegration:
    """LangChain Agent组件集成测试（2.4模块）"""

    @pytest.fixture
    def mock_langchain_agent(self):
        """模拟LangChain Agent组件"""
        with patch.multiple(
            'src.rag_action.service.langchain_rag_service',
            LANGCHAIN_AGENT_AVAILABLE=True,
            create_react_agent=Mock(),
            AgentExecutor=Mock(),
            Tool=Mock()
        ) as mocks:
            yield mocks

    @pytest.mark.asyncio
    async def test_agent_initialization(self, mock_settings, mock_services, mock_langchain_agent):
        """测试Agent组件初始化"""
        # 模拟Agent执行器
        mock_agent_executor = Mock()
        mock_langchain_agent['AgentExecutor'].return_value = mock_agent_executor

        # 模拟工具创建
        mock_tool = Mock()
        mock_tool.name = "test_tool"
        mock_langchain_agent['Tool'].return_value = mock_tool

        service = LangChainRAGService()

        # 验证Agent组件初始化
        assert service.use_langchain_agent == True
        assert service.agent_executor is not None
        assert len(service.agent_tools) > 0

    @pytest.mark.asyncio
    async def test_agent_query_handling(self, mock_settings, mock_services, mock_langchain_agent):
        """测试Agent查询处理"""
        service = LangChainRAGService()

        # 模拟Agent执行器
        mock_agent_executor = AsyncMock()
        mock_agent_executor.ainvoke.return_value = {
            "output": "这是Agent生成的回答",
            "intermediate_steps": [
                (Mock(tool="knowledge_search", tool_input="测试查询"), "检索结果"),
                (Mock(tool="simple_conversation", tool_input="处理对话"), "对话结果")
            ]
        }
        service.agent_executor = mock_agent_executor
        service.use_langchain_agent = True

        # 执行Agent查询
        response = await service._handle_agent_query(
            "测试问题", 5, None, time.time(), None
        )

        # 验证响应
        assert response.answer == "这是Agent生成的回答"
        assert response.metadata["processing_method"] == "langchain_agent"
        assert response.metadata["agent_steps"] == 2
        assert "knowledge_search" in response.metadata["tools_used"]
        assert "simple_conversation" in response.metadata["tools_used"]

    def test_agent_tools_creation(self, mock_settings, mock_services):
        """测试Agent工具创建"""
        service = LangChainRAGService()

        # 模拟工具创建
        with patch('src.rag_action.service.langchain_rag_service.Tool') as mock_tool:
            mock_tool_instance = Mock()
            mock_tool_instance.name = "test_tool"
            mock_tool.return_value = mock_tool_instance

            service._create_agent_tools()

            # 验证工具创建
            assert len(service.agent_tools) > 0
            assert mock_tool.called

    def test_knowledge_search_tool(self, mock_settings, mock_services):
        """测试知识库检索工具"""
        service = LangChainRAGService()

        # 模拟检索结果
        service._sync_retrieve_documents = Mock(return_value=[
            {"content": "测试文档内容", "note_id": 1, "similarity_score": 0.9}
        ])
        service._format_context = Mock(return_value="格式化的上下文")

        # 执行工具
        result = service._knowledge_search_tool("测试查询")

        # 验证结果
        assert "基于知识库的信息" in result
        assert "格式化的上下文" in result

    def test_conversation_tool(self, mock_settings, mock_services):
        """测试对话工具"""
        service = LangChainRAGService()

        # 测试不同类型的对话
        test_cases = [
            ("你好", "您好！我是AI助手"),
            ("谢谢", "不客气！"),
            ("好的", "很好！"),
            ("再见", "再见！"),
            ("其他内容", "我理解您的意思")
        ]

        for input_text, expected_keyword in test_cases:
            result = service._conversation_tool(input_text)
            assert expected_keyword in result

    def test_extract_tools_used(self, mock_settings, mock_services):
        """测试工具使用提取"""
        service = LangChainRAGService()

        # 模拟中间步骤
        intermediate_steps = [
            (Mock(tool="knowledge_search"), "结果1"),
            (Mock(tool="database_query"), "结果2"),
            (Mock(tool="knowledge_search"), "结果3")  # 重复工具
        ]

        tools_used = service._extract_tools_used(intermediate_steps)

        # 验证提取结果
        assert len(tools_used) == 2  # 去重后应该只有2个
        assert "knowledge_search" in tools_used
        assert "database_query" in tools_used

    def test_format_reasoning_process(self, mock_settings, mock_services):
        """测试推理过程格式化"""
        service = LangChainRAGService()

        # 模拟中间步骤
        intermediate_steps = [
            (Mock(tool="knowledge_search", tool_input="查询1"), "观察1"),
            (Mock(tool="database_query", tool_input="查询2"), "观察2")
        ]

        reasoning = service._format_reasoning_process(intermediate_steps)

        # 验证格式化结果
        assert "步骤1: 使用knowledge_search工具" in reasoning
        assert "步骤2: 使用database_query工具" in reasoning
        assert "输入: 查询1" in reasoning
        assert "结果: 观察1" in reasoning

    def test_get_agent_info(self, mock_settings, mock_services):
        """测试获取Agent状态信息"""
        service = LangChainRAGService()

        # 模拟Agent状态
        service.use_langchain_agent = True
        service.agent_executor = Mock()
        service.agent_tools = [Mock(name="tool1"), Mock(name="tool2")]

        info = service.get_agent_info()

        # 验证状态信息
        assert info["use_langchain_agent"] == True
        assert info["agent_components"]["agent_executor"] == True
        assert info["agent_components"]["tools_count"] == 2
        assert "tool1" in info["agent_components"]["tools_list"]
        assert "tool2" in info["agent_components"]["tools_list"]
        assert "agent_config" in info

    @pytest.mark.asyncio
    async def test_agent_fallback_mechanism(self, mock_settings, mock_services):
        """测试Agent降级机制"""
        service = LangChainRAGService()

        # 模拟Agent失败
        service.use_langchain_agent = True
        service.agent_executor = AsyncMock()
        service.agent_executor.ainvoke.side_effect = Exception("Agent执行失败")

        # 模拟原有路由成功
        service.query_router = Mock()
        service.query_router.route_query.return_value = {
            "intent": "knowledge_search",
            "confidence": 0.8
        }
        service._handle_knowledge_search_lcel = AsyncMock(return_value=QAResponse(
            question="测试问题",
            answer="降级处理结果",
            sources=[],
            processing_time=1.0,
            metadata={"method": "fallback"}
        ))

        # 执行查询（应该自动降级）
        response = await service.answer_question("测试问题")

        # 验证降级成功
        assert response.answer == "降级处理结果"
        assert response.metadata["method"] == "fallback"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
