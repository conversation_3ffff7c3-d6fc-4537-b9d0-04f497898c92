// 基础类型定义
export interface ApiResponse<T = any> {
  code?: number
  message?: string
  data?: T
  success?: boolean
}

// 笔记相关类型
export interface Note {
  id: number
  title: string
  content: string
  source_type: 'pdf' | 'manual' | 'markdown'
  source_file?: string
  file_size?: number
  total_pages?: number
  created_at: string
  updated_at: string
  tags: Tag[]
}

export interface NoteCreate {
  title: string
  content: string
  tags?: string[]
}

export interface NotesListResponse {
  notes: Note[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 标签类型
export interface Tag {
  id: number
  name: string
  color?: string
  created_at: string
}

// 上传相关类型
export interface UploadFile {
  id: string
  name: string
  size: number
  status: 'waiting' | 'uploading' | 'success' | 'error'
  progress: number
  error?: string
  result?: any
}

export interface UploadProgress {
  progress: number
  stage: string
  message?: string
}

export interface TaskStatus {
  task_id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: UploadProgress
  result?: any
  error?: string
}

// 问答相关类型
export interface QueryRequest {
  query: string
  top_k?: number
  note_ids?: number[]
}

export interface QueryResponse {
  answer: string
  sources: QuerySource[]
  query_time: number
  total_tokens?: number
  conversation_id?: string
  message_id?: string
  question?: string
  processing_time?: number
  metadata?: {
    conversation_id?: string
    user_message_id?: string
    assistant_message_id?: string
    intent?: string
    token_count?: number
    routing_confidence?: number
    sources_count?: number
    [key: string]: any
  }
}

// 流式问答相关类型
export interface StreamingQueryRequest {
  message: string
  conversation_id?: string
  top_k?: number
  note_ids?: number[]
  stream?: boolean
}

export interface StreamingChunk {
  type: 'retrieval_start' | 'retrieval_complete' | 'generation_start' | 'answer_chunk' | 'answer_complete' | 'database_query_start' | 'database_query_complete' | 'session_complete' | 'error'
  content: string
  finished: boolean
  sources_count?: number
  processing_time?: number
  sources?: QuerySource[]
  conversation_id?: string
  message_id?: string
  user_message_id?: string
  metadata?: {
    intent?: string
    routing_confidence?: number
    token_count?: number
    sql?: string
    row_count?: number
    execution_time?: number
    retry_count?: number
    [key: string]: any
  }
}

export interface StreamingCallbacks {
  onProgress?: (stage: string, message: string) => void
  onChunk?: (chunk: string) => void
  onComplete?: (response: QueryResponse) => void
  onError?: (error: any) => void
}

export interface QuerySource {
  note_id: number
  note_title: string
  content: string
  page_number?: number
  score: number
}

// 对话相关类型
export interface ConversationMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
  metadata?: {
    sources?: QuerySource[]
    processing_time?: number
    query_type?: string
    [key: string]: any
  }
}

export interface Conversation {
  conversation_id: string
  messages: ConversationMessage[]
  created_at: string
  updated_at: string
  metadata?: {
    [key: string]: any
  }
}

export interface ConversationSummary {
  conversation_id: string
  title: string
  last_message: string
  message_count: number
  created_at: string
  updated_at: string
}

export interface QueryHistory {
  id: string
  query: string
  answer: string
  sources: QuerySource[]
  timestamp: string
  query_time: number
}

// 系统监控类型
export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: number
  checks: Record<string, HealthCheck>
}

export interface HealthCheck {
  status: 'healthy' | 'unhealthy' | 'error'
  details?: any
  error?: string
}

export interface SystemStats {
  mysql: {
    notes_count: number
    tags_count: number
    chunks_count: number
  }
  milvus: {
    total_vectors: number
    collection_status: string
  }
  system: {
    app_title: string
    app_version: string
    embedding_model: string
    llm_model: string
  }
}

// 用户设置类型
export interface UserSettings {
  theme: 'light' | 'dark' | 'auto'
  language: 'zh-CN' | 'en-US'
  pageSize: number
  autoSave: boolean
  notifications: boolean
}

// 路由元信息类型
export interface RouteMeta {
  title?: string
  icon?: string
  requiresAuth?: boolean
  keepAlive?: boolean
  hidden?: boolean
}

// 菜单项类型
export interface MenuItem {
  path: string
  name: string
  title: string
  icon?: string
  children?: MenuItem[]
  meta?: RouteMeta
}

// 表格分页类型
export interface Pagination {
  page: number
  pageSize: number
  total: number
}

// 搜索筛选类型
export interface SearchFilters {
  keyword?: string
  tags?: string[]
  source_type?: string
  date_range?: [string, string]
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: 'upload_progress' | 'system_notification' | 'task_update'
  data: any
  timestamp: number
}

// 通知类型
export interface Notification {
  id: string
  type: 'success' | 'warning' | 'error' | 'info'
  title: string
  message: string
  duration?: number
  timestamp: number
}
