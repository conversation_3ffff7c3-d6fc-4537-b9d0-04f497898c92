#!/usr/bin/env python3
"""
LangChain Memory对话管理测试脚本（2.3模块）
验证LangChain Memory组件的功能和智能对话管理能力

运行方式：
python tests/test_langchain_memory_module.py
"""
import asyncio
import time
import logging
from typing import List, Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_memory_basic_functionality():
    """Memory基础功能测试"""
    print("🧠 LangChain Memory基础功能测试")
    print("=" * 60)
    
    try:
        # 导入服务
        from src.rag_action.service.langchain_rag_service import LangChainRAGService
        
        # 创建服务实例
        service = LangChainRAGService()
        
        # 获取Memory状态信息
        memory_info = service.get_memory_info()
        print(f"📊 Memory组件状态:")
        print(f"  - LangChain Memory可用: {memory_info['langchain_memory_available']}")
        print(f"  - 使用LangChain Memory: {memory_info['use_langchain_memory']}")
        print(f"  - 摘要记忆: {'✅' if memory_info['memory_components']['summary_memory'] else '❌'}")
        print(f"  - 向量记忆: {'✅' if memory_info['memory_components']['vector_memory'] else '❌'}")
        print(f"  - 对话缓冲: {'✅' if memory_info['memory_components']['conversation_memory'] else '❌'}")
        
        # 显示Memory配置
        config = memory_info['memory_config']
        print(f"  - 最大Token数: {config['max_tokens']}")
        print(f"  - 窗口大小: {config['window_size']}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

async def test_conversation_context_enhancement():
    """对话上下文增强测试"""
    print("\n💬 对话上下文增强测试")
    print("=" * 60)
    
    try:
        from src.rag_action.service.langchain_rag_service import LangChainRAGService
        
        service = LangChainRAGService()
        
        # 模拟长对话历史
        long_conversation = [
            {"role": "user", "content": "什么是人工智能？"},
            {"role": "assistant", "content": "人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。"},
            {"role": "user", "content": "它有哪些主要分支？"},
            {"role": "assistant", "content": "AI的主要分支包括机器学习、深度学习、自然语言处理、计算机视觉、专家系统等。"},
            {"role": "user", "content": "机器学习是如何工作的？"},
            {"role": "assistant", "content": "机器学习通过算法分析数据，识别模式，并做出预测或决策，无需明确编程。"},
            {"role": "user", "content": "深度学习和机器学习有什么区别？"},
            {"role": "assistant", "content": "深度学习是机器学习的一个子集，使用多层神经网络来模拟人脑的工作方式。"},
            {"role": "user", "content": "能给我一些AI的实际应用例子吗？"}
        ]
        
        print(f"🔄 处理包含{len(long_conversation)}轮对话的上下文...")
        
        # 测试上下文转换和增强
        start_time = time.time()
        enhanced_messages = service._convert_conversation_context(long_conversation)
        end_time = time.time()
        
        print(f"✅ 上下文增强完成:")
        print(f"  - 原始对话轮数: {len(long_conversation)}")
        print(f"  - 增强后消息数: {len(enhanced_messages)}")
        print(f"  - 处理时间: {end_time - start_time:.3f}s")
        
        # 显示增强效果
        if service.use_langchain_memory:
            print(f"  - Memory增强: ✅ 启用")
            
            # 检查是否添加了摘要或历史信息
            system_messages = [msg for msg in enhanced_messages if hasattr(msg, 'content') and 
                             ('摘要' in str(msg.content) or '历史' in str(msg.content))]
            if system_messages:
                print(f"  - 添加了{len(system_messages)}条Memory增强信息")
                for i, msg in enumerate(system_messages[:2], 1):
                    print(f"    {i}. {str(msg.content)[:50]}...")
        else:
            print(f"  - Memory增强: ❌ 未启用")
        
        return True
        
    except Exception as e:
        print(f"❌ 对话上下文增强测试失败: {e}")
        return False

async def test_memory_types_functionality():
    """不同类型Memory功能测试"""
    print("\n🔧 Memory类型功能测试")
    print("=" * 60)
    
    try:
        from src.rag_action.service.langchain_rag_service import LangChainRAGService
        
        service = LangChainRAGService()
        
        if not service.use_langchain_memory:
            print("⚠️ LangChain Memory不可用，跳过功能测试")
            return True
        
        # 测试摘要记忆
        print("📝 测试摘要记忆功能...")
        if service.summary_memory:
            test_conversation = [
                {"role": "user", "content": "解释量子计算的基本原理"},
                {"role": "assistant", "content": "量子计算利用量子力学原理，使用量子比特进行计算..."},
                {"role": "user", "content": "它比传统计算有什么优势？"},
                {"role": "assistant", "content": "量子计算在某些特定问题上具有指数级的速度优势..."}
            ]
            
            summary = service._get_summary_memory_context(test_conversation)
            print(f"  ✅ 摘要记忆: {'有效' if summary else '无内容'}")
            if summary:
                print(f"    摘要内容: {summary[:100]}...")
        else:
            print("  ❌ 摘要记忆未初始化")
        
        # 测试向量记忆
        print("\n🔍 测试向量记忆功能...")
        if service.vector_memory:
            test_query = "人工智能的发展历史"
            vector_context = service._get_vector_memory_context(test_query)
            print(f"  ✅ 向量记忆: {'有效' if vector_context else '无内容'}")
            if vector_context:
                print(f"    相关历史: {vector_context[:100]}...")
        else:
            print("  ❌ 向量记忆未初始化")
        
        # 测试对话缓冲
        print("\n💾 测试对话缓冲功能...")
        if service.conversation_memory:
            test_buffer_conversation = [
                {"role": "user", "content": "测试缓冲记忆"},
                {"role": "assistant", "content": "这是测试回答"}
            ]
            
            service._update_conversation_buffer(test_buffer_conversation)
            print("  ✅ 对话缓冲: 更新成功")
        else:
            print("  ❌ 对话缓冲未初始化")
        
        return True
        
    except Exception as e:
        print(f"❌ Memory类型功能测试失败: {e}")
        return False

async def test_memory_performance_impact():
    """Memory性能影响测试"""
    print("\n⚡ Memory性能影响测试")
    print("=" * 60)
    
    try:
        from src.rag_action.service.langchain_rag_service import LangChainRAGService
        
        service = LangChainRAGService()
        
        # 准备测试对话
        test_conversations = [
            # 短对话
            [
                {"role": "user", "content": "简短问题"},
                {"role": "assistant", "content": "简短回答"}
            ],
            # 中等对话
            [
                {"role": "user", "content": "什么是机器学习？"},
                {"role": "assistant", "content": "机器学习是人工智能的一个分支..."},
                {"role": "user", "content": "它有哪些应用？"},
                {"role": "assistant", "content": "机器学习广泛应用于..."}
            ],
            # 长对话
            [
                {"role": "user", "content": f"这是第{i}个问题"} if i % 2 == 0 
                else {"role": "assistant", "content": f"这是第{i}个回答"}
                for i in range(20)
            ]
        ]
        
        print("🔄 开始性能测试...")
        
        for i, conversation in enumerate(test_conversations, 1):
            conv_length = len(conversation)
            print(f"\n测试 {i}: {conv_length}轮对话")
            
            # 测试不使用Memory的性能
            original_use_memory = service.use_langchain_memory
            service.use_langchain_memory = False
            
            start_time = time.time()
            basic_messages = service._convert_conversation_context(conversation)
            basic_time = time.time() - start_time
            
            print(f"  📊 基础转换: {basic_time:.4f}s ({len(basic_messages)}条消息)")
            
            # 测试使用Memory的性能
            service.use_langchain_memory = original_use_memory
            
            if service.use_langchain_memory:
                start_time = time.time()
                enhanced_messages = service._convert_conversation_context(conversation)
                enhanced_time = time.time() - start_time
                
                print(f"  🧠 Memory增强: {enhanced_time:.4f}s ({len(enhanced_messages)}条消息)")
                
                # 计算性能影响
                overhead = ((enhanced_time - basic_time) / basic_time) * 100 if basic_time > 0 else 0
                print(f"  📈 性能开销: {overhead:.1f}%")
            else:
                print("  ⚠️ Memory未启用，跳过增强测试")
        
        return True
        
    except Exception as e:
        print(f"❌ Memory性能测试失败: {e}")
        return False

async def test_memory_integration_with_rag():
    """Memory与RAG集成测试"""
    print("\n🔗 Memory与RAG集成测试")
    print("=" * 60)
    
    try:
        from src.rag_action.service.langchain_rag_service import LangChainRAGService
        
        service = LangChainRAGService()
        
        # 模拟带有对话历史的问答
        conversation_context = [
            {"role": "user", "content": "什么是深度学习？"},
            {"role": "assistant", "content": "深度学习是机器学习的一个子集，使用多层神经网络..."},
            {"role": "user", "content": "它在图像识别中如何应用？"}
        ]
        
        print("🤖 测试带Memory的智能问答...")
        
        # 测试完整的RAG流程（包含Memory）
        if service.rag_chain and service.use_langchain_memory:
            try:
                # 构建输入
                chain_input = {
                    "question": "深度学习在图像识别中的具体应用有哪些？",
                    "top_k": 3
                }
                
                # 构建配置（包含对话上下文）
                config = RunnableConfig()
                if conversation_context:
                    messages = service._convert_conversation_context(conversation_context)
                    config["configurable"] = {"conversation_history": messages}
                
                start_time = time.time()
                # 注意：这里只是测试配置构建，实际调用需要完整的环境
                print(f"  ✅ RAG+Memory配置构建成功")
                print(f"  📝 对话上下文: {len(conversation_context)}轮")
                print(f"  🧠 Memory增强: {'启用' if service.use_langchain_memory else '禁用'}")
                
            except Exception as e:
                print(f"  ⚠️ RAG链调用测试跳过: {e}")
        else:
            print("  ⚠️ RAG链或Memory不可用，跳过集成测试")
        
        return True
        
    except Exception as e:
        print(f"❌ Memory与RAG集成测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🎉 LangChain Memory对话管理测试")
    print("=" * 80)
    
    test_results = []
    
    # 运行各项测试
    tests = [
        ("Memory基础功能测试", test_memory_basic_functionality),
        ("对话上下文增强测试", test_conversation_context_enhancement),
        ("Memory类型功能测试", test_memory_types_functionality),
        ("Memory性能影响测试", test_memory_performance_impact),
        ("Memory与RAG集成测试", test_memory_integration_with_rag)
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"\n🚀 开始 {test_name}...")
            result = await test_func()
            test_results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
                
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "=" * 80)
    print("📊 测试结果总结:")
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  - {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎊 所有测试通过！LangChain Memory对话管理实现成功！")
    else:
        print("⚠️ 部分测试失败，请检查实现或环境配置")

if __name__ == "__main__":
    asyncio.run(main())
