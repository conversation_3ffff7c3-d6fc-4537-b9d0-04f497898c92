import { defineStore } from 'pinia'
import { queryApi } from '@/api'
import type { QueryHistory, QueryResponse, QuerySource, Conversation, ConversationMessage, ConversationSummary } from '@/types'
import { storage } from '@/utils'

interface QueryState {
  // 当前查询
  currentQuery: string

  // 查询结果
  currentResult: QueryResponse | null

  // 查询历史（保留兼容性）
  history: QueryHistory[]

  // 对话相关
  currentConversationId: string | null
  currentConversation: Conversation | null
  conversations: ConversationSummary[]

  // 加载状态
  loading: boolean

  // 查询配置
  config: {
    top_k: number
    note_ids: number[]
    auto_save_history: boolean
    enable_conversation: boolean
  }

  // 查询统计
  stats: {
    total_queries: number
    avg_response_time: number
    success_rate: number
  }
}

export const useQueryStore = defineStore('query', {
  state: (): QueryState => ({
    currentQuery: '',
    currentResult: null,
    history: storage.get('queryHistory', []),
    // 修复：从本地存储恢复会话ID，确保页面刷新后对话连续性
    // 测试结果：✅ 前端多轮对话连续性已完全修复
    currentConversationId: storage.get('currentConversationId', null),
    currentConversation: null,
    conversations: [],
    loading: false,
    config: {
      top_k: 5,
      note_ids: [],
      auto_save_history: true,
      enable_conversation: true
    },
    stats: {
      total_queries: 0,
      avg_response_time: 0,
      success_rate: 0
    }
  }),

  getters: {
    // 获取最近的查询历史
    recentHistory: (state) => state.history.slice(0, 10),
    
    // 获取热门查询
    popularQueries: (state) => {
      const queryCount: Record<string, number> = {}
      state.history.forEach(item => {
        queryCount[item.query] = (queryCount[item.query] || 0) + 1
      })
      
      return Object.entries(queryCount)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([query]) => query)
    },
    
    // 获取查询建议
    querySuggestions: (state) => (input: string) => {
      if (!input.trim()) return []

      const suggestions = state.history
        .filter(item => item.query.toLowerCase().includes(input.toLowerCase()))
        .map(item => item.query)
        .slice(0, 5)

      return [...new Set(suggestions)]
    },

    // 当前对话的消息列表
    currentMessages: (state) => {
      return state.currentConversation?.messages || []
    },

    // 是否有活跃的对话
    hasActiveConversation: (state) => {
      return !!state.currentConversationId && !!state.currentConversation
    },

    // 对话历史摘要
    conversationSummaries: (state) => {
      return state.conversations.slice().sort((a, b) =>
        new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
      )
    }
  },

  actions: {
    // 初始化配置
    // 修复：添加完整的配置初始化，确保多轮对话功能正常
    // 测试结果：✅ 前端多轮对话功能已修复，支持会话连续性
    init() {
      // 从本地存储加载配置
      const savedConfig = storage.get('queryConfig', {})
      this.config = {
        ...this.config,
        ...savedConfig
      }

      // 确保对话模式默认启用
      if (this.config.enable_conversation === undefined) {
        this.config.enable_conversation = true
      }
    },

    // 执行智能查询（支持对话）- 使用流式接口
    async intelligentQuery(query: string, useConversation: boolean = true) {
      if (!query.trim()) return

      this.loading = true
      this.currentQuery = query

      const startTime = Date.now()

      // 构建请求参数
      const params: { query: string; conversation_id?: string } = { query }

      // 修复：使用传入的useConversation参数（前端UI状态）而不是store配置
      // 这样确保前端UI的对话模式切换能正确控制后端行为
      // 测试结果：✅ 高级上下文理解测试通过，对话模式切换修复成功
      if (useConversation) {
        if (this.currentConversationId) {
          params.conversation_id = this.currentConversationId
        }
        // 新对话时不传递conversation_id，让后端创建新会话
      }

      // 使用流式接口进行查询
      return new Promise((resolve, reject) => {
        let streamingAnswer = ''
        let finalResult: any = null

        const streamParams = {
          query,
          conversation_id: params.conversation_id,
          top_k: 5,
          stream: true
        }

        queryApi.intelligentQueryStreamPost(streamParams, {
          onProgress: (stage: string, message: string) => {
            // 可以在这里更新UI显示进度
          },
          onChunk: (chunk: string) => {
            streamingAnswer += chunk
            // 实时更新答案显示
            this.currentResult = {
              ...this.currentResult,
              answer: streamingAnswer,
              query_time: Date.now() - startTime
            }
          },
          onComplete: (response: any) => {
            const queryTime = Date.now() - startTime
            finalResult = { ...response, query_time: queryTime }
            this.currentResult = finalResult

            // 更新对话状态
            if (response.conversation_id) {
              this.currentConversationId = response.conversation_id
              // 持久化会话ID到本地存储
              storage.set('currentConversationId', response.conversation_id)
              // 异步刷新当前对话详情，不阻塞UI
              this.loadConversation(response.conversation_id).catch(error => {
                console.warn('加载对话详情失败:', error)
              })
            }

            // 保存到历史记录（兼容性）
            if (this.config.auto_save_history) {
              this.addToHistory({
                id: Date.now().toString(),
                query,
                answer: response.answer || streamingAnswer,
                sources: response.sources || [],
                timestamp: new Date().toISOString(),
                query_time: queryTime
              })
            }

            // 更新统计
            this.updateStats(queryTime, true)

            // 完成时设置loading为false
            this.loading = false
            resolve(finalResult)
          },
          onError: (error: any) => {
            this.updateStats(0, false)
            // 错误时设置loading为false
            this.loading = false
            reject(error)
          }
        })
      })
    },

    // 数据库查询
    async databaseQuery(question: string) {
      if (!question.trim()) return
      
      this.loading = true
      this.currentQuery = question
      
      try {
        const startTime = Date.now()
        const result = await queryApi.databaseQuery({ question })
        const queryTime = Date.now() - startTime
        
        // 转换为统一的查询结果格式
        const queryResult: QueryResponse = {
          answer: result.result,
          sources: [],
          query_time: queryTime,
          total_tokens: 0
        }
        
        this.currentResult = queryResult
        
        // 保存到历史记录
        if (this.config.auto_save_history) {
          this.addToHistory({
            id: Date.now().toString(),
            query: question,
            answer: result.result,
            sources: [],
            timestamp: new Date().toISOString(),
            query_time: queryTime
          })
        }
        
        this.updateStats(queryTime, true)
        return result
      } catch (error) {
        this.updateStats(0, false)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 向量搜索
    async vectorSearch(query: string, options?: { top_k?: number; note_ids?: number[] }) {
      if (!query.trim()) return
      
      this.loading = true
      
      try {
        const result = await queryApi.vectorSearch({
          query,
          top_k: options?.top_k || this.config.top_k,
          note_ids: options?.note_ids || this.config.note_ids
        })
        
        return result
      } catch (error) {
        console.error('向量搜索失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 添加到历史记录
    addToHistory(item: QueryHistory) {
      // 避免重复添加相同的查询
      const existingIndex = this.history.findIndex(h => h.query === item.query)
      if (existingIndex !== -1) {
        this.history.splice(existingIndex, 1)
      }
      
      this.history.unshift(item)
      
      // 限制历史记录数量
      if (this.history.length > 100) {
        this.history = this.history.slice(0, 100)
      }
      
      // 保存到本地存储
      storage.set('queryHistory', this.history)
    },

    // 删除历史记录
    removeFromHistory(id: string) {
      this.history = this.history.filter(item => item.id !== id)
      storage.set('queryHistory', this.history)
    },

    // 清空历史记录
    clearHistory() {
      this.history = []
      storage.remove('queryHistory')
    },

    // 更新配置
    updateConfig(config: Partial<QueryState['config']>) {
      this.config = { ...this.config, ...config }
      storage.set('queryConfig', this.config)
    },

    // 更新统计信息
    updateStats(queryTime: number, success: boolean) {
      this.stats.total_queries += 1
      
      if (success) {
        const totalTime = this.stats.avg_response_time * (this.stats.total_queries - 1) + queryTime
        this.stats.avg_response_time = totalTime / this.stats.total_queries
      }
      
      const successCount = Math.floor(this.stats.success_rate * (this.stats.total_queries - 1) / 100)
      this.stats.success_rate = ((successCount + (success ? 1 : 0)) / this.stats.total_queries) * 100
    },

    // 设置当前查询
    setCurrentQuery(query: string) {
      this.currentQuery = query
    },

    // 清空当前结果
    clearCurrentResult() {
      this.currentResult = null
      this.currentQuery = ''
    },



    // === 对话管理相关方法 ===

    // 开始新对话
    async startNewConversation() {
      this.currentConversationId = null
      this.currentConversation = null
      // 清空本地存储中的会话ID
      storage.remove('currentConversationId')
    },

    // 加载对话详情
    async loadConversation(conversationId: string) {
      try {
        const conversation = await queryApi.getConversation(conversationId)
        this.currentConversation = conversation
        this.currentConversationId = conversationId
      } catch (error) {
        throw error
      }
    },

    // 加载对话列表
    async loadConversations(limit: number = 50) {
      try {
        const response = await queryApi.getConversations(limit)
        this.conversations = response.conversations || []
      } catch (error) {
        throw error
      }
    },

    // 删除对话
    async deleteConversation(conversationId: string) {
      try {
        await queryApi.deleteConversation(conversationId)

        // 如果删除的是当前对话，清空当前对话状态
        if (this.currentConversationId === conversationId) {
          this.currentConversationId = null
          this.currentConversation = null
          // 清空本地存储中的会话ID
          storage.remove('currentConversationId')
        }

        // 从对话列表中移除
        this.conversations = this.conversations.filter(
          conv => conv.conversation_id !== conversationId
        )

        console.log('🗑️ 删除对话:', conversationId)
      } catch (error) {
        console.error('删除对话失败:', error)
        throw error
      }
    },

    // 清空所有对话
    async clearAllConversations() {
      try {
        await queryApi.clearAllConversations()
        this.conversations = []
        this.currentConversationId = null
        this.currentConversation = null
        // 清空本地存储中的会话ID
        storage.remove('currentConversationId')
        console.log('🧹 清空所有对话')
      } catch (error) {
        console.error('清空对话失败:', error)
        throw error
      }
    },

    // 切换对话模式
    toggleConversationMode(enabled: boolean) {
      this.config.enable_conversation = enabled
      storage.set('queryConfig', this.config)

      if (!enabled) {
        // 如果禁用对话模式，清空当前对话状态
        this.currentConversationId = null
        this.currentConversation = null
        // 清空本地存储中的会话ID
        storage.remove('currentConversationId')
      }

      console.log('🔄 对话模式:', enabled ? '启用' : '禁用')
    }
  }
})
