"""
03 - 分支控制流
展示根据条件选择不同路径的分支控制流
"""

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from typing import Dict, List, Any, TypedDict, Annotated

# 创建 LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0
)

# 定义状态类型
class BranchState(TypedDict):
    messages: Annotated[List, add_messages]  # 消息历史
    user_input: str  # 用户输入
    question_type: str  # 问题类型
    answer: str  # 最终回答
    path_taken: str  # 选择的路径

def branch_workflow():
    """分支工作流：根据问题类型选择不同的处理路径"""
    
    print("=== 分支控制流 ===")
    print("流程：用户输入 -> 分类 -> 选择路径 -> 专业处理 -> 输出")
    print()
    
    def classify_node(state: BranchState) -> BranchState:
        """分类节点：判断问题类型"""
        user_input = state["user_input"]
        
        print(f"步骤1: 分析问题类型...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
请分析以下问题，判断它属于哪种类型：

问题：{user_input}

请从以下类型中选择一个：
1. "technical" - 技术问题（编程、开发、技术架构等）
2. "business" - 商业问题（营销、市场、商业模式等）
3. "learning" - 学习问题（教育、培训、学习方法等）
4. "general" - 一般问题（日常、生活、闲聊等）

请只返回类型名称（technical/business/learning/general）：
""")
        ])
        
        question_type = response.content.strip().lower()
        
        return {
            **state,
            "question_type": question_type,
            "messages": [AIMessage(content=f"问题类型：{question_type}")]
        }
    
    def technical_node(state: BranchState) -> BranchState:
        """技术问题处理节点"""
        user_input = state["user_input"]
        
        print(f"步骤2: 技术专家处理...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
你是一个技术专家。请回答以下技术问题：

问题：{user_input}

请提供技术性的详细回答，包括：
1. 技术原理
2. 实现方法
3. 最佳实践
4. 相关工具或框架
""")
        ])
        
        answer = response.content
        
        return {
            **state,
            "answer": answer,
            "path_taken": "技术专家路径",
            "messages": [AIMessage(content=f"技术回答：{answer}")]
        }
    
    def business_node(state: BranchState) -> BranchState:
        """商业问题处理节点"""
        user_input = state["user_input"]
        
        print(f"步骤2: 商业顾问处理...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
你是一个商业顾问。请回答以下商业问题：

问题：{user_input}

请提供商业性的详细回答，包括：
1. 市场分析
2. 策略建议
3. 风险评估
4. 实施步骤
""")
        ])
        
        answer = response.content
        
        return {
            **state,
            "answer": answer,
            "path_taken": "商业顾问路径",
            "messages": [AIMessage(content=f"商业回答：{answer}")]
        }
    
    def learning_node(state: BranchState) -> BranchState:
        """学习问题处理节点"""
        user_input = state["user_input"]
        
        print(f"步骤2: 教育专家处理...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
你是一个教育专家。请回答以下学习问题：

问题：{user_input}

请提供教育性的详细回答，包括：
1. 学习目标
2. 学习方法
3. 学习资源
4. 学习计划
""")
        ])
        
        answer = response.content
        
        return {
            **state,
            "answer": answer,
            "path_taken": "教育专家路径",
            "messages": [AIMessage(content=f"教育回答：{answer}")]
        }
    
    def general_node(state: BranchState) -> BranchState:
        """一般问题处理节点"""
        user_input = state["user_input"]
        
        print(f"步骤2: 智能助手处理...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
你是一个智能助手。请回答以下问题：

问题：{user_input}

请提供友好、有用的回答：
""")
        ])
        
        answer = response.content
        
        return {
            **state,
            "answer": answer,
            "path_taken": "智能助手路径",
            "messages": [AIMessage(content=f"一般回答：{answer}")]
        }
    
    def route_node(state: BranchState) -> str:
        """路由节点：根据问题类型选择路径"""
        question_type = state["question_type"]
        
        if "technical" in question_type:
            return "technical"
        elif "business" in question_type:
            return "business"
        elif "learning" in question_type:
            return "learning"
        else:
            return "general"
    
    # 创建图
    workflow = StateGraph(BranchState)
    
    # 添加节点
    workflow.add_node("classify", classify_node)
    workflow.add_node("technical", technical_node)
    workflow.add_node("business", business_node)
    workflow.add_node("learning", learning_node)
    workflow.add_node("general", general_node)
    
    # 设置入口点
    workflow.set_entry_point("classify")
    
    # 添加条件边：根据分类结果选择路径
    workflow.add_conditional_edges(
        "classify",
        route_node,
        {
            "technical": "technical",
            "business": "business",
            "learning": "learning",
            "general": "general"
        }
    )
    
    # 所有路径都结束
    workflow.add_edge("technical", END)
    workflow.add_edge("business", END)
    workflow.add_edge("learning", END)
    workflow.add_edge("general", END)
    
    # 编译图
    app = workflow.compile()
    
    # 测试
    test_inputs = [
        "如何优化数据库查询性能？",
        "制定产品定价策略的方法",
        "推荐一些学习编程的资源",
        "今天天气怎么样？"
    ]
    
    for i, user_input in enumerate(test_inputs, 1):
        print(f"\n测试 {i}: {user_input}")
        print("=" * 60)
        
        # 运行图
        result = app.invoke({
            "user_input": user_input,
            "messages": [],
            "question_type": "",
            "answer": "",
            "path_taken": ""
        })
        
        print(f"\n问题类型: {result['question_type']}")
        print(f"选择路径: {result['path_taken']}")
        print(f"最终回答: {result['answer']}")
        print("=" * 60)

def main():
    """主函数"""
    branch_workflow()

if __name__ == "__main__":
    main() 