import uuid
from typing import Annotated, Dict, List, Any, Optional, Union, Iterator
from typing_extensions import TypedDict
from dataclasses import dataclass
from datetime import datetime
import logging

from langgraph.store.memory import InMemoryStore
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langgraph.graph import StateGraph, MessagesState, START
from langgraph.checkpoint.memory import MemorySaver
from langgraph.store.base import BaseStore
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import BaseMessage

# 添加日志配置
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class MemoryItem:
    """记忆项数据类"""
    id: str
    content: str
    type: str
    user_id: str
    timestamp: str
    score: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "content": self.content,
            "type": self.type,
            "user_id": self.user_id,
            "timestamp": self.timestamp,
            "score": self.score
        }

# 初始化InMemoryStore和嵌入模型
in_memory_store = InMemoryStore(
    index={
        "embed": OpenAIEmbeddings(
            model="text-embedding-3-small",
            base_url="https://api.zhizengzeng.com/v1",
            api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
        ),
        "dims": 1536,  # text-embedding-3-small的维度
    }
)

# 初始化LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0
)

class CrossThreadMemoryManager:
    """跨线程记忆管理器 - 优化版本"""
    
    def __init__(self, store: BaseStore, max_batch_size: int = 1000):
        self.store = store
        self.max_batch_size = max_batch_size
        self._memory_cache = {}  # 简单缓存
    
    def get_user_namespace(self, user_id: str) -> tuple:
        """获取用户记忆的命名空间"""
        return ("memories", user_id)
    
    def _parse_memory_item(self, memory_key: str, memory_value: Dict[str, Any]) -> Optional[MemoryItem]:
        """解析记忆项，统一数据处理逻辑"""
        try:
            return MemoryItem(
                id=memory_key,
                content=memory_value.get("content", ""),
                type=memory_value.get("type", "general"),
                user_id=memory_value.get("user_id", ""),
                timestamp=memory_value.get("timestamp", ""),
                score=getattr(memory_value, 'score', None) if hasattr(memory_value, 'score') else None
            )
        except Exception as e:
            logger.warning(f"解析记忆项失败 {memory_key}: {e}")
            return None
    
    def get_all_memories(
        self, 
        user_id: str, 
        page: Optional[int] = None, 
        page_size: int = 100,
        memory_type: Optional[str] = None,
        include_score: bool = False
    ) -> Union[List[MemoryItem], Dict[str, Any]]:
        """
        获取用户所有记忆 - 优化版本
        
        Args:
            user_id: 用户ID
            page: 页码（从1开始），None表示不分页
            page_size: 每页大小
            memory_type: 记忆类型过滤
            include_score: 是否包含相关性分数
            
        Returns:
            如果分页：返回包含数据和分页信息的字典
            如果不分页：返回记忆项列表
        """
        if not user_id or not user_id.strip():
            logger.error("用户ID不能为空")
            return [] if page is None else {"memories": [], "total": 0, "page": 1, "page_size": page_size}
        
        namespace = self.get_user_namespace(user_id)
        cache_key = f"{user_id}_{memory_type}_{include_score}"
        
        try:
            # 检查缓存
            if cache_key in self._memory_cache:
                logger.debug(f"从缓存获取用户 [{user_id}] 的记忆")
                all_memories = self._memory_cache[cache_key]
            else:
                # 从存储获取所有记忆
                logger.info(f"从存储获取用户 [{user_id}] 的所有记忆")
                
                # 使用批量获取避免内存问题
                all_memories = []
                batch_count = 0
                
                try:
                    # 尝试批量获取
                    raw_memories = self.store.search(namespace, limit=self.max_batch_size)
                    
                    for memory in raw_memories:
                        if batch_count >= self.max_batch_size:
                            logger.warning(f"用户 [{user_id}] 记忆数量超过限制 {self.max_batch_size}，已截断")
                            break
                        
                        # 统一解析记忆项
                        memory_item = self._parse_memory_item(memory.key, memory.value)
                        if memory_item:
                            # 类型过滤
                            if memory_type is None or memory_item.type == memory_type:
                                # 处理分数
                                if include_score and hasattr(memory, 'score'):
                                    memory_item.score = memory.score
                                all_memories.append(memory_item)
                        
                        batch_count += 1
                
                except Exception as search_error:
                    logger.error(f"搜索记忆时出错: {search_error}")
                    # 尝试备用方法
                    all_memories = self._fallback_get_memories(namespace, memory_type)
                
                # 更新缓存（限制缓存大小）
                if len(self._memory_cache) < 100:  # 限制缓存条目数
                    self._memory_cache[cache_key] = all_memories
            
            # 按时间戳排序（最新的在前）
            all_memories.sort(key=lambda x: x.timestamp, reverse=True)
            
            logger.info(f"获取到用户 [{user_id}] 的 {len(all_memories)} 条记忆")
            
            # 分页处理
            if page is not None:
                return self._paginate_memories(all_memories, page, page_size)
            else:
                return all_memories
                
        except Exception as e:
            logger.error(f"获取所有记忆时出错 [{user_id}]: {e}")
            return [] if page is None else {"memories": [], "total": 0, "page": 1, "page_size": page_size}
    
    def _fallback_get_memories(self, namespace: tuple, memory_type: Optional[str] = None) -> List[MemoryItem]:
        """备用获取记忆方法"""
        logger.info("使用备用方法获取记忆")
        try:
            # 这里可以实现备用逻辑，比如直接遍历存储
            # 目前返回空列表作为安全回退
            return []
        except Exception as e:
            logger.error(f"备用方法也失败: {e}")
            return []
    
    def _paginate_memories(self, memories: List[MemoryItem], page: int, page_size: int) -> Dict[str, Any]:
        """分页处理记忆列表"""
        total = len(memories)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        
        paginated_memories = memories[start_idx:end_idx]
        
        return {
            "memories": [memory.to_dict() for memory in paginated_memories],
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size,
            "has_next": end_idx < total,
            "has_prev": page > 1
        }
    
    def get_memories_iterator(self, user_id: str, batch_size: int = 50) -> Iterator[MemoryItem]:
        """
        获取记忆的迭代器，适用于大量数据的流式处理
        
        Args:
            user_id: 用户ID
            batch_size: 批次大小
            
        Yields:
            MemoryItem: 记忆项
        """
        if not user_id or not user_id.strip():
            return
        
        namespace = self.get_user_namespace(user_id)
        
        try:
            logger.info(f"开始流式获取用户 [{user_id}] 的记忆")
            
            # 这里可以实现真正的流式获取
            # 目前使用批量获取模拟
            memories = self.store.search(namespace, limit=self.max_batch_size)
            
            for memory in memories:
                memory_item = self._parse_memory_item(memory.key, memory.value)
                if memory_item:
                    yield memory_item
                    
        except Exception as e:
            logger.error(f"流式获取记忆时出错 [{user_id}]: {e}")
    
    def get_memory_stats(self, user_id: str) -> Dict[str, Any]:
        """获取用户记忆统计信息"""
        try:
            memories = self.get_all_memories(user_id)
            
            if not memories:
                return {"total": 0, "by_type": {}, "latest_timestamp": None}
            
            # 统计各类型记忆数量
            type_counts = {}
            latest_timestamp = None
            
            for memory in memories:
                # 类型统计
                memory_type = memory.type
                type_counts[memory_type] = type_counts.get(memory_type, 0) + 1
                
                # 最新时间戳
                if latest_timestamp is None or memory.timestamp > latest_timestamp:
                    latest_timestamp = memory.timestamp
            
            return {
                "total": len(memories),
                "by_type": type_counts,
                "latest_timestamp": latest_timestamp,
                "user_id": user_id
            }
            
        except Exception as e:
            logger.error(f"获取记忆统计时出错 [{user_id}]: {e}")
            return {"total": 0, "by_type": {}, "latest_timestamp": None}
    
    def clear_cache(self, user_id: Optional[str] = None):
        """清除缓存"""
        if user_id:
            # 清除特定用户的缓存
            keys_to_remove = [key for key in self._memory_cache.keys() if key.startswith(user_id)]
            for key in keys_to_remove:
                del self._memory_cache[key]
            logger.info(f"清除用户 [{user_id}] 的缓存")
        else:
            # 清除所有缓存
            self._memory_cache.clear()
            logger.info("清除所有缓存")
    
    def store_memory(self, user_id: str, memory_content: str, memory_type: str = "general") -> str:
        """存储用户记忆"""
        namespace = self.get_user_namespace(user_id)
        memory_id = str(uuid.uuid4())
        
        memory_data = {
            "content": memory_content,
            "type": memory_type,
            "user_id": user_id,
            "timestamp": str(uuid.uuid1().time)
        }
        
        self.store.put(namespace, memory_id, memory_data)
        print(f"💾 存储记忆 [{user_id}]: {memory_content}")
        return memory_id
    
    def search_memories(self, user_id: str, query: str, limit: int = 5) -> List[MemoryItem]:
        """搜索用户相关记忆 - 优化版本"""
        if not user_id or not user_id.strip():
            logger.error("用户ID不能为空")
            return []
        
        if not query or not query.strip():
            logger.warning("查询内容为空")
            return []
        
        namespace = self.get_user_namespace(user_id)
        
        try:
            # 使用向量搜索查找相关记忆
            memories = self.store.search(namespace, query=query, limit=limit)
            
            relevant_memories = []
            for memory in memories:
                memory_item = self._parse_memory_item(memory.key, memory.value)
                if memory_item:
                    # 添加相关性分数
                    if hasattr(memory, 'score'):
                        memory_item.score = memory.score
                    relevant_memories.append(memory_item)
            
            logger.info(f"为用户 [{user_id}] 找到 {len(relevant_memories)} 条相关记忆")
            return relevant_memories
            
        except Exception as e:
            logger.error(f"搜索记忆时出错 [{user_id}]: {e}")
            return []
    
    def delete_memory(self, user_id: str, memory_id: str) -> bool:
        """删除特定记忆"""
        namespace = self.get_user_namespace(user_id)
        
        try:
            self.store.delete(namespace, memory_id)
            print(f"🗑️ 删除记忆 [{user_id}]: {memory_id}")
            return True
        except Exception as e:
            print(f"⚠️ 删除记忆时出错: {e}")
            return False

# 初始化记忆管理器
memory_manager = CrossThreadMemoryManager(in_memory_store)

def call_model_with_memory(state: MessagesState, config: RunnableConfig, *, store: BaseStore):
    """带记忆功能的模型调用节点"""
    
    # 获取用户ID
    user_id = config["configurable"].get("user_id", "default_user")
    thread_id = config["configurable"].get("thread_id", "default_thread")
    
    print(f"\n🔄 处理请求 - 用户: {user_id}, 线程: {thread_id}")
    
    # 获取最后一条用户消息
    last_message = state["messages"][-1]
    user_input = last_message.content
    
    print(f"👤 用户输入: {user_input}")
    
    # 检查是否需要存储新记忆
    if any(keyword in user_input.lower() for keyword in ["我叫", "我是", "我的名字", "记住", "我今年", "我喜欢"]):
        # 提取并存储记忆
        memory_manager.store_memory(user_id, user_input, "personal_info")
    
    # 搜索相关记忆
    relevant_memories = memory_manager.search_memories(user_id, user_input)
    
    # 构建系统提示
    memory_context = ""
    if relevant_memories:
        memory_context = "用户的相关记忆信息:\n"
        for memory in relevant_memories:
            # 修复：使用属性访问而不是字典访问
            memory_context += f"- {memory.content}\n"
    
    system_message = f"""你是一个有记忆的AI助手。你可以记住用户告诉你的信息，并在后续对话中使用这些信息。

{memory_context}

请基于用户的历史记忆信息，提供个性化和有帮助的回答。如果用户询问之前告诉过你的信息，请准确回答。
"""
    
    # 调用LLM
    messages_with_system = [
        {"role": "system", "content": system_message}
    ] + state["messages"]
    
    response = llm.invoke(messages_with_system)
    
    print(f"🤖 助手回复: {response.content}")
    
    return {"messages": response}

# 构建LangGraph
builder = StateGraph(MessagesState)
builder.add_node("call_model", call_model_with_memory)
builder.add_edge(START, "call_model")

# 编译图，同时使用checkpointer和store
graph = builder.compile(
    checkpointer=MemorySaver(),  # 用于线程隔离的对话历史
    store=in_memory_store        # 用于跨线程的长期记忆
)

def chat_with_memory(user_id: str, thread_id: str, message: str):
    """带记忆的聊天函数"""
    config = {
        "configurable": {
            "user_id": user_id,
            "thread_id": thread_id
        }
    }
    
    input_messages = {"messages": [{"role": "user", "content": message}]}
    
    print(f"\n{'='*60}")
    print(f"🗣️ 对话 - 用户: {user_id}, 线程: {thread_id}")
    print(f"👤 用户: {message}")
    
    try:
        for chunk in graph.stream(input_messages, config):
            if "call_model" in chunk:
                # 安全处理消息输出
                messages = chunk["call_model"]["messages"]
                if isinstance(messages, list):
                    if messages:
                        print(f"🤖 助手: {messages[-1].content}")
                else:
                    print(f"🤖 助手: {messages.content}")
    except Exception as e:
        print(f"❌ 对话出错: {e}")

def demo_cross_thread_memory():
    """演示跨线程记忆共享"""
    
    print("🚀 开始跨线程记忆共享演示")
    print("="*80)
    
    # 用户A在线程1中建立记忆
    print("\n📝 阶段1: 用户A在线程1中建立个人信息")
    chat_with_memory("user_a", "thread_1", "你好，我叫张三")
    chat_with_memory("user_a", "thread_1", "我今年25岁，是一名程序员")
    chat_with_memory("user_a", "thread_1", "我喜欢打篮球和看电影")
    
    # 用户A在线程2中访问记忆
    print("\n🔄 阶段2: 用户A在线程2中访问之前的记忆")
    chat_with_memory("user_a", "thread_2", "我叫什么名字？")
    chat_with_memory("user_a", "thread_2", "我的职业是什么？")
    chat_with_memory("user_a", "thread_2", "我有什么爱好？")
    
    # 用户B在线程3中建立自己的记忆
    print("\n👥 阶段3: 用户B建立独立的记忆")
    chat_with_memory("user_b", "thread_3", "你好，我叫李四")
    chat_with_memory("user_b", "thread_3", "我是一名设计师，喜欢画画")
    
    # 验证用户隔离
    print("\n🔒 阶段4: 验证用户记忆隔离")
    chat_with_memory("user_b", "thread_4", "张三是谁？")  # 应该不知道
    chat_with_memory("user_a", "thread_3", "李四是谁？")  # 应该不知道
    
    # 用户A回到线程1，验证线程内对话历史
    print("\n🔄 阶段5: 验证线程内对话历史保持")
    chat_with_memory("user_a", "thread_1", "我们刚才聊了什么？")
    
    # 显示记忆统计
    print("\n📊 记忆统计信息")
    print("-" * 40)
    
    user_a_memories = memory_manager.get_all_memories("user_a")
    user_b_memories = memory_manager.get_all_memories("user_b")
    
    print(f"👤 用户A的记忆数量: {len(user_a_memories)}")
    for i, memory in enumerate(user_a_memories, 1):
        # 修复：使用属性访问而不是字典访问
        print(f"   {i}. [{memory.type}] {memory.content}")
    
    print(f"👤 用户B的记忆数量: {len(user_b_memories)}")
    for i, memory in enumerate(user_b_memories, 1):
        # 修复：使用属性访问而不是字典访问
        print(f"   {i}. [{memory.type}] {memory.content}")

def test_memory_operations():
    """测试记忆操作功能"""
    print("\n🧪 测试记忆管理功能")
    print("="*50)
    
    test_user = "test_user"
    
    # 存储测试记忆
    print("1. 存储记忆测试")
    memory_id1 = memory_manager.store_memory(test_user, "我住在北京", "location")
    memory_id2 = memory_manager.store_memory(test_user, "我的手机号是13800138000", "contact")
    
    # 搜索记忆测试
    print("\n2. 搜索记忆测试")
    results = memory_manager.search_memories(test_user, "我住在哪里")
    print(f"搜索结果: {len(results)} 条")
    for result in results:
        # 修复：使用属性访问而不是字典访问
        score_text = f" (相关度: {result.score:.2f})" if result.score is not None else ""
        print(f"   - {result.content}{score_text}")
    
    # 获取所有记忆测试
    print("\n3. 获取所有记忆测试")
    all_memories = memory_manager.get_all_memories(test_user)
    print(f"总记忆数: {len(all_memories)}")
    for memory in all_memories:
        # 修复：使用属性访问而不是字典访问
        print(f"   - [{memory.type}] {memory.content}")
    
    # 删除记忆测试
    print("\n4. 删除记忆测试")
    success = memory_manager.delete_memory(test_user, memory_id1)
    print(f"删除结果: {'成功' if success else '失败'}")
    
    # 验证删除
    remaining_memories = memory_manager.get_all_memories(test_user)
    print(f"删除后剩余记忆数: {len(remaining_memories)}")
    for memory in remaining_memories:
        # 修复：使用属性访问而不是字典访问
        print(f"   - [{memory.type}] {memory.content}")

if __name__ == "__main__":
    try:
        # 运行记忆操作测试
        test_memory_operations()
        
        # 运行跨线程记忆演示
        demo_cross_thread_memory()
        
        print("\n✅ 跨线程记忆系统演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
        import traceback
        traceback.print_exc()
