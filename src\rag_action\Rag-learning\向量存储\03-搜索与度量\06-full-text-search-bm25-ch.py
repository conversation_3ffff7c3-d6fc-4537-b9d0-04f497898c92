from pymilvus import MilvusClient, DataType, Function, FunctionType
import json

"""
本示例详细讲解如何在 Milvus 中实现基于 BM25 算法的中文全文检索，流程如下：

1. 连接 Milvus 服务，准备客户端。
2. 定义集合 schema，包括主键、文本字段（支持分词）、稀疏向量字段。
3. 定义 BM25 函数，实现文本到稀疏向量的自动转换。
4. 配置稀疏倒排索引及 BM25 检索参数。
5. 创建集合。
6. 插入文本数据，自动生成稀疏向量。
7. 加载集合到内存，准备检索。
8. 执行 BM25 全文检索，展示结果结构和内容。
9. 清理资源。

下面是详细代码及注释：
"""

# 1. 设置 Milvus 客户端
client = MilvusClient(uri="http://localhost:19530")
COLLECTION_NAME = "full_text_search_demo"

# 2. 如果集合已存在，则删除，保证流程可重复
if client.has_collection(COLLECTION_NAME):
    print(f"删除已存在的集合: {COLLECTION_NAME}")
    client.drop_collection(COLLECTION_NAME)

# 3. 创建 schema，包含主键、文本字段（支持分词）、稀疏向量字段
print("\n创建 schema...")
schema = client.create_schema()
schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True, auto_id=True)
schema.add_field(field_name="text", datatype=DataType.VARCHAR, max_length=1000, enable_analyzer=True)
schema.add_field(field_name="sparse", datatype=DataType.SPARSE_FLOAT_VECTOR)

# 4. 定义 BM25 函数，实现文本到稀疏向量的自动转换
print("定义 BM25 函数...")
bm25_function = Function(
    name="text_bm25_emb",
    input_field_names=["text"],
    output_field_names=["sparse"],
    function_type=FunctionType.BM25,
)
schema.add_function(bm25_function)

# 5. 配置稀疏倒排索引及 BM25 检索参数
print("配置索引参数...")
index_params = client.prepare_index_params()
index_params.add_index(
    field_name="sparse",
    index_name="sparse_inverted_index",
    index_type="SPARSE_INVERTED_INDEX",
    metric_type="BM25",
    params={
        "inverted_index_algo": "DAAT_MAXSCORE",
        "bm25_k1": 1.2,
        "bm25_b": 0.75
    },
)

# 6. 创建集合
print(f"创建集合: {COLLECTION_NAME}")
client.create_collection(
    collection_name=COLLECTION_NAME,
    schema=schema,
    index_params=index_params
)

# 7. 插入文本数据，自动生成稀疏向量
print("\n插入文本数据...")
documents = [
    {'text': '信息检索是一个研究领域。'},
    {'text': '信息检索专注于在大型数据集中查找相关信息。'},
    {'text': '数据挖掘和信息检索在研究中有所重叠。'},
    {'text': '搜索引擎是信息检索系统的一个典型例子。'},
    {'text': '自然语言处理在信息检索中扮演重要角色。'},
]
insert_result = client.insert(COLLECTION_NAME, documents)
client.flush(collection_name=COLLECTION_NAME)
print(f"插入结果: {insert_result}")

# 8. 加载集合到内存，准备检索
print("\n加载集合...")
client.load_collection(collection_name=COLLECTION_NAME)

# 9. 执行 BM25 全文检索，展示结果结构和内容
print("\n=== 全文搜索示例 ===")
search_params = {
    'params': {'drop_ratio_search': 0.2},
}
query_text = "信息检索"
print(f"\n执行搜索，查询文本: {query_text}")
results = client.search(
    collection_name=COLLECTION_NAME,
    data=[query_text],
    anns_field='sparse',
    limit=3,
    search_params=search_params,
    output_fields=["text"]  # 添加输出字段以显示原始文本
)

print("\n搜索结果结构:")
print(json.dumps(results, indent=2, ensure_ascii=False))

print("\n搜索结果:")
if results and len(results) > 0:
    for hits in results:
        for hit in hits:
            # 打印完整的 hit 结构
            print("\nHit 结构:")
            print(json.dumps(hit, indent=2, ensure_ascii=False))
            # 尝试不同的字段访问方式
            if 'entity' in hit:
                print(f"ID: {hit.get('id', 'N/A')}, 文本: {hit['entity'].get('text', 'N/A')}")
            else:
                print("未找到 entity 字段")
else:
    print("没有找到匹配的结果")

# 10. 清理资源
print("\n清理资源...")
client.release_collection(collection_name=COLLECTION_NAME) 