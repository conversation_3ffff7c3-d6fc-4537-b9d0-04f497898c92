import pandas as pd
import networkx as nx
import matplotlib.pyplot as plt
from typing import List, Dict, Set, Optional
from dataclasses import dataclass

# 假设你已经有了大模型的API客户端
from langchain_openai import ChatOpenAI

@dataclass
class ClarificationNode:
    id: str
    text: str
    aspect: str
    depth: int
    parent_id: Optional[str] = None
    answers: Optional[List[str]] = None

def create_knowledge_base() -> Dict:
    """
    构建一个简单的知识库，包含角色的能力和关系等结构化信息。
    """
    return {
        "abilities": {
            "combat": {
                "physical": ["金刚不坏", "力大无穷", "筋骨如铁"],
                "magical": ["七十二变", "筋斗云", "定身术"],
                "weapon": ["棒法精通", "兵器大师", "法器操控"]
            },
            "perception": {
                "detection": ["火眼金睛", "妖气探查", "气息感知"],
                "insight": ["心性洞察", "破妖辨识", "灵识感应"]
            }
        },
        "relationships": {
            "allies": {
                "disciples": ["红孩儿", "明月", "六耳"],
                "friends": ["杨戬", "哪吒", "龙王三太子"]
            },
            "enemies": {
                "heavenly": ["天兵天将", "二郎神部众"]
            }
        }
    }

def create_question_templates(character_name: str) -> Dict[str, Dict[str, str]]:
    """
    针对不同角色，生成澄清问题的模板，便于后续自动生成澄清树节点的文本。
    """
    return {
        "abilities": {
            "root": f"您想了解{character_name}的哪类能力？",
            "combat": f"在战斗能力方面，您对{character_name}的哪个方面更感兴趣？",
            "perception": f"关于{character_name}的感知能力，您想了解哪些具体内容？"
        },
        "relationships": {
            "root": f"您想了解{character_name}与谁的关系？",
            "allies": f"在{character_name}的盟友中，您想知道哪类关系？",
            "enemies": f"关于{character_name}的对手，您对哪方势力感兴趣？"
        }
    }

def identify_main_aspects(question: str) -> Set[str]:
    """
    根据用户问题中的关键词，识别出主要关注的方面（如能力、关系等）。
    如果未识别到，则默认返回“abilities”。
    """
    aspects = set()
    keywords = {
        "abilities": ["能力", "本领", "技能", "法术", "神通"],
        "relationships": ["关系", "朋友", "敌人", "师徒", "结交"]
    }
    for aspect, words in keywords.items():
        if any(word in question for word in words):
            aspects.add(aspect)
    if not aspects:
        aspects.add("abilities")
    return aspects

def identify_character(question: str, characters: List[str]) -> Optional[str]:
    """
    从问题中识别出涉及的角色名称。
    """
    for character in characters:
        if character in question:
            return character
    return None

def build_clarification_tree(
    question: str,
    character: str,
    knowledge_base: Dict,
    templates: Dict[str, Dict[str, str]]
) -> nx.DiGraph:
    """
    构建澄清树的核心函数。
    """
    G = nx.DiGraph()
    root_id = "root"
    G.add_node(root_id, text=question, depth=0)
    main_aspects = identify_main_aspects(question)
    for aspect in main_aspects:
        if aspect not in knowledge_base:
            continue
        node_id = f"{aspect}_root"
        G.add_node(node_id, text=templates[aspect]["root"], depth=1)
        G.add_edge(root_id, node_id)
        for sub_aspect, sub_data in knowledge_base[aspect].items():
            sub_node_id = f"{aspect}_{sub_aspect}"
            template_text = templates[aspect].get(
                sub_aspect, f"关于{sub_aspect}，您想了解什么？"
            )
            G.add_node(sub_node_id, text=template_text, depth=2)
            G.add_edge(node_id, sub_node_id)
            for detail_type, details in sub_data.items():
                detail_node_id = f"{aspect}_{sub_aspect}_{detail_type}"
                detail_text = f"您想了解{detail_type}相关的：{', '.join(details)}？"
                G.add_node(detail_node_id, text=detail_text, depth=3, answers=details)
                G.add_edge(sub_node_id, detail_node_id)
    return G

def visualize_tree(G: nx.DiGraph, figsize=(15, 10)):
    """
    对澄清树进行可视化展示，不同层级节点用不同颜色区分。
    """
    plt.figure(figsize=figsize)
    pos = nx.spring_layout(G, k=2, iterations=50)
    depths = nx.get_node_attributes(G, 'depth')
    colors = ['lightblue', 'lightgreen', 'lightyellow', 'lightpink']
    for depth in range(max(depths.values()) + 1):
        nodes_at_depth = [node for node, d in depths.items() if d == depth]
        nx.draw_networkx_nodes(G, pos,
                             nodelist=nodes_at_depth,
                             node_color=colors[depth],
                             node_size=2000)
    nx.draw_networkx_edges(G, pos, edge_color='gray', arrows=True)
    labels = nx.get_node_attributes(G, 'text')
    nx.draw_networkx_labels(G, pos, labels, font_size=8, font_weight='bold')
    plt.title("问题澄清树")
    plt.axis('off')
    plt.tight_layout()
    plt.show()

def extract_clarification_paths(G: nx.DiGraph) -> List[List[str]]:
    """
    提取澄清树中所有从根节点到叶子节点的路径（每条路径代表一次澄清链路）。
    """
    root = "root"
    leaves = [n for n in G.nodes if G.out_degree(n) == 0]
    paths = []
    for leaf in leaves:
        for path in nx.all_simple_paths(G, source=root, target=leaf):
            paths.append(path)
    return paths

def get_path_texts(G: nx.DiGraph, path: List[str]) -> List[str]:
    """
    获取路径上每个节点的text内容。
    """
    return [G.nodes[n]['text'] for n in path]

def answer_with_llm(question: str, clarification_path_texts: List[str], knowledge_base: Dict, model_client) -> str:
    """
    基于澄清树路径和知识库，调用大模型生成最终回答。
    """
    # 这里可以根据clarification_path_texts和知识库，构建prompt
    context = []
    # 简单拼接clarification_path_texts作为澄清过程
    context.append("澄清过程：\n" + "\n".join(clarification_path_texts))
    # 可以根据路径最后的节点，查找相关知识
    # 这里只做简单示例，实际可根据需要更复杂地组织知识
    context.append("知识库摘要：")
    # 这里只取abilities和relationships的内容简单拼接
    for k, v in knowledge_base.items():
        context.append(f"{k}: {v}")
    prompt = f"""你是一个智能问答助手。请根据以下用户问题、澄清过程和知识库内容，给出简明、准确的中文回答。

用户问题：{question}
{chr(10).join(context)}

请直接给出最终答案，不要加多余说明。"""

    response = model_client.invoke(prompt)
    return response.content

def process_question_with_llm(question: str, model_client):
    """
    新流程：基于澄清树和大模型自动回答用户问题
    """
    kb = create_knowledge_base()
    characters = ["孙悟空", "杨戬", "猪八戒"]
    character = identify_character(question, characters)
    if not character:
        return "未能识别问题中的角色"
    templates = create_question_templates(character)
    tree = build_clarification_tree(question, character, kb, templates)
    # 可视化澄清树（可选）
    #visualize_tree(tree)
    # 提取所有澄清路径
    paths = extract_clarification_paths(tree)
    # 这里只取第一条路径做示例，实际可根据用户交互选择路径
    if not paths:
        return "未能生成澄清路径"
    path_texts = get_path_texts(tree, paths[0])
    # 调用大模型生成答案
    answer = answer_with_llm(question, path_texts, kb, model_client)
    return answer

# 示例流程演示
if __name__ == "__main__":
    # 初始化大模型客户端（以OpenAI为例，需替换为你自己的API Key和Base URL）
    client = ChatOpenAI(
        model="gpt-4o-mini",
        temperature=0.6,
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",  # 请替换为你的API Key
        base_url="https://api.zhizengzeng.com/v1"
    )
    question = "孙悟空有什么能力？"
    answer = process_question_with_llm(question, client)
    print("最终回答：", answer)

"""
详细流程说明：

1. 用户输入一个关于角色的问题（如“孙悟空有什么能力？”）。
2. 系统首先通过identify_character识别出问题涉及的角色（如“孙悟空”）。
3. 然后通过identify_main_aspects分析问题关注的主题（如“abilities”）。
4. 基于角色和主题，create_question_templates生成澄清问题的模板。
5. build_clarification_tree根据知识库和模板，逐层构建澄清树。
6. 提取澄清树的路径，作为澄清链路。
7. 结合澄清链路和知识库内容，构建prompt，调用大模型生成最终答案。
8. 返回大模型的回答。
"""
