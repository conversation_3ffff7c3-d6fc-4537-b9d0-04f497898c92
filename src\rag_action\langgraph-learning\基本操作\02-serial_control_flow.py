"""
02 - 串行控制流
展示多个节点按顺序执行的串行控制流
"""

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from typing import Dict, List, Any, TypedDict, Annotated

# 创建 LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0
)

# 定义状态类型
class SerialState(TypedDict):
    messages: Annotated[List, add_messages]  # 消息历史
    user_input: str  # 用户输入
    analysis: str  # 分析结果
    answer: str  # 最终回答
    steps: List[str]  # 执行步骤

def serial_workflow():
    """串行工作流：分析 -> 生成 -> 优化"""
    
    print("=== 串行控制流 ===")
    print("流程：用户输入 -> 分析 -> 生成回答 -> 优化 -> 输出")
    print()
    
    def analyze_node(state: SerialState) -> SerialState:
        """分析节点：分析用户输入"""
        user_input = state["user_input"]
        
        print(f"步骤1: 分析用户输入...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
请分析以下用户输入，判断用户的需求类型：

用户输入：{user_input}

请分析：
1. 用户想要什么？
2. 这是什么类型的问题？
3. 需要什么深度的回答？

请用简洁的语言回答：
""")
        ])
        
        analysis = response.content
        
        return {
            **state,
            "analysis": analysis,
            "steps": state["steps"] + ["分析完成"],
            "messages": [AIMessage(content=f"分析结果：{analysis}")]
        }
    
    def generate_node(state: SerialState) -> SerialState:
        """生成节点：基于分析生成回答"""
        user_input = state["user_input"]
        analysis = state["analysis"]
        
        print(f"步骤2: 生成初步回答...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
基于以下分析，为用户生成回答：

用户输入：{user_input}
分析结果：{analysis}

请生成一个初步的回答：
""")
        ])
        
        answer = response.content
        
        return {
            **state,
            "answer": answer,
            "steps": state["steps"] + ["生成完成"],
            "messages": [AIMessage(content=f"初步回答：{answer}")]
        }
    
    def optimize_node(state: SerialState) -> SerialState:
        """优化节点：优化最终回答"""
        user_input = state["user_input"]
        answer = state["answer"]
        
        print(f"步骤3: 优化回答...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
请优化以下回答，使其更加清晰、准确、有用：

原始问题：{user_input}
初步回答：{answer}

请提供优化后的最终回答：
""")
        ])
        
        optimized_answer = response.content
        
        return {
            **state,
            "answer": optimized_answer,
            "steps": state["steps"] + ["优化完成"],
            "messages": [AIMessage(content=f"最终回答：{optimized_answer}")]
        }
    
    # 创建图
    workflow = StateGraph(SerialState)
    
    # 添加节点
    workflow.add_node("analyze", analyze_node)
    workflow.add_node("generate", generate_node)
    workflow.add_node("optimize", optimize_node)
    
    # 设置入口点
    workflow.set_entry_point("analyze")
    
    # 添加串行边：analyze -> generate -> optimize -> END
    workflow.add_edge("analyze", "generate")
    workflow.add_edge("generate", "optimize")
    workflow.add_edge("optimize", END)
    
    # 编译图
    app = workflow.compile()
    
    # 测试
    test_inputs = [
        "请解释什么是人工智能",
        "如何学习编程？",
        "推荐一些好的学习方法"
    ]
    
    for i, user_input in enumerate(test_inputs, 1):
        print(f"\n测试 {i}: {user_input}")
        print("=" * 60)
        
        # 运行图
        result = app.invoke({
            "user_input": user_input,
            "messages": [],
            "analysis": "",
            "answer": "",
            "steps": []
        })
        
        print(f"\n执行步骤: {' -> '.join(result['steps'])}")
        print(f"最终回答: {result['answer']}")
        print("=" * 60)

def main():
    """主函数"""
    serial_workflow()

if __name__ == "__main__":
    main() 