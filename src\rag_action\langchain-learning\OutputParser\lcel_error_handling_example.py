"""
LCEL + 输出容错机制示例
结合 LangChain Expression Language 和错误处理
"""

from typing import List, Optional, Union
from pydantic import BaseModel, Field, ConfigDict
from langchain.output_parsers import PydanticOutputParser, OutputFixingParser, RetryOutputParser
from langchain.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from langchain_core.runnables import RunnablePassthrough, RunnableLambda
from langchain_core.output_parsers import BaseOutputParser

class UserProfile(BaseModel):
    """用户档案模型"""
    
    model_config = ConfigDict(
        extra="forbid",
        str_strip_whitespace=True,
    )
    
    name: str = Field(description="用户姓名")
    age: int = Field(description="用户年龄", ge=0, le=150)
    email: str = Field(description="邮箱地址")
    interests: List[str] = Field(description="兴趣爱好列表")
    bio: Optional[str] = Field(description="个人简介", default=None)

def extract_content(message):
    """提取消息内容"""
    if hasattr(message, 'content'):
        return message.content
    return str(message)

def basic_lcel_with_error_handling():
    """基本的 LCEL 错误处理示例"""
    
    print("=== 基本 LCEL 错误处理示例 ===")
    
    # 创建 LLM
    llm = ChatOpenAI(
        temperature=0.7,  # 提高温度增加随机性
        base_url="https://api.zhizengzeng.com/v1",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
    )
    
    # 创建解析器
    parser = PydanticOutputParser(pydantic_object=UserProfile)
    fixing_parser = OutputFixingParser.from_llm(parser=parser, llm=llm)
    
    # 创建提示模板
    prompt = PromptTemplate(
        template="请根据以下信息创建用户档案：\n{format_instructions}\n\n用户信息：{user_info}",
        input_variables=["user_info"],
        partial_variables={"format_instructions": parser.get_format_instructions()}
    )
    
    # 定义错误处理函数
    def robust_parse(message):
        """健壮的解析函数"""
        text = extract_content(message)
        print(f"解析文本: {text[:100]}...")  # 只显示前100个字符
        
        try:
            return parser.parse(text)
        except Exception as e1:
            print(f"原始解析失败: {e1}")
            try:
                return fixing_parser.parse(text)
            except Exception as e2:
                print(f"修复解析失败: {e2}")
                # 返回默认值
                return UserProfile(
                    name="解析失败",
                    age=0,
                    email="<EMAIL>",
                    interests=["未知"],
                    bio="解析失败"
                )
    
    # 构建 LCEL 链
    chain = prompt | llm | RunnableLambda(robust_parse)
    
    # 示例用户信息
    user_info = """
    姓名：张三
    年龄：28岁
    邮箱：<EMAIL>
    个人简介：热爱技术的软件工程师
    兴趣爱好：编程、阅读、游泳
    """
    
    # 执行链
    try:
        result = chain.invoke({"user_info": user_info})
        print("LCEL 链执行成功:")
        print(f"姓名: {result.name}")
        print(f"年龄: {result.age}")
        print(f"邮箱: {result.email}")
        print(f"兴趣爱好: {result.interests}")
        print(f"个人简介: {result.bio}")
        
    except Exception as e:
        print(f"LCEL 链执行失败: {e}")

def advanced_lcel_with_fallback():
    """高级 LCEL 回退机制示例"""
    
    print("\n=== 高级 LCEL 回退机制示例 ===")
    
    # 创建 LLM
    llm = ChatOpenAI(
        temperature=0,
        base_url="https://api.zhizengzeng.com/v1",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
    )
    
    # 创建解析器
    parser = PydanticOutputParser(pydantic_object=UserProfile)
    fixing_parser = OutputFixingParser.from_llm(parser=parser, llm=llm)
    retry_parser = RetryOutputParser.from_llm(parser=parser, llm=llm)
    
    # 创建提示模板
    prompt = PromptTemplate(
        template="请根据以下信息创建用户档案：\n{format_instructions}\n\n用户信息：{user_info}",
        input_variables=["user_info"],
        partial_variables={"format_instructions": parser.get_format_instructions()}
    )
    
    # 定义回退函数
    def fallback_parser(input_dict):
        """回退解析器"""
        message = input_dict["message"]
        prompt_value = input_dict["prompt_value"]
        text = extract_content(message)
        
        print(f"解析文本: {text[:100]}...")
        
        try:
            return parser.parse(text)
        except Exception as e1:
            print(f"原始解析失败: {e1}")
            
            try:
                return fixing_parser.parse(text)
            except Exception as e2:
                print(f"修复解析失败: {e2}")
                
                try:
                    return retry_parser.parse_with_prompt(text, prompt_value)
                except Exception as e3:
                    print(f"重试解析失败: {e3}")
                    # 返回默认值
                    return UserProfile(
                        name="未知用户",
                        age=0,
                        email="<EMAIL>",
                        interests=["未知"],
                        bio="解析失败"
                    )
    
    # 构建复杂的 LCEL 链
    chain = (
        {"user_info": RunnablePassthrough()} 
        | prompt 
        | llm 
        | {"message": RunnablePassthrough(), "prompt_value": prompt} 
        | RunnableLambda(fallback_parser)
    )
    
    # 示例用户信息
    user_info = """
    姓名：李四
    年龄：25岁
    邮箱：<EMAIL>
    个人简介：音乐爱好者
    兴趣爱好：音乐、电影
    """
    
    # 执行链
    try:
        result = chain.invoke(user_info)
        print("高级 LCEL 链执行成功:")
        print(f"姓名: {result.name}")
        print(f"年龄: {result.age}")
        print(f"邮箱: {result.email}")
        print(f"兴趣爱好: {result.interests}")
        print(f"个人简介: {result.bio}")
        
    except Exception as e:
        print(f"高级 LCEL 链执行失败: {e}")

def streaming_lcel_with_error_handling():
    """流式 LCEL 错误处理示例"""
    
    print("\n=== 流式 LCEL 错误处理示例 ===")
    
    # 创建 LLM
    llm = ChatOpenAI(
        temperature=0,
        base_url="https://api.zhizengzeng.com/v1",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
    )
    
    # 创建解析器
    parser = PydanticOutputParser(pydantic_object=UserProfile)
    fixing_parser = OutputFixingParser.from_llm(parser=parser, llm=llm)
    
    # 创建提示模板
    prompt = PromptTemplate(
        template="请根据以下信息创建用户档案：\n{format_instructions}\n\n用户信息：{user_info}",
        input_variables=["user_info"],
        partial_variables={"format_instructions": parser.get_format_instructions()}
    )
    
    # 定义流式解析函数
    def stream_parser(input_dict):
        """流式解析器"""
        message = input_dict["message"]
        text = extract_content(message)
        
        try:
            result = parser.parse(text)
            return {"status": "success", "data": result}
        except Exception as e:
            try:
                result = fixing_parser.parse(text)
                return {"status": "fixed", "data": result}
            except Exception as e2:
                return {"status": "error", "error": str(e2)}
    
    # 构建流式 LCEL 链
    chain = (
        {"user_info": RunnablePassthrough()} 
        | prompt 
        | llm 
        | {"message": RunnablePassthrough()} 
        | RunnableLambda(stream_parser)
    )
    
    # 示例用户信息
    user_info = """
    姓名：王五
    年龄：30岁
    邮箱：<EMAIL>
    个人简介：摄影师
    兴趣爱好：摄影、旅行
    """
    
    # 流式执行
    print("开始流式处理...")
    try:
        for chunk in chain.stream(user_info):
            print(f"处理中: {chunk}")
        
        print("流式处理完成")
        
    except Exception as e:
        print(f"流式处理失败: {e}")

def conditional_lcel_chain():
    """条件 LCEL 链示例"""
    
    print("\n=== 条件 LCEL 链示例 ===")
    
    # 创建 LLM
    llm = ChatOpenAI(
        temperature=0,
        base_url="https://api.zhizengzeng.com/v1",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
    )
    
    # 创建解析器
    parser = PydanticOutputParser(pydantic_object=UserProfile)
    fixing_parser = OutputFixingParser.from_llm(parser=parser, llm=llm)
    
    # 创建提示模板
    prompt = PromptTemplate(
        template="请根据以下信息创建用户档案：\n{format_instructions}\n\n用户信息：{user_info}",
        input_variables=["user_info"],
        partial_variables={"format_instructions": parser.get_format_instructions()}
    )
    
    # 定义条件解析函数
    def conditional_parser(input_dict):
        """条件解析器"""
        message = input_dict["message"]
        confidence = input_dict.get("confidence", 0.5)
        text = extract_content(message)
        
        print(f"解析文本: {text[:100]}...")
        
        # 根据置信度选择解析策略
        if confidence > 0.8:
            try:
                return parser.parse(text)
            except Exception as e:
                print(f"高置信度解析失败: {e}")
                return fixing_parser.parse(text)
        else:
            try:
                return fixing_parser.parse(text)
            except Exception as e:
                print(f"低置信度解析失败: {e}")
                return parser.parse(text)
    
    # 构建条件 LCEL 链
    chain = (
        {"user_info": RunnablePassthrough()} 
        | prompt 
        | llm 
        | {"message": RunnablePassthrough(), "confidence": lambda x: 0.7} 
        | RunnableLambda(conditional_parser)
    )
    
    # 示例用户信息
    user_info = """
    姓名：赵六
    年龄：27岁
    邮箱：<EMAIL>
    个人简介：健身教练
    兴趣爱好：健身、烹饪
    """
    
    # 执行链
    try:
        result = chain.invoke(user_info)
        print("条件 LCEL 链执行成功:")
        print(f"姓名: {result.name}")
        print(f"年龄: {result.age}")
        print(f"邮箱: {result.email}")
        print(f"兴趣爱好: {result.interests}")
        print(f"个人简介: {result.bio}")
        
    except Exception as e:
        print(f"条件 LCEL 链执行失败: {e}")

def main():
    """主函数"""
    basic_lcel_with_error_handling()
    advanced_lcel_with_fallback()
    streaming_lcel_with_error_handling()
    conditional_lcel_chain()

if __name__ == "__main__":
    main() 