from pymilvus import MilvusClient, DataType
import random

"""
IVF_PQ（倒排文件+乘积量化索引，Inverted File with Product Quantization）是一种高效的近似向量检索索引类型，适用于大规模向量数据的 ANN（近似最近邻）搜索。

1. 原理简介：
   - 首先将所有向量通过聚类（如K-means）划分为 nlist 个簇（cluster），每个簇对应一个倒排桶（inverted list）。
   - 每个桶内的向量不再直接存储原始向量，而是用 PQ（Product Quantization，乘积量化）方法对向量进行压缩编码，大幅降低存储和计算成本。
   - PQ 会将原始向量分割为 m 个子向量，每个子向量用 nbits 位编码，极大减少内存占用。

2. 优点：
   - 存储效率高：PQ 压缩后，内存占用远小于原始向量或 IVF_FLAT。
   - 检索速度快：只需在部分簇内用压缩码做近似距离计算，速度远超暴力搜索。
   - 适合超大规模数据：百万、千万甚至上亿级向量的近似检索。

3. 缺点：
   - 检索为近似结果，精度略低于 IVF_FLAT，但可通过调参权衡速度与精度。
   - 参数较多（nlist, m, nbits），需结合数据量和业务需求合理设置。

4. 主要参数说明：
   - nlist：聚类中心数量，影响索引构建和检索速度/精度。
   - m：将向量分割为 m 个子向量，m 越大压缩越强，但可能损失精度。
   - nbits：每个子向量编码位数，常用 8 位。
   - nprobe：检索时实际遍历的簇数，影响召回率和速度（在 search 时设置）。

5. 适用场景：
   - 超大规模向量数据的高效 ANN 检索。
   - 对存储和检索速度有较高要求，允许一定精度损失的场景。

总结：IVF_PQ 是一种兼顾存储效率和检索速度的近似向量索引，适合大规模、资源受限场景下的向量检索任务。
"""

# 1. 设置 Milvus 客户端
client = MilvusClient(uri="http://localhost:19530")
COLLECTION_NAME = "flat_index_demo"

# 如果集合已存在，则删除
if client.has_collection(COLLECTION_NAME):
    client.drop_collection(COLLECTION_NAME)

# 2. 创建 schema
schema = MilvusClient.create_schema(auto_id=False, enable_dynamic_field=True)
schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True)
schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=128)

# 3. 创建集合
client.create_collection(collection_name=COLLECTION_NAME, schema=schema)

# 4. 插入随机向量数据
num_vectors = 1000
vectors = [[random.random() for _ in range(128)] for _ in range(num_vectors)]
ids = list(range(num_vectors))
entities = [{"id": ids[i], "vector": vectors[i]} for i in range(num_vectors)]

client.insert(collection_name=COLLECTION_NAME, data=entities)
# flush 保证数据落盘
client.flush(COLLECTION_NAME)

# 5. 创建索引（此时集合中已有数据）
index_params = MilvusClient.prepare_index_params()
index_params.add_index(
    field_name="vector",
    metric_type="L2",
    index_type="IVF_PQ",
    index_name="vector_index",
    params={
        "nlist": 64,  # 聚类中心数量，通常设置为 4*sqrt(n)，n 为向量数量
        "m": 32,      # 向量被分割的子向量数量，通常为 dim/m >= 2，这里 128/32=4
        "nbits": 8    # 每个子向量的编码位数，通常为 8 位
    }
)
client.create_index(
    collection_name=COLLECTION_NAME,
    index_params=index_params,
    sync=True
)

# 验证索引
print("索引列表:", client.list_indexes(collection_name=COLLECTION_NAME))
print("索引详情:", client.describe_index(
    collection_name=COLLECTION_NAME,
    index_name="vector_index"
))

# 6. load 后再搜索
client.load_collection(collection_name=COLLECTION_NAME)
search_vectors = [[random.random() for _ in range(128)]]
results = client.search(
    collection_name=COLLECTION_NAME,
    data=search_vectors,
    ann_field="vector",
    limit=5,
    output_fields=["id"],
    search_params={
        "params": {
            "nprobe": 10  # 设置搜索时检查的聚类数量
        }
    }
)

print("\n搜索结果:")
for hits in results:
    for hit in hits:
        # 注意用 dict 方式访问
        print(f"ID: {hit['id']}, 距离: {hit['distance']}")

# 清理
client.release_collection(collection_name=COLLECTION_NAME)
# client.disconnect()
