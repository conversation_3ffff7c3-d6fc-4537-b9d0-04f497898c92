"""
流程说明：
本脚本演示了如何使用LangChain的ContextualCompressionRetriever结合BM25检索器和Cohere重排序器，实现“检索-重排-压缩”一体化的高效文档检索流程。

主要步骤如下：
1. 加载依赖库和环境变量（如API Key）。
2. 构建原始文档集合。
3. 使用BM25算法进行初步检索，获取与查询相关的候选文档。
4. 利用Cohere的重排序模型对候选文档进行语义重排，提升相关性排序的准确性。
5. 通过ContextualCompressionRetriever将检索和重排压缩为一步，最终输出高质量的精简文档结果。
"""

# 1. 导入所需的库
from langchain_cohere import CohereRerank
from langchain.retrievers.contextual_compression import ContextualCompressionRetriever
from langchain_core.documents import Document
from langchain_community.retrievers import BM25Retriever
from dotenv import load_dotenv
import os

# 2. 加载环境变量（如.env文件中的API Key）
load_dotenv()

# 3. 设置Cohere API Key（可在.env文件或此处直接设置）
# Cohere API Key获取地址：https://dashboard.cohere.com/api-keys
api_key = 'VBKB2itaGQEUA81LesMqtQ1uCxLSjUmv2prXLzGs'
os.environ['CO_API_KEY'] = api_key

# 4. 构建原始文档集合，每个Document包含内容和元数据
documents = [
    Document(
        page_content="五台山是中国四大佛教名山之一，以文殊菩萨道场闻名。",
        metadata={"source": "山西旅游指南"}
    ),
    Document(
        page_content="云冈石窟是中国三大石窟之一，以精美的佛教雕塑著称。",
        metadata={"source": "山西旅游指南"}
    ),
    Document(
        page_content="平遥古城是中国保存最完整的古代县城之一，被列为世界文化遗产。",
        metadata={"source": "山西旅游指南"}
    )
]

# 5. 创建BM25检索器，对文档进行初步相关性检索
retriever = BM25Retriever.from_documents(documents)
retriever.k = 3  # 设置返回前3个相关文档

# 6. 配置Cohere重排序器（Reranker），提升相关性排序
# 模型地址: https://huggingface.co/Cohere/rerank-multilingual-v3.0
compressor = CohereRerank(model="rerank-multilingual-v3.0")

# 7. 构建ContextualCompressionRetriever，实现检索+重排+压缩一体化
# 
# 这里的“压缩”指的是：在初步检索（如BM25）返回多个相关文档后，利用Cohere重排序器（compressor）对这些文档进行语义重排和筛选，
# 只保留与查询最相关、最有信息量的部分，去除冗余或无关内容，从而“压缩”文档集合，提升最终输出的质量和精炼度。
# 具体过程如下：
#   1. base_retriever（如BM25）先检索出一批候选文档；
#   2. base_compressor（如CohereRerank）对这些文档进行语义重排和筛选，自动过滤掉相关性较低的内容；
#   3. ContextualCompressionRetriever将上述两步封装为一步，用户只需调用invoke即可获得“检索+重排+压缩”后的高质量文档结果。
compression_retriever = ContextualCompressionRetriever(
    base_compressor=compressor,   # 负责重排和压缩（即“压缩”操作的核心）
    base_retriever=retriever      # 负责初步检索
)

# 8. 执行查询，自动完成检索、重排和压缩
query = "山西有哪些著名的旅游景点？"
compressed_docs = compression_retriever.invoke(query)

# 9. 输出最终的重排与压缩结果
print(f"查询：{query}\n")
print("重排并压缩后的结果：")
for i, doc in enumerate(compressed_docs, 1):
    print(f"{i}. {doc.page_content}")
