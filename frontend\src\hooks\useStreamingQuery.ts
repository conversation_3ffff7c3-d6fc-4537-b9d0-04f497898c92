import { useState, useCallback, useRef } from 'react'
import { queryApi } from '@/api/modules/query'
import type { QueryResponse, StreamingQueryRequest, StreamingCallbacks } from '@/types'

export interface StreamingState {
  isStreaming: boolean
  currentStage: string
  stageMessage: string
  answer: string
  fullResponse: QueryResponse | null
  error: string | null
}

export interface UseStreamingQueryReturn {
  state: StreamingState
  startStreaming: (params: StreamingQueryRequest, method?: 'GET' | 'POST') => void
  stopStreaming: () => void
  clearState: () => void
}

/**
 * 流式问答Hook
 * 提供流式问答的状态管理和控制功能
 */
export function useStreamingQuery(): UseStreamingQueryReturn {
  const [state, setState] = useState<StreamingState>({
    isStreaming: false,
    currentStage: '',
    stageMessage: '',
    answer: '',
    fullResponse: null,
    error: null
  })

  const eventSourceRef = useRef<EventSource | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  const clearState = useCallback(() => {
    setState({
      isStreaming: false,
      currentStage: '',
      stageMessage: '',
      answer: '',
      fullResponse: null,
      error: null
    })
  }, [])

  const stopStreaming = useCallback(() => {
    // 关闭EventSource连接
    if (eventSourceRef.current) {
      eventSourceRef.current.close()
      eventSourceRef.current = null
    }

    // 取消fetch请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      abortControllerRef.current = null
    }

    setState(prev => ({
      ...prev,
      isStreaming: false
    }))
  }, [])

  const startStreaming = useCallback((params: StreamingQueryRequest, method: 'GET' | 'POST' = 'GET') => {
    // 清理之前的连接
    stopStreaming()
    clearState()

    setState(prev => ({
      ...prev,
      isStreaming: true,
      error: null
    }))

    const callbacks: StreamingCallbacks = {
      onProgress: (stage: string, message: string) => {
        setState(prev => ({
          ...prev,
          currentStage: stage,
          stageMessage: message
        }))
      },

      onChunk: (chunk: string) => {
        setState(prev => ({
          ...prev,
          answer: prev.answer + chunk
        }))
      },

      onComplete: (response: QueryResponse) => {
        setState(prev => ({
          ...prev,
          isStreaming: false,
          fullResponse: response,
          answer: response.answer
        }))
      },

      onError: (error: any) => {
        console.error('流式问答错误:', error)
        setState(prev => ({
          ...prev,
          isStreaming: false,
          error: error.message || '流式问答失败'
        }))
      }
    }

    if (method === 'GET') {
      // 使用GET方式的流式问答
      queryApi.intelligentQueryStream({
        query: params.message,
        conversation_id: params.conversation_id
      }, callbacks)
    } else {
      // 使用POST方式的流式问答
      queryApi.intelligentQueryStreamPost(params, callbacks)
    }
  }, [stopStreaming, clearState])

  return {
    state,
    startStreaming,
    stopStreaming,
    clearState
  }
}

/**
 * 简化版流式问答Hook
 * 只提供基本的流式问答功能
 */
export function useSimpleStreamingQuery() {
  const [isStreaming, setIsStreaming] = useState(false)
  const [answer, setAnswer] = useState('')
  const [error, setError] = useState<string | null>(null)

  const query = useCallback(async (question: string, conversationId?: string) => {
    setIsStreaming(true)
    setAnswer('')
    setError(null)

    try {
      queryApi.intelligentQueryStream(
        { query: question, conversation_id: conversationId },
        {
          onChunk: (chunk: string) => {
            setAnswer(prev => prev + chunk)
          },
          onComplete: () => {
            setIsStreaming(false)
          },
          onError: (err: any) => {
            setError(err.message || '查询失败')
            setIsStreaming(false)
          }
        }
      )
    } catch (err: any) {
      setError(err.message || '查询失败')
      setIsStreaming(false)
    }
  }, [])

  return {
    isStreaming,
    answer,
    error,
    query,
    clearAnswer: () => setAnswer(''),
    clearError: () => setError(null)
  }
}

/**
 * 流式问答进度指示器Hook
 */
export function useStreamingProgress() {
  const [progress, setProgress] = useState({
    stage: '',
    message: '',
    isActive: false
  })

  const updateProgress = useCallback((stage: string, message: string) => {
    setProgress({
      stage,
      message,
      isActive: true
    })
  }, [])

  const clearProgress = useCallback(() => {
    setProgress({
      stage: '',
      message: '',
      isActive: false
    })
  }, [])

  const getProgressIcon = useCallback((stage: string) => {
    switch (stage) {
      case 'retrieval':
        return '🔍'
      case 'generation':
        return '🤖'
      case 'database':
        return '🗄️'
      default:
        return '⏳'
    }
  }, [])

  const getProgressColor = useCallback((stage: string) => {
    switch (stage) {
      case 'retrieval':
        return '#1890ff'
      case 'generation':
        return '#52c41a'
      case 'database':
        return '#722ed1'
      default:
        return '#faad14'
    }
  }, [])

  return {
    progress,
    updateProgress,
    clearProgress,
    getProgressIcon,
    getProgressColor
  }
}

/**
 * 流式问答性能监控Hook
 */
export function useStreamingPerformance() {
  const [metrics, setMetrics] = useState({
    startTime: 0,
    firstChunkTime: 0,
    endTime: 0,
    totalChunks: 0,
    totalCharacters: 0
  })

  const startMonitoring = useCallback(() => {
    const startTime = Date.now()
    setMetrics({
      startTime,
      firstChunkTime: 0,
      endTime: 0,
      totalChunks: 0,
      totalCharacters: 0
    })
  }, [])

  const recordFirstChunk = useCallback(() => {
    setMetrics(prev => ({
      ...prev,
      firstChunkTime: prev.firstChunkTime === 0 ? Date.now() : prev.firstChunkTime
    }))
  }, [])

  const recordChunk = useCallback((chunkLength: number) => {
    setMetrics(prev => ({
      ...prev,
      totalChunks: prev.totalChunks + 1,
      totalCharacters: prev.totalCharacters + chunkLength
    }))
  }, [])

  const endMonitoring = useCallback(() => {
    setMetrics(prev => ({
      ...prev,
      endTime: Date.now()
    }))
  }, [])

  const getPerformanceReport = useCallback(() => {
    const { startTime, firstChunkTime, endTime, totalChunks, totalCharacters } = metrics
    
    if (startTime === 0) return null

    return {
      timeToFirstChunk: firstChunkTime > 0 ? firstChunkTime - startTime : 0,
      totalTime: endTime > 0 ? endTime - startTime : Date.now() - startTime,
      averageChunkSize: totalChunks > 0 ? totalCharacters / totalChunks : 0,
      charactersPerSecond: endTime > 0 ? totalCharacters / ((endTime - startTime) / 1000) : 0,
      totalChunks,
      totalCharacters
    }
  }, [metrics])

  return {
    startMonitoring,
    recordFirstChunk,
    recordChunk,
    endMonitoring,
    getPerformanceReport
  }
}
