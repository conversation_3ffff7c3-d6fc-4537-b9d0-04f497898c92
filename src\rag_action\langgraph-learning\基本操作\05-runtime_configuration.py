"""
05 - 图的运行时配置
展示如何在运行时动态配置和修改 LangGraph 图
"""

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from typing import Dict, List, Any, TypedDict, Annotated, Optional
import json
import asyncio
from datetime import datetime

# 创建 LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0
)

# 定义状态类型
class ConfigState(TypedDict):
    messages: Annotated[List, add_messages]  # 消息历史
    user_input: str  # 用户输入
    config: Dict[str, Any]  # 运行时配置
    result: str  # 处理结果
    execution_path: List[str]  # 执行路径

def dynamic_config_example():
    """动态配置示例：根据配置动态调整处理流程"""
    
    print("=== 动态配置示例 ===")
    print("根据运行时配置动态调整处理流程")
    print()
    
    def config_loader_node(state: ConfigState) -> ConfigState:
        """配置加载节点：根据用户输入加载配置"""
        user_input = state["user_input"]
        
        print(f"步骤1: 加载运行时配置...")
        
        # 根据用户输入动态生成配置
        response = llm.invoke([
            HumanMessage(content=f"""
请分析以下用户输入，生成处理配置：

用户输入：{user_input}

请返回JSON格式的配置，包含以下字段：
{{
    "detail_level": "simple|medium|detailed",
    "response_style": "formal|casual|technical",
    "max_iterations": 1-5,
    "include_examples": true|false,
    "include_resources": true|false
}}

请只返回JSON配置：
""")
        ])
        
        try:
            config = json.loads(response.content)
        except:
            # 默认配置
            config = {
                "detail_level": "medium",
                "response_style": "casual",
                "max_iterations": 2,
                "include_examples": True,
                "include_resources": False
            }
        
        return {
            **state,
            "config": config,
            "execution_path": state["execution_path"] + ["配置加载完成"],
            "messages": [AIMessage(content=f"配置：{json.dumps(config, ensure_ascii=False)}")]
        }
    
    def adaptive_process_node(state: ConfigState) -> ConfigState:
        """自适应处理节点：根据配置调整处理方式"""
        user_input = state["user_input"]
        config = state["config"]
        
        print(f"步骤2: 自适应处理 (配置: {config['detail_level']}, {config['response_style']})...")
        
        # 根据配置构建提示词
        detail_prompts = {
            "simple": "请提供简洁的回答",
            "medium": "请提供详细的回答",
            "detailed": "请提供非常详细的回答，包含深入分析"
        }
        
        style_prompts = {
            "formal": "请使用正式的语言风格",
            "casual": "请使用轻松友好的语言风格",
            "technical": "请使用技术性的语言风格"
        }
        
        detail_prompt = detail_prompts.get(config["detail_level"], "请提供回答")
        style_prompt = style_prompts.get(config["response_style"], "请提供回答")
        
        response = llm.invoke([
            HumanMessage(content=f"""
{detail_prompt}，{style_prompt}：

问题：{user_input}

请根据配置要求提供回答：
""")
        ])
        
        answer = response.content
        
        return {
            **state,
            "result": answer,
            "execution_path": state["execution_path"] + ["自适应处理完成"],
            "messages": [AIMessage(content=f"处理结果：{answer}")]
        }
    
    def example_node(state: ConfigState) -> ConfigState:
        """示例节点：根据配置决定是否添加示例"""
        user_input = state["user_input"]
        current_result = state["result"]
        config = state["config"]
        
        if not config.get("include_examples", False):
            return {
                **state,
                "execution_path": state["execution_path"] + ["跳过示例"],
                "messages": [AIMessage(content="跳过示例添加")]
            }
        
        print(f"步骤3: 添加示例...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
请为以下回答添加相关示例：

原始问题：{user_input}
当前回答：{current_result}

请添加2-3个具体的示例来支持回答：
""")
        ])
        
        examples = response.content
        enhanced_result = f"{current_result}\n\n示例：\n{examples}"
        
        return {
            **state,
            "result": enhanced_result,
            "execution_path": state["execution_path"] + ["示例添加完成"],
            "messages": [AIMessage(content=f"添加示例：{examples}")]
        }
    
    def resource_node(state: ConfigState) -> ConfigState:
        """资源节点：根据配置决定是否添加资源"""
        user_input = state["user_input"]
        current_result = state["result"]
        config = state["config"]
        
        if not config.get("include_resources", False):
            return {
                **state,
                "execution_path": state["execution_path"] + ["跳过资源"],
                "messages": [AIMessage(content="跳过资源添加")]
            }
        
        print(f"步骤4: 添加资源...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
请为以下回答添加相关学习资源：

原始问题：{user_input}
当前回答：{current_result}

请推荐相关的学习资源（书籍、网站、课程等）：
""")
        ])
        
        resources = response.content
        enhanced_result = f"{current_result}\n\n推荐资源：\n{resources}"
        
        return {
            **state,
            "result": enhanced_result,
            "execution_path": state["execution_path"] + ["资源添加完成"],
            "messages": [AIMessage(content=f"推荐资源：{resources}")]
        }
    
    def optimize_node(state: ConfigState) -> ConfigState:
        """优化节点：根据配置进行多次优化"""
        user_input = state["user_input"]
        current_result = state["result"]
        config = state["config"]
        max_iterations = config.get("max_iterations", 1)
        
        # 检查当前迭代次数
        current_iterations = len([step for step in state["execution_path"] if "优化" in step])
        
        if current_iterations >= max_iterations:
            return {
                **state,
                "execution_path": state["execution_path"] + ["优化完成"],
                "messages": [AIMessage(content="优化次数已达上限")]
            }
        
        print(f"步骤5: 优化回答 (第{current_iterations + 1}次)...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
请优化以下回答：

原始问题：{user_input}
当前回答：{current_result}

请根据配置要求进行优化：
- 详细程度：{config['detail_level']}
- 语言风格：{config['response_style']}

请提供优化后的回答：
""")
        ])
        
        optimized_result = response.content
        
        return {
            **state,
            "result": optimized_result,
            "execution_path": state["execution_path"] + [f"优化{current_iterations + 1}完成"],
            "messages": [AIMessage(content=f"优化结果：{optimized_result}")]
        }
    
    def should_continue_optimizing(state: ConfigState) -> str:
        """判断是否继续优化"""
        config = state["config"]
        max_iterations = config.get("max_iterations", 1)
        current_iterations = len([step for step in state["execution_path"] if "优化" in step])
        
        if current_iterations >= max_iterations:
            return "end"
        else:
            return "continue"
    
    # 创建图
    workflow = StateGraph(ConfigState)
    
    # 添加节点
    workflow.add_node("config_loader", config_loader_node)
    workflow.add_node("adaptive_process", adaptive_process_node)
    workflow.add_node("example", example_node)
    workflow.add_node("resource", resource_node)
    workflow.add_node("optimize", optimize_node)
    
    # 设置入口点
    workflow.set_entry_point("config_loader")
    
    # 添加边：config_loader -> adaptive_process -> example -> resource -> optimize
    workflow.add_edge("config_loader", "adaptive_process")
    workflow.add_edge("adaptive_process", "example")
    workflow.add_edge("example", "resource")
    workflow.add_edge("resource", "optimize")
    
    # 优化节点可以循环或结束
    workflow.add_conditional_edges(
        "optimize",
        should_continue_optimizing,
        {
            "continue": "optimize",
            "end": END
        }
    )
    
    # 编译图
    app = workflow.compile()
    
    # 测试不同配置
    test_cases = [
        {
            "input": "什么是Python？",
            "description": "简单问题，基础配置"
        },
        {
            "input": "请详细解释机器学习中的神经网络原理，并提供实际应用案例",
            "description": "复杂问题，详细配置"
        },
        {
            "input": "推荐一些学习编程的方法",
            "description": "学习问题，包含资源"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['description']}")
        print(f"输入: {test_case['input']}")
        print("=" * 60)
        
        # 运行图
        result = app.invoke({
            "user_input": test_case["input"],
            "messages": [],
            "config": {},
            "result": "",
            "execution_path": []
        })
        
        print(f"\n运行时配置: {json.dumps(result['config'], ensure_ascii=False, indent=2)}")
        print(f"执行路径: {' -> '.join(result['execution_path'])}")
        print(f"最终结果: {result['result']}")
        print("=" * 60)

def config_from_external_source():
    """从外部源加载配置示例"""
    
    print("\n=== 外部配置源示例 ===")
    print("从外部源（如数据库、API、文件）加载配置")
    print()
    
    # 模拟外部配置源
    external_configs = {
        "user_preferences": {
            "detail_level": "detailed",
            "response_style": "technical",
            "max_iterations": 3,
            "include_examples": True,
            "include_resources": True
        },
        "system_settings": {
            "detail_level": "medium",
            "response_style": "formal",
            "max_iterations": 2,
            "include_examples": False,
            "include_resources": True
        },
        "performance_mode": {
            "detail_level": "simple",
            "response_style": "casual",
            "max_iterations": 1,
            "include_examples": False,
            "include_resources": False
        }
    }
    
    def load_external_config_node(state: ConfigState) -> ConfigState:
        """从外部源加载配置"""
        user_input = state["user_input"]
        
        print(f"步骤1: 从外部源加载配置...")
        
        # 模拟根据用户输入选择配置源
        if "详细" in user_input or "深入" in user_input:
            config_source = "user_preferences"
        elif "系统" in user_input or "正式" in user_input:
            config_source = "system_settings"
        else:
            config_source = "performance_mode"
        
        config = external_configs[config_source]
        
        return {
            **state,
            "config": config,
            "execution_path": state["execution_path"] + [f"加载配置: {config_source}"],
            "messages": [AIMessage(content=f"外部配置: {json.dumps(config, ensure_ascii=False)}")]
        }
    
    def process_with_external_config_node(state: ConfigState) -> ConfigState:
        """使用外部配置处理"""
        user_input = state["user_input"]
        config = state["config"]
        
        print(f"步骤2: 使用外部配置处理...")
        
        response = llm.invoke([
            HumanMessage(content=f"""
请根据以下配置要求回答问题：

配置：{json.dumps(config, ensure_ascii=False)}

问题：{user_input}

请严格按照配置要求提供回答：
""")
        ])
        
        answer = response.content
        
        return {
            **state,
            "result": answer,
            "execution_path": state["execution_path"] + ["外部配置处理完成"],
            "messages": [AIMessage(content=f"处理结果：{answer}")]
        }
    
    # 创建图
    workflow = StateGraph(ConfigState)
    
    # 添加节点
    workflow.add_node("load_external_config", load_external_config_node)
    workflow.add_node("process_with_external_config", process_with_external_config_node)
    
    # 设置入口点和边
    workflow.set_entry_point("load_external_config")
    workflow.add_edge("load_external_config", "process_with_external_config")
    workflow.add_edge("process_with_external_config", END)
    
    # 编译图
    app = workflow.compile()
    
    # 测试外部配置
    test_inputs = [
        "请详细解释什么是人工智能",
        "系统性地介绍编程基础",
        "简单介绍一下Python"
    ]
    
    for i, user_input in enumerate(test_inputs, 1):
        print(f"\n测试 {i}: {user_input}")
        print("-" * 40)
        
        result = app.invoke({
            "user_input": user_input,
            "messages": [],
            "config": {},
            "result": "",
            "execution_path": []
        })
        
        print(f"执行路径: {' -> '.join(result['execution_path'])}")
        print(f"最终结果: {result['result']}")
        print("-" * 40)

def main():
    """主函数"""
    dynamic_config_example()
    config_from_external_source()

if __name__ == "__main__":
    main() 