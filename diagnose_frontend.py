#!/usr/bin/env python3
"""
前端WebSocket连接诊断脚本
"""

import asyncio
import websockets
import json
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import sys

async def monitor_websocket_server():
    """监控WebSocket服务器端的连接"""
    print("🔍 监控WebSocket服务器端连接...")
    
    uri = "ws://localhost:8000/ws/chat"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ 服务器端WebSocket连接正常")
            
            # 发送测试消息
            test_message = {
                "message": "服务器端测试消息",
                "user_id": "server-test",
                "voice": "zh-CN-XiaomoNeural"
            }
            
            await websocket.send(json.dumps(test_message))
            print("📤 发送测试消息")
            
            # 接收响应
            response_count = 0
            while response_count < 5:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    data = json.loads(response)
                    response_count += 1
                    print(f"📥 收到响应 {response_count}: {data.get('type', 'unknown')}")
                    
                    if data.get("type") == "complete":
                        break
                        
                except asyncio.TimeoutError:
                    break
                    
            return True
            
    except Exception as e:
        print(f"❌ 服务器端连接失败: {e}")
        return False

def test_frontend_with_browser():
    """使用浏览器测试前端页面"""
    print("\n🌐 使用浏览器测试前端页面...")
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        
        # 访问前端页面
        driver.get("http://localhost:8000")
        print("✅ 成功访问前端页面")
        
        # 等待页面加载
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "websocketStatus"))
        )
        
        # 检查WebSocket状态
        time.sleep(3)  # 等待WebSocket连接建立
        
        websocket_status = driver.find_element(By.ID, "websocketStatusText").text
        print(f"📡 WebSocket状态: {websocket_status}")
        
        # 检查控制台错误
        logs = driver.get_log('browser')
        if logs:
            print("⚠️ 浏览器控制台日志:")
            for log in logs:
                if log['level'] == 'SEVERE':
                    print(f"   ❌ 错误: {log['message']}")
                elif log['level'] == 'WARNING':
                    print(f"   ⚠️ 警告: {log['message']}")
                else:
                    print(f"   ℹ️ 信息: {log['message']}")
        else:
            print("✅ 无控制台错误")
        
        # 尝试发送测试消息
        if "已连接" in websocket_status:
            print("\n💬 测试发送消息...")
            
            chat_input = driver.find_element(By.ID, "chatInput")
            send_button = driver.find_element(By.ID, "sendBtn")
            
            chat_input.send_keys("浏览器测试消息")
            send_button.click()
            
            print("📤 已发送测试消息")
            
            # 等待响应
            time.sleep(5)
            
            # 检查聊天历史
            chat_history = driver.find_element(By.ID, "chatHistory")
            messages = chat_history.find_elements(By.CLASS_NAME, "message")
            
            print(f"📝 聊天消息数量: {len(messages)}")
            
            for i, msg in enumerate(messages[-2:]):  # 显示最后两条消息
                print(f"   消息 {i+1}: {msg.text[:50]}...")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"❌ 浏览器测试失败: {e}")
        try:
            driver.quit()
        except:
            pass
        return False

def check_static_files():
    """检查静态文件服务"""
    print("\n📁 检查静态文件服务...")
    
    import requests
    
    try:
        # 检查主页
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            print("✅ 主页访问正常")
            
            # 检查页面内容
            if "WebSocket" in response.text:
                print("✅ 页面包含WebSocket相关内容")
            else:
                print("⚠️ 页面可能缺少WebSocket相关内容")
                
        else:
            print(f"❌ 主页访问失败: {response.status_code}")
            return False
            
        # 检查静态资源
        static_files = [
            "/static/style.css",
            "/favicon.ico"
        ]
        
        for file_path in static_files:
            try:
                response = requests.get(f"http://localhost:8000{file_path}", timeout=5)
                if response.status_code == 200:
                    print(f"✅ 静态文件正常: {file_path}")
                else:
                    print(f"⚠️ 静态文件异常: {file_path} - {response.status_code}")
            except:
                print(f"⚠️ 静态文件无法访问: {file_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 静态文件检查失败: {e}")
        return False

async def main():
    """主诊断函数"""
    print("🔧 前端WebSocket连接诊断")
    print("=" * 50)
    
    # 检查服务器端WebSocket
    server_ok = await monitor_websocket_server()
    
    # 检查静态文件服务
    static_ok = check_static_files()
    
    # 检查浏览器端
    browser_ok = False
    try:
        browser_ok = test_frontend_with_browser()
    except Exception as e:
        print(f"⚠️ 浏览器测试跳过: {e}")
        print("   (可能需要安装Chrome和ChromeDriver)")
    
    print("\n" + "=" * 50)
    print("📊 诊断结果:")
    print(f"   服务器端WebSocket: {'✅ 正常' if server_ok else '❌ 异常'}")
    print(f"   静态文件服务: {'✅ 正常' if static_ok else '❌ 异常'}")
    print(f"   浏览器端测试: {'✅ 正常' if browser_ok else '⚠️ 跳过/异常'}")
    
    if server_ok and static_ok:
        print("\n🎉 服务器端功能正常！")
        print("\n💡 如果前端仍有问题，请:")
        print("   1. 打开浏览器开发者工具 (F12)")
        print("   2. 查看控制台 (Console) 选项卡的错误信息")
        print("   3. 查看网络 (Network) 选项卡的WebSocket连接状态")
        print("   4. 确认浏览器没有阻止WebSocket连接")
        print("   5. 尝试刷新页面或清除浏览器缓存")
        return 0
    else:
        print("\n❌ 发现问题，请检查服务器配置")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 诊断被中断")
        exit_code = 1
    
    # 删除诊断脚本
    try:
        import os
        os.remove(__file__)
        print(f"\n🗑️ 诊断脚本已删除")
    except:
        pass
    
    sys.exit(exit_code)
