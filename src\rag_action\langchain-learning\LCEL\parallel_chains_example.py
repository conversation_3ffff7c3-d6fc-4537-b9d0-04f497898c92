"""
使用 RunnableParallel 并行运行多条链
"""
"""
并行链（Parallel Chain）是指在 LangChain 等框架中，能够同时（并发）运行多条独立的链（Chain），并将它们的结果统一收集和处理的一种机制。通过并行链，可以显著提升多任务处理的效率，减少整体等待时间，特别适合需要对同一输入进行多角度分析或多种处理的场景。

【应用场景】
1. 多维度分析：如对同一主题，分别生成技术解释、优缺点分析、应用场景等内容，并行返回，适用于报告生成、知识问答等。
2. 多模型协作：同时调用不同的 LLM 或工具链，对同一问题给出多种答案或建议，便于对比和融合。
3. 批量处理：对一组输入数据，分别并行处理，提高吞吐量，适合数据清洗、摘要生成等批量任务。
4. 多语言输出：同一内容并行生成多种语言的版本，适合国际化应用。

【优点】
- 显著提升处理速度，减少总耗时（相较于串行执行）。
- 充分利用多核/多线程/异步等计算资源，提高系统吞吐能力。
- 代码结构清晰，易于扩展和维护。
- 支持灵活的后处理和结果聚合，便于实现复杂的业务逻辑。

在 LangChain 中，`RunnableParallel` 是实现并行链的核心工具，支持同步、异步和流式等多种并行执行方式。
"""



from langchain_core.output_parsers import StrOutputParser
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableParallel
import asyncio
import time

# 创建 LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0
)

def basic_parallel_example():
    """基础并行示例"""
    
    print("=== 基础并行示例 ===")
    
    # 创建多个链
    tech_chain = ChatPromptTemplate.from_template("请详细解释：{topic}") | llm | StrOutputParser()
    pros_cons_chain = ChatPromptTemplate.from_template("请分析 {topic} 的优缺点：") | llm | StrOutputParser()
    application_chain = ChatPromptTemplate.from_template("请列举 {topic} 的主要应用场景：") | llm | StrOutputParser()
    
    # 使用 RunnableParallel 并行执行
    parallel_chain = RunnableParallel({
        "技术解释": tech_chain,
        "优缺点分析": pros_cons_chain,
        "应用场景": application_chain
    })
    
    # 执行并行链
    start_time = time.time()
    results = parallel_chain.invoke({"topic": "人工智能"})
    total_time = time.time() - start_time
    
    print(f"总耗时: {total_time:.2f}秒")
    
    # 显示结果
    for name, result in results.items():
        print(f"\n【{name}】")
        print(f"{result[:200]}...")
    
    return results

def advanced_parallel_example():
    """高级并行示例"""
    
    print("\n=== 高级并行示例 ===")
    
    # 创建更复杂的并行链
    parallel_chain = RunnableParallel({
        "技术解释": ChatPromptTemplate.from_template("请详细解释：{topic}") | llm | StrOutputParser(),
        "优缺点分析": ChatPromptTemplate.from_template("请分析 {topic} 的优缺点：") | llm | StrOutputParser(),
        "应用场景": ChatPromptTemplate.from_template("请列举 {topic} 的主要应用场景：") | llm | StrOutputParser(),
        "发展趋势": ChatPromptTemplate.from_template("请分析 {topic} 的发展趋势：") | llm | StrOutputParser(),
        "技术挑战": ChatPromptTemplate.from_template("请分析 {topic} 面临的技术挑战：") | llm | StrOutputParser()
    })
    
    # 执行并行链
    start_time = time.time()
    results = parallel_chain.invoke({"topic": "机器学习"})
    total_time = time.time() - start_time
    
    print(f"总耗时: {total_time:.2f}秒")
    
    # 显示结果
    for name, result in results.items():
        print(f"\n【{name}】")
        print(f"{result[:150]}...")
    
    return results

def parallel_with_processing():
    """并行执行后处理结果"""
    
    print("\n=== 并行执行后处理结果 ===")
    
    # 创建并行链
    parallel_chain = RunnableParallel({
        "技术解释": ChatPromptTemplate.from_template("请详细解释：{topic}") | llm | StrOutputParser(),
        "优缺点分析": ChatPromptTemplate.from_template("请分析 {topic} 的优缺点：") | llm | StrOutputParser(),
        "应用场景": ChatPromptTemplate.from_template("请列举 {topic} 的主要应用场景：") | llm | StrOutputParser()
    })
    
    # 创建后处理链
    summary_prompt = ChatPromptTemplate.from_template("""
    请根据以下信息生成一个总结：
    
    技术解释：{技术解释}
    优缺点分析：{优缺点分析}
    应用场景：{应用场景}
    
    请生成一个关于 {topic} 的综合性总结。
    """)
    
    # 组合并行链和后处理链
    full_chain = parallel_chain | summary_prompt | llm | StrOutputParser()
    
    # 执行
    start_time = time.time()
    result = full_chain.invoke({"topic": "深度学习"})
    total_time = time.time() - start_time
    
    print(f"总耗时: {total_time:.2f}秒")
    print(f"\n【综合总结】")
    print(result)
    
    return result

async def async_parallel_example():
    """异步并行示例"""
    
    print("\n=== 异步并行示例 ===")
    
    # 创建并行链
    parallel_chain = RunnableParallel({
        "技术解释": ChatPromptTemplate.from_template("请详细解释：{topic}") | llm | StrOutputParser(),
        "优缺点分析": ChatPromptTemplate.from_template("请分析 {topic} 的优缺点：") | llm | StrOutputParser(),
        "应用场景": ChatPromptTemplate.from_template("请列举 {topic} 的主要应用场景：") | llm | StrOutputParser()
    })
    
    # 异步执行
    start_time = time.time()
    results = await parallel_chain.ainvoke({"topic": "区块链"})
    total_time = time.time() - start_time
    
    print(f"总耗时: {total_time:.2f}秒")
    
    # 显示结果
    for name, result in results.items():
        print(f"\n【{name}】")
        print(f"{result[:200]}...")
    
    return results

def streaming_parallel_example():
    """流式并行示例"""
    
    print("\n=== 流式并行示例 ===")
    
    # 创建并行链
    parallel_chain = RunnableParallel({
        "技术解释": ChatPromptTemplate.from_template("请详细解释：{topic}") | llm | StrOutputParser(),
        "优缺点分析": ChatPromptTemplate.from_template("请分析 {topic} 的优缺点：") | llm | StrOutputParser()
    })
    
    # 流式执行
    print("开始流式处理...")
    start_time = time.time()
    
    for chunk in parallel_chain.stream({"topic": "云计算"}):
        print(f"收到数据块: {list(chunk.keys())}")
        for key, value in chunk.items():
            print(f"  {key}: {value[:50]}...")
    
    total_time = time.time() - start_time
    print(f"\n总耗时: {total_time:.2f}秒")

def main():
    """主函数"""
    # 基础并行示例
    basic_parallel_example()
    
    # 高级并行示例
    advanced_parallel_example()
    
    # 并行执行后处理
    parallel_with_processing()
    
    # 异步并行示例
    asyncio.run(async_parallel_example())
    
    # 流式并行示例
    streaming_parallel_example()

if __name__ == "__main__":
    main() 