import logging
from langchain_community.vectorstores import Chroma
from langchain_community.document_loaders import TextLoader
from langchain_openai import ChatOpenAI
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain.retrievers.multi_query import MultiQueryRetriever # 多角度查询检索器
# 设置日志记录
logging.basicConfig()
logging.getLogger("langchain.retrievers.multi_query").setLevel(logging.INFO)
# 加载游戏相关文档并构建向量数据库
loader = TextLoader("C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/黑悟空wiki.txt", encoding='utf-8')
data = loader.load()
text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=0)
splits = text_splitter.split_documents(data)
embed_model = HuggingFaceEmbeddings(model_name="BAAI/bge-small-zh")
vectorstore = Chroma.from_documents(documents=splits, embedding= embed_model)
# 通过MultiQueryRetriever 生成多角度查询
llm = ChatOpenAI(model="gpt-4o-mini", temperature=0,api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",base_url="https://api.zhizengzeng.com/v1")
retriever_from_llm = MultiQueryRetriever.from_llm(
    retriever=vectorstore.as_retriever(), 
    llm=llm
)
query = "那个，我刚开始玩这个游戏，想问问故事有多少个章节"
# 调用RePhraseQueryRetriever进行查询分解
docs = retriever_from_llm.invoke(query)
print(docs)

client = ChatOpenAI(model="gpt-4o-mini", temperature=0.6,api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",base_url="https://api.zhizengzeng.com/v1")
prompt = f"""
你是一个黑神话的游戏客服，现在给你提供一下资料，请根据资料回答用户的问题。
资料：{docs}
用户问题：{query}
请直接给出回答（不要加任何前缀或者说明）。
"""

response = client.invoke(prompt)
print(response.content)