"""
JSON 输出解析示例
使用 StructuredOutputParser 生成 JSON 格式输出
"""

from langchain.output_parsers import ResponseSchema, StructuredOutputParser
from langchain.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
import json

def main():
    """主函数"""
    
    # 定义响应模式 - 这会生成 JSON 格式
    response_schemas = [
        ResponseSchema(name="name", description="用户姓名", type="string"),
        ResponseSchema(name="age", description="用户年龄", type="integer"),
        ResponseSchema(name="email", description="邮箱地址", type="string"),
        ResponseSchema(name="interests", description="兴趣爱好列表", type="list"),
        ResponseSchema(name="bio", description="个人简介", type="string")
    ]
    
    # 创建解析器 - 这会生成 JSON 格式的输出
    parser = StructuredOutputParser.from_response_schemas(response_schemas)
    
    # 创建提示模板
    prompt = PromptTemplate(
        template="请根据以下信息创建用户档案：\n{format_instructions}\n\n用户信息：{user_info}",
        input_variables=["user_info"],
        partial_variables={"format_instructions": parser.get_format_instructions()}
    )
    
    # 创建 LLM
    llm = ChatOpenAI(
        temperature=0,
        base_url="https://api.zhizengzeng.com/v1",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
    )
    
    # 构建 LCEL 链
    chain = prompt | llm | parser
    
    # 示例用户信息
    user_info = """
    姓名：张三
    年龄：28岁
    邮箱：<EMAIL>
    个人简介：热爱技术的软件工程师
    兴趣爱好：编程、阅读、游泳
    """
    
    # 执行链
    try:
        result = chain.invoke({"user_info": user_info})
        print("JSON 解析成功:")
        print(f"姓名: {result['name']}")
        print(f"年龄: {result['age']}")
        print(f"邮箱: {result['email']}")
        print(f"兴趣爱好: {result['interests']}")
        print(f"个人简介: {result['bio']}")
        
        # 显示原始 JSON
        print(f"\n原始 JSON 输出:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f"解析失败: {e}")

if __name__ == "__main__":
    main() 