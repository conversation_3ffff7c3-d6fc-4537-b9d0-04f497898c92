from pymilvus import MilvusClient, DataType
import random

"""
IVF_FLAT（倒排文件+扁平索引）是一种常用的向量检索索引类型，具有以下特性：

1. 原理简介：
   - 首先将所有向量通过聚类（如K-means）划分为 nlist 个簇（cluster）。
   - 每个簇对应一个倒排桶（inverted list），每个桶内存储属于该簇的原始向量。
   - 检索时，先根据查询向量找到最近的 nprobe 个簇，只在这些簇内做精确的暴力搜索（flat search）。

2. 优点：
   - 检索速度快：只需在部分簇内做暴力搜索，减少了比较次数。
   - 支持大规模数据：适合百万级、千万级向量的近似检索。
   - 精度和速度可调：通过 nlist（聚类数）和 nprobe（搜索簇数）灵活调整召回率和检索速度。

3. 缺点：
   - 近似检索：不是全量暴力搜索，可能存在漏检（召回率略低于 FLAT）。
   - 需要调参：nlist 和 nprobe 需根据数据量和业务需求合理设置。

4. 适用场景：
   - 适合大规模向量数据的 ANN（近似最近邻）检索。
   - 对检索速度有较高要求，允许少量召回损失的场景。

5. 相关参数说明：
   - nlist：聚类中心数量，影响索引构建和检索速度/精度。
   - nprobe：检索时实际遍历的簇数，影响召回率和速度（在 search 时设置）。

总结：IVF_FLAT 是一种高效的近似向量检索索引，适合大规模数据场景，速度和精度可灵活权衡。
"""

# 1. 设置 Milvus 客户端
client = MilvusClient(uri="http://localhost:19530")
COLLECTION_NAME = "flat_index_demo"

# 如果集合已存在，则删除
if client.has_collection(COLLECTION_NAME):
    client.drop_collection(COLLECTION_NAME)

# 2. 创建 schema
schema = MilvusClient.create_schema(auto_id=False, enable_dynamic_field=True)
schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True)
schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=128)

# 3. 创建集合
client.create_collection(collection_name=COLLECTION_NAME, schema=schema)

# 4. 插入随机向量数据
num_vectors = 1000
vectors = [[random.random() for _ in range(128)] for _ in range(num_vectors)]
ids = list(range(num_vectors))
entities = [{"id": ids[i], "vector": vectors[i]} for i in range(num_vectors)]

client.insert(collection_name=COLLECTION_NAME, data=entities)
# flush 保证数据落盘
client.flush(COLLECTION_NAME)

# 5. 创建索引（此时集合中已有数据）
index_params = MilvusClient.prepare_index_params()
index_params.add_index(
    field_name="vector",
    metric_type="L2",
    index_type="IVF_FLAT",
    index_name="vector_index",
    params={
        # nlist 是 IVF_FLAT 索引的聚类数量（即倒排文件的桶数），
        # 它决定了向量空间被划分为多少个簇（cluster），
        # nlist 越大，检索时每个簇内的向量越少，召回速度更快但召回率可能下降；
        # nlist 越小，召回率高但速度慢。一般建议 nlist 取值为数据量的 1/4~1/10。
        "nlist": 64
        # 另外，nprobe 是搜索时实际遍历的簇的数量（在 search 时设置），
        # nprobe 越大，召回率越高但速度越慢；nprobe 越小，速度快但可能漏检。
    }
)
client.create_index(
    collection_name=COLLECTION_NAME,
    index_params=index_params,
    sync=True
)

# 验证索引
print("索引列表:", client.list_indexes(collection_name=COLLECTION_NAME))
print("索引详情:", client.describe_index(
    collection_name=COLLECTION_NAME,
    index_name="vector_index"
))

# 6. load 后再搜索
client.load_collection(collection_name=COLLECTION_NAME)
search_vectors = [[random.random() for _ in range(128)]]
results = client.search(
    collection_name=COLLECTION_NAME,
    data=search_vectors,
    ann_field="vector",
    limit=5,
    output_fields=["id"],
    search_params={
        "params": {
            "nprobe": 10  # 设置搜索时检查的聚类数量
        }
    }
)

print("\n搜索结果:")
for hits in results:
    for hit in hits:
        # 注意用 dict 方式访问
        print(f"ID: {hit['id']}, 距离: {hit['distance']}")

# 清理
client.release_collection(collection_name=COLLECTION_NAME)
# client.disconnect()
