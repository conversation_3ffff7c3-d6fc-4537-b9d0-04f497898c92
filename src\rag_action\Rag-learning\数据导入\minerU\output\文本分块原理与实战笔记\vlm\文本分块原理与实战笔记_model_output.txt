<|box_start|>160 122 389 142<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 文本分块原理与实战笔记<|md_end|>
<|box_start|>159 156 385 210<|box_end|><|ref_start|>text<|ref_end|><|md_start|>笔记本： 我的第一个笔记本创建时间： 2025- 06- 09 22:16作者： 高晓琪<|md_end|>
<|box_start|>499 174 723 190<|box_end|><|ref_start|>text<|ref_end|><|md_start|>更新时间： 2025- 06- 09 22:24<|md_end|>
<|box_start|>223 247 691 285<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 文本分块原理与实战笔记<|md_end|>
<|box_start|>157 324 593 357<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 一、文本分块的原理与重要性<|md_end|>
<|box_start|>159 389 325 414<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 1. 为什么分块？<|md_end|>
<|box_start|>168 441 837 565<|box_end|><|ref_start|>text<|ref_end|><|md_start|>- 上下文窗口限制：大语言模型（如 GPT）输入受限于最大 token 数，例如 GPT-3.5 是 4k，GPT-4 支持最多 128k。- 减少噪声，提高匹配精度：将长文本切成短块，避免无关信息干扰嵌入或生成。- 提升召回率：小块语义更清晰，有助于更精确的相似度计算与检索。- 避免“Lost in the Middle”现象：生成模型对中间内容注意力减弱，分块有助于模型识别重点信息。<|md_end|>
<|box_start|>157 595 428 619<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 2. 嵌入模型 vs 生成模型<|md_end|>
<|box_start|>170 647 464 665<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 嵌入模型（Embedding Model）：<|md_end|>
<|box_start|>199 668 840 728<|box_end|><|ref_start|>text<|ref_end|><|md_start|>- 文本被拆成多个 token，每个 token 映射成向量，整体再池化（如平均）成一个句向量。- 长文本越长，平均后信息损失越严重。<|md_end|>
<|box_start|>170 731 344 748<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 生成模型（LLM）：<|md_end|>
<|box_start|>199 752 773 770<|box_end|><|ref_start|>text<|ref_end|><|md_start|>- 太长的输入易导致主题稀释，注意力集中在前后两端，中间信息被忽略。<|md_end|>
<|box_start|>154 803 427 834<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 二、文本分块方法<|md_end|>
<|box_start|>157 867 635 891<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 1. CharacterTextSplitter（固定字符切分）<|md_end|>
<|box_start|>169 920 723 960<|box_end|><|ref_start|>text<|ref_end|><|md_start|>- 按固定字符数分块，如每 1000 字符一块，设置重叠（如 100 字符）。- 适合规则性文本，如新闻、小说等。<|md_end|>
--------------------------------------------------
<|box_start|>157 035 704 059<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 2. RecursiveCharacterTextSplitter（递归分割）<|md_end|>
<|box_start|>170 088 667 127<|box_end|><|ref_start|>text<|ref_end|><|md_start|>定义多个分隔符（如 \(\sqrt[n]{n}\) ，"，"，"），按优先级递归使用。- 可保证语义完整性，适合结构化文档。<|md_end|>
<|box_start|>158 157 385 182<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 3. 按格式或语言切分<|md_end|>
<|box_start|>170 211 718 250<|box_end|><|ref_start|>text<|ref_end|><|md_start|>- 支持如 get_separators_for_language(Language.JAVA) 的语言级分块。- 用于代码文档、技术手册等。<|md_end|>
<|box_start|>157 279 527 304<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 4. SemanticSplitter（语义分块）<|md_end|>
<|box_start|>170 333 704 372<|box_end|><|ref_start|>text<|ref_end|><|md_start|>- Ilamalndex 提供的语义切块方式。- 使用 embedding 向量余弦相似度将语义接近的句子组合为一个块。<|md_end|>
<|box_start|>157 402 509 427<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 5. Unstructured 文档结构分块<|md_end|>
<|box_start|>170 455 534 557<|box_end|><|ref_start|>text<|ref_end|><|md_start|>- 适用于 PDF、Word 等复杂结构文档。- Basic 策略：按元素顺序合并，超限则切分。- By Title：遇标题即分块，适合分章节文档。- By Page：每页为一个块。- By Similarity：将语义相近的元素合并为块。<|md_end|>
<|box_start|>156 589 427 620<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 三、分块高级技巧<|md_end|>
<|box_start|>158 653 337 678<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 1. 滑动窗口策略<|md_end|>
<|box_start|>169 707 733 726<|box_end|><|ref_start|>text<|ref_end|><|md_start|>- 分块时设置重叠区域（如 overlap=100 tokens），保证上下文连续性。<|md_end|>
<|box_start|>157 755 314 779<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 2. 父子块结构<|md_end|>
<|box_start|>170 808 612 848<|box_end|><|ref_start|>text<|ref_end|><|md_start|>- 大文档先生成摘要（父块），然后对子块细分。- Ilamalndex 示例：先 5000 字摘要，再分 500 字子块。<|md_end|>
<|box_start|>157 878 361 902<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 3. 分块元数据注入<|md_end|>
<|box_start|>169 931 697 949<|box_end|><|ref_start|>text<|ref_end|><|md_start|>- 在文本块中附加 metadata，如：文件名、页码、创建时间、类别。<|md_end|>
--------------------------------------------------
<|box_start|>172 033 438 051<|box_end|><|ref_start|>text<|ref_end|><|md_start|>- 有助于后续检索过滤和 UI 展示。<|md_end|>
<|box_start|>157 082 337 106<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 4. 多层索引构建<|md_end|>
<|box_start|>170 134 671 173<|box_end|><|ref_start|>text<|ref_end|><|md_start|>- 构建Treelndex、Summarylndex、Vectorlndex等层级结构。- 实现摘要 \(\rightarrow\) 全文定位，节省成本。<|md_end|>
<|box_start|>157 204 523 228<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 5. Chunk Embedding 入向量库<|md_end|>
<|box_start|>170 256 824 295<|box_end|><|ref_start|>text<|ref_end|><|md_start|>- 每个 chunk 经 embedding 后写入向量数据库（如 FAISS、Milvus、Weaviate）。- 与 metadata 一起入库，提升检索能力。<|md_end|>
<|box_start|>157 327 501 350<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 6. Late Chunking (延迟分块)<|md_end|>
<|box_start|>170 380 578 418<|box_end|><|ref_start|>text<|ref_end|><|md_start|>- 先对全文 embedding，再切分，以保留全局语义。- 有研究发现能提升长文召回准确率。<|md_end|>
<|box_start|>156 451 551 481<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 四、最佳 Chunk 参数推荐<|md_end|>
<|box_start|>170 513 502 593<|box_end|><|ref_start|>text<|ref_end|><|md_start|>- 简短问答任务推荐：64-128 tokens- 一般文档摘要推荐：256-512 tokens- 全文语义召回推荐：512-1024 tokens- 一般建议设置 overlap：50-100 tokens<|md_end|>
<|box_start|>155 626 459 657<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 五、可视化调试工具<|md_end|>
<|box_start|>170 688 545 748<|box_end|><|ref_start|>text<|ref_end|><|md_start|>- chunk-viz：社区提供的文本分块可视化工具。- 用于调试块大小、分割边界是否合理。- 网址示例：https://chunkviz.com<|md_end|>
<|box_start|>154 781 491 812<|box_end|><|ref_start|>title<|ref_end|><|md_start|># 六、小结关键词对照表<|md_end|>
<|box_start|>155 841 796 945<|box_end|><|ref_start|>table<|ref_end|><|md_start|><fcel>项目<fcel>工具/策略<fcel>特点<nl>
<fcel>固定分块<fcel>CharacterTextSplitter<fcel>简单高效<nl>
<fcel>递归分块<fcel>RecursiveCharacterTextSplitter<fcel>多级语义粒度<nl><|md_end|>
--------------------------------------------------
<|box_start|>157 031 796 205<|box_end|><|ref_start|>table<|ref_end|><|md_start|><fcel>项目<fcel>工具/策略<fcel>特点<nl>
<fcel>语义分块<fcel>SemanticSplitter<fcel>基于余弦相似度<nl>
<fcel>结构感知<fcel>Unstructured<fcel>适合PDF/复杂格式<nl>
<fcel>可视化<fcel>chunk-viz<fcel>直观查看分块效果<nl>
<fcel>高级技巧<fcel>overlap, 层级索引<fcel>提升效果与召回<nl><|md_end|>
<|box_start|>155 237 794 277<|box_end|><|ref_start|>text<|ref_end|><|md_start|>该笔记适用于构建 RAG 系统、语义搜索系统、文档 QA 系统等所有对“文本块化处理”有依赖的场景。<|md_end|>