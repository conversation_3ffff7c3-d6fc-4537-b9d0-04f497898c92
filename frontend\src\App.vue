<template>
  <div id="app">
    <!-- 使用路由视图显示页面内容 -->
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/stores'

const appStore = useAppStore()

// 监听窗口大小变化
const handleResize = () => {
  appStore.handleResize()
}

// 监听在线状态
const handleOnline = () => {
  ElMessage.success('网络连接已恢复')
}

const handleOffline = () => {
  ElMessage.warning('网络连接已断开')
}

// 监听键盘快捷键
const handleKeydown = (e: KeyboardEvent) => {
  // Ctrl/Cmd + K 打开搜索
  if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
    e.preventDefault()
    // 触发全局搜索
    // TODO: 实现全局搜索功能
  }
  
  // Ctrl/Cmd + / 切换侧边栏
  if ((e.ctrlKey || e.metaKey) && e.key === '/') {
    e.preventDefault()
    appStore.toggleSidebar()
  }
  
  // Esc 键关闭模态框等
  if (e.key === 'Escape') {
    // TODO: 实现全局Esc处理
  }
}

onMounted(() => {
  // 初始化应用
  appStore.initApp()
  
  // 添加事件监听
  window.addEventListener('resize', handleResize)
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  window.addEventListener('keydown', handleKeydown)
  
  // 检查网络状态
  if (!navigator.onLine) {
    ElMessage.warning('当前网络连接不可用')
  }
})

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
  window.removeEventListener('keydown', handleKeydown)
})
</script>

<style lang="scss">
@use "@/styles/variables.scss" as *;

#app {
  height: 100vh;
  overflow: hidden;
}

// 全局滚动条样式
* {
  // scrollbar styles would go here if needed
}

// Element Plus 主题定制
:root {
  --el-color-primary: var(--primary-color);
  --el-color-success: var(--success-color);
  --el-color-warning: var(--warning-color);
  --el-color-danger: var(--danger-color);
  --el-color-info: var(--info-color);
  
  --el-text-color-primary: var(--text-primary);
  --el-text-color-regular: var(--text-regular);
  --el-text-color-secondary: var(--text-secondary);
  --el-text-color-placeholder: var(--text-placeholder);
  
  --el-border-color: var(--border-base);
  --el-border-color-light: var(--border-light);
  --el-border-color-lighter: var(--border-lighter);
  --el-border-color-extra-light: var(--border-extra-light);
  
  --el-bg-color: var(--bg-color);
  --el-bg-color-page: var(--bg-page);
  --el-bg-color-overlay: var(--bg-overlay);
  
  --el-box-shadow: var(--shadow-base);
  --el-box-shadow-light: var(--shadow-light);
  --el-box-shadow-dark: var(--shadow-dark);
  
  --el-border-radius-base: var(--border-radius-base);
  --el-border-radius-small: var(--border-radius-small);
  --el-border-radius-large: var(--border-radius-large);
  --el-border-radius-round: var(--border-radius-round);
}

// 暗色主题下的Element Plus样式调整
[data-theme='dark'] {
  .el-message-box {
    --el-messagebox-bg-color: var(--bg-color);
    --el-messagebox-border-color: var(--border-base);
  }
  
  .el-dialog {
    --el-dialog-bg-color: var(--bg-color);
    --el-dialog-border-color: var(--border-base);
  }
  
  .el-drawer {
    --el-drawer-bg-color: var(--bg-color);
  }
  
  .el-popover {
    --el-popover-bg-color: var(--bg-color);
    --el-popover-border-color: var(--border-base);
  }
  
  .el-tooltip__popper {
    --el-tooltip-bg-color: var(--bg-color);
    --el-tooltip-border-color: var(--border-base);
  }
}

// 自定义Element Plus组件样式
.el-loading-mask {
  background-color: var(--bg-overlay);
}

.el-message {
  --el-message-bg-color: var(--bg-color);
  --el-message-border-color: var(--border-base);
  box-shadow: var(--shadow-light);
}

.el-notification {
  --el-notification-bg-color: var(--bg-color);
  --el-notification-border-color: var(--border-base);
  box-shadow: var(--shadow-light);
}

// 响应式断点调整
@include respond-below(md) {
  .el-dialog {
    width: 90% !important;
    margin: 5vh auto !important;
  }
  
  .el-drawer {
    width: 90% !important;
  }
  
  .el-message-box {
    width: 90% !important;
  }
}

// 打印样式
@media print {
  .app-sidebar,
  .app-header,
  .el-button,
  .el-pagination {
    display: none !important;
  }
  
  .app-content {
    margin: 0 !important;
    padding: 0 !important;
  }
  
  * {
    color: #000 !important;
    background: #fff !important;
    box-shadow: none !important;
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  :root {
    --border-base: #000;
    --border-light: #333;
    --text-primary: #000;
    --text-regular: #333;
  }
  
  [data-theme='dark'] {
    --border-base: #fff;
    --border-light: #ccc;
    --text-primary: #fff;
    --text-regular: #ccc;
  }
}

// 减少动画（用户偏好）
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
