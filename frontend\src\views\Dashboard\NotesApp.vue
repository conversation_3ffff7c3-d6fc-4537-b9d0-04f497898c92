<template>
  <div class="notes-app">
    <!-- 顶部导航栏 -->
    <header class="app-header">
      <div class="header-left">
        <h1 class="app-title">
          <el-icon><Document /></el-icon>
          AI笔记系统
        </h1>
      </div>
      
      <div class="header-center">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索笔记内容..."
          class="search-input"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建笔记
        </el-button>
        <el-button @click="showUploadDialog = true">
          <el-icon><Upload /></el-icon>
          上传文件
        </el-button>
        <el-button
          type="primary"
          @click="handleQueryClick"
          class="query-button"
        >
          <el-icon><ChatDotRound /></el-icon>
          智能问答
        </el-button>
      </div>
    </header>

    <!-- 主内容区域 -->
    <div class="app-main">
      <!-- 快速问答卡片 -->
      <div class="quick-query-card" v-if="!selectedNote">
        <el-card class="query-card">
          <template #header>
            <div class="card-header">
              <el-icon class="query-icon"><ChatDotRound /></el-icon>
              <span>快速智能问答</span>
            </div>
          </template>
          <div class="query-content">
            <p class="query-description">
              🤖 有什么问题想要了解？我可以帮您从知识库中找到答案
            </p>
            <div class="query-examples">
              <p class="examples-title">💡 试试这些问题：</p>
              <div class="example-buttons">
                <el-button
                  size="small"
                  type="info"
                  plain
                  @click="handleExampleQuery('什么是数据导入？')"
                >
                  什么是数据导入？
                </el-button>
                <el-button
                  size="small"
                  type="info"
                  plain
                  @click="handleExampleQuery('Java编程有什么特点？')"
                >
                  Java编程有什么特点？
                </el-button>
                <el-button
                  size="small"
                  type="info"
                  plain
                  @click="handleExampleQuery('如何设计数据库？')"
                >
                  如何设计数据库？
                </el-button>
              </div>
            </div>
            <div class="query-actions">
              <el-button
                type="primary"
                size="large"
                @click="handleQueryClick"
                class="full-query-button"
              >
                <el-icon><ChatDotRound /></el-icon>
                进入完整问答模式
              </el-button>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 左侧边栏 -->
      <aside class="app-sidebar">
        <!-- 标签筛选 -->
        <div class="sidebar-section">
          <h3 class="section-title">标签筛选</h3>
          <div class="tags-filter">
            <el-tag
              v-for="tag in availableTags"
              :key="tag.id"
              :type="selectedTags.includes(tag.name) ? 'primary' : 'info'"
              :effect="selectedTags.includes(tag.name) ? 'dark' : 'plain'"
              class="tag-item"
              @click="toggleTag(tag.name)"
            >
              {{ tag.name }}
            </el-tag>
          </div>
        </div>

        <!-- 分类导航 -->
        <div class="sidebar-section">
          <h3 class="section-title">文档类型</h3>
          <el-menu
            v-model="selectedSourceType"
            class="source-menu"
            @select="handleSourceTypeChange"
          >
            <el-menu-item index="">
              <el-icon><FolderOpened /></el-icon>
              全部文档
            </el-menu-item>
            <el-menu-item index="manual">
              <el-icon><Edit /></el-icon>
              手动创建
            </el-menu-item>
            <el-menu-item index="pdf">
              <el-icon><Document /></el-icon>
              PDF文档
            </el-menu-item>
            <el-menu-item index="markdown">
              <el-icon><Document /></el-icon>
              Markdown
            </el-menu-item>
          </el-menu>
        </div>

        <!-- 笔记列表 -->
        <div class="sidebar-section notes-list-section">
          <h3 class="section-title">
            笔记列表 ({{ filteredNotes.length }})
            <el-button
              size="small"
              text
              @click="refreshNotes"
              :loading="loading"
            >
              <el-icon><Refresh /></el-icon>
            </el-button>
          </h3>
          
          <div class="notes-list" v-loading="loading">
            <div
              v-for="note in filteredNotes"
              :key="note.id"
              class="note-item"
              :class="{ active: selectedNote?.id === note.id }"
              @click="selectNote(note)"
            >
              <div class="note-header">
                <h4 class="note-title">{{ note.title }}</h4>
                <span class="note-source">{{ getSourceTypeLabel(note.source_type) }}</span>
              </div>
              <p class="note-preview">{{ getContentPreview(note.content_preview || note.content) }}</p>
              <div class="note-meta">
                <span class="note-date">{{ formatDate(note.created_at) }}</span>
                <div class="note-tags">
                  <el-tag
                    v-for="tag in note.tags"
                    :key="tag.id"
                    size="small"
                    type="info"
                  >
                    {{ tag.name }}
                  </el-tag>
                </div>
              </div>
            </div>
            
            <el-empty v-if="!loading && filteredNotes.length === 0" description="暂无笔记" />
          </div>
        </div>
      </aside>

      <!-- 主内容区域 -->
      <main class="app-content">
        <!-- 笔记详情/编辑区域 -->
        <div v-if="selectedNote" class="note-detail">
          <div class="note-detail-header">
            <div class="note-info">
              <h2 class="note-title">{{ selectedNote.title }}</h2>
              <div class="note-meta-info">
                <span>创建时间: {{ formatDate(selectedNote.created_at) }}</span>
                <span>更新时间: {{ formatDate(selectedNote.updated_at) }}</span>
                <span>来源: {{ getSourceTypeLabel(selectedNote.source_type) }}</span>
              </div>
            </div>
            <div class="note-actions">
              <el-button @click="editNote" :disabled="isEditing">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="danger" @click="deleteNote">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </div>

          <!-- 笔记内容 -->
          <div class="note-content">
            <div v-if="!isEditing" class="note-view">
              <div class="markdown-content" v-html="renderedContent"></div>
            </div>
            <div v-else class="note-edit">
              <el-input
                v-model="editingNote.title"
                placeholder="笔记标题"
                class="title-input"
              />
              <el-input
                v-model="editingNote.content"
                type="textarea"
                placeholder="请输入笔记内容（支持Markdown）"
                :rows="20"
                class="content-input"
              />
              <div class="edit-actions">
                <el-button @click="cancelEdit">取消</el-button>
                <el-button type="primary" @click="saveNote" :loading="saving">保存</el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <el-empty description="请选择一个笔记查看详情">
            <el-button type="primary" @click="showCreateDialog = true">创建第一个笔记</el-button>
          </el-empty>
        </div>
      </main>
    </div>

    <!-- 创建笔记对话框 -->
    <CreateNoteDialog
      v-model="showCreateDialog"
      @created="handleNoteCreated"
    />

    <!-- 上传文件对话框 -->
    <UploadDialog
      v-model="showUploadDialog"
      @uploaded="handleFileUploaded"
    />


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  Search,
  Plus,
  Upload,
  ChatDotRound,
  FolderOpened,
  Edit,
  Delete,
  Refresh
} from '@element-plus/icons-vue'
import { notesApi } from '@/api'
import type { Note, Tag } from '@/types'
import CreateNoteDialog from './components/CreateNoteDialog.vue'
import UploadDialog from './components/UploadDialog.vue'

// 路由
const router = useRouter()

// 响应式数据
const notes = ref<Note[]>([])
const availableTags = ref<Tag[]>([])
const selectedNote = ref<Note | null>(null)
const searchKeyword = ref('')
const selectedTags = ref<string[]>([])
const selectedSourceType = ref('')
const loading = ref(false)
const saving = ref(false)
const isEditing = ref(false)

// 对话框状态
const showCreateDialog = ref(false)
const showUploadDialog = ref(false)

// 编辑状态
const editingNote = ref({
  title: '',
  content: ''
})

// 计算属性
const filteredNotes = computed(() => {
  let filtered = notes.value

  // 按关键词筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(note =>
      note.title.toLowerCase().includes(keyword) ||
      (note.content || '').toLowerCase().includes(keyword)
    )
  }

  // 按标签筛选
  if (selectedTags.value.length > 0) {
    filtered = filtered.filter(note =>
      note.tags.some(tag => selectedTags.value.includes(tag.name))
    )
  }

  // 按来源类型筛选
  if (selectedSourceType.value) {
    filtered = filtered.filter(note => note.source_type === selectedSourceType.value)
  }

  return filtered
})

const renderedContent = computed(() => {
  if (!selectedNote.value?.content) return ''
  // 这里可以集成Markdown渲染器
  return selectedNote.value.content.replace(/\n/g, '<br>')
})

// 方法
const loadNotes = async () => {
  loading.value = true
  try {
    const response = await notesApi.getNotes({ page: 1, page_size: 100 })
    notes.value = response.notes || []
  } catch (error) {
    ElMessage.error('加载笔记失败')
  } finally {
    loading.value = false
  }
}

const loadTags = async () => {
  try {
    availableTags.value = await notesApi.getTags()
  } catch (error) {
    // 忽略标签加载错误
  }
}

const refreshNotes = () => {
  loadNotes()
  loadTags()
}

// 智能问答点击处理
const handleQueryClick = () => {
  // 使用Vue Router进行页面跳转
  router.push('/query')
}

// 处理示例问题
const handleExampleQuery = (question: string) => {
  console.log('🔥 示例问题被点击:', question)
  // 跳转到智能问答页面并传递问题参数
  router.push({
    path: '/query',
    query: { q: question }
  })
}

const selectNote = async (note: Note) => {
  try {
    selectedNote.value = await notesApi.getNoteById(note.id)
    isEditing.value = false
  } catch (error) {
    console.error('加载笔记详情失败:', error)
    ElMessage.error('加载笔记详情失败')
  }
}

const editNote = () => {
  if (!selectedNote.value) return
  editingNote.value = {
    title: selectedNote.value.title,
    content: selectedNote.value.content || ''
  }
  isEditing.value = true
}

const cancelEdit = () => {
  isEditing.value = false
  editingNote.value = { title: '', content: '' }
}

const saveNote = async () => {
  if (!selectedNote.value) return

  saving.value = true
  try {
    const updatedNote = await notesApi.updateNote(selectedNote.value.id, {
      title: editingNote.value.title,
      content: editingNote.value.content
    })

    selectedNote.value = updatedNote
    isEditing.value = false

    // 更新列表中的笔记
    const index = notes.value.findIndex(n => n.id === updatedNote.id)
    if (index !== -1) {
      notes.value[index] = updatedNote
    }

    ElMessage.success('笔记保存成功')
  } catch (error) {
    console.error('保存笔记失败:', error)
    ElMessage.error('保存笔记失败')
  } finally {
    saving.value = false
  }
}

const deleteNote = async () => {
  if (!selectedNote.value) return

  try {
    await ElMessageBox.confirm('确定要删除这个笔记吗？', '确认删除', {
      type: 'warning'
    })

    await notesApi.deleteNote(selectedNote.value.id)

    // 从列表中移除
    notes.value = notes.value.filter(n => n.id !== selectedNote.value!.id)
    selectedNote.value = null

    ElMessage.success('笔记删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除笔记失败:', error)
      ElMessage.error('删除笔记失败')
    }
  }
}

const toggleTag = (tagName: string) => {
  const index = selectedTags.value.indexOf(tagName)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tagName)
  }
}

const handleSourceTypeChange = (sourceType: string) => {
  selectedSourceType.value = sourceType
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleNoteCreated = (note: Note) => {
  notes.value.unshift(note)
  selectedNote.value = note
  showCreateDialog.value = false
  ElMessage.success('笔记创建成功')
}

const handleFileUploaded = (note: Note) => {
  notes.value.unshift(note)
  selectedNote.value = note
  showUploadDialog.value = false
  ElMessage.success('文件上传成功')
}

// 工具方法
const getSourceTypeLabel = (type: string) => {
  const labelMap = {
    pdf: 'PDF',
    markdown: 'MD',
    manual: '手动'
  }
  return labelMap[type] || type
}

const getContentPreview = (content: string) => {
  if (!content) return '暂无内容'
  return content.length > 100 ? content.substring(0, 100) + '...' : content
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadNotes()
  loadTags()
})
</script>

<style lang="scss" scoped>
.notes-app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.app-header {
  display: flex;
  align-items: center;
  padding: 0 24px;
  height: 64px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  .header-left {
    flex: 0 0 auto;

    .app-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #303133;
    }
  }

  .header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    padding: 0 24px;

    .search-input {
      max-width: 400px;
      width: 100%;
    }
  }

  .header-right {
    flex: 0 0 auto;
    display: flex;
    gap: 12px;
  }
}

.app-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.app-sidebar {
  width: 320px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .sidebar-section {
    padding: 16px;
    border-bottom: 1px solid #f0f2f5;

    .section-title {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #606266;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .tags-filter {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .tag-item {
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        transform: translateY(-1px);
      }
    }
  }

  .source-menu {
    border: none;
    background: transparent;

    :deep(.el-menu-item) {
      height: 36px;
      line-height: 36px;
      margin-bottom: 4px;
      border-radius: 6px;

      &:hover {
        background: #f0f2f5;
      }

      &.is-active {
        background: #e6f7ff;
        color: #1890ff;
      }
    }
  }

  .notes-list-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-bottom: none;

    .notes-list {
      flex: 1;
      overflow-y: auto;
      padding: 0 16px 16px;

      .note-item {
        padding: 12px;
        margin-bottom: 8px;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s;
        background: white;

        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        }

        &.active {
          border-color: #409eff;
          background: #e6f7ff;
        }

        .note-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .note-title {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: #303133;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            flex: 1;
          }

          .note-source {
            font-size: 12px;
            color: #909399;
            background: #f0f2f5;
            padding: 2px 6px;
            border-radius: 4px;
            margin-left: 8px;
          }
        }

        .note-preview {
          margin: 0 0 8px 0;
          font-size: 12px;
          color: #606266;
          line-height: 1.4;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }

        .note-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .note-date {
            font-size: 11px;
            color: #909399;
          }

          .note-tags {
            display: flex;
            gap: 4px;
          }
        }
      }
    }
  }
}

.app-content {
  flex: 1;
  background: white;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.note-detail {
  height: 100%;
  display: flex;
  flex-direction: column;

  .note-detail-header {
    padding: 24px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .note-info {
      flex: 1;

      .note-title {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
      }

      .note-meta-info {
        display: flex;
        gap: 16px;
        font-size: 14px;
        color: #606266;
      }
    }

    .note-actions {
      display: flex;
      gap: 8px;
    }
  }

  .note-content {
    flex: 1;
    overflow: hidden;

    .note-view {
      height: 100%;
      overflow-y: auto;
      padding: 24px;

      .markdown-content {
        line-height: 1.6;
        color: #303133;
        font-size: 14px;
      }
    }

    .note-edit {
      height: 100%;
      padding: 24px;
      display: flex;
      flex-direction: column;
      gap: 16px;

      .title-input {
        :deep(.el-input__inner) {
          font-size: 18px;
          font-weight: 600;
        }
      }

      .content-input {
        flex: 1;

        :deep(.el-textarea__inner) {
          height: 100% !important;
          resize: none;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
      }

      .edit-actions {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
      }
    }
  }
}

.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 智能问答按钮样式
.query-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 15px 0 rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px 0 rgba(102, 126, 234, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
}

// 快速问答卡片样式
.quick-query-card {
  margin: 20px;

  .query-card {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border: none;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;

      .query-icon {
        font-size: 24px;
        color: #667eea;
      }
    }

    .query-content {
      .query-description {
        font-size: 16px;
        color: #5a6c7d;
        margin-bottom: 20px;
        text-align: center;
      }

      .query-examples {
        margin-bottom: 24px;

        .examples-title {
          font-size: 14px;
          color: #7f8c8d;
          margin-bottom: 12px;
        }

        .example-buttons {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          justify-content: center;
        }
      }

      .query-actions {
        text-align: center;

        .full-query-button {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
          border-radius: 25px;
          padding: 12px 32px;
          font-size: 16px;
          font-weight: 600;
          box-shadow: 0 4px 15px 0 rgba(102, 126, 234, 0.3);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px 0 rgba(102, 126, 234, 0.4);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-header {
    flex-direction: column;
    height: auto;
    padding: 16px;

    .header-center {
      order: -1;
      padding: 0 0 16px 0;
    }

    .header-right {
      justify-content: center;
    }
  }

  .app-sidebar {
    width: 100%;
    max-height: 40vh;
  }

  .app-main {
    flex-direction: column;
  }
}
</style>
