from langchain_core.tools import tool
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
import asyncio

# 如果要加入系统prompt，可以通过create_react_agent的prompt参数传递
# 例如，定义一个系统prompt
system_prompt = "你是一个数学家，可以适当调用工具。"

@tool
#两数乘积
def multiply(a: int, b: int) -> int:
    """
    计算两数乘积
    """
    return a * b

llm = ChatOpenAI(
    model="gpt-4o-mini",
    temperature=0,
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
)

client = MultiServerMCPClient(
    {
        "math": {
            "url": "http://localhost:8000/sse",
            "transport": "sse"
        }
    }
)

async def main():
    # 注意：client.get_tools() 是异步方法，需要 await
    tools = await client.get_tools()
    tools.append(multiply)
    print(tools)
    # 通过prompt参数传递系统prompt
    agent = create_react_agent(
        model=llm,
        tools=tools,
        prompt=system_prompt  # 这里传入系统prompt
    )

    # invoke/ainvoke 的参数通常为 {"messages": [{"role": "user", "content": ...}]}
    result = await agent.ainvoke({
        "messages": [{"role": "user", "content": "你好，请问3*3等于多少？"}]
    })
    print(result["messages"][-1].content)

if __name__ == "__main__":
    asyncio.run(main())
