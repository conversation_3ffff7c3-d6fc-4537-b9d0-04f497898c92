# 🚀 minerU PDF转Markdown 快速开始

## 📋 文件说明

| 文件名 | 功能 | 适用场景 |
|--------|------|----------|
| `基础转换.py` | 最简单的转换脚本 | 新手、标准文档 |
| `高质量转换.py` | 使用VLM模式的高质量转换 | 复杂文档、学术论文 |
| `批量转换.py` | 并发处理多个文件 | 大批量文档 |
| `高级转换.py` | 支持命令行参数 | 需要精确控制 |
| `配置文件.py` | 各种场景的配置 | 自定义配置 |
| `使用示例.py` | 展示不同用法 | 学习参考 |
| `使用说明.md` | 详细文档 | 完整指南 |

## ⚡ 快速开始

### 1. 安装依赖
```bash
uv add "mineru[core]" --default-index https://pypi.tuna.tsinghua.edu.cn/simple
```

### 2. 准备文件
```bash
# 创建目录
mkdir pdfs
mkdir output

# 将PDF文件放入pdfs目录
cp your_document.pdf pdfs/
```

### 3. 运行转换

#### 🎯 新手推荐（最简单）
```bash
python 基础转换.py
```

#### 🏆 高质量转换（推荐）
```bash
python 高质量转换.py
```

#### 📦 批量处理
```bash
python 批量转换.py
```

#### ⚙️ 高级配置
```bash
# 查看所有选项
python 高级转换.py --help

# 转换英文文档
python 高级转换.py --lang en

# 指定页面范围
python 高级转换.py --start-page 1 --end-page 10
```

## 🎯 选择建议

| 场景 | 推荐脚本 | 原因 |
|------|----------|------|
| 第一次使用 | `基础转换.py` | 最简单，出错率低 |
| 学术论文 | `高质量转换.py` | VLM模式，质量更高 |
| 大量文档 | `批量转换.py` | 并发处理，速度快 |
| 需要精确控制 | `高级转换.py` | 支持各种参数 |

## 📂 输出文件

转换完成后会生成：
- `.md` - Markdown文件（主要输出）
- `_middle.json` - 结构化数据
- `_content_list.json` - 内容列表
- `images/` - 提取的图片

## 🔧 常见问题

### 网络问题
```bash
# 设置环境变量使用国内源
export MINERU_MODEL_SOURCE="modelscope"
```

### 内存不足
```bash
# 使用基础转换模式
python 基础转换.py
```

### 转换质量不高
```bash
# 使用高质量模式
python 高质量转换.py
```

## 📚 更多信息

- 详细说明：查看 `使用说明.md`
- 使用示例：运行 `python 使用示例.py`
- 配置选项：查看 `配置文件.py` 