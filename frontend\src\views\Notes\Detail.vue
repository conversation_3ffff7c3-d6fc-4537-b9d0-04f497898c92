<template>
  <div class="note-detail">
    <div class="detail-header">
      <el-button @click="$router.back()" type="text">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <div class="header-actions">
        <el-button @click="editNote">
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <div v-else-if="note" class="note-content">
      <h1 class="note-title">{{ note.title }}</h1>
      
      <div class="note-meta">
        <div class="meta-item">
          <span class="meta-label">创建时间:</span>
          <span class="meta-value">{{ formatTime(note.created_at) }}</span>
        </div>
        <div class="meta-item">
          <span class="meta-label">更新时间:</span>
          <span class="meta-value">{{ formatTime(note.updated_at) }}</span>
        </div>
        <div class="meta-item">
          <span class="meta-label">文档类型:</span>
          <span class="meta-value">{{ getSourceTypeLabel(note.source_type) }}</span>
        </div>
        <div v-if="note.file_size" class="meta-item">
          <span class="meta-label">文件大小:</span>
          <span class="meta-value">{{ formatFileSize(note.file_size) }}</span>
        </div>
      </div>

      <div v-if="note.tags.length > 0" class="note-tags">
        <el-tag
          v-for="tag in note.tags"
          :key="tag.id"
          type="info"
          class="tag-item"
        >
          {{ tag.name }}
        </el-tag>
      </div>

      <div class="note-body">
        <div v-html="formattedContent"></div>
      </div>
    </div>

    <el-empty v-else description="笔记不存在" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useNotesStore } from '@/stores'
import { formatTime, formatFileSize } from '@/utils'
import { marked } from 'marked'
import { ArrowLeft, Edit } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const notesStore = useNotesStore()

const loading = ref(false)

const note = computed(() => notesStore.currentNote)

const formattedContent = computed(() => {
  if (!note.value?.content) return ''
  
  // 如果是Markdown格式，使用marked解析
  if (note.value.source_type === 'markdown') {
    return marked(note.value.content)
  }
  
  // 否则直接显示，保持换行
  return note.value.content.replace(/\n/g, '<br>')
})

const getSourceTypeLabel = (type: string) => {
  const labelMap = {
    pdf: 'PDF文档',
    markdown: 'Markdown',
    manual: '手动创建'
  }
  return labelMap[type] || type
}

const editNote = () => {
  router.push(`/notes/${route.params.id}/edit`)
}

const fetchNoteDetail = async () => {
  const noteId = parseInt(route.params.id as string)
  if (!noteId) return
  
  loading.value = true
  try {
    await notesStore.fetchNoteById(noteId)
  } catch (error) {
    ElMessage.error('获取笔记详情失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchNoteDetail()
})
</script>

<style lang="scss" scoped>
.note-detail {
  padding: var(--spacing-lg);
  max-width: 1000px;
  margin: 0 auto;
  
  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-lighter);
  }
  
  .loading-container {
    padding: var(--spacing-xl);
  }
  
  .note-content {
    .note-title {
      font-size: 32px;
      font-weight: var(--font-weight-bold);
      color: var(--text-primary);
      margin-bottom: var(--spacing-lg);
      line-height: 1.3;
    }
    
    .note-meta {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-lg);
      padding: var(--spacing-lg);
      background: var(--bg-page);
      border-radius: var(--border-radius-base);
      
      .meta-item {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
        
        .meta-label {
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
        }
        
        .meta-value {
          font-weight: var(--font-weight-medium);
          color: var(--text-primary);
        }
      }
    }
    
    .note-tags {
      margin-bottom: var(--spacing-lg);
      
      .tag-item {
        margin-right: var(--spacing-sm);
        margin-bottom: var(--spacing-sm);
      }
    }
    
    .note-body {
      line-height: 1.8;
      color: var(--text-primary);
      
      :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
        margin: var(--spacing-lg) 0 var(--spacing-md) 0;
        color: var(--text-primary);
      }
      
      :deep(p) {
        margin-bottom: var(--spacing-md);
      }
      
      :deep(ul), :deep(ol) {
        margin-bottom: var(--spacing-md);
        padding-left: var(--spacing-lg);
      }
      
      :deep(blockquote) {
        border-left: 4px solid var(--primary-color);
        padding-left: var(--spacing-md);
        margin: var(--spacing-md) 0;
        color: var(--text-secondary);
        font-style: italic;
      }
      
      :deep(code) {
        background: var(--bg-page);
        padding: 2px 4px;
        border-radius: 2px;
        font-family: 'Monaco', 'Consolas', monospace;
        font-size: 0.9em;
      }
      
      :deep(pre) {
        background: var(--bg-page);
        padding: var(--spacing-md);
        border-radius: var(--border-radius-base);
        overflow-x: auto;
        margin-bottom: var(--spacing-md);
        
        code {
          background: none;
          padding: 0;
        }
      }
      
      :deep(table) {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: var(--spacing-md);
        
        th, td {
          border: 1px solid var(--border-base);
          padding: var(--spacing-sm);
          text-align: left;
        }
        
        th {
          background: var(--bg-page);
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }
}

@include respond-below(md) {
  .note-detail {
    padding: var(--spacing-md);
    
    .note-content {
      .note-title {
        font-size: 24px;
      }
      
      .note-meta {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>
