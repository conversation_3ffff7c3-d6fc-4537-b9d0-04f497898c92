import pdf2image
import pytesseract
import os

file_path = "C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/黑神话悟空.pdf"
images = pdf2image.convert_from_path(file_path)

# 将 pdf 转化为图片并保存
for i, image in enumerate(images):
    image.save(f"C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/黑神话悟空.pdf_{i}.jpg")

"""
pytesseract 详细解释

pytesseract 是什么？
==================
pytesseract 是 Python 的 Tesseract OCR 引擎的包装器，用于从图像中提取文本。

主要功能：
1. 图像文字识别 (OCR - Optical Character Recognition)
2. 支持多种图像格式 (PNG, JPEG, TIFF, BMP 等)
3. 支持多种语言 (中文、英文、日文等)
4. 可以识别手写文字和印刷文字

使用场景：
- 文档数字化
- 图片文字提取
- 自动化数据录入
- 文档处理和分析
- RAG 系统中的文档解析

安装要求：
1. Python 包: pip install pytesseract
2. 系统引擎: 需要安装 Tesseract OCR 引擎
   - Windows: 下载安装包
   - Linux: sudo apt-get install tesseract-ocr
   - Mac: brew install tesseract

"""
# 使用 pytesseract 识别图片中的文字

for i, image in enumerate(images):
    text = pytesseract.image_to_string(image, lang="chi_sim")
    print(f"第 {i+1} 页:")
    print(text)
    print("-" * 50)




