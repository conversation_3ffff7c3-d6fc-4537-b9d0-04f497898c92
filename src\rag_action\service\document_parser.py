"""
文档解析服务
支持MinierU和PyMuPDF两种解析方式
"""
import os
import logging
import tempfile
import json
from typing import Dict, List, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

# 尝试导入MinierU - 使用正确的导入方式
try:
    from mineru.cli.common import convert_pdf_bytes_to_bytes_by_pypdfium2, prepare_env, read_fn
    from mineru.data.data_reader_writer import FileBasedDataWriter
    from mineru.utils.enum_class import MakeMode
    from mineru.backend.pipeline.pipeline_analyze import doc_analyze as pipeline_doc_analyze
    from mineru.backend.pipeline.pipeline_middle_json_mkcontent import union_make as pipeline_union_make
    from mineru.backend.pipeline.model_json_to_middle_json import result_to_middle_json as pipeline_result_to_middle_json
    MINERU_AVAILABLE = True
    logger.info("MinierU 可用")
except ImportError as e:
    MINERU_AVAILABLE = False
    logger.warning(f"MinierU 不可用，将使用PyMuPDF作为备选: {e}")

# 导入PyMuPDF作为备选
try:
    import pymupdf
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    logger.error("PyMuPDF 也不可用，PDF解析功能将不可用")


class DocumentParser:
    """文档解析器，支持多种解析方式"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.use_mineru = config.get("use_mineru", True) and MINERU_AVAILABLE
        self.fallback_parser = config.get("fallback_parser", "pymupdf")
        
        logger.info(f"文档解析器初始化: MinierU={'可用' if self.use_mineru else '不可用'}")
    
    def parse_pdf(self, file_path: str, filename: str = "") -> Dict[str, Any]:
        """
        解析PDF文件
        
        Args:
            file_path: PDF文件路径
            filename: 文件名（用于元数据）
            
        Returns:
            解析结果字典，包含页面映射和元数据
        """
        try:
            if self.use_mineru:
                return self._parse_with_mineru(file_path, filename)
            else:
                return self._parse_with_pymupdf(file_path, filename)
        except Exception as e:
            logger.error(f"PDF解析失败: {e}")
            # 尝试备选解析器
            if self.use_mineru and PYMUPDF_AVAILABLE:
                logger.info("尝试使用PyMuPDF备选解析器")
                return self._parse_with_pymupdf(file_path, filename)
            raise
    
    def _parse_with_mineru(self, file_path: str, filename: str) -> Dict[str, Any]:
        """使用MinierU解析PDF - 参考示例文件的正确实现"""
        if not MINERU_AVAILABLE:
            raise ImportError("MinierU不可用")

        logger.info(f"使用MinierU解析PDF: {filename}")

        try:
            # 创建临时输出目录
            with tempfile.TemporaryDirectory() as temp_dir:
                output_dir = Path(temp_dir) / "output"
                output_dir.mkdir(exist_ok=True)

                # 读取PDF文件
                pdf_bytes = read_fn(file_path)
                pdf_bytes_list = [pdf_bytes]
                file_name_list = [Path(filename).stem]
                lang_list = ["ch"]  # 默认中文

                # 使用pipeline模式进行解析
                infer_results, all_image_lists, all_pdf_docs, lang_list, ocr_enabled_list = pipeline_doc_analyze(
                    pdf_bytes_list,
                    lang_list,
                    parse_method="auto",
                    formula_enable=True,
                    table_enable=True
                )

                # 处理解析结果
                if infer_results and len(infer_results) > 0:
                    model_list = infer_results[0]
                    pdf_file_name = file_name_list[0]

                    # 准备环境
                    local_image_dir, local_md_dir = prepare_env(str(output_dir), pdf_file_name, "auto")
                    image_writer = FileBasedDataWriter(local_image_dir)

                    images_list = all_image_lists[0]
                    pdf_doc = all_pdf_docs[0]
                    _lang = lang_list[0]
                    _ocr_enable = ocr_enabled_list[0]

                    # 转换为中间JSON格式
                    middle_json = pipeline_result_to_middle_json(
                        model_list, images_list, pdf_doc, image_writer, _lang, _ocr_enable, True
                    )

                    # 获取PDF信息
                    if isinstance(middle_json, dict) and "pdf_info" in middle_json:
                        pdf_info = middle_json["pdf_info"]
                    else:
                        pdf_info = middle_json

                    # 生成Markdown内容
                    image_dir = str(os.path.basename(local_image_dir))
                    markdown_content = pipeline_union_make(pdf_info, MakeMode.MM_MD, image_dir)

                    # 提取页面信息
                    page_map = []
                    full_text = markdown_content

                    # 处理页面信息 - pdf_info是页面列表
                    if isinstance(pdf_info, list):
                        for i, page_info in enumerate(pdf_info, 1):
                            page_text = ""
                            if isinstance(page_info, dict):
                                # 从para_blocks中提取文本
                                if "para_blocks" in page_info:
                                    for block in page_info["para_blocks"]:
                                        if isinstance(block, dict):
                                            # 尝试不同的文本字段
                                            if "text" in block:
                                                page_text += block["text"] + "\n"
                                            elif "content" in block:
                                                page_text += block["content"] + "\n"
                                            elif "lines" in block:
                                                for line in block["lines"]:
                                                    if isinstance(line, dict) and "spans" in line:
                                                        for span in line["spans"]:
                                                            if isinstance(span, dict) and "text" in span:
                                                                page_text += span["text"] + " "
                                                    page_text += "\n"

                            # 如果没有提取到文本，使用空字符串
                            page_map.append({
                                "page": i,
                                "text": page_text.strip()
                            })

                    # 如果没有提取到页面信息，创建一个默认页面
                    if not page_map:
                        page_map.append({
                            "page": 1,
                            "text": markdown_content if markdown_content else "PDF解析完成，但未提取到文本内容"
                        })

                    return {
                        "content": full_text,
                        "markdown_content": markdown_content,
                        "page_map": page_map,
                        "metadata": {
                            "filename": filename,
                            "total_pages": len(page_map),
                            "parser": "mineru",
                            "has_images": True,
                            "has_tables": True,
                            "has_formulas": True
                        }
                    }
                else:
                    raise ValueError("MinierU解析未返回有效结果")

        except Exception as e:
            logger.error(f"MinierU解析失败: {e}")
            raise
    
    def _parse_with_pymupdf(self, file_path: str, filename: str) -> Dict[str, Any]:
        """使用PyMuPDF解析PDF"""
        if not PYMUPDF_AVAILABLE:
            raise ImportError("PyMuPDF不可用")
        
        logger.info(f"使用PyMuPDF解析PDF: {filename}")
        
        try:
            doc = pymupdf.open(file_path)
            page_map = []
            full_text = ""
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                page_text = page.get_text()
                
                page_map.append({
                    "page": page_num + 1,
                    "text": page_text
                })
                full_text += page_text + "\n\n"
            
            doc.close()
            
            return {
                "content": full_text.strip(),
                "page_map": page_map,
                "metadata": {
                    "filename": filename,
                    "total_pages": len(page_map),
                    "parser": "pymupdf",
                    "has_images": False,  # PyMuPDF基础版本不检测图片
                    "has_tables": False   # PyMuPDF基础版本不检测表格
                }
            }
            
        except Exception as e:
            logger.error(f"PyMuPDF解析失败: {e}")
            raise
    
    def parse_text(self, text: str, filename: str = "") -> Dict[str, Any]:
        """
        解析纯文本
        
        Args:
            text: 文本内容
            filename: 文件名
            
        Returns:
            解析结果字典
        """
        page_map = [{
            "page": 1,
            "text": text
        }]
        
        return {
            "content": text,
            "page_map": page_map,
            "metadata": {
                "filename": filename,
                "total_pages": 1,
                "parser": "text",
                "has_images": False,
                "has_tables": False
            }
        }


def get_document_parser(config: Dict[str, Any]) -> DocumentParser:
    """获取文档解析器实例"""
    return DocumentParser(config)
