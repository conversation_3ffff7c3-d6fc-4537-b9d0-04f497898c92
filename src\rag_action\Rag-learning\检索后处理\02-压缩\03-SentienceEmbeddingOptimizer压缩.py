from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from llama_index.core import VectorStoreIndex
from llama_index.core.readers import SimpleDirectoryReader
from llama_index.core.postprocessor import SentenceEmbeddingOptimizer
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding
# 1. 明确设置本地嵌入模型
embed_model = HuggingFaceEmbedding(model_name="BAAI/bge-small-zh")

# 2. 读取文档
documents = SimpleDirectoryReader("C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/陕西文旅").load_data()

# 3. 创建索引时传入本地嵌入模型
index = VectorStoreIndex.from_documents(
    documents, 
    embed_model=embed_model  # 明确指定使用本地模型
)

# 配置大模型（以OpenAI为例，可以替换为你自己的大模型）
llm = OpenAI(model="gpt-3.5-turbo", api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",api_base="https://api.zhizengzeng.com/v1")  # 请替换为你的API_KEY

# 不使用优化的查询，使用大模型
print("不使用优化（使用大模型）：")
query_engine = index.as_query_engine(llm=llm,embed_model=embed_model)
response = query_engine.query("山西省的主要旅游景点有哪些？")
print(f"答案：{response}")

# 使用优化（百分比截断），使用大模型
print("\n使用优化（percentile_cutoff=0.5，使用大模型）：")
query_engine = index.as_query_engine(
    llm=llm,
    embed_model=embed_model,
    node_postprocessors=[SentenceEmbeddingOptimizer(percentile_cutoff=0.5,embed_model=embed_model)]
)
response = query_engine.query("山西省的主要旅游景点有哪些？")
print(f"答案：{response}")

# 使用优化（阈值截断），使用大模型
print("\n使用优化（threshold_cutoff=0.7，使用大模型）：")
query_engine = index.as_query_engine(
    llm=llm,
    embed_model=embed_model,
    node_postprocessors=[SentenceEmbeddingOptimizer(threshold_cutoff=0.7,embed_model=embed_model)]
)
response = query_engine.query("山西省的主要旅游景点有哪些？")
print(f"答案：{response}")
