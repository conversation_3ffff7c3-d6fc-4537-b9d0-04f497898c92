file_path = "C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/云冈石窟-en.pdf"
from langchain_unstructured import UnstructuredLoader
loader = UnstructuredLoader(file_path=file_path,
                            strategy="hi_res")

docs = []
for doc in loader.lazy_load():
    docs.append(doc)

# 仅仅筛选第一页的doc
for i, doc in enumerate(docs, 1):
    print(f"Doc {i}:")
    print(f"内容: {doc.page_content}")
    print(f"分类: {doc.metadata.get('category')}")
    print(f"父ID: {doc.metadata.get('parent_id')}")
    print(f"元素ID: {doc.metadata.get('element_id')}")
    print("-" * 50)

# 仅仅筛选第一页的Title

page_number = 1
title_dict = {}

# 收集title,并建立parent_id -> title的映射
for doc in docs:
    if doc.metadata.get('category') == 'Title' and doc.metadata.get('page_number') == page_number:
        title_id = doc.metadata.get('element_id')
        title_text = doc.page_content.strip()
        # 避免重复添加相同的标题
        if title_text not in [data['title'] for data in title_dict.values()]:
            title_dict[title_id] = {
                'title': title_text,
                'content': []
            }

# 关联title 和 其对应的Text

for doc in docs:
    if doc.metadata.get('category') in ["NarrativeText", "Text"] and doc.metadata.get('page_number') == page_number:
        parent_id = doc.metadata.get('parent_id')
        if parent_id in title_dict:
            content = doc.page_content.strip()
            if content: #只添加非空内容
                title_dict[parent_id]['content'].append(content)

# 输出第一页的标题和内容
for title_id, data in title_dict.items():
    print(f"标题: {data['title']}")
    print(f"内容: {data['content']}")
    print("-" * 50)




