# Text-to-SQL向量存储优化指南

## 概述

本文档介绍了Text-to-SQL系统的向量存储优化方案，将原有的硬编码Few-Shot示例和数据库Schema迁移到Milvus向量数据库中，实现动态检索和智能匹配。

## 系统架构

### 优化前 vs 优化后

**优化前（硬编码方式）：**
- Few-Shot示例硬编码在代码中
- 数据库Schema信息固定
- 无法动态更新和扩展
- 检索效率低，无法根据查询相似度排序

**优化后（向量检索方式）：**
- Few-Shot示例存储在Milvus向量数据库
- Schema信息向量化存储
- 支持动态更新和扩展
- 基于语义相似度的智能检索

### 核心组件

1. **TextToSQLVectorStore**: 向量存储管理器
2. **FewShotExample**: Few-Shot示例数据结构
3. **SchemaInfo**: 数据库Schema信息数据结构
4. **TextToSQLService**: 优化后的SQL生成服务

## 数据存储结构

### Few-Shot示例集合 (text2sql_few_shot_examples)

```python
{
    "id": "自动生成的主键",
    "vector": "问题文本的向量嵌入",
    "question": "自然语言问题",
    "sql": "对应的SQL语句",
    "explanation": "查询说明",
    "category": "查询类别（aggregate/join/filter/general）",
    "difficulty": "难度等级（easy/medium/hard）",
    "created_at": "创建时间戳"
}
```

### 数据库Schema集合 (text2sql_database_schema)

```python
{
    "id": "自动生成的主键",
    "vector": "表结构描述的向量嵌入",
    "table_name": "表名",
    "description": "表描述",
    "ddl": "DDL语句",
    "columns": "列信息（JSON字符串）",
    "created_at": "创建时间戳"
}
```

## 使用指南

### 1. 数据迁移

首次使用需要将现有数据迁移到Milvus：

```bash
# 执行数据迁移脚本
python scripts/migrate_text2sql_to_milvus.py
```

### 2. 测试验证

验证向量检索功能是否正常：

```bash
# 运行测试脚本
python scripts/test_text2sql_vectorstore.py
```

### 3. 管理维护

使用管理工具进行日常维护：

```bash
# 添加单个Few-Shot示例
python scripts/manage_text2sql_vectorstore.py add-example \
    --question "有多少个PDF笔记？" \
    --sql "SELECT COUNT(*) as pdf_count FROM notes WHERE source_type = 'pdf';" \
    --explanation "统计PDF类型的笔记数量" \
    --category "aggregate" \
    --difficulty "easy"

# 从文件批量添加示例
python scripts/manage_text2sql_vectorstore.py add-examples-file \
    --file examples.json

# 搜索相关示例
python scripts/manage_text2sql_vectorstore.py search-examples \
    --query "笔记数量统计" \
    --top-k 5

# 搜索相关Schema
python scripts/manage_text2sql_vectorstore.py search-schemas \
    --query "笔记表结构" \
    --top-k 3

# 获取统计信息
python scripts/manage_text2sql_vectorstore.py stats

# 导出示例到文件
python scripts/manage_text2sql_vectorstore.py export-examples \
    --file exported_examples.json \
    --top-k 50
```

## API接口变化

### TextToSQLService类

**新增方法：**
- `_retrieve_relevant_schemas(question, top_k)`: 检索相关Schema
- `_retrieve_relevant_examples(question, top_k)`: 检索相关示例

**修改方法：**
- `_build_text_to_sql_prompt(question)`: 基于向量检索构建动态提示词
- `get_schema_info(query, top_k)`: 支持基于查询的Schema检索
- `get_few_shot_examples(query, top_k)`: 支持基于查询的示例检索

### 向量检索流程

```python
# 1. 用户提问
question = "有多少个笔记？"

# 2. 检索相关Schema（top-k=3）
relevant_schemas = vectorstore.search_schema_info(question, top_k=3)

# 3. 检索相关Few-Shot示例（top-k=5）
relevant_examples = vectorstore.search_few_shot_examples(question, top_k=5)

# 4. 动态构建提示词
prompt = build_prompt(schemas, examples, question)

# 5. 调用LLM生成SQL
sql_result = llm.invoke(prompt)
```

## 性能优化

### 向量检索优化

1. **索引类型**: 使用HNSW索引，平衡检索速度和准确性
2. **相似度度量**: 使用COSINE相似度，适合文本语义匹配
3. **向量维度**: 支持1536维（text-embedding-3-small）和3072维（text-embedding-3-large）

### 缓存策略

1. **集合加载**: 按需加载集合到内存
2. **检索缓存**: 相同查询的检索结果可以缓存
3. **嵌入缓存**: 相同文本的向量嵌入可以缓存

## 扩展功能

### 1. 动态示例学习

系统可以从用户的成功查询中学习新的Few-Shot示例：

```python
# 添加用户成功的查询作为新示例
def learn_from_user_query(question: str, sql: str, success: bool):
    if success:
        example = FewShotExample(
            question=question,
            sql=sql,
            explanation="用户成功查询",
            category="user_generated"
        )
        vectorstore.insert_few_shot_examples([example])
```

### 2. 查询分类优化

基于检索到的示例类别，可以进行查询分类：

```python
def classify_query(question: str) -> str:
    examples = vectorstore.search_few_shot_examples(question, top_k=3)
    categories = [ex.get('category', 'general') for ex in examples]
    return max(set(categories), key=categories.count)
```

### 3. 难度评估

根据检索到的示例难度，评估查询复杂度：

```python
def estimate_difficulty(question: str) -> str:
    examples = vectorstore.search_few_shot_examples(question, top_k=5)
    difficulties = [ex.get('difficulty', 'easy') for ex in examples]
    difficulty_scores = {'easy': 1, 'medium': 2, 'hard': 3}
    avg_score = sum(difficulty_scores.get(d, 1) for d in difficulties) / len(difficulties)
    
    if avg_score < 1.5:
        return 'easy'
    elif avg_score < 2.5:
        return 'medium'
    else:
        return 'hard'
```

## 监控和维护

### 1. 性能监控

- 检索延迟监控
- 向量相似度分布分析
- SQL生成成功率统计

### 2. 数据质量

- 定期检查示例质量
- 清理重复或低质量示例
- 更新过时的Schema信息

### 3. 版本管理

- 支持Schema版本控制
- 示例数据备份和恢复
- 渐进式数据更新

## 故障排除

### 常见问题

1. **向量检索返回空结果**
   - 检查Milvus连接状态
   - 确认集合是否正确加载
   - 验证嵌入服务是否正常

2. **SQL生成质量下降**
   - 检查检索到的示例质量
   - 调整top_k参数
   - 更新Few-Shot示例库

3. **性能问题**
   - 优化向量索引参数
   - 调整检索批次大小
   - 启用结果缓存

### 调试工具

```python
# 启用详细日志
logging.getLogger('rag_action.service.text_to_sql_vectorstore').setLevel(logging.DEBUG)

# 检查检索结果
def debug_retrieval(question: str):
    vectorstore = get_text_to_sql_vectorstore()
    
    # 检索示例
    examples = vectorstore.search_few_shot_examples(question, top_k=10)
    print(f"检索到{len(examples)}个示例，相似度范围: {min(ex.get('similarity_score', 0) for ex in examples):.3f} - {max(ex.get('similarity_score', 0) for ex in examples):.3f}")
    
    # 检索Schema
    schemas = vectorstore.search_schema_info(question, top_k=5)
    print(f"检索到{len(schemas)}个Schema，相似度范围: {min(sch.get('similarity_score', 0) for sch in schemas):.3f} - {max(sch.get('similarity_score', 0) for sch in schemas):.3f}")
```

## 总结

通过向量存储优化，Text-to-SQL系统实现了：

1. **动态检索**: 基于语义相似度的智能匹配
2. **可扩展性**: 支持动态添加和更新示例
3. **高性能**: 向量索引加速检索过程
4. **高质量**: 相关性更强的Few-Shot示例
5. **易维护**: 完善的管理工具和监控机制

这个优化方案显著提升了Text-to-SQL系统的智能化程度和实用性，为用户提供更准确、更相关的SQL生成服务。
