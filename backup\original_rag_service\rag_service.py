"""
RAG服务
实现检索增强生成的核心逻辑
"""
import logging
import time
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

from rag_action.core.config import get_settings
from rag_action.core.database import SessionLocal
from rag_action.models.database import Note, NoteChunk
from rag_action.models.schemas import QAR<PERSON>ponse, RetrievedDocument
from rag_action.service.vector_service import VectorService
from rag_action.service.embedding_service import get_embedding_service
from rag_action.service.ensemble_retriever import get_ensemble_retriever
from rag_action.service.query_processor import get_query_processor
from rag_action.service.reranker import get_retrieval_post_processor
from rag_action.service.intelligent_query_router import get_intelligent_query_router
from rag_action.service.database_query_service import get_database_query_service
from rag_action.service.text_to_sql_agent import get_text_to_sql_agent

logger = logging.getLogger(__name__)
settings = get_settings()


class LLMService:
    """大语言模型服务 - 使用LangChain接口"""

    def __init__(self):
        self.provider = settings.llm.provider.lower()
        self._init_client()

    def _init_client(self):
        """初始化LLM客户端 - 使用LangChain"""
        try:
            if self.provider == "openai":
                from langchain_openai import ChatOpenAI
                self.llm = ChatOpenAI(
                    openai_api_key=settings.llm.api_key,
                    openai_api_base=settings.llm.base_url,
                    model_name=settings.llm.model_name,
                    temperature=settings.llm.temperature,
                    max_tokens=settings.llm.max_tokens,
                    request_timeout=60,  # 请求超时
                    max_retries=3       # 重试次数
                )
            else:
                raise ValueError(f"不支持的LLM提供商: {self.provider}")

            logger.info(f"初始化LangChain LLM服务，提供商: {self.provider}")

        except Exception as e:
            logger.error(f"初始化LangChain LLM服务失败: {e}")
            raise
    
    def generate_response(self, prompt: str, conversation_context: Optional[List[Dict[str, str]]] = None) -> tuple[str, int]:
        """生成回答 - 使用LangChain接口，支持对话上下文（非流式）"""
        try:
            from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

            messages = []

            # 如果有对话上下文，构建消息历史
            if conversation_context:
                for ctx_msg in conversation_context:
                    role = ctx_msg.get("role", "user")
                    content = ctx_msg.get("content", "")

                    if role == "system":
                        messages.append(SystemMessage(content=content))
                    elif role == "assistant":
                        messages.append(AIMessage(content=content))
                    else:  # user
                        messages.append(HumanMessage(content=content))

            # 添加当前问题
            messages.append(HumanMessage(content=prompt))

            # 使用LangChain的invoke方法
            response = self.llm.invoke(messages)

            answer = response.content
            # 估算token数量
            total_context_tokens = sum(len(msg.content.split()) for msg in messages)
            answer_tokens = len(answer.split()) if answer else 0
            total_tokens = total_context_tokens + answer_tokens

            return answer, total_tokens

        except Exception as e:
            logger.error(f"LangChain生成回答失败: {e}")
            raise

    async def generate_response_stream(self, prompt: str, conversation_context: Optional[List[Dict[str, str]]] = None):
        """生成流式回答 - 使用LangChain流式接口"""
        try:
            from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

            messages = []

            # 如果有对话上下文，构建消息历史
            if conversation_context:
                for ctx_msg in conversation_context:
                    role = ctx_msg.get("role", "user")
                    content = ctx_msg.get("content", "")

                    if role == "system":
                        messages.append(SystemMessage(content=content))
                    elif role == "assistant":
                        messages.append(AIMessage(content=content))
                    else:  # user
                        messages.append(HumanMessage(content=content))

            # 添加当前问题
            messages.append(HumanMessage(content=prompt))

            # 使用LangChain的流式调用
            full_answer = ""
            token_count = 0

            async for chunk in self.llm.astream(messages):
                if hasattr(chunk, 'content') and chunk.content:
                    content = chunk.content
                    full_answer += content
                    token_count += 1

                    # 确保中文字符完整性
                    if self._is_complete_utf8_sequence(content):
                        yield {
                            "content": content,
                            "full_answer": full_answer,
                            "token_count": token_count,
                            "finished": False
                        }

            # 最终响应
            yield {
                "content": "",
                "full_answer": full_answer,
                "token_count": token_count,
                "finished": True
            }

        except Exception as e:
            logger.error(f"LangChain流式生成失败: {e}")
            # 回退到非流式生成
            answer, tokens = self.generate_response(prompt, conversation_context)
            yield {
                "content": answer,
                "full_answer": answer,
                "token_count": tokens,
                "finished": True
            }

    def _is_complete_utf8_sequence(self, text: str) -> bool:
        """检查文本是否为完整的UTF-8序列，避免中文字符截断"""
        if not text:
            return True

        try:
            # 如果是字符串，检查是否包含不完整的字节序列
            # 对于Python字符串，如果能正常处理就说明是完整的

            # 检查是否包含替换字符（通常表示编码问题）
            if '\ufffd' in text:
                return False

            # 尝试编码为UTF-8字节，然后解码回来
            encoded = text.encode('utf-8')
            decoded = encoded.decode('utf-8')

            # 检查是否一致
            return text == decoded

        except (UnicodeEncodeError, UnicodeDecodeError):
            return False
        except Exception:
            # 对于其他异常，保守地返回False
            return False

    def generate(self, prompt: str, max_tokens: int = None, temperature: float = None) -> str:
        """简化的生成方法，用于查询重写等场景"""
        try:
            from langchain_core.messages import HumanMessage

            # 临时调整参数
            original_max_tokens = self.llm.max_tokens
            original_temperature = self.llm.temperature

            if max_tokens:
                self.llm.max_tokens = max_tokens
            if temperature is not None:
                self.llm.temperature = temperature

            message = HumanMessage(content=prompt)
            response = self.llm.invoke([message])

            # 恢复原始参数
            self.llm.max_tokens = original_max_tokens
            self.llm.temperature = original_temperature

            return response.content

        except Exception as e:
            logger.error(f"LangChain简化生成失败: {e}")
            raise


class RAGService:
    """RAG服务类"""

    def __init__(self):
        # 尝试从服务管理器获取已预加载的服务，如果不存在则创建新的
        from rag_action.core.service_manager import service_manager

        # 获取向量服务
        self.vector_service = service_manager.get_service("vector_service")
        if self.vector_service is None:
            self.vector_service = VectorService()

        # 获取嵌入服务
        self.embedding_service = service_manager.get_service("embedding_service")
        if self.embedding_service is None:
            self.embedding_service = get_embedding_service()

        # 获取LLM服务
        self.llm_service = service_manager.get_service("llm_service")
        if self.llm_service is None:
            self.llm_service = LLMService()

        # 初始化新的检索组件（使用默认配置）
        default_ensemble_config = {
            "enabled": True,
            "vector": {"weight": 0.7, "top_k": 10},
            "bm25": {"weight": 0.3, "top_k": 10, "language": "chinese"},
            "final_top_k": 5
        }
        default_preprocessing_config = {
            "query_refinement": {"enabled": False},
            "self_query": {"enabled": False},
            "query_routing": {"enabled": False}
        }
        default_postprocessing_config = {
            "reranking": {
                "enabled": True,
                "model": "cross-encoder/ms-marco-MiniLM-L-12-v2",  # 使用更强的重排序模型
                "top_k": 3
            },
            "context_compression": {"enabled": True, "max_tokens": 4000}  # 增加token限制
        }

        self.ensemble_retriever = get_ensemble_retriever(
            self.vector_service,
            self.embedding_service,
            default_ensemble_config
        )
        self.query_processor = get_query_processor(
            self.llm_service,
            default_preprocessing_config
        )
        # 获取重排序服务 - 直接使用工厂函数
        self.post_processor = get_retrieval_post_processor(default_postprocessing_config)

        # 新增：智能查询路由和数据库查询服务
        self.query_router = get_intelligent_query_router()
        self.db_query_service = get_database_query_service()
        self.text_to_sql_agent = get_text_to_sql_agent()

        # 构建BM25索引
        self._initialize_bm25_index()

        logger.info("RAG服务初始化完成（包含智能查询路由）")

    def _initialize_bm25_index(self):
        """初始化BM25索引"""
        try:
            db = SessionLocal()
            self.ensemble_retriever.build_bm25_index(db)
            db.close()
            logger.info("BM25索引初始化完成")
        except Exception as e:
            logger.error(f"BM25索引初始化失败: {e}")

    async def answer_question(self, question: str, top_k: int = 5,
                            note_ids: Optional[List[int]] = None,
                            conversation_context: Optional[List[Dict[str, str]]] = None) -> QAResponse:
        """
        统一智能问答接口
        自动识别用户意图并路由到合适的处理方式：
        - 数据库查询：统计、计数、排序等结构化查询（使用Text-to-SQL + Agent纠错）
        - 知识库检索：概念解释、原理说明等语义查询（使用向量检索）
        - 混合查询：需要同时使用数据库和知识库的复杂查询
        """
        start_time = time.time()

        try:
            # 1. 智能查询路由 - 识别用户意图（传递对话上下文）
            routing_result = self.query_router.route_query(question, conversation_context)
            intent = routing_result.get("intent", "knowledge_search")
            confidence = routing_result.get("confidence", 0.5)

            logger.info(f"查询路由结果: {question} -> {intent} (置信度: {confidence:.2f})")

            # 2. 根据意图路由到不同的处理方式
            if intent == "simple_conversation":
                # 简单对话延续：直接基于上下文回答
                return await self._handle_simple_conversation(question, start_time, routing_result, conversation_context)
            elif intent == "context_based_answer":
                # 基于上下文的回答：不需要新的查询
                return await self._handle_context_based_answer(question, start_time, routing_result, conversation_context)
            elif intent == "database_query":
                # 数据库查询：使用Text-to-SQL + Agent纠错
                return await self._handle_database_query_with_agent(question, start_time, routing_result, conversation_context)
            elif intent == "hybrid_query":
                # 混合查询：结合数据库和知识库
                return await self._handle_hybrid_query_with_agent(question, top_k, note_ids, start_time, routing_result, conversation_context)
            else:  # knowledge_search
                # 知识库检索：语义搜索
                return await self._handle_knowledge_search(question, top_k, note_ids, start_time, routing_result, conversation_context)

        except Exception as e:
            logger.error(f"统一问答处理失败: {e}")
            return QAResponse(
                question=question,
                answer=f"抱歉，处理您的问题时出现错误: {str(e)}",
                sources=[],
                processing_time=time.time() - start_time,
                metadata={"error": str(e), "intent": "error"}
            )

    async def answer_question_stream(self, question: str, top_k: int = 5,
                                   note_ids: Optional[List[int]] = None,
                                   conversation_context: Optional[List[Dict[str, str]]] = None):
        """
        流式智能问答接口
        支持实时流式输出，提供更好的用户体验
        """
        start_time = time.time()

        try:
            # 1. 智能查询路由 - 识别用户意图
            routing_result = self.query_router.route_query(question, conversation_context)
            intent = routing_result.get("intent", "knowledge_search")
            confidence = routing_result.get("confidence", 0.5)

            logger.info(f"流式查询路由结果: {question} -> {intent} (置信度: {confidence:.2f})")

            # 2. 根据意图路由到不同的处理方式
            if intent == "simple_conversation":
                async for chunk in self._handle_simple_conversation_stream(question, start_time, routing_result, conversation_context):
                    yield chunk
            elif intent == "context_based_answer":
                async for chunk in self._handle_context_based_answer_stream(question, start_time, routing_result, conversation_context):
                    yield chunk
            elif intent == "database_query":
                async for chunk in self._handle_database_query_stream(question, start_time, routing_result, conversation_context):
                    yield chunk
            elif intent == "hybrid_query":
                async for chunk in self._handle_hybrid_query_stream(question, top_k, note_ids, start_time, routing_result, conversation_context):
                    yield chunk
            else:  # knowledge_search
                async for chunk in self._handle_knowledge_search_stream(question, top_k, note_ids, start_time, routing_result, conversation_context):
                    yield chunk

        except Exception as e:
            logger.error(f"流式问答处理失败: {e}")
            yield {
                "type": "error",
                "content": f"抱歉，处理您的问题时出现错误: {str(e)}",
                "finished": True,
                "processing_time": time.time() - start_time,
                "metadata": {"error": str(e), "intent": "error"}
            }

    async def _handle_knowledge_search_stream(self, question: str, top_k: int, note_ids: Optional[List[int]],
                                            start_time: float, routing_result: Dict,
                                            conversation_context: Optional[List[Dict[str, str]]] = None):
        """流式处理知识库检索"""
        try:
            # 1. 发送检索开始信号
            yield {
                "type": "retrieval_start",
                "content": "正在搜索相关知识...",
                "finished": False
            }

            # 2. 执行检索
            search_results = await self.ensemble_retriever.ensemble_search_unified(question, top_k, note_ids)

            # 3. 发送检索完成信号
            yield {
                "type": "retrieval_complete",
                "content": f"找到 {len(search_results)} 个相关文档",
                "finished": False,
                "sources_count": len(search_results)
            }

            if not search_results:
                yield {
                    "type": "answer",
                    "content": "抱歉，没有找到相关的信息来回答您的问题。",
                    "finished": True,
                    "processing_time": time.time() - start_time,
                    "sources": [],
                    "metadata": {"intent": "knowledge_search", "sources_found": 0}
                }
                return

            # 4. 构建检索文档对象
            retrieved_docs = []
            note_ids_found = list(set(doc["note_id"] for doc in search_results))
            note_titles = {}

            try:
                from ..models.database import Note
                from ..core.database import get_db

                db = next(get_db())
                notes = db.query(Note).filter(Note.id.in_(note_ids_found)).all()
                note_titles = {note.id: note.title for note in notes}
                db.close()
            except Exception as e:
                logger.warning(f"获取笔记标题失败: {e}")
                for note_id in note_ids_found:
                    note_titles[note_id] = f"笔记{note_id}"

            for doc in search_results:
                retrieved_doc = RetrievedDocument(
                    content=doc["content"],
                    note_id=doc["note_id"],
                    note_title=note_titles.get(doc["note_id"], f"笔记{doc['note_id']}"),
                    chunk_id=doc.get("chunk_id", doc.get("id")),
                    page_number=doc.get("page_number"),
                    similarity_score=doc.get("final_score", doc.get("hybrid_score", doc.get("similarity_score", 0)))
                )
                retrieved_docs.append(retrieved_doc)

            # 5. 发送生成开始信号
            yield {
                "type": "generation_start",
                "content": "正在生成回答...",
                "finished": False
            }

            # 6. 构建提示词
            context = "\n\n".join([f"文档{i+1}: {doc.content}" for i, doc in enumerate(retrieved_docs)])
            prompt = f"""基于以下检索到的文档内容，回答用户的问题。请确保回答准确、完整且有条理。

检索到的文档：
{context}

用户问题：{question}

请基于上述文档内容回答问题："""

            # 7. 流式生成回答
            full_answer = ""
            async for chunk in self.llm_service.generate_response_stream(prompt, conversation_context):
                if chunk["content"]:
                    yield {
                        "type": "answer_chunk",
                        "content": chunk["content"],
                        "finished": False
                    }
                full_answer = chunk["full_answer"]

                if chunk["finished"]:
                    # 8. 发送最终响应
                    yield {
                        "type": "answer_complete",
                        "content": full_answer,
                        "finished": True,
                        "processing_time": time.time() - start_time,
                        "sources": retrieved_docs,
                        "metadata": {
                            "intent": "knowledge_search",
                            "routing_confidence": routing_result.get("confidence", 0),
                            "sources_count": len(retrieved_docs),
                            "token_count": chunk["token_count"]
                        }
                    }

        except Exception as e:
            logger.error(f"流式知识搜索失败: {e}")
            yield {
                "type": "error",
                "content": f"知识搜索过程中出现错误: {str(e)}",
                "finished": True,
                "processing_time": time.time() - start_time,
                "metadata": {"error": str(e), "intent": "knowledge_search"}
            }

    async def _handle_simple_conversation_stream(self, question: str, start_time: float, routing_result: Dict,
                                               conversation_context: Optional[List[Dict[str, str]]] = None):
        """流式处理简单对话"""
        try:
            yield {
                "type": "generation_start",
                "content": "正在思考回答...",
                "finished": False
            }

            # 直接使用LLM生成回答
            full_answer = ""
            async for chunk in self.llm_service.generate_response_stream(question, conversation_context):
                if chunk["content"]:
                    yield {
                        "type": "answer_chunk",
                        "content": chunk["content"],
                        "finished": False
                    }
                full_answer = chunk["full_answer"]

                if chunk["finished"]:
                    yield {
                        "type": "answer_complete",
                        "content": full_answer,
                        "finished": True,
                        "processing_time": time.time() - start_time,
                        "sources": [],
                        "metadata": {
                            "intent": "simple_conversation",
                            "routing_confidence": routing_result.get("confidence", 0),
                            "token_count": chunk["token_count"]
                        }
                    }

        except Exception as e:
            logger.error(f"流式简单对话失败: {e}")
            yield {
                "type": "error",
                "content": f"对话过程中出现错误: {str(e)}",
                "finished": True,
                "processing_time": time.time() - start_time,
                "metadata": {"error": str(e), "intent": "simple_conversation"}
            }

    async def _handle_context_based_answer_stream(self, question: str, start_time: float, routing_result: Dict,
                                                conversation_context: Optional[List[Dict[str, str]]] = None):
        """流式处理基于上下文的回答"""
        try:
            yield {
                "type": "generation_start",
                "content": "正在基于上下文生成回答...",
                "finished": False
            }

            # 基于上下文生成回答
            full_answer = ""
            async for chunk in self.llm_service.generate_response_stream(question, conversation_context):
                if chunk["content"]:
                    yield {
                        "type": "answer_chunk",
                        "content": chunk["content"],
                        "finished": False
                    }
                full_answer = chunk["full_answer"]

                if chunk["finished"]:
                    yield {
                        "type": "answer_complete",
                        "content": full_answer,
                        "finished": True,
                        "processing_time": time.time() - start_time,
                        "sources": [],
                        "metadata": {
                            "intent": "context_based_answer",
                            "routing_confidence": routing_result.get("confidence", 0),
                            "token_count": chunk["token_count"]
                        }
                    }

        except Exception as e:
            logger.error(f"流式上下文回答失败: {e}")
            yield {
                "type": "error",
                "content": f"上下文回答过程中出现错误: {str(e)}",
                "finished": True,
                "processing_time": time.time() - start_time,
                "metadata": {"error": str(e), "intent": "context_based_answer"}
            }

    async def _handle_database_query_stream(self, question: str, start_time: float, routing_result: Dict,
                                          conversation_context: Optional[List[Dict[str, str]]] = None):
        """流式处理数据库查询"""
        try:
            yield {
                "type": "database_query_start",
                "content": "正在分析数据库查询...",
                "finished": False
            }

            # 执行数据库查询
            agent_result = await self.text_to_sql_agent.execute_natural_language_query_with_retry(question)

            if agent_result.success:
                yield {
                    "type": "database_query_complete",
                    "content": f"查询完成，找到 {agent_result.row_count} 条记录",
                    "finished": False
                }

                # 生成润色后的回答
                # 修复：SQLExecutionResult对象使用.data属性而不是.result属性
                # 测试结果：✅ 后端错误已修复，流式数据库查询正常工作
                prompt = f"""请基于以下数据库查询结果，用自然语言回答用户的问题：

用户问题：{question}
SQL查询：{agent_result.sql}
查询结果：{agent_result.data}

请用清晰、友好的语言解释查询结果："""

                full_answer = ""
                async for chunk in self.llm_service.generate_response_stream(prompt, conversation_context):
                    if chunk["content"]:
                        yield {
                            "type": "answer_chunk",
                            "content": chunk["content"],
                            "finished": False
                        }
                    full_answer = chunk["full_answer"]

                    if chunk["finished"]:
                        sources = [RetrievedDocument(
                            content=f"SQL查询: {agent_result.sql}",
                            note_title="数据库查询结果",
                            note_id=0,
                            chunk_id=0,
                            page_number=1,
                            similarity_score=1.0
                        )]

                        yield {
                            "type": "answer_complete",
                            "content": full_answer,
                            "finished": True,
                            "processing_time": time.time() - start_time,
                            "sources": sources,
                            "metadata": {
                                "intent": "database_query",
                                "routing_confidence": routing_result.get("confidence", 0),
                                "sql": agent_result.sql,
                                "row_count": agent_result.row_count,
                                "execution_time": agent_result.execution_time,
                                "retry_count": agent_result.retry_count,
                                "token_count": chunk["token_count"]
                            }
                        }
            else:
                yield {
                    "type": "error",
                    "content": f"数据库查询失败: {agent_result.error}",
                    "finished": True,
                    "processing_time": time.time() - start_time,
                    "metadata": {"error": agent_result.error, "intent": "database_query"}
                }

        except Exception as e:
            logger.error(f"流式数据库查询失败: {e}")
            yield {
                "type": "error",
                "content": f"数据库查询过程中出现错误: {str(e)}",
                "finished": True,
                "processing_time": time.time() - start_time,
                "metadata": {"error": str(e), "intent": "database_query"}
            }

    async def _handle_hybrid_query_stream(self, question: str, top_k: int, note_ids: Optional[List[int]],
                                        start_time: float, routing_result: Dict,
                                        conversation_context: Optional[List[Dict[str, str]]] = None):
        """流式处理混合查询"""
        try:
            yield {
                "type": "hybrid_query_start",
                "content": "正在执行混合查询...",
                "finished": False
            }

            # 混合查询可以先执行知识搜索，然后根据需要执行数据库查询
            # 这里先执行知识搜索
            async for chunk in self._handle_knowledge_search_stream(question, top_k, note_ids, start_time, routing_result, conversation_context):
                # 修改intent标识
                if "metadata" in chunk:
                    chunk["metadata"]["intent"] = "hybrid_query"
                yield chunk

        except Exception as e:
            logger.error(f"流式混合查询失败: {e}")
            yield {
                "type": "error",
                "content": f"混合查询过程中出现错误: {str(e)}",
                "finished": True,
                "processing_time": time.time() - start_time,
                "metadata": {"error": str(e), "intent": "hybrid_query"}
            }

    async def _handle_database_query_with_agent(self, question: str, start_time: float, routing_result: Dict,
                                              conversation_context: Optional[List[Dict[str, str]]] = None) -> QAResponse:
        """使用Text-to-SQL Agent处理数据库查询，具备自动纠错功能"""
        try:
            # 使用Text-to-SQL Agent执行查询（具备自动重试和纠错）
            agent_result = await self.text_to_sql_agent.execute_natural_language_query_with_retry(question)

            if agent_result.success:
                # 查询成功，使用大模型润色结果
                answer = await self._polish_database_result_with_llm(
                    agent_result, question, conversation_context
                )

                # 构建来源信息
                sources = [RetrievedDocument(
                    content=f"SQL查询: {agent_result.sql}",
                    note_title="数据库查询结果",
                    note_id=0,
                    chunk_id=0,  # 添加必需的chunk_id字段
                    page_number=1,
                    similarity_score=1.0
                )]

                return QAResponse(
                    question=question,
                    answer=answer,
                    sources=sources,
                    processing_time=time.time() - start_time,
                    metadata={
                        "intent": "database_query",
                        "routing_confidence": routing_result.get("confidence", 0),
                        "sql": agent_result.sql,
                        "row_count": agent_result.row_count,
                        "execution_time": agent_result.execution_time,
                        "retry_count": agent_result.retry_count,
                        "agent_used": True
                    }
                )
            else:
                # Agent重试后仍然失败，降级到知识库检索
                logger.warning(f"Text-to-SQL Agent执行失败（已重试{agent_result.retry_count}次），降级到知识库检索: {agent_result.error}")

                # 先尝试返回部分有用的错误信息
                if agent_result.error_analysis:
                    error_answer = f"数据库查询遇到问题：{agent_result.error_analysis}\n\n让我从知识库中为您查找相关信息："
                else:
                    error_answer = "数据库查询暂时无法完成，让我从知识库中为您查找相关信息："

                # 执行知识库检索作为备选
                kb_result = await self._handle_knowledge_search(question, 5, None, start_time, routing_result, conversation_context)

                # 合并错误信息和知识库结果
                kb_result.answer = error_answer + "\n\n" + kb_result.answer
                kb_result.metadata["fallback_from"] = "database_query"
                kb_result.metadata["database_error"] = agent_result.error
                kb_result.metadata["retry_count"] = agent_result.retry_count

                return kb_result

        except Exception as e:
            logger.error(f"Text-to-SQL Agent处理失败: {e}")
            # 降级到知识库检索
            return await self._handle_knowledge_search(question, 5, None, start_time, routing_result, conversation_context)

    async def _handle_hybrid_query_with_agent(self, question: str, top_k: int, note_ids: Optional[List[int]],
                                 start_time: float, routing_result: Dict,
                                 conversation_context: Optional[List[Dict[str, str]]] = None) -> QAResponse:
        """处理混合查询（数据库+知识库），使用Agent进行数据库查询"""
        try:
            # 1. 使用Agent尝试数据库查询
            agent_result = await self.text_to_sql_agent.execute_natural_language_query_with_retry(question)

            # 2. 执行知识库检索
            kb_result = await self._handle_knowledge_search(question, top_k, note_ids, start_time, routing_result, conversation_context)

            # 3. 融合结果
            if agent_result.success and agent_result.row_count > 0:
                # 有数据库结果，使用大模型融合两种结果
                combined_answer = await self._polish_hybrid_result_with_llm(
                    agent_result, kb_result, question, conversation_context
                )

                # 合并来源
                db_source = RetrievedDocument(
                    content=f"SQL查询: {agent_result.sql}",
                    note_title="数据库查询结果",
                    note_id=0,
                    chunk_id=0,  # 添加必需的chunk_id字段
                    page_number=1,
                    similarity_score=1.0
                )

                combined_sources = [db_source] + kb_result.sources

                return QAResponse(
                    question=question,
                    answer=combined_answer,
                    sources=combined_sources,
                    processing_time=time.time() - start_time,
                    metadata={
                        "intent": "hybrid_query",
                        "routing_confidence": routing_result.get("confidence", 0),
                        "db_success": True,
                        "db_retry_count": agent_result.retry_count,
                        "kb_sources": len(kb_result.sources),
                        "agent_used": True
                    }
                )
            else:
                # 数据库查询失败或无结果，返回知识库结果
                kb_result.metadata["intent"] = "hybrid_query"
                kb_result.metadata["db_success"] = False
                kb_result.metadata["db_retry_count"] = agent_result.retry_count
                kb_result.metadata["db_error"] = agent_result.error
                return kb_result

        except Exception as e:
            logger.error(f"混合查询处理失败: {e}")
            # 降级到知识库检索
            return await self._handle_knowledge_search(question, top_k, note_ids, start_time, routing_result, conversation_context)

    async def _handle_simple_conversation(self, question: str, start_time: float, routing_result: Dict,
                                        conversation_context: Optional[List[Dict[str, str]]] = None) -> QAResponse:
        """处理简单对话延续"""
        try:
            # 构建简单回应的提示词
            simple_prompt = f"""用户说了："{question}"

这是一个简单的对话延续。请给出自然、友好的回应。

回应要求：
1. 保持友好和礼貌
2. 如果用户表示感谢，要谦虚回应
3. 如果用户询问"还有吗"等，可以询问是否需要其他帮助
4. 回应要简洁，不超过50字

回应："""

            # 调用LLM生成简单回应
            answer, total_tokens = self.llm_service.generate_response(simple_prompt, conversation_context)

            return QAResponse(
                question=question,
                answer=answer,
                sources=[],
                processing_time=time.time() - start_time,
                total_tokens=total_tokens,
                metadata={
                    "intent": "simple_conversation",
                    "routing_confidence": routing_result.get("confidence", 0),
                    "context_based": True
                }
            )

        except Exception as e:
            logger.error(f"简单对话处理失败: {e}")
            return QAResponse(
                question=question,
                answer="好的，还有什么我可以帮助您的吗？",
                sources=[],
                processing_time=time.time() - start_time,
                metadata={"error": str(e), "intent": "simple_conversation"}
            )

    async def _handle_context_based_answer(self, question: str, start_time: float, routing_result: Dict,
                                         conversation_context: Optional[List[Dict[str, str]]] = None) -> QAResponse:
        """处理基于上下文的回答"""
        try:
            # 构建对话历史摘要
            context_summary = self._build_conversation_summary(conversation_context)

            # 构建基于上下文的提示词，明确包含对话历史
            context_prompt = f"""请基于以下对话历史回答用户的问题。

**对话历史：**
{context_summary}

**用户当前问题：**
{question}

**回答要求：**
1. 仔细分析对话历史中的信息
2. 如果问题涉及指代词（如"它"、"这个"、"第一句话"等），要明确指出指代的具体内容
3. 基于对话历史提供准确、相关的回答
4. 如果对话历史中信息不足，请诚实说明并建议用户提供更多信息

**回答：**"""

            # 调用LLM生成基于上下文的回答（不再传递conversation_context，因为已经在提示词中包含了）
            answer, total_tokens = self.llm_service.generate_response(context_prompt, None)

            return QAResponse(
                question=question,
                answer=answer,
                sources=[],
                processing_time=time.time() - start_time,
                total_tokens=total_tokens,
                metadata={
                    "intent": "context_based_answer",
                    "routing_confidence": routing_result.get("confidence", 0),
                    "context_based": True
                }
            )

        except Exception as e:
            logger.error(f"上下文回答处理失败: {e}")
            # 降级到知识库检索
            return await self._handle_knowledge_search(question, 5, None, start_time, routing_result, conversation_context)

    def _build_conversation_summary(self, conversation_context: Optional[List[Dict[str, str]]] = None) -> str:
        """构建对话历史摘要"""
        if not conversation_context:
            return "无对话历史"

        # 获取最近的对话（最多10轮，避免上下文过长）
        recent_context = conversation_context[-20:] if len(conversation_context) > 20 else conversation_context

        context_lines = []
        for msg in recent_context:
            role = msg.get("role", "user")
            content = msg.get("content", "").strip()

            if not content:
                continue

            # 限制每条消息的长度，避免上下文过长
            if len(content) > 200:
                content = content[:200] + "..."

            if role == "user":
                context_lines.append(f"用户: {content}")
            elif role == "assistant":
                context_lines.append(f"助手: {content}")
            elif role == "system":
                context_lines.append(f"系统: {content}")

        if not context_lines:
            return "无有效对话历史"

        return "\n".join(context_lines)

    async def _handle_knowledge_search(self, question: str, top_k: int, note_ids: Optional[List[int]],
                                     start_time: float, routing_result: Dict,
                                     conversation_context: Optional[List[Dict[str, str]]] = None) -> QAResponse:
        """处理知识库检索（原始逻辑）"""
        try:
            # 1. 查询预处理
            query_result = self.query_processor.process_query(question)
            processed_query = query_result["refined_query"]
            metadata_filters = query_result["metadata_filters"]

            logger.info(f"知识库检索: 重写查询={processed_query}")

            # 2. 混合检索（使用新的统一接口）
            search_results = await self.ensemble_retriever.ensemble_search_unified(processed_query, top_k)

            # 3. 应用元数据过滤
            if metadata_filters:
                search_results = self.query_processor.apply_metadata_filters(
                    search_results, metadata_filters
                )
            
            if not search_results:
                return QAResponse(
                    question=question,
                    answer="抱歉，我没有找到相关的文档内容来回答您的问题。",
                    sources=[],
                    processing_time=time.time() - start_time
                )
            
            # 4. 检索后处理（重排序和上下文压缩）
            post_process_result = self.post_processor.process(processed_query, search_results)
            final_documents = post_process_result["documents"]
            compressed_context = post_process_result["context"]

            # 5. 生成回答
            prompt = self._build_prompt(question, compressed_context)
            answer, total_tokens = self.llm_service.generate_response(prompt, conversation_context)

            # 6. 构建响应
            retrieved_docs = []

            # 获取所有相关的note_id，用于批量查询note标题
            note_ids = list(set(doc["note_id"] for doc in final_documents))
            note_titles = {}

            try:
                from ..models.database import Note
                from ..core.database import get_db

                db = next(get_db())
                notes = db.query(Note).filter(Note.id.in_(note_ids)).all()
                note_titles = {note.id: note.title for note in notes}
                db.close()
            except Exception as e:
                logger.warning(f"获取笔记标题失败: {e}")
                # 使用默认标题
                for note_id in note_ids:
                    note_titles[note_id] = f"笔记{note_id}"

            for doc in final_documents:
                retrieved_doc = RetrievedDocument(
                    content=doc["content"],
                    note_id=doc["note_id"],
                    note_title=note_titles.get(doc["note_id"], f"笔记{doc['note_id']}"),
                    chunk_id=doc.get("chunk_id", doc.get("id")),
                    page_number=doc.get("page_number"),
                    similarity_score=doc.get("final_score", doc.get("hybrid_score", doc.get("similarity_score", 0)))
                )
                retrieved_docs.append(retrieved_doc)

            response = QAResponse(
                question=question,
                answer=answer,
                sources=retrieved_docs,
                processing_time=time.time() - start_time,
                metadata={
                    "intent": routing_result.get("intent", "knowledge_search"),
                    "routing_confidence": routing_result.get("confidence", 0),
                    "total_tokens": total_tokens,
                    "search_results_count": len(search_results),
                    "final_documents_count": len(final_documents),
                    "processed_query": processed_query
                }
            )

            return response
            
        except Exception as e:
            logger.error(f"知识库检索失败: {e}")
            return QAResponse(
                question=question,
                answer=f"抱歉，处理您的问题时出现错误: {str(e)}",
                sources=[],
                processing_time=time.time() - start_time,
                metadata={"error": str(e), "intent": "knowledge_search"}
            )

    def _format_agent_database_result(self, agent_result, question: str, brief: bool = False) -> str:
        """格式化Agent数据库查询结果"""
        try:
            if not agent_result.success:
                return f"数据库查询失败: {agent_result.error}"

            data = agent_result.data or []
            row_count = agent_result.row_count
            retry_info = f"（重试{agent_result.retry_count}次后成功）" if agent_result.retry_count > 0 else ""

            if row_count == 0:
                return f"查询成功{retry_info}，但没有找到匹配的数据。"

            # 生成数据摘要
            if brief:
                if row_count == 1 and data:
                    # 单行结果，显示关键信息
                    first_row = data[0]
                    if len(first_row) == 1:
                        # 单个值（如计数）
                        value = list(first_row.values())[0]
                        return f"{value}{retry_info}"
                    else:
                        # 多个字段
                        return f"找到{row_count}条记录{retry_info}"
                else:
                    return f"找到{row_count}条记录{retry_info}"

            # 详细模式
            answer = f"查询成功{retry_info}，"

            # 如果是统计查询（只有一行）
            if row_count == 1 and data:
                answer += "结果如下：\n"
                for key, value in data[0].items():
                    answer += f"- {key}: {value}\n"

            # 如果是列表查询（多行）
            elif row_count > 1 and data:
                answer += f"找到{row_count}条记录"
                if row_count <= 5:
                    answer += "：\n"
                    for i, row in enumerate(data, 1):
                        answer += f"\n{i}. "
                        # 显示前3个字段
                        fields = list(row.items())[:3]
                        for key, value in fields:
                            answer += f"{key}: {value}, "
                        answer = answer.rstrip(", ")
                else:
                    answer += f"，显示前5条：\n"
                    for i, row in enumerate(data[:5], 1):
                        answer += f"\n{i}. "
                        fields = list(row.items())[:3]
                        for key, value in fields:
                            answer += f"{key}: {value}, "
                        answer = answer.rstrip(", ")
                    answer += f"\n... 还有{row_count - 5}条记录"

            # 添加SQL信息
            if agent_result.sql and not brief:
                answer += f"\n\n执行的SQL查询:\n```sql\n{agent_result.sql}\n```"

            return answer

        except Exception as e:
            logger.error(f"格式化Agent数据库结果失败: {e}")
            return f"数据库查询结果格式化失败: {str(e)}"

    def _format_database_result(self, db_result: Dict, brief: bool = False) -> str:
        """格式化数据库查询结果"""
        try:
            if not db_result.get("success", False):
                return f"数据库查询失败: {db_result.get('error', '未知错误')}"

            data = db_result.get("data", [])
            row_count = db_result.get("row_count", 0)
            summary = db_result.get("summary", "")

            if row_count == 0:
                return "查询成功，但没有找到匹配的数据。"

            # 简要模式
            if brief:
                return summary

            # 详细模式
            answer = summary + "\n\n"

            # 如果是统计查询（只有一行）
            if row_count == 1 and data:
                answer += "查询结果:\n"
                for key, value in data[0].items():
                    answer += f"- {key}: {value}\n"

            # 如果是列表查询（多行）
            elif row_count > 1 and data:
                answer += f"查询结果（前{min(5, row_count)}条）:\n"

                # 获取列名
                columns = db_result.get("columns", [])
                if columns:
                    # 表格格式
                    for i, row in enumerate(data[:5], 1):
                        answer += f"\n{i}. "
                        for col in columns[:3]:  # 只显示前3列
                            value = row.get(col, "")
                            answer += f"{col}: {value}, "
                        answer = answer.rstrip(", ")

                if row_count > 5:
                    answer += f"\n... 还有{row_count - 5}条记录"

            # 添加SQL信息
            sql = db_result.get("sql", "")
            if sql:
                answer += f"\n\n执行的SQL查询:\n```sql\n{sql}\n```"

            return answer

        except Exception as e:
            logger.error(f"格式化数据库结果失败: {e}")
            return f"数据库查询结果格式化失败: {str(e)}"

    async def _polish_database_result_with_llm(self, agent_result, question: str,
                                             conversation_context: Optional[List[Dict[str, str]]] = None) -> str:
        """使用大模型润色数据库查询结果，使其更自然易读"""
        try:
            # 1. 构建原始结果摘要
            raw_result = self._format_agent_database_result(agent_result, question, brief=False)

            # 2. 构建润色提示词
            polish_prompt = f"""请将以下数据库查询结果转换为结构化、友好的回答：

用户问题：{question}

查询结果：
{raw_result}

请按以下要求润色：
1. 用自然语言描述查询结果，避免技术术语
2. 使用Markdown格式美化输出：
   - 使用 **粗体** 突出关键信息
   - 使用表格展示多条记录
   - 使用列表展示要点
   - 使用代码块展示SQL（如果需要）
3. 如果有多条记录，用表格格式展示
4. 保持友好、专业的语调
5. 如果结果为空，给出有帮助的建议

示例格式：
## 查询结果

根据您的查询，我找到了以下信息：

| 字段名 | 值 |
|--------|-----|
| 标题 | 示例标题 |
| 时间 | 2024-01-01 |

**总结**：共找到 X 条记录。

润色后的回答："""

            # 3. 调用LLM进行润色
            polished_answer, _ = self.llm_service.generate_response(polish_prompt, conversation_context)

            # 4. 如果润色失败，返回原始格式化结果
            if not polished_answer or len(polished_answer.strip()) < 10:
                logger.warning("LLM润色结果过短，使用原始格式化结果")
                return raw_result

            return polished_answer

        except Exception as e:
            logger.error(f"数据库结果润色失败: {e}")
            # 降级到原始格式化结果
            return self._format_agent_database_result(agent_result, question, brief=False)

    async def _polish_hybrid_result_with_llm(self, agent_result, kb_result, question: str,
                                           conversation_context: Optional[List[Dict[str, str]]] = None) -> str:
        """使用大模型融合数据库和知识库查询结果"""
        try:
            # 1. 获取数据库结果摘要
            db_summary = self._format_agent_database_result(agent_result, question, brief=True)

            # 2. 构建融合提示词
            fusion_prompt = f"""请将以下数据库查询结果和知识库检索结果融合为一个完整、自然的回答：

用户问题：{question}

数据库查询结果：
{db_summary}

知识库检索结果：
{kb_result.answer}

请按以下要求融合：
1. 优先展示数据库的准确数据
2. 用知识库内容补充和解释数据
3. 确保信息的逻辑性和连贯性
4. 用自然语言表达，避免重复
5. 保持友好、专业的语调

融合后的回答："""

            # 3. 调用LLM进行融合
            fused_answer, _ = self.llm_service.generate_response(fusion_prompt, conversation_context)

            # 4. 如果融合失败，返回简单拼接结果
            if not fused_answer or len(fused_answer.strip()) < 10:
                logger.warning("LLM融合结果过短，使用简单拼接")
                return f"根据数据库查询：{db_summary}\n\n相关知识内容：{kb_result.answer}"

            return fused_answer

        except Exception as e:
            logger.error(f"混合结果融合失败: {e}")
            # 降级到简单拼接
            db_summary = self._format_agent_database_result(agent_result, question, brief=True)
            return f"根据数据库查询：{db_summary}\n\n相关知识内容：{kb_result.answer}"
    
    async def _retrieve_documents(self, question: str, top_k: int, 
                                note_ids: Optional[List[int]] = None) -> List[RetrievedDocument]:
        """检索相关文档"""
        try:
            # 生成问题的向量嵌入
            query_embedding = self.embedding_service.embed_text(question)
            
            # 向量搜索
            search_results = await self.vector_service.search_similar(
                query_embedding, top_k, note_ids
            )
            
            # 过滤低相似度结果
            filtered_results = [
                result for result in search_results 
                if result["similarity_score"] >= settings.rag.similarity_threshold
            ]
            
            # 获取笔记信息并构建响应
            retrieved_docs = []
            db = SessionLocal()
            
            try:
                for result in filtered_results:
                    # 获取笔记信息
                    note = db.query(Note).filter(Note.id == result["note_id"]).first()
                    if not note:
                        continue
                    
                    retrieved_doc = RetrievedDocument(
                        content=result["content"],
                        note_id=result["note_id"],
                        note_title=note.title,
                        chunk_id=result["chunk_id"],
                        page_number=result.get("page_number"),
                        similarity_score=result["similarity_score"]
                    )
                    
                    retrieved_docs.append(retrieved_doc)
                
                return retrieved_docs
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"文档检索失败: {e}")
            raise
    
    def _build_context(self, retrieved_docs: List[RetrievedDocument]) -> str:
        """构建上下文"""
        context_parts = []
        total_length = 0
        
        for i, doc in enumerate(retrieved_docs):
            # 构建文档片段
            doc_part = f"文档{i+1}（来源：{doc.note_title}"
            if doc.page_number:
                doc_part += f"，第{doc.page_number}页"
            doc_part += f"）：\n{doc.content}\n"
            
            # 检查长度限制
            if total_length + len(doc_part) > settings.rag.max_context_length:
                break
            
            context_parts.append(doc_part)
            total_length += len(doc_part)
        
        return "\n".join(context_parts)
    
    def _build_prompt(self, question: str, context: str) -> str:
        """构建提示词"""
        return settings.rag.prompt_template.format(
            context=context,
            question=question
        )
    
    async def search_documents(self, query: str, top_k: int = 10, 
                             filters: Dict[str, Any] = None) -> List[RetrievedDocument]:
        """搜索文档（不生成回答）"""
        try:
            # 生成查询向量
            query_embedding = self.embedding_service.embed_text(query)
            
            # 执行搜索
            if filters:
                search_results = await self.vector_service.hybrid_search(
                    query, filters, top_k
                )
            else:
                search_results = await self.vector_service.search_similar(
                    query_embedding, top_k
                )
            
            # 构建响应
            retrieved_docs = []
            db = SessionLocal()
            
            try:
                for result in search_results:
                    note = db.query(Note).filter(Note.id == result["note_id"]).first()
                    if not note:
                        continue
                    
                    retrieved_doc = RetrievedDocument(
                        content=result["content"],
                        note_id=result["note_id"],
                        note_title=note.title,
                        chunk_id=result["chunk_id"],
                        page_number=result.get("page_number"),
                        similarity_score=result["similarity_score"]
                    )
                    
                    retrieved_docs.append(retrieved_doc)
                
                return retrieved_docs
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"文档搜索失败: {e}")
            raise
    
    async def get_related_notes(self, note_id: int, top_k: int = 5) -> List[RetrievedDocument]:
        """获取相关笔记"""
        try:
            db = SessionLocal()
            
            try:
                # 获取当前笔记
                note = db.query(Note).filter(Note.id == note_id).first()
                if not note:
                    return []
                
                # 使用笔记标题作为查询
                query = note.title
                
                # 搜索相关文档，排除当前笔记
                query_embedding = self.embedding_service.embed_text(query)
                search_results = await self.vector_service.search_similar(
                    query_embedding, top_k * 2  # 获取更多结果以便过滤
                )
                
                # 过滤掉当前笔记的结果
                filtered_results = [
                    result for result in search_results 
                    if result["note_id"] != note_id
                ][:top_k]
                
                # 构建响应
                related_docs = []
                for result in filtered_results:
                    related_note = db.query(Note).filter(Note.id == result["note_id"]).first()
                    if not related_note:
                        continue
                    
                    related_doc = RetrievedDocument(
                        content=result["content"],
                        note_id=result["note_id"],
                        note_title=related_note.title,
                        chunk_id=result["chunk_id"],
                        page_number=result.get("page_number"),
                        similarity_score=result["similarity_score"]
                    )
                    
                    related_docs.append(related_doc)
                
                return related_docs
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取相关笔记失败: {e}")
            raise
    
    async def summarize_note(self, note_id: int) -> str:
        """生成笔记摘要"""
        try:
            db = SessionLocal()
            
            try:
                note = db.query(Note).filter(Note.id == note_id).first()
                if not note:
                    raise ValueError("笔记不存在")
                
                # 构建摘要提示词
                prompt = f"""请为以下笔记内容生成一个简洁的摘要（200字以内）：

标题：{note.title}

内容：
{note.content[:2000]}  # 限制内容长度

请生成摘要："""
                
                summary, _ = self.llm_service.generate_response(prompt)
                return summary
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"生成笔记摘要失败: {e}")
            raise
