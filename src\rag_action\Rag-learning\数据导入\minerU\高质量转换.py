#!/usr/bin/env python3
"""
高质量PDF转Markdown转换脚本
参考 minerU_pdf2markdown.py 的实现方式
使用VLM模式，适合复杂文档和学术论文
"""
import os
import sys
import json
from pathlib import Path
from loguru import logger

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入mineru相关模块
from mineru.cli.common import convert_pdf_bytes_to_bytes_by_pypdfium2, prepare_env, read_fn
from mineru.data.data_reader_writer import FileBasedDataWriter
from mineru.utils.draw_bbox import draw_layout_bbox, draw_span_bbox
from mineru.utils.enum_class import MakeMode
from mineru.backend.vlm.vlm_analyze import doc_analyze as vlm_doc_analyze
from mineru.backend.vlm.vlm_middle_json_mkcontent import union_make as vlm_union_make

def setup_logging():
    """设置日志"""
    logger.add("conversion.log", rotation="1 day", level="INFO")

def main():
    """高质量转换主函数 - 使用VLM模式"""
    setup_logging()
    print("🚀 开始高质量PDF转Markdown转换...")
    
    # 设置路径
    __dir__ = os.path.dirname(os.path.abspath(__file__))
    pdf_files_dir = os.path.join(__dir__, "pdfs")
    output_dir = os.path.join(__dir__, "output")
    
    # 确保目录存在
    os.makedirs(pdf_files_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)
    
    # 收集PDF文件
    pdf_suffixes = [".pdf"]
    doc_path_list = []
    
    for doc_path in Path(pdf_files_dir).glob('*'):
        if doc_path.suffix.lower() in pdf_suffixes:
            doc_path_list.append(doc_path)
    
    print(f"📁 找到 {len(doc_path_list)} 个PDF文件:")
    for path in doc_path_list:
        print(f"   - {path.name}")
    
    if not doc_path_list:
        print("❌ 未找到PDF文件！请将PDF文件放入 'pdfs' 目录")
        return
    
    # 开始转换
    try:
        print("\n🔄 开始高质量转换...")
        
        # 准备数据
        file_name_list = []
        pdf_bytes_list = []
        
        for path in doc_path_list:
            file_name = str(Path(path).stem)
            pdf_bytes = read_fn(path)
            file_name_list.append(file_name)
            pdf_bytes_list.append(pdf_bytes)
        
        # 使用VLM模式进行转换
        parse_with_vlm(
            output_dir=output_dir,
            pdf_file_names=file_name_list,
            pdf_bytes_list=pdf_bytes_list,
            backend="transformers"  # 使用transformers后端
        )
        
        print("✅ 高质量转换完成！")
        print(f"📂 输出目录: {output_dir}")
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        print("💡 如果VLM模式失败，请尝试使用基础转换模式")
        logger.exception(e)

def parse_with_vlm(
    output_dir,
    pdf_file_names: list[str],
    pdf_bytes_list: list[bytes],
    backend="transformers",
    server_url=None,
    start_page_id=0,
    end_page_id=None
):
    """使用VLM模式解析PDF"""
    
    # 处理backend名称
    if backend.startswith("vlm-"):
        backend = backend[4:]
    
    parse_method = "vlm"
    
    # 处理每个PDF文件
    for idx, pdf_bytes in enumerate(pdf_bytes_list):
        pdf_file_name = pdf_file_names[idx]
        
        # 处理页面范围
        pdf_bytes = convert_pdf_bytes_to_bytes_by_pypdfium2(pdf_bytes, start_page_id, end_page_id)
        
        # 准备环境
        local_image_dir, local_md_dir = prepare_env(output_dir, pdf_file_name, parse_method)
        image_writer, md_writer = FileBasedDataWriter(local_image_dir), FileBasedDataWriter(local_md_dir)
        
        # 使用VLM进行分析
        middle_json, infer_result = vlm_doc_analyze(
            pdf_bytes, 
            image_writer=image_writer, 
            backend=backend, 
            server_url=server_url
        )

        pdf_info = middle_json["pdf_info"]

        # 生成Markdown文件
        image_dir = str(os.path.basename(local_image_dir))
        md_content_str = vlm_union_make(pdf_info, MakeMode.MM_MD, image_dir)
        md_writer.write_string(f"{pdf_file_name}.md", md_content_str)

        # 生成内容列表
        content_list = vlm_union_make(pdf_info, MakeMode.CONTENT_LIST, image_dir)
        md_writer.write_string(
            f"{pdf_file_name}_content_list.json",
            json.dumps(content_list, ensure_ascii=False, indent=4)
        )

        # 生成中间JSON
        md_writer.write_string(
            f"{pdf_file_name}_middle.json",
            json.dumps(middle_json, ensure_ascii=False, indent=4)
        )

        # 生成模型输出
        model_output = ("\n" + "-" * 50 + "\n").join(infer_result)
        md_writer.write_string(f"{pdf_file_name}_model_output.txt", model_output)

        logger.info(f"输出目录: {local_md_dir}")

if __name__ == "__main__":
    main() 