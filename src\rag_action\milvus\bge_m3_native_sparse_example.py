"""
BGE-M3 原生稀疏向量 Milvus 混合检索示例

本示例展示如何使用 BGE-M3 模型原生生成的稀疏向量和密集向量
进行混合检索，并与 BM25 方法进行对比。

关键特性：
1. 使用 BGE-M3 原生稀疏向量（250,002 维）
2. 对比 BGE-M3 稀疏向量 vs BM25 稀疏向量
3. 展示学习的词汇扩展能力
4. 性能和准确性分析

作者: RAG Action Team
日期: 2025-08-05
"""

import os
import logging
import time
import numpy as np
from typing import List, Dict, Any, Tuple
from scipy.sparse import csr_matrix

# 设置环境变量（如果需要代理）
os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"
# os.environ["HTTP_PROXY"] = "http://127.0.0.1:1080"
# os.environ["HTTPS_PROXY"] = "http://127.0.0.1:1080"

from pymilvus import (
    MilvusClient,
    DataType,
    Function,
    FunctionType,
    AnnSearchRequest,
    WeightedRanker,
    RRFRanker
)
from pymilvus.model.hybrid import BGEM3EmbeddingFunction

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class BGEM3NativeSparseSearcher:
    """
    BGE-M3 原生稀疏向量混合检索器
    
    使用 BGE-M3 模型原生生成的稀疏向量和密集向量进行混合检索
    """
    
    def __init__(self, 
                 uri: str = "http://localhost:19530",
                 collection_name: str = "bge_m3_native_demo",
                 device: str = "cpu"):
        """
        初始化 BGE-M3 原生稀疏向量检索器
        
        Args:
            uri: Milvus 服务器地址
            collection_name: 集合名称
            device: 计算设备
        """
        self.uri = uri
        self.collection_name = collection_name
        self.client = None
        self.embedding_function = None
        self.device = device
        
        # BGE-M3 向量维度
        self.dense_dim = 1024      # BGE-M3 密集向量维度
        self.sparse_dim = 250002   # BGE-M3 稀疏向量维度（BERT 词汇表大小）
        
        self._init_bge_m3_model()
        logger.info(f"初始化 BGE-M3 原生稀疏向量检索器，URI: {uri}")
    
    def _init_bge_m3_model(self):
        """初始化 BGE-M3 模型"""
        try:
            logger.info("正在初始化 BGE-M3 模型...")
            self.embedding_function = BGEM3EmbeddingFunction(
                model_name='BAAI/bge-m3',
                device=self.device,
                use_fp16=False
            )
            
            # 验证维度
            self.dense_dim = self.embedding_function.dim["dense"]
            self.sparse_dim = self.embedding_function.dim["sparse"]
            
            logger.info(f"BGE-M3 模型初始化成功")
            logger.info(f"密集向量维度: {self.dense_dim}")
            logger.info(f"稀疏向量维度: {self.sparse_dim}")
            
        except Exception as e:
            logger.error(f"BGE-M3 模型初始化失败: {e}")
            raise
    
    def connect(self):
        """连接到 Milvus 数据库"""
        try:
            logger.info(f"正在连接到 Milvus 数据库: {self.uri}")
            self.client = MilvusClient(uri=self.uri)
            logger.info("Milvus 数据库连接成功")
        except Exception as e:
            logger.error(f"连接 Milvus 数据库失败: {e}")
            raise
    
    def create_collection_with_native_sparse(self, drop_existing: bool = True):
        """
        创建支持 BGE-M3 原生稀疏向量的集合
        
        注意：这里不使用 BM25 Function，而是直接存储 BGE-M3 生成的稀疏向量
        """
        try:
            # 检查并删除已存在的集合
            if drop_existing and self.client.has_collection(self.collection_name):
                logger.info(f"删除已存在的集合: {self.collection_name}")
                self.client.drop_collection(self.collection_name)
            
            # 创建集合模式
            schema = self.client.create_schema()
            
            # 添加字段
            schema.add_field(
                field_name="id", 
                datatype=DataType.INT64, 
                is_primary=True, 
                auto_id=True
            )
            schema.add_field(
                field_name="text", 
                datatype=DataType.VARCHAR, 
                max_length=2000
            )
            # BGE-M3 密集向量字段
            schema.add_field(
                field_name="bge_dense_vector", 
                datatype=DataType.FLOAT_VECTOR, 
                dim=self.dense_dim
            )
            # BGE-M3 原生稀疏向量字段
            schema.add_field(
                field_name="bge_sparse_vector", 
                datatype=DataType.SPARSE_FLOAT_VECTOR
            )
            
            # 注意：这里不添加 BM25 Function，因为我们直接使用 BGE-M3 的稀疏向量
            
            # 准备索引参数
            index_params = self.client.prepare_index_params()
            
            # 为密集向量添加索引
            index_params.add_index(
                field_name="bge_dense_vector",
                index_type="AUTOINDEX",
                metric_type="COSINE"
            )
            
            # 为稀疏向量添加索引
            index_params.add_index(
                field_name="bge_sparse_vector",
                index_type="SPARSE_INVERTED_INDEX",
                metric_type="IP"  # 使用内积，因为 BGE-M3 稀疏向量已经是权重
            )
            
            # 创建集合
            logger.info(f"正在创建集合: {self.collection_name}")
            self.client.create_collection(
                collection_name=self.collection_name,
                schema=schema,
                index_params=index_params
            )
            
            logger.info(f"集合 {self.collection_name} 创建成功")
            
        except Exception as e:
            logger.error(f"创建集合失败: {e}")
            raise
    
    def _convert_sparse_to_dict(self, sparse_matrix: csr_matrix) -> Dict[int, float]:
        """
        将 scipy 稀疏矩阵转换为 Milvus 支持的字典格式
        
        Args:
            sparse_matrix: scipy.sparse.csr_matrix
            
        Returns:
            Dict[int, float]: {index: value} 格式的稀疏向量
        """
        # 获取非零元素的索引和值
        row_indices, col_indices = sparse_matrix.nonzero()
        values = sparse_matrix.data
        
        # 转换为字典格式
        sparse_dict = {}
        for i, (col_idx, value) in enumerate(zip(col_indices, values)):
            sparse_dict[int(col_idx)] = float(value)
        
        return sparse_dict
    
    def insert_data_with_native_sparse(self, texts: List[str], batch_size: int = 10):
        """
        插入使用 BGE-M3 原生稀疏向量的数据
        
        Args:
            texts: 要插入的文本列表
            batch_size: 批处理大小（稀疏向量处理较慢，建议小批量）
        """
        try:
            logger.info(f"正在插入 {len(texts)} 条数据（使用 BGE-M3 原生稀疏向量）...")
            
            # 生成嵌入向量
            logger.info("正在生成 BGE-M3 嵌入向量...")
            embeddings = self.embedding_function.encode_documents(texts)
            
            # 分批插入数据
            total_inserted = 0
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                batch_dense = embeddings["dense"][i:i + batch_size]
                batch_sparse = embeddings["sparse"][i:i + batch_size]
                
                # 转换稀疏向量格式
                batch_sparse_dicts = []
                for j in range(len(batch_texts)):
                    sparse_row = batch_sparse.getrow(j)
                    sparse_dict = self._convert_sparse_to_dict(sparse_row)
                    batch_sparse_dicts.append(sparse_dict)
                
                # 准备批次数据
                batch_data = [
                    {
                        "text": text,
                        "bge_dense_vector": dense_vec,
                        "bge_sparse_vector": sparse_dict
                    }
                    for text, dense_vec, sparse_dict in zip(
                        batch_texts, batch_dense, batch_sparse_dicts
                    )
                ]
                
                # 插入数据
                result = self.client.insert(
                    collection_name=self.collection_name,
                    data=batch_data
                )
                
                total_inserted += result["insert_count"]
                logger.info(f"已插入 {total_inserted}/{len(texts)} 条数据")
            
            logger.info(f"数据插入完成，总计: {total_inserted} 条")
            
        except Exception as e:
            logger.error(f"插入数据失败: {e}")
            raise
    
    def dense_search(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """执行密集向量检索"""
        try:
            logger.info(f"执行 BGE-M3 密集向量检索: {query}")
            
            # 生成查询向量
            query_embeddings = self.embedding_function.encode_queries([query])
            query_dense = query_embeddings["dense"][0]
            
            # 执行检索
            results = self.client.search(
                collection_name=self.collection_name,
                data=[query_dense],
                anns_field="bge_dense_vector",
                limit=limit,
                output_fields=["text"],
                search_params={"metric_type": "COSINE"}
            )
            
            return [{"text": hit["entity"]["text"], "score": hit["distance"]} 
                   for hit in results[0]]
            
        except Exception as e:
            logger.error(f"密集向量检索失败: {e}")
            raise
    
    def native_sparse_search(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """执行 BGE-M3 原生稀疏向量检索"""
        try:
            logger.info(f"执行 BGE-M3 原生稀疏向量检索: {query}")
            
            # 生成查询稀疏向量
            query_embeddings = self.embedding_function.encode_queries([query])
            query_sparse = query_embeddings["sparse"][0]
            
            # 转换稀疏向量格式
            query_sparse_dict = self._convert_sparse_to_dict(query_sparse)
            
            # 执行检索
            results = self.client.search(
                collection_name=self.collection_name,
                data=[query_sparse_dict],
                anns_field="bge_sparse_vector",
                limit=limit,
                output_fields=["text"],
                search_params={"metric_type": "IP"}
            )
            
            return [{"text": hit["entity"]["text"], "score": hit["distance"]} 
                   for hit in results[0]]
            
        except Exception as e:
            logger.error(f"BGE-M3 稀疏向量检索失败: {e}")
            raise
    
    def hybrid_search_native(self, 
                           query: str, 
                           limit: int = 10,
                           sparse_weight: float = 0.7,
                           dense_weight: float = 1.0) -> List[Dict[str, Any]]:
        """执行 BGE-M3 原生混合检索"""
        try:
            logger.info(f"执行 BGE-M3 原生混合检索: {query}")
            
            # 生成查询向量
            query_embeddings = self.embedding_function.encode_queries([query])
            query_dense = query_embeddings["dense"][0]
            query_sparse = query_embeddings["sparse"][0]
            query_sparse_dict = self._convert_sparse_to_dict(query_sparse)
            
            # 创建密集向量检索请求
            dense_req = AnnSearchRequest(
                data=[query_dense],
                anns_field="bge_dense_vector",
                param={"metric_type": "COSINE"},
                limit=limit
            )
            
            # 创建稀疏向量检索请求
            sparse_req = AnnSearchRequest(
                data=[query_sparse_dict],
                anns_field="bge_sparse_vector",
                param={"metric_type": "IP"},
                limit=limit
            )
            
            # 使用加权重排序器
            reranker = WeightedRanker(sparse_weight, dense_weight)
            
            # 执行混合检索
            results = self.client.hybrid_search(
                collection_name=self.collection_name,
                reqs=[sparse_req, dense_req],
                ranker=reranker,
                limit=limit,
                output_fields=["text"]
            )
            
            return [{"text": hit["entity"]["text"], "score": hit["distance"]} 
                   for hit in results[0]]
            
        except Exception as e:
            logger.error(f"BGE-M3 混合检索失败: {e}")
            raise
    
    def analyze_sparse_vector(self, text: str) -> Dict[str, Any]:
        """
        分析 BGE-M3 稀疏向量的特性
        
        Args:
            text: 要分析的文本
            
        Returns:
            Dict: 稀疏向量分析结果
        """
        try:
            # 生成稀疏向量
            embeddings = self.embedding_function.encode_documents([text])
            sparse_vector = embeddings["sparse"][0]
            
            # 获取非零元素
            row_indices, col_indices = sparse_vector.nonzero()
            values = sparse_vector.data
            
            # 分析统计信息
            analysis = {
                "text": text,
                "total_dimensions": sparse_vector.shape[1],
                "non_zero_dimensions": len(values),
                "sparsity": 1 - (len(values) / sparse_vector.shape[1]),
                "max_weight": float(np.max(values)),
                "min_weight": float(np.min(values)),
                "mean_weight": float(np.mean(values)),
                "top_weights": []
            }
            
            # 获取权重最高的词汇
            top_indices = np.argsort(values)[-10:][::-1]  # 前10个最高权重
            for idx in top_indices:
                col_idx = col_indices[idx]
                weight = values[idx]
                analysis["top_weights"].append({
                    "vocab_index": int(col_idx),
                    "weight": float(weight)
                })
            
            return analysis
            
        except Exception as e:
            logger.error(f"稀疏向量分析失败: {e}")
            raise
    
    def close(self):
        """关闭连接"""
        try:
            logger.info("连接已关闭")
        except Exception as e:
            logger.warning(f"关闭连接时出现警告: {e}")


def create_sample_data() -> List[str]:
    """创建示例数据"""
    return [
        "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
        "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。",
        "深度学习是机器学习的一个分支，使用神经网络来模拟人脑的工作方式。",
        "自然语言处理是人工智能的一个领域，专注于计算机与人类语言之间的交互。",
        "计算机视觉是人工智能的一个分支，使计算机能够理解和解释视觉信息。",
        "向量数据库是专门用于存储和检索高维向量数据的数据库系统。",
        "Milvus 是一个开源的向量数据库，专为人工智能应用而设计。",
        "语义搜索使用向量嵌入来理解查询和文档之间的语义关系。",
        "混合检索结合了密集向量和稀疏向量的优势，提供更准确的搜索结果。",
        "RAG（检索增强生成）是一种结合信息检索和文本生成的技术。"
    ]


def compare_with_bm25_approach():
    """
    对比 BGE-M3 原生稀疏向量 vs BM25 稀疏向量的方法
    """
    logger.info("\n" + "="*60)
    logger.info("BGE-M3 原生稀疏向量 vs BM25 稀疏向量对比分析")
    logger.info("="*60)

    comparison = {
        "BGE-M3 原生稀疏向量": {
            "优势": [
                "✅ 学习的词汇重要性权重",
                "✅ 支持语义词汇扩展",
                "✅ 深度上下文理解",
                "✅ 多语言支持",
                "✅ 与密集向量来自同一模型，一致性好"
            ],
            "劣势": [
                "❌ 计算复杂度高",
                "❌ 需要 GPU 加速",
                "❌ 模型文件大",
                "❌ 稀疏向量维度高（250K+）"
            ],
            "适用场景": [
                "🎯 需要高精度语义匹配",
                "🎯 多语言检索",
                "🎯 复杂查询理解",
                "🎯 生产环境高质量检索"
            ]
        },
        "BM25 稀疏向量": {
            "优势": [
                "✅ 计算速度快",
                "✅ 内存占用小",
                "✅ 实现简单",
                "✅ 关键词匹配精确"
            ],
            "劣势": [
                "❌ 无语义理解",
                "❌ 无词汇扩展",
                "❌ 对同义词不敏感",
                "❌ 依赖精确词汇匹配"
            ],
            "适用场景": [
                "🎯 精确关键词搜索",
                "🎯 资源受限环境",
                "🎯 实时检索需求",
                "🎯 传统文档检索"
            ]
        }
    }

    for method, details in comparison.items():
        logger.info(f"\n📊 {method}:")
        for category, items in details.items():
            logger.info(f"  {category}:")
            for item in items:
                logger.info(f"    {item}")


def analyze_sparse_vector_characteristics():
    """分析 BGE-M3 稀疏向量特性"""
    logger.info("\n" + "="*50)
    logger.info("BGE-M3 稀疏向量特性分析")
    logger.info("="*50)

    try:
        # 初始化模型进行分析
        embedding_function = BGEM3EmbeddingFunction(
            model_name='BAAI/bge-m3',
            device='cpu',
            use_fp16=False
        )

        # 测试文本
        test_texts = [
            "机器学习算法",
            "深度神经网络",
            "人工智能应用"
        ]

        for text in test_texts:
            logger.info(f"\n🔍 分析文本: '{text}'")

            # 生成稀疏向量
            embeddings = embedding_function.encode_documents([text])
            sparse_vector = embeddings["sparse"][0]

            # 获取非零元素
            row_indices, col_indices = sparse_vector.nonzero()
            values = sparse_vector.data

            # 统计信息
            total_dims = sparse_vector.shape[1]
            non_zero_dims = len(values)
            sparsity = 1 - (non_zero_dims / total_dims)

            logger.info(f"  📈 总维度: {total_dims:,}")
            logger.info(f"  📈 非零维度: {non_zero_dims}")
            logger.info(f"  📈 稀疏度: {sparsity:.6f} ({sparsity*100:.4f}%)")
            logger.info(f"  📈 最大权重: {np.max(values):.4f}")
            logger.info(f"  📈 平均权重: {np.mean(values):.4f}")

            # 显示前5个最高权重
            top_indices = np.argsort(values)[-5:][::-1]
            logger.info(f"  🏆 前5个最高权重:")
            for i, idx in enumerate(top_indices, 1):
                col_idx = col_indices[idx]
                weight = values[idx]
                logger.info(f"    {i}. 词汇索引 {col_idx}: {weight:.4f}")

    except Exception as e:
        logger.error(f"稀疏向量分析失败: {e}")


def main():
    """主函数 - 演示 BGE-M3 原生稀疏向量混合检索"""

    try:
        logger.info("=== BGE-M3 原生稀疏向量混合检索示例开始 ===")
        logger.info("注意：此示例需要运行 Milvus 服务器")

        # 先进行理论分析
        compare_with_bm25_approach()
        analyze_sparse_vector_characteristics()

        # 初始化检索器
        searcher = BGEM3NativeSparseSearcher(
            uri="http://localhost:19530",
            collection_name="bge_m3_native_demo",
            device="cpu"
        )

        # 连接数据库
        searcher.connect()

        # 创建集合
        searcher.create_collection_with_native_sparse()

        # 准备示例数据
        sample_data = create_sample_data()
        logger.info(f"\n📝 准备插入 {len(sample_data)} 条示例数据")

        # 插入数据
        searcher.insert_data_with_native_sparse(sample_data, batch_size=5)

        # 等待数据索引完成
        time.sleep(3)

        # 执行检索测试
        query = "什么是机器学习和深度学习？"
        logger.info(f"\n🔍 检索查询: {query}")

        # 1. BGE-M3 密集向量检索
        logger.info(f"\n--- BGE-M3 密集向量检索结果 ---")
        dense_results = searcher.dense_search(query, limit=3)
        for i, result in enumerate(dense_results, 1):
            logger.info(f"{i}. [分数: {result['score']:.4f}] {result['text']}")

        # 2. BGE-M3 原生稀疏向量检索
        logger.info(f"\n--- BGE-M3 原生稀疏向量检索结果 ---")
        sparse_results = searcher.native_sparse_search(query, limit=3)
        for i, result in enumerate(sparse_results, 1):
            logger.info(f"{i}. [分数: {result['score']:.4f}] {result['text']}")

        # 3. BGE-M3 原生混合检索
        logger.info(f"\n--- BGE-M3 原生混合检索结果 ---")
        hybrid_results = searcher.hybrid_search_native(query, limit=3)
        for i, result in enumerate(hybrid_results, 1):
            logger.info(f"{i}. [分数: {result['score']:.4f}] {result['text']}")

        # 4. 稀疏向量特性分析
        logger.info(f"\n--- 查询稀疏向量分析 ---")
        analysis = searcher.analyze_sparse_vector(query)
        logger.info(f"查询文本: {analysis['text']}")
        logger.info(f"稀疏度: {analysis['sparsity']:.6f}")
        logger.info(f"非零维度: {analysis['non_zero_dimensions']}")
        logger.info(f"权重范围: {analysis['min_weight']:.4f} ~ {analysis['max_weight']:.4f}")

        logger.info(f"\n=== BGE-M3 原生稀疏向量示例执行完成 ===")

        # 性能总结
        logger.info(f"\n" + "="*50)
        logger.info("🎯 BGE-M3 原生稀疏向量优势总结:")
        logger.info("✅ 学习的词汇重要性，比 BM25 更智能")
        logger.info("✅ 支持语义词汇扩展，召回率更高")
        logger.info("✅ 与密集向量来自同一模型，一致性好")
        logger.info("✅ 适合高质量的生产环境检索")
        logger.info("="*50)

    except ConnectionError as e:
        logger.error("❌ 无法连接到 Milvus 服务器")
        logger.error("请确保 Milvus 服务器正在运行在 localhost:19530")
        logger.error("参考 docker_setup_guide.md 了解如何启动 Milvus")
        logger.error(f"错误详情: {e}")
    except Exception as e:
        logger.error(f"示例执行失败: {e}")
        raise
    finally:
        # 清理资源
        if 'searcher' in locals():
            searcher.close()


if __name__ == "__main__":
    main()
