"""
Milvus BM25混合检索器
基于Milvus 2.5原生BM25功能的混合检索实现
"""
import logging
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


class MilvusBM25EnsembleRetriever:
    """基于Milvus 2.5 BM25的混合检索器（支持父子文档检索和重排序）"""

    def __init__(self, vector_service, embedding_service, config: Dict[str, Any]):
        self.vector_service = vector_service
        self.embedding_service = embedding_service
        self.config = config

        # 检查Milvus是否启用BM25
        self.enable_bm25 = getattr(vector_service, 'enable_bm25', False)

        # 检索权重配置
        self.dense_weight = config.get("dense_weight", 0.6)
        self.sparse_weight = config.get("sparse_weight", 0.4)

        # 检索参数
        self.dense_top_k = config.get("dense_top_k", 10)
        self.sparse_top_k = config.get("sparse_top_k", 10)
        self.final_top_k = config.get("final_top_k", 5)

        # 父子文档检索配置（修复：添加父子文档检索支持）
        self.enable_parent_child = config.get("enable_parent_child", True)
        self.parent_child_strategy = config.get("parent_child_strategy", "child_first")

        # 重排序配置（修复：添加重排序支持）
        self.enable_reranking = config.get("enable_reranking", True)
        self.reranker = None

        # 初始化增强向量服务（用于父子文档检索）
        self.enhanced_vector_service = None
        if self.enable_parent_child:
            try:
                from .enhanced_vector_service import EnhancedVectorService
                self.enhanced_vector_service = EnhancedVectorService()
                logger.info("✅ 增强向量服务初始化成功，父子文档检索已启用")
            except Exception as e:
                logger.warning(f"增强向量服务初始化失败，父子文档检索将被禁用: {e}")
                self.enable_parent_child = False

        # 初始化重排序器
        if self.enable_reranking:
            try:
                from .reranker import Reranker
                reranker_config = {
                    "enabled": True,
                    "model": "cross-encoder/ms-marco-MiniLM-L-12-v2",
                    "top_k": self.final_top_k
                }
                self.reranker = Reranker(reranker_config)
                logger.info("✅ 重排序器初始化成功")
            except Exception as e:
                logger.warning(f"重排序器初始化失败，重排序功能将被禁用: {e}")
                self.enable_reranking = False

        logger.info(f"Milvus BM25混合检索器初始化: BM25={'启用' if self.enable_bm25 else '禁用'}, "
                   f"父子文档={'启用' if self.enable_parent_child else '禁用'}, "
                   f"重排序={'启用' if self.enable_reranking else '禁用'}")
    
    async def search_dense(self, query: str, top_k: int = None, note_ids: List[int] = None) -> List[Dict[str, Any]]:
        """密集向量检索"""
        if top_k is None:
            top_k = self.dense_top_k
        
        try:
            # 生成查询向量
            query_embedding = self.embedding_service.embed_text(query)
            
            # 执行向量搜索
            results = await self.vector_service.search(query_embedding, top_k, note_ids)
            
            # 标记结果来源
            for result in results:
                result["source"] = "dense"
                result["dense_score"] = result.get("similarity_score", 0.0)
            
            logger.debug(f"密集向量检索返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"密集向量检索失败: {e}")
            return []
    
    async def search_sparse(self, query: str, top_k: int = None, note_ids: List[int] = None) -> List[Dict[str, Any]]:
        """稀疏向量BM25检索"""
        if not self.enable_bm25:
            logger.warning("BM25功能未启用，跳过稀疏向量检索")
            return []
        
        if top_k is None:
            top_k = self.sparse_top_k
        
        try:
            # 执行BM25搜索
            results = await self.vector_service.search_bm25(query, top_k, note_ids)
            
            # 标记结果来源
            for result in results:
                result["source"] = "sparse"
                result["sparse_score"] = result.get("bm25_score", 0.0)
            
            logger.debug(f"稀疏向量BM25检索返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"稀疏向量BM25检索失败: {e}")
            return []
    
    async def hybrid_search_native(self, query: str, top_k: int = None, note_ids: List[int] = None) -> List[Dict[str, Any]]:
        """使用Milvus原生混合检索（优化版本）"""
        if not self.enable_bm25:
            logger.warning("BM25功能未启用，回退到密集向量检索")
            return await self.search_dense(query, top_k, note_ids)

        if top_k is None:
            top_k = self.final_top_k

        # 输入验证
        if not query or not query.strip():
            logger.warning("查询文本为空")
            return []

        try:
            # 生成查询向量
            query_embedding = self.embedding_service.embed_text(query.strip())

            # 执行Milvus原生混合搜索
            results = await self.vector_service.hybrid_search(query, query_embedding, top_k, note_ids)

            # 标记结果来源并优化结果格式
            for result in results:
                result["source"] = "hybrid_native"
                # 确保分数字段一致性
                if "hybrid_score" in result and "final_score" not in result:
                    result["final_score"] = result["hybrid_score"]

            logger.debug(f"原生混合检索返回 {len(results)} 个结果，查询: '{query[:50]}...'")
            return results

        except Exception as e:
            logger.error(f"原生混合检索失败: {e}")
            # 尝试分别执行BM25和密集向量检索作为备选方案
            try:
                logger.info("尝试BM25检索作为备选方案")
                bm25_results = await self.search_sparse(query, top_k, note_ids)
                if bm25_results:
                    return bm25_results
            except Exception as bm25_error:
                logger.error(f"BM25备选方案也失败: {bm25_error}")

            # 最终回退到密集向量检索
            logger.info("回退到密集向量检索")
            return await self.search_dense(query, top_k, note_ids)
    

    
    async def ensemble_search(self, query: str, top_k: int = None, note_ids: List[int] = None) -> List[Dict[str, Any]]:
        """
        统一的混合检索接口（修复：集成父子文档检索和重排序）

        Args:
            query: 查询文本
            top_k: 返回结果数量
            note_ids: 限制搜索的笔记ID
        """
        if top_k is None:
            top_k = self.final_top_k

        logger.info(f"🔍 开始混合检索: query='{query}', top_k={top_k}, "
                   f"parent_child={self.enable_parent_child}, reranking={self.enable_reranking}")

        # 第一阶段：执行基础混合检索
        if self.enable_bm25:
            initial_results = await self.hybrid_search_native(query, top_k * 2, note_ids)  # 获取更多结果用于后续处理
        else:
            logger.warning("BM25功能未启用，使用密集向量检索")
            initial_results = await self.search_dense(query, top_k * 2, note_ids)

        # 第二阶段：父子文档检索增强（修复：添加父子文档检索）
        if self.enable_parent_child and self.enhanced_vector_service and initial_results:
            try:
                logger.info("🔗 执行父子文档检索增强")
                enhanced_results = await self._enhance_with_parent_child(query, initial_results, top_k)
                initial_results = enhanced_results
            except Exception as e:
                logger.warning(f"父子文档检索增强失败，使用原始结果: {e}")

        # 第三阶段：重排序（修复：添加重排序功能）
        if self.enable_reranking and self.reranker and initial_results:
            try:
                logger.info("📊 执行重排序")
                reranked_results = await self._apply_reranking(query, initial_results, top_k)
                return reranked_results
            except Exception as e:
                logger.warning(f"重排序失败，使用原始结果: {e}")

        # 返回最终结果
        return initial_results[:top_k]

    async def _enhance_with_parent_child(self, query: str, initial_results: List[Dict[str, Any]],
                                       top_k: int) -> List[Dict[str, Any]]:
        """
        使用父子文档检索增强结果（修复：实现父子文档检索增强）

        Args:
            query: 查询文本
            initial_results: 初始检索结果
            top_k: 目标结果数量

        Returns:
            增强后的检索结果
        """
        try:
            # 生成查询向量
            query_embedding = self.embedding_service.embed_text(query)

            # 使用增强向量服务进行父子文档检索
            parent_child_results = self.enhanced_vector_service.search_with_parent_child(
                query_embedding=query_embedding,
                top_k=top_k,
                search_strategy=self.parent_child_strategy
            )

            # 合并和去重结果
            enhanced_results = []
            seen_ids = set()

            # 首先添加父子文档检索的结果（优先级更高）
            for result in parent_child_results:
                result_id = result.get("id") or result.get("chunk_id")
                if result_id and result_id not in seen_ids:
                    # 标记为父子文档检索结果
                    result["source"] = "parent_child"
                    result["parent_child_enhanced"] = True

                    # 确保包含父文档上下文信息
                    if "parent_context" not in result and result.get("search_type") == "child":
                        # 如果是子文档但没有父文档上下文，尝试添加
                        parent_id = result.get("metadata", {}).get("parent_id")
                        if parent_id:
                            result["parent_context"] = f"父文档ID: {parent_id}"

                    enhanced_results.append(result)
                    seen_ids.add(result_id)

            # 然后添加初始结果中未包含的项目
            for result in initial_results:
                result_id = result.get("id") or result.get("chunk_id")
                if result_id and result_id not in seen_ids:
                    enhanced_results.append(result)
                    seen_ids.add(result_id)

            logger.info(f"父子文档检索增强完成: {len(parent_child_results)}个父子文档结果, "
                       f"{len(enhanced_results)}个总结果")

            return enhanced_results[:top_k * 2]  # 返回更多结果供重排序使用

        except Exception as e:
            logger.error(f"父子文档检索增强失败: {e}")
            return initial_results

    async def _apply_reranking(self, query: str, results: List[Dict[str, Any]],
                             top_k: int) -> List[Dict[str, Any]]:
        """
        应用重排序（修复：实现重排序功能）

        Args:
            query: 查询文本
            results: 待重排序的结果
            top_k: 目标结果数量

        Returns:
            重排序后的结果
        """
        try:
            if not results:
                return results

            # 准备重排序数据
            rerank_docs = []
            for result in results:
                content = result.get("content", "")
                if not content:
                    # 尝试从metadata中获取内容
                    metadata = result.get("metadata", {})
                    content = metadata.get("content", "")

                if content:
                    rerank_docs.append({
                        "content": content,
                        "original_result": result
                    })

            if not rerank_docs:
                logger.warning("没有找到可重排序的内容")
                return results[:top_k]

            # 执行重排序
            reranked_docs = self.reranker.rerank(query, rerank_docs)

            # 构建最终结果
            final_results = []
            for doc in reranked_docs:
                original_result = doc["original_result"]

                # 添加重排序分数
                original_result["rerank_score"] = doc.get("rerank_score", 0.0)
                original_result["reranked"] = True

                # 更新来源信息
                if "source" not in original_result:
                    original_result["source"] = "reranked"
                else:
                    original_result["source"] = f"{original_result['source']}_reranked"

                final_results.append(original_result)

            logger.info(f"重排序完成: {len(reranked_docs)}个结果已重排序")

            return final_results[:top_k]

        except Exception as e:
            logger.error(f"重排序失败: {e}")
            return results[:top_k]
