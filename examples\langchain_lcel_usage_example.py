#!/usr/bin/env python3
"""
LangChain LCEL使用示例
演示如何使用新的LangChain LCEL增强版RAG服务

运行方式：
python examples/langchain_lcel_usage_example.py
"""
import asyncio
import json
import time
from typing import Dict, Any

# 导入新的RAG服务（完全兼容原有接口）
try:
    from src.rag_action.service.rag_service_adapter import RAGService
    from src.rag_action.service.langchain_rag_service import get_enhanced_rag_service, get_global_rag_service
    from src.rag_action.models.schemas import QAResponse
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保项目依赖已正确安装")
    exit(1)


async def basic_usage_example():
    """基础使用示例"""
    print("🚀 基础使用示例")
    print("=" * 50)
    
    # 创建RAG服务实例（自动选择最佳实现）
    rag_service = RAGService()
    
    # 获取服务信息
    service_info = rag_service.get_service_info()
    print(f"📊 当前使用实现: {service_info['service_type']}")
    print(f"📊 LangChain可用: {service_info['langchain_available']}")
    print(f"📊 版本: {service_info['version']}")
    
    # 基础问答
    print("\n🤖 基础问答测试:")
    question = "什么是人工智能？请简要介绍。"
    
    start_time = time.time()
    response = await rag_service.answer_question(question)
    end_time = time.time()
    
    print(f"❓ 问题: {question}")
    print(f"✅ 回答: {response.answer}")
    print(f"⏱️  响应时间: {end_time - start_time:.2f}s")
    print(f"📄 检索到的文档数: {len(response.sources)}")
    print(f"🔧 处理时间: {response.processing_time:.2f}s")
    
    if response.metadata:
        print(f"📋 元数据: {json.dumps(response.metadata, ensure_ascii=False, indent=2)}")


async def streaming_usage_example():
    """流式输出使用示例"""
    print("\n🌊 流式输出示例")
    print("=" * 50)
    
    rag_service = RAGService()
    
    question = "请详细解释机器学习的基本概念和应用场景。"
    print(f"❓ 问题: {question}")
    print("💬 流式回答:")
    
    full_answer = ""
    chunk_count = 0
    start_time = time.time()
    
    async for chunk in rag_service.answer_question_stream(question):
        chunk_count += 1
        chunk_type = chunk.get("type", "unknown")
        content = chunk.get("content", "")
        finished = chunk.get("finished", False)
        
        if chunk_type == "retrieval_start":
            print(f"🔍 {content}")
        elif chunk_type == "retrieval_complete":
            sources_count = chunk.get("sources_count", 0)
            print(f"📄 {content} (找到{sources_count}个文档)")
        elif chunk_type == "generation_start":
            print(f"🤖 {content}")
            print("📝 ", end="", flush=True)
        elif chunk_type == "answer_chunk":
            print(content, end="", flush=True)
            full_answer += content
        elif chunk_type == "answer_complete":
            full_answer = content  # 使用完整答案
            processing_time = chunk.get("processing_time", 0)
            sources = chunk.get("sources", [])
            metadata = chunk.get("metadata", {})
            
            print(f"\n\n✅ 流式输出完成!")
            print(f"⏱️  总处理时间: {processing_time:.2f}s")
            print(f"📊 数据块数量: {chunk_count}")
            print(f"📄 源文档数量: {len(sources)}")
            
            if metadata:
                implementation = metadata.get("implementation", "unknown")
                print(f"🔧 使用实现: {implementation}")
            
            break
        elif chunk_type == "error":
            print(f"\n❌ 错误: {content}")
            break
    
    end_time = time.time()
    print(f"⏱️  总耗时: {end_time - start_time:.2f}s")


async def advanced_usage_example():
    """高级使用示例"""
    print("\n🎯 高级使用示例")
    print("=" * 50)
    
    # 直接使用增强版服务
    try:
        enhanced_service = get_enhanced_rag_service()
        print("✅ 成功获取LangChain增强版服务")
        
        # 带参数的问答
        question = "深度学习在自然语言处理中的应用"
        response = await enhanced_service.answer_question(
            question=question,
            top_k=3,  # 限制检索文档数量
            note_ids=None,  # 不限制笔记范围
            conversation_context=[  # 提供对话上下文
                {"role": "user", "content": "我想了解AI技术"},
                {"role": "assistant", "content": "好的，我来为您介绍AI技术的相关内容。"}
            ]
        )
        
        print(f"❓ 问题: {question}")
        print(f"✅ 回答: {response.answer}")
        print(f"📄 检索文档数: {len(response.sources)}")
        
        # 显示源文档信息
        if response.sources:
            print("\n📚 源文档信息:")
            for i, source in enumerate(response.sources[:2], 1):  # 只显示前2个
                print(f"  {i}. 笔记: {source.note_title}")
                print(f"     内容: {source.content[:100]}...")
                print(f"     相似度: {source.similarity_score:.3f}")
        
    except Exception as e:
        print(f"⚠️  增强版服务不可用: {e}")
        print("🔄 自动降级到兼容版本")


async def performance_comparison_example():
    """性能对比示例"""
    print("\n⚡ 性能对比示例")
    print("=" * 50)
    
    # 测试问题列表
    test_questions = [
        "什么是机器学习？",
        "深度学习的原理是什么？",
        "自然语言处理有哪些应用？"
    ]
    
    rag_service = RAGService()
    
    print("🧪 开始性能测试...")
    
    total_time = 0
    successful_queries = 0
    
    for i, question in enumerate(test_questions, 1):
        try:
            start_time = time.time()
            response = await rag_service.answer_question(question)
            end_time = time.time()
            
            query_time = end_time - start_time
            total_time += query_time
            successful_queries += 1
            
            print(f"  测试 {i}: {query_time:.2f}s - ✅")
            
        except Exception as e:
            print(f"  测试 {i}: 失败 - ❌ ({e})")
    
    if successful_queries > 0:
        avg_time = total_time / successful_queries
        print(f"\n📊 性能统计:")
        print(f"  成功查询: {successful_queries}/{len(test_questions)}")
        print(f"  平均响应时间: {avg_time:.2f}s")
        print(f"  总耗时: {total_time:.2f}s")
        
        # 性能评级
        if avg_time < 1.0:
            grade = "🚀 优秀"
        elif avg_time < 2.0:
            grade = "✅ 良好"
        elif avg_time < 3.0:
            grade = "⚠️  一般"
        else:
            grade = "❌ 需要优化"
        
        print(f"  性能评级: {grade}")


async def error_handling_example():
    """错误处理示例"""
    print("\n🛡️  错误处理示例")
    print("=" * 50)
    
    rag_service = RAGService()
    
    # 测试各种边界情况
    test_cases = [
        ("", "空问题"),
        ("a" * 10000, "超长问题"),
        ("🤖💻🔬", "特殊字符问题"),
        ("正常问题", "正常情况")
    ]
    
    for question, description in test_cases:
        try:
            print(f"🧪 测试 {description}...")
            
            if len(question) > 100:
                display_question = question[:50] + "..." + question[-50:]
            else:
                display_question = question
            
            response = await rag_service.answer_question(question)
            
            print(f"  ✅ 成功: {display_question}")
            print(f"  📝 回答长度: {len(response.answer)} 字符")
            
        except Exception as e:
            print(f"  ❌ 失败: {description} - {str(e)[:100]}")


async def main():
    """主函数"""
    print("🎉 LangChain LCEL使用示例")
    print("=" * 80)
    
    try:
        # 运行各种示例
        await basic_usage_example()
        await streaming_usage_example()
        await advanced_usage_example()
        await performance_comparison_example()
        await error_handling_example()
        
        print("\n🎊 所有示例运行完成!")
        print("=" * 80)
        
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断执行")
    except Exception as e:
        print(f"\n\n❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
