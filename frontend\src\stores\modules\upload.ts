import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import { notesApi, systemApi } from '@/api'
import type { UploadFile, TaskStatus } from '@/types'
import { generateId } from '@/utils'

interface UploadState {
  // 上传文件列表
  files: UploadFile[]
  
  // 任务状态映射
  tasks: Record<string, TaskStatus>
  
  // 上传配置
  config: {
    maxFileSize: number // MB
    allowedTypes: string[]
    maxConcurrent: number
    autoUpload: boolean
  }
  
  // 全局上传状态
  uploading: boolean
  
  // 上传统计
  stats: {
    total: number
    success: number
    failed: number
    pending: number
  }
}

export const useUploadStore = defineStore('upload', {
  state: (): UploadState => ({
    files: [],
    tasks: {},
    config: {
      maxFileSize: 100, // 100MB
      allowedTypes: ['.pdf', '.md', '.txt'],
      maxConcurrent: 3,
      autoUpload: true
    },
    uploading: false,
    stats: {
      total: 0,
      success: 0,
      failed: 0,
      pending: 0
    }
  }),

  getters: {
    // 获取待上传文件
    pendingFiles: (state) => state.files.filter(file => file.status === 'waiting'),
    
    // 获取上传中文件
    uploadingFiles: (state) => state.files.filter(file => file.status === 'uploading'),
    
    // 获取成功文件
    successFiles: (state) => state.files.filter(file => file.status === 'success'),
    
    // 获取失败文件
    failedFiles: (state) => state.files.filter(file => file.status === 'error'),
    
    // 计算总进度
    totalProgress: (state) => {
      if (state.files.length === 0) return 0
      const totalProgress = state.files.reduce((sum, file) => sum + file.progress, 0)
      return Math.round(totalProgress / state.files.length)
    },
    
    // 是否有文件在上传
    hasUploading: (state) => state.files.some(file => file.status === 'uploading')
  },

  actions: {
    // 添加文件
    addFiles(fileList: FileList | File[]) {
      const files = Array.from(fileList)
      
      files.forEach(file => {
        // 验证文件
        const validation = this.validateFile(file)
        if (!validation.valid) {
          ElMessage.error(validation.message)
          return
        }
        
        const uploadFile: UploadFile = {
          id: generateId(),
          name: file.name,
          size: file.size,
          status: 'waiting',
          progress: 0,
          file
        }
        
        this.files.push(uploadFile)
      })
      
      this.updateStats()
      
      // 自动上传
      if (this.config.autoUpload) {
        this.startUpload()
      }
    },

    // 验证文件
    validateFile(file: File): { valid: boolean; message?: string } {
      // 检查文件大小
      if (file.size > this.config.maxFileSize * 1024 * 1024) {
        return {
          valid: false,
          message: `文件大小不能超过 ${this.config.maxFileSize}MB`
        }
      }
      
      // 检查文件类型
      const extension = '.' + file.name.split('.').pop()?.toLowerCase()
      if (!this.config.allowedTypes.includes(extension)) {
        return {
          valid: false,
          message: `不支持的文件类型，支持的类型：${this.config.allowedTypes.join(', ')}`
        }
      }
      
      return { valid: true }
    },

    // 开始上传
    async startUpload() {
      if (this.uploading) return
      
      this.uploading = true
      const pendingFiles = this.pendingFiles.slice(0, this.config.maxConcurrent)
      
      const uploadPromises = pendingFiles.map(file => this.uploadFile(file))
      
      try {
        await Promise.allSettled(uploadPromises)
      } finally {
        this.uploading = false
        
        // 如果还有待上传文件，继续上传
        if (this.pendingFiles.length > 0) {
          setTimeout(() => this.startUpload(), 1000)
        }
      }
    },

    // 上传单个文件
    async uploadFile(uploadFile: UploadFile) {
      const file = uploadFile.file as File
      
      try {
        uploadFile.status = 'uploading'
        uploadFile.progress = 0
        
        // 根据文件类型选择上传接口
        let result
        if (file.name.endsWith('.pdf')) {
          result = await notesApi.uploadPdfAsync(file)
        } else if (file.name.endsWith('.md')) {
          result = await notesApi.uploadMarkdown(file)
        } else {
          throw new Error('不支持的文件类型')
        }
        
        // 如果是异步上传，监控任务进度
        if (result.task_id) {
          await this.monitorTask(uploadFile, result.task_id)
        } else {
          uploadFile.status = 'success'
          uploadFile.progress = 100
          uploadFile.result = result
        }
      } catch (error) {
        uploadFile.status = 'error'
        uploadFile.error = error instanceof Error ? error.message : '上传失败'
        console.error('文件上传失败:', error)
      } finally {
        this.updateStats()
      }
    },

    // 监控任务进度
    async monitorTask(uploadFile: UploadFile, taskId: string) {
      const maxAttempts = 300 // 最多5分钟
      let attempts = 0
      
      while (attempts < maxAttempts) {
        try {
          const taskStatus = await systemApi.getTaskStatus(taskId)
          this.tasks[taskId] = taskStatus
          
          // 更新文件进度
          if (taskStatus.progress) {
            uploadFile.progress = taskStatus.progress.progress
          }
          
          if (taskStatus.status === 'completed') {
            uploadFile.status = 'success'
            uploadFile.progress = 100
            uploadFile.result = taskStatus.result
            break
          } else if (taskStatus.status === 'failed') {
            uploadFile.status = 'error'
            uploadFile.error = taskStatus.error || '处理失败'
            break
          }
          
          // 等待2秒后继续检查
          await new Promise(resolve => setTimeout(resolve, 2000))
          attempts++
        } catch (error) {
          console.error('获取任务状态失败:', error)
          attempts++
          await new Promise(resolve => setTimeout(resolve, 2000))
        }
      }
      
      // 超时处理
      if (attempts >= maxAttempts) {
        uploadFile.status = 'error'
        uploadFile.error = '处理超时'
      }
    },

    // 重试上传
    async retryUpload(fileId: string) {
      const file = this.files.find(f => f.id === fileId)
      if (!file) return
      
      file.status = 'waiting'
      file.progress = 0
      file.error = undefined
      
      this.updateStats()
      
      if (!this.uploading) {
        this.startUpload()
      }
    },

    // 取消上传
    cancelUpload(fileId: string) {
      const file = this.files.find(f => f.id === fileId)
      if (!file) return
      
      // 如果有关联的任务，尝试取消
      const taskId = Object.keys(this.tasks).find(id => 
        this.tasks[id].result?.note_id === file.result?.id
      )
      
      if (taskId) {
        systemApi.cancelTask(taskId).catch(console.error)
      }
      
      file.status = 'error'
      file.error = '已取消'
      
      this.updateStats()
    },

    // 移除文件
    removeFile(fileId: string) {
      this.files = this.files.filter(f => f.id !== fileId)
      this.updateStats()
    },

    // 清空文件列表
    clearFiles() {
      this.files = []
      this.tasks = {}
      this.updateStats()
    },

    // 清空已完成文件
    clearCompleted() {
      this.files = this.files.filter(f => f.status === 'waiting' || f.status === 'uploading')
      this.updateStats()
    },

    // 更新统计信息
    updateStats() {
      this.stats = {
        total: this.files.length,
        success: this.successFiles.length,
        failed: this.failedFiles.length,
        pending: this.pendingFiles.length
      }
    },

    // 更新配置
    updateConfig(config: Partial<UploadState['config']>) {
      this.config = { ...this.config, ...config }
    }
  }
})
