from collections import Counter
import math

# 1. 准备战斗日志数据
# 这里有三条猢狲的战斗日志，每条日志为一个字符串，内容用顿号“，”分割
battle_logs = [
    "猢狲，施展，烈焰拳，击退，妖怪；随后开启，金刚体，抵挡，神兵，攻击。",
    "妖怪，使用，寒冰箭，攻击，猢狲，但被，烈焰拳，反击，击溃。",
    "猢狲，召唤，烈焰拳，与，毁灭咆哮，击败，妖怪，随后，收集，妖怪，精华。"
]

# 2. 设置BM25算法的超参数
k1 = 1.5  # 控制词频的平滑参数
b = 0.75  # 控制文档长度归一化的参数

# 3. 构建词表
# 将所有日志按“，”分割，收集所有出现过的词，形成词表（去重）
vocabulary = set(word for log in battle_logs for word in log.split("，"))
# 为每个词分配一个唯一的索引，方便后续向量表示
vocab_to_idx = {word: idx for idx, word in enumerate(vocabulary)}

# 4. 计算每个词的逆文档频率（IDF）
N = len(battle_logs)  # 日志总数
# 统计每个词在多少条日志中出现过（文档频率DF）
df = Counter(word for log in battle_logs for word in set(log.split("，")))
# 计算每个词的IDF值，防止分母为0，分子分母都加0.5
idf = {word: math.log((N - df[word] + 0.5) / (df[word] + 0.5) + 1) for word in vocabulary}

# 5. 计算日志的平均长度
avg_log_len = sum(len(log.split("，")) for log in battle_logs) / N

# 6. 定义BM25稀疏嵌入生成函数
def bm25_sparse_embedding(log):
    """
    输入一条日志，输出其BM25稀疏向量表示（字典形式，key为词索引，value为BM25分数）
    """
    tf = Counter(log.split("，"))  # 统计该日志中每个词的词频
    log_len = len(log.split("，"))  # 当前日志的长度
    embedding = {}
    for word, freq in tf.items():
        if word in vocabulary:
            idx = vocab_to_idx[word]  # 获取词的索引
            # 计算BM25分数
            score = idf[word] * (freq * (k1 + 1)) / (freq + k1 * (1 - b + b * log_len / avg_log_len))
            embedding[idx] = score
    return embedding

# 7. 对每条日志生成BM25稀疏向量，并输出
for log in battle_logs:
    sparse_embedding = bm25_sparse_embedding(log)
    print(f"日志: {log}")
    print(f"稀疏嵌入： {sparse_embedding}")
    print("-" * 40)