#!/usr/bin/env python3
"""
Milvus BM25迁移回退脚本
在迁移出现问题时快速回退到原系统
"""
import sys
import os
import logging
import shutil
import yaml
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pymilvus import connections, utility, Collection

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MigrationRollback:
    """迁移回退器"""
    
    def __init__(self):
        self.project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.config_file = os.path.join(self.project_root, "config.yaml")
        self.backup_config_file = os.path.join(self.project_root, "config.yaml.backup")
        
        # 连接Milvus
        self._connect_milvus()
    
    def _connect_milvus(self):
        """连接Milvus"""
        try:
            # 尝试从配置文件读取连接信息
            config = self._load_config()
            milvus_config = config.get("milvus", {})
            
            host = milvus_config.get("host", "localhost")
            port = milvus_config.get("port", 19530)
            
            connections.connect(
                alias="default",
                host=host,
                port=port
            )
            logger.info("Milvus连接成功")
        except Exception as e:
            logger.warning(f"Milvus连接失败: {e}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        return {}
    
    def _save_config(self, config: Dict[str, Any]):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
    
    def check_rollback_feasibility(self) -> bool:
        """检查回退可行性"""
        logger.info("检查回退可行性...")
        
        issues = []
        
        # 1. 检查备份配置文件
        if not os.path.exists(self.backup_config_file):
            issues.append("备份配置文件不存在")
        
        # 2. 检查Milvus连接
        try:
            # 尝试列出集合
            collections = utility.list_collections()
            logger.info(f"当前Milvus集合: {collections}")
        except Exception as e:
            issues.append(f"Milvus连接失败: {e}")
        
        # 3. 检查原集合是否存在
        config = self._load_config()
        milvus_config = config.get("milvus", {})
        collection_name = milvus_config.get("collection_name", "note_embeddings")
        
        # 检查原集合或备份集合
        original_exists = utility.has_collection(collection_name)
        backup_exists = utility.has_collection(f"{collection_name}_backup")
        
        if not original_exists and not backup_exists:
            issues.append("原集合和备份集合都不存在")
        
        if issues:
            logger.error("回退可行性检查失败:")
            for issue in issues:
                logger.error(f"  - {issue}")
            return False
        
        logger.info("✅ 回退可行性检查通过")
        return True
    
    def rollback_config(self) -> bool:
        """回退配置文件"""
        logger.info("回退配置文件...")
        
        try:
            if os.path.exists(self.backup_config_file):
                # 备份当前配置
                if os.path.exists(self.config_file):
                    shutil.copy2(self.config_file, f"{self.config_file}.rollback_backup")
                
                # 恢复备份配置
                shutil.copy2(self.backup_config_file, self.config_file)
                logger.info("✅ 配置文件已回退")
                return True
            else:
                # 手动修改配置
                logger.info("备份配置不存在，手动修改配置...")
                config = self._load_config()
                
                # 禁用Milvus BM25
                if "milvus" in config:
                    config["milvus"]["enable_bm25"] = False
                
                if "ensemble_retriever" in config:
                    config["ensemble_retriever"]["use_milvus_bm25"] = False
                
                self._save_config(config)
                logger.info("✅ 配置已手动回退")
                return True
                
        except Exception as e:
            logger.error(f"配置回退失败: {e}")
            return False
    
    def rollback_milvus_collection(self) -> bool:
        """回退Milvus集合"""
        logger.info("回退Milvus集合...")
        
        try:
            config = self._load_config()
            milvus_config = config.get("milvus", {})
            collection_name = milvus_config.get("collection_name", "note_embeddings")
            
            bm25_collection_name = f"{collection_name}_bm25"
            backup_collection_name = f"{collection_name}_backup"
            
            # 1. 检查BM25集合是否存在
            if utility.has_collection(bm25_collection_name):
                logger.info(f"删除BM25集合: {bm25_collection_name}")
                utility.drop_collection(bm25_collection_name)
            
            # 2. 检查是否需要恢复备份集合
            if utility.has_collection(backup_collection_name):
                if not utility.has_collection(collection_name):
                    logger.info(f"从备份恢复原集合: {backup_collection_name} -> {collection_name}")
                    
                    # 注意：Milvus可能不支持直接重命名集合
                    # 这里需要通过数据复制来实现
                    backup_collection = Collection(backup_collection_name)
                    backup_collection.load()
                    
                    # 获取备份数据
                    results = backup_collection.query(
                        expr="",
                        output_fields=["*"],
                        limit=1000000
                    )
                    
                    if results:
                        # 创建原集合（使用备份的Schema）
                        original_collection = Collection(collection_name, backup_collection.schema)
                        
                        # 插入数据
                        original_collection.insert(results)
                        original_collection.flush()
                        
                        # 创建索引（使用原来的索引配置）
                        from src.rag_action.models.database import MilvusSchema
                        index_params = MilvusSchema.get_index_params(enable_bm25=False)
                        
                        for index_param in index_params:
                            if index_param["field_name"] == "embedding":  # 只创建密集向量索引
                                original_collection.create_index(
                                    field_name=index_param["field_name"],
                                    index_params=index_param
                                )
                        
                        original_collection.load()
                        logger.info("✅ 原集合已从备份恢复")
                    
                # 删除备份集合
                logger.info(f"删除备份集合: {backup_collection_name}")
                utility.drop_collection(backup_collection_name)
            
            logger.info("✅ Milvus集合回退完成")
            return True
            
        except Exception as e:
            logger.error(f"Milvus集合回退失败: {e}")
            return False
    
    def verify_rollback(self) -> bool:
        """验证回退结果"""
        logger.info("验证回退结果...")
        
        try:
            # 1. 验证配置文件
            config = self._load_config()
            milvus_config = config.get("milvus", {})
            ensemble_config = config.get("ensemble_retriever", {})
            
            bm25_enabled = milvus_config.get("enable_bm25", False)
            use_milvus_bm25 = ensemble_config.get("use_milvus_bm25", False)
            
            if bm25_enabled or use_milvus_bm25:
                logger.warning("⚠️ 配置文件中仍启用BM25功能")
            else:
                logger.info("✅ 配置文件验证通过")
            
            # 2. 验证Milvus集合
            collection_name = milvus_config.get("collection_name", "note_embeddings")
            
            if utility.has_collection(collection_name):
                collection = Collection(collection_name)
                collection.load()
                
                # 检查集合Schema
                schema = collection.schema
                field_names = [field.name for field in schema.fields]
                
                if "sparse_embedding" in field_names:
                    logger.warning("⚠️ 集合仍包含稀疏向量字段")
                else:
                    logger.info("✅ 集合Schema验证通过")
                
                # 检查数据量
                count = collection.num_entities
                logger.info(f"集合数据量: {count}")
                
            else:
                logger.error("❌ 原集合不存在")
                return False
            
            logger.info("✅ 回退验证完成")
            return True
            
        except Exception as e:
            logger.error(f"回退验证失败: {e}")
            return False
    
    def execute_rollback(self):
        """执行完整回退流程"""
        logger.info("=== 开始执行迁移回退 ===")
        
        try:
            # 1. 检查回退可行性
            if not self.check_rollback_feasibility():
                logger.error("回退可行性检查失败，终止回退")
                return False
            
            # 2. 回退配置文件
            if not self.rollback_config():
                logger.error("配置文件回退失败")
                return False
            
            # 3. 回退Milvus集合
            if not self.rollback_milvus_collection():
                logger.error("Milvus集合回退失败")
                return False
            
            # 4. 验证回退结果
            if not self.verify_rollback():
                logger.error("回退验证失败")
                return False
            
            logger.info("✅ 迁移回退完成")
            logger.info("\n后续步骤:")
            logger.info("1. 重启应用服务")
            logger.info("2. 验证系统功能正常")
            logger.info("3. 监控系统运行状态")
            
            return True
            
        except Exception as e:
            logger.error(f"回退过程中出现异常: {e}")
            return False


def main():
    """主函数"""
    print("=" * 60)
    print("Milvus BM25迁移回退工具")
    print("=" * 60)
    print()
    print("此工具将执行以下回退操作:")
    print("1. 恢复配置文件到迁移前状态")
    print("2. 删除BM25相关的Milvus集合")
    print("3. 恢复原始集合（如果有备份）")
    print("4. 验证回退结果")
    print()
    print("⚠️ 警告:")
    print("- 回退操作将删除BM25迁移后的数据")
    print("- 请确保有完整的数据备份")
    print("- 建议在测试环境先验证回退流程")
    print()
    
    response = input("确认执行回退操作？(y/N): ")
    if response.lower() != 'y':
        print("回退操作已取消")
        return
    
    rollback = MigrationRollback()
    success = rollback.execute_rollback()
    
    if success:
        print("\n🎉 回退操作成功完成！")
    else:
        print("\n❌ 回退操作失败，请检查日志并手动处理")


if __name__ == "__main__":
    main()
