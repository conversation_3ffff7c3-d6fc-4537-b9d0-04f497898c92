#!/usr/bin/env python3
"""
PDF转Markdown接口脚本
只做PDF→Markdown，不做后续分块、向量化等处理
可作为函数调用，也可命令行批量转换
"""
import os
import sys
import json
from pathlib import Path
from loguru import logger

# mineru相关依赖
from mineru.cli.common import convert_pdf_bytes_to_bytes_by_pypdfium2, prepare_env, read_fn
from mineru.data.data_reader_writer import FileBasedDataWriter
from mineru.utils.enum_class import MakeMode
from mineru.backend.pipeline.pipeline_analyze import doc_analyze as pipeline_doc_analyze
from mineru.backend.pipeline.pipeline_middle_json_mkcontent import union_make as pipeline_union_make
from mineru.backend.pipeline.model_json_to_middle_json import result_to_middle_json as pipeline_result_to_middle_json

def pdf_to_markdown(pdf_path, output_dir, lang="ch", parse_method="auto"):
    """
    将单个PDF文件转为Markdown
    :param pdf_path: PDF文件路径
    :param output_dir: 输出目录
    :param lang: 语言（默认中文）
    :param parse_method: 解析方法
    :return: 输出的Markdown文件路径
    """
    file_name = str(Path(pdf_path).stem)
    pdf_bytes = read_fn(pdf_path)
    
    # pipeline分析
    infer_results, all_image_lists, all_pdf_docs, lang_list, ocr_enabled_list = pipeline_doc_analyze(
        [pdf_bytes], [lang], parse_method=parse_method, formula_enable=True, table_enable=True
    )
    model_list = infer_results[0]
    images_list = all_image_lists[0]
    pdf_doc = all_pdf_docs[0]
    _lang = lang_list[0]
    _ocr_enable = ocr_enabled_list[0]
    
    # 环境准备
    local_image_dir, local_md_dir = prepare_env(output_dir, file_name, parse_method)
    image_writer, md_writer = FileBasedDataWriter(local_image_dir), FileBasedDataWriter(local_md_dir)
    
    # 转为中间JSON
    middle_json = pipeline_result_to_middle_json(
        model_list, images_list, pdf_doc, image_writer, _lang, _ocr_enable, True
    )
    pdf_info = middle_json["pdf_info"]
    
    # 生成Markdown
    image_dir = str(os.path.basename(local_image_dir))
    md_content_str = pipeline_union_make(pdf_info, MakeMode.MM_MD, image_dir)
    md_path = os.path.join(local_md_dir, f"{file_name}.md")
    md_writer.write_string(f"{file_name}.md", md_content_str)
    logger.info(f"PDF转Markdown完成: {md_path}")
    return md_path

def batch_pdf_to_markdown(pdf_dir, output_dir, lang="ch"):
    """
    批量将目录下所有PDF转为Markdown
    :param pdf_dir: PDF文件目录
    :param output_dir: 输出目录
    :param lang: 语言
    """
    pdf_files = [p for p in Path(pdf_dir).glob("*.pdf")]
    if not pdf_files:
        print(f"❌ 未找到PDF文件，请将PDF放入 {pdf_dir}")
        return
    print(f"📁 共找到 {len(pdf_files)} 个PDF文件：")
    for p in pdf_files:
        print(f"  - {p.name}")
    for p in pdf_files:
        try:
            pdf_to_markdown(str(p), output_dir, lang=lang)
        except Exception as e:
            logger.error(f"转换失败: {p.name} - {e}")

def main():
    import argparse
    parser = argparse.ArgumentParser(description="PDF转Markdown批量转换")
    parser.add_argument("--pdf-dir", default="pdfs", help="PDF文件目录")
    parser.add_argument("--output-dir", default="output", help="输出目录")
    parser.add_argument("--lang", default="ch", help="文档语言")
    args = parser.parse_args()
    os.makedirs(args.pdf_dir, exist_ok=True)
    os.makedirs(args.output_dir, exist_ok=True)
    batch_pdf_to_markdown(args.pdf_dir, args.output_dir, lang=args.lang)

if __name__ == "__main__":
    main() 