#!/usr/bin/env python3
"""
LangChain Retriever标准化实现测试脚本（2.2模块）
验证LangChain标准化检索器的功能和性能

运行方式：
python tests/test_langchain_retriever_module.py
"""
import asyncio
import time
import logging
from typing import List, Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_langchain_retriever_basic():
    """基础功能测试"""
    print("🧪 LangChain Retriever基础功能测试")
    print("=" * 60)
    
    try:
        # 导入服务
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
        from src.rag_action.service.langchain_rag_service import LangChainRAGService
        
        # 创建服务实例
        service = LangChainRAGService()
        
        # 获取检索器状态信息
        retriever_info = service.get_retriever_info()
        print(f"📊 检索器状态:")
        print(f"  - LangChain检索器可用: {retriever_info['langchain_retrievers_available']}")
        print(f"  - 使用LangChain检索器: {retriever_info['use_langchain_retrievers']}")
        print(f"  - Milvus检索器: {'✅' if retriever_info['retrievers']['milvus'] else '❌'}")
        print(f"  - BM25检索器: {'✅' if retriever_info['retrievers']['bm25'] else '❌'}")
        print(f"  - 混合检索器: {'✅' if retriever_info['retrievers']['ensemble'] else '❌'}")
        print(f"  - 降级可用: {'✅' if retriever_info['fallback_available'] else '❌'}")
        
        # 测试检索功能
        if service.use_langchain_retrievers:
            print("\n🔍 测试LangChain标准化检索...")
            
            test_query = "人工智能的发展历史"
            start_time = time.time()
            
            try:
                # 测试LangChain检索
                results = await service._langchain_retrieve_documents(test_query, 3)
                end_time = time.time()
                
                print(f"✅ LangChain检索成功:")
                print(f"  - 查询: {test_query}")
                print(f"  - 结果数量: {len(results)}")
                print(f"  - 检索时间: {end_time - start_time:.2f}s")
                
                # 显示检索结果
                for i, doc in enumerate(results[:2], 1):
                    print(f"  - 文档{i}: {doc['content'][:50]}...")
                    print(f"    笔记ID: {doc['note_id']}, 相似度: {doc['similarity_score']:.3f}")
                
            except Exception as e:
                print(f"❌ LangChain检索失败: {e}")
                
        else:
            print("⚠️ LangChain检索器不可用，跳过检索测试")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

async def test_retriever_comparison():
    """检索器性能对比测试"""
    print("\n⚡ 检索器性能对比测试")
    print("=" * 60)
    
    try:
        from src.rag_action.service.langchain_rag_service import LangChainRAGService
        
        service = LangChainRAGService()
        
        test_queries = [
            "什么是机器学习？",
            "深度学习的应用场景",
            "自然语言处理技术"
        ]
        
        langchain_times = []
        legacy_times = []
        
        print("🔄 开始性能对比测试...")
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n测试查询 {i}: {query}")
            
            # 测试LangChain检索器性能
            if service.use_langchain_retrievers:
                try:
                    start_time = time.time()
                    langchain_results = await service._langchain_retrieve_documents(query, 5)
                    langchain_time = time.time() - start_time
                    langchain_times.append(langchain_time)
                    
                    print(f"  📚 LangChain检索: {langchain_time:.3f}s ({len(langchain_results)}个结果)")
                    
                except Exception as e:
                    print(f"  ❌ LangChain检索失败: {e}")
                    langchain_times.append(float('inf'))
            
            # 测试原有检索器性能
            try:
                start_time = time.time()
                legacy_results = await service.ensemble_retriever.ensemble_search_unified(query, 5)
                legacy_time = time.time() - start_time
                legacy_times.append(legacy_time)
                
                print(f"  🔧 原有检索: {legacy_time:.3f}s ({len(legacy_results)}个结果)")
                
            except Exception as e:
                print(f"  ❌ 原有检索失败: {e}")
                legacy_times.append(float('inf'))
        
        # 计算平均性能
        if langchain_times and all(t != float('inf') for t in langchain_times):
            avg_langchain = sum(langchain_times) / len(langchain_times)
            print(f"\n📊 LangChain平均检索时间: {avg_langchain:.3f}s")
        
        if legacy_times and all(t != float('inf') for t in legacy_times):
            avg_legacy = sum(legacy_times) / len(legacy_times)
            print(f"📊 原有检索器平均时间: {avg_legacy:.3f}s")
            
            if langchain_times and all(t != float('inf') for t in langchain_times):
                improvement = ((avg_legacy - avg_langchain) / avg_legacy) * 100
                print(f"📈 性能改进: {improvement:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能对比测试失败: {e}")
        return False

async def test_retriever_fallback():
    """检索器降级机制测试"""
    print("\n🛡️ 检索器降级机制测试")
    print("=" * 60)
    
    try:
        from src.rag_action.service.langchain_rag_service import LangChainRAGService
        
        service = LangChainRAGService()
        
        # 模拟LangChain检索器故障
        if service.use_langchain_retrievers:
            print("🔧 模拟LangChain检索器故障...")
            
            # 临时禁用LangChain检索器
            original_retriever = service.langchain_ensemble_retriever
            service.langchain_ensemble_retriever = None
            service.use_langchain_retrievers = False
            
            # 测试降级检索
            test_query = "测试降级机制"
            inputs = {"question": test_query, "top_k": 3}
            
            start_time = time.time()
            results = await service._async_retrieve_documents(inputs)
            end_time = time.time()
            
            print(f"✅ 降级检索成功:")
            print(f"  - 查询: {test_query}")
            print(f"  - 结果数量: {len(results)}")
            print(f"  - 检索时间: {end_time - start_time:.2f}s")
            
            # 恢复原始状态
            service.langchain_ensemble_retriever = original_retriever
            service.use_langchain_retrievers = True
            
            print("🔄 检索器状态已恢复")
            
        else:
            print("⚠️ LangChain检索器不可用，无法测试降级机制")
        
        return True
        
    except Exception as e:
        print(f"❌ 降级机制测试失败: {e}")
        return False

async def test_document_format_conversion():
    """文档格式转换测试"""
    print("\n🔄 文档格式转换测试")
    print("=" * 60)
    
    try:
        from src.rag_action.service.langchain_rag_service import LangChainRAGService
        from langchain_core.documents import Document
        
        service = LangChainRAGService()
        
        # 创建模拟的LangChain Document对象
        langchain_docs = [
            Document(
                page_content="这是第一个测试文档的内容，包含了关于人工智能的基础知识。",
                metadata={
                    "note_id": 101,
                    "chunk_id": 201,
                    "page_number": 1,
                    "score": 0.95,
                    "source": "milvus_vector"
                }
            ),
            Document(
                page_content="这是第二个测试文档，讨论了机器学习的应用场景和发展趋势。",
                metadata={
                    "note_id": 102,
                    "chunk_id": 202,
                    "page_number": 2,
                    "score": 0.87,
                    "source": "bm25_text"
                }
            )
        ]
        
        # 执行格式转换
        converted_docs = service._convert_langchain_documents(langchain_docs)
        
        print(f"✅ 文档格式转换成功:")
        print(f"  - 原始文档数: {len(langchain_docs)}")
        print(f"  - 转换后文档数: {len(converted_docs)}")
        
        # 验证转换结果
        for i, doc in enumerate(converted_docs, 1):
            print(f"\n  文档 {i}:")
            print(f"    内容: {doc['content'][:50]}...")
            print(f"    笔记ID: {doc['note_id']}")
            print(f"    块ID: {doc['chunk_id']}")
            print(f"    页码: {doc['page_number']}")
            print(f"    相似度: {doc['similarity_score']}")
            print(f"    来源: {doc['source']}")
            print(f"    检索器类型: {doc['retriever_type']}")
            print(f"    原始元数据: {bool(doc.get('original_metadata'))}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文档格式转换测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🎉 LangChain Retriever标准化实现测试")
    print("=" * 80)
    
    test_results = []
    
    # 运行各项测试
    tests = [
        ("基础功能测试", test_langchain_retriever_basic),
        ("性能对比测试", test_retriever_comparison),
        ("降级机制测试", test_retriever_fallback),
        ("格式转换测试", test_document_format_conversion)
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"\n🚀 开始 {test_name}...")
            result = await test_func()
            test_results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
                
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "=" * 80)
    print("📊 测试结果总结:")
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  - {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎊 所有测试通过！LangChain Retriever标准化实现成功！")
    else:
        print("⚠️ 部分测试失败，请检查实现或环境配置")

if __name__ == "__main__":
    asyncio.run(main())
