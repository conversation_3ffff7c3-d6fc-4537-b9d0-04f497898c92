# 双层检索-富豪榜 - 需要pip install openpyxl
import os
from dotenv import load_dotenv
import pandas as pd
from langchain_huggingface import HuggingFaceEmbeddings
import torch
from pymilvus import MilvusClient, DataType, FieldSchema, CollectionSchema
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 加载环境变量
load_dotenv()

# 初始化嵌入模型
embedding_function = HuggingFaceEmbeddings(model_name="BAAI/bge-small-zh")

# 连接到Milvus
client = MilvusClient(host="localhost", port="19530")

# 1. 创建summary向量数据库
summary_collection_name = "billionaires_summary"
test = embedding_function.embed_query("123")
print(len(test))
summary_fields = [
    FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
    FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=len(test)),
    FieldSchema(name="table_name", dtype=DataType.VARCHAR, max_length=100)
]

summary_schema = CollectionSchema(summary_fields, "富豪榜年份摘要")
if not client.has_collection(summary_collection_name):
    client.create_collection(
        collection_name=summary_collection_name,
        schema=summary_schema
    )
else:
    logging.info(f"summary集合已存在: {summary_collection_name}")
    client.drop_collection(summary_collection_name)
    client.create_collection(
        collection_name=summary_collection_name,
        schema=summary_schema
    )

# 2. 创建details向量数据库
details_collection_name = "billionaires_details"
details_fields = [
    FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
    FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=len(test)),
    FieldSchema(name="table_name", dtype=DataType.VARCHAR, max_length=100),
    FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=10000)  # 存储整个表格内容
]

details_schema = CollectionSchema(details_fields, "富豪榜详细信息")
if not client.has_collection(details_collection_name):
    client.create_collection(
        collection_name=details_collection_name,
        schema=details_schema
    )
else:
    logging.info(f"details集合已存在: {details_collection_name}")
    client.drop_collection(details_collection_name)
    client.create_collection(
        collection_name=details_collection_name,
        schema=details_schema
    )


# 3. 加载Excel文件并准备数据
excel_file = "C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/世界十大富豪.xlsx"

# 读取Excel文件中的所有sheet并插入数据
with pd.ExcelFile(excel_file) as xls:
    for sheet_name in xls.sheet_names:
        try:
            df = pd.read_excel(xls, sheet_name=sheet_name)
            logging.info(f"正在处理sheet: {sheet_name}")
            
            # 插入summary数据 - 只存储表名
            summary_embedding = embedding_function.embed_query(sheet_name)
            
            client.insert(
                collection_name=summary_collection_name,
                data=[{
                    "vector": summary_embedding,
                    "table_name": sheet_name
                }]
            )
            
            # 插入details数据 - 存储整个表格内容
            # 将整个DataFrame转换为字符串
            table_content = df.to_string(index=False)
            detail_embedding = embedding_function.embed_query(table_content)
            
            client.insert(
                collection_name=details_collection_name,
                data=[{
                    "vector": detail_embedding,
                    "table_name": sheet_name,
                    "content": table_content
                }]
            )
            
            logging.info(f"成功处理sheet: {sheet_name}")
            
        except Exception as e:
            logging.error(f"处理sheet {sheet_name} 时出错: {str(e)}")
            logging.error(f"错误详情: {e.__class__.__name__}")
            continue

# 4. 创建索引
# 删除已存在的索引（如果有）
try:
    client.drop_index(collection_name=summary_collection_name, index_name="vector")
except Exception as e:
    logging.warning(f"删除summary索引时出错: {str(e)}")

try:
    client.drop_index(collection_name=details_collection_name, index_name="vector")
except Exception as e:
    logging.warning(f"删除details索引时出错: {str(e)}")

# 创建新索引
try:
    # 使用prepare_index_params方法创建索引参数
    summary_index_params = client.prepare_index_params()
    summary_index_params.add_index(
        field_name="vector",  # 指定要为哪个字段创建索引，这里是向量字段
        index_type="IVF_FLAT",  # 索引类型
        metric_type="COSINE",  # 使用余弦相似度作为向量相似度度量方式
        params={"nlist": 1024}  # 索引参数
    )
    
    client.create_index(
        collection_name=summary_collection_name,
        index_params=summary_index_params
    )
    logging.info("成功创建summary索引")
except Exception as e:
    logging.error(f"创建summary索引时出错: {str(e)}")

try:
    # 使用prepare_index_params方法创建索引参数
    details_index_params = client.prepare_index_params()
    details_index_params.add_index(
        field_name="vector",  # 指定要为哪个字段创建索引，这里是向量字段
        index_type="IVF_FLAT",  # 索引类型
        metric_type="COSINE",  # 使用余弦相似度作为向量相似度度量方式
        params={"nlist": 1024}  # 索引参数
    )
    
    client.create_index(
        collection_name=details_collection_name,
        index_params=details_index_params
    )
    logging.info("成功创建details索引")
except Exception as e:
    logging.error(f"创建details索引时出错: {str(e)}")

# 加载集合以使索引生效
try:
    client.load_collection(summary_collection_name)
    client.load_collection(details_collection_name)
    logging.info("成功加载集合")
except Exception as e:
    logging.error(f"加载集合时出错: {str(e)}")

def search_relevant_table(question):
    """
    支持比较类问题的双层检索。
    如果问题涉及比较（如“比较A表和B表的内容”），则返回多个相关表及其内容。
    否则，返回最相关的一个表及其内容。
    """
    query_embedding = embedding_function.embed_query(question)
    
    # 第一层检索：在summary集合中搜索最相关的sheet，返回多个表名
    summary_results = client.search(
        collection_name=summary_collection_name,
        data=[query_embedding],
        limit=5,  # 返回前5个相关表
        output_fields=["table_name"],
        search_params={
            "metric_type": "COSINE",
            "params": {"nprobe": 10}
        }
    )
    
    if not summary_results or not summary_results[0]:
        return None, None

    # 获取所有相关表名
    matched_tables = [hit['entity']['table_name'] for hit in summary_results[0]]

    # 直接不判断问题类型，始终返回多个相关表及其内容
    table_contents = {}
    for table in matched_tables:
        details_results = client.search(
            collection_name=details_collection_name,
            data=[query_embedding],
            filter=f"table_name == '{table}'",
            limit=5,
            output_fields=["content"],
            search_params={
                "metric_type": "COSINE",
                "params": {"nprobe": 10}
            }
        )
        if details_results and details_results[0]:
            table_contents[table] = details_results[0][0]['entity']['content']
        else:
            table_contents[table] = None
    return matched_tables, table_contents   


def generate_answer(question):
    # 检索相关信息
    table_name, content = search_relevant_table(question)
    
    if not table_name or not content:
        return "抱歉，没有找到相关信息。"
    
    # 构建提示词
    prompt = f"""根据以下表格信息回答问题：

表格名称：{table_name}

表格内容：
{content}

问题：{question}

请基于以上信息给出详细回答："""

    # 使用DeepSeek生成答案
    from langchain_openai import ChatOpenAI
    client = ChatOpenAI(
        model="gpt-4o-mini",
        temperature=0,
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
        base_url="https://api.zhizengzeng.com/v1"
    )

    response = client.invoke(prompt)
    return response.content

# 测试示例
if __name__ == "__main__":
    # 循环问答，直到用户输入"退出"为止
    while True:
        question = input("请输入您的问题（输入“退出”结束）：")
        if question.strip() == "退出":
            print("已退出问答。")
            break
        answer = generate_answer(question)
        print(f"问题：{question}")
        print(f"答案：{answer}\n")