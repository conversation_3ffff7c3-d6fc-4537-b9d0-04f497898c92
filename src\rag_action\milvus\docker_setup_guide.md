# Milvus Docker 安装和配置指南

由于 milvus-lite 在 Windows 上不可用，我们需要使用 Docker 来运行 Milvus 服务器。

## 前置要求

1. 安装 Docker Desktop for Windows
2. 确保 Docker 服务正在运行

## 快速启动 Milvus

### 方法一：使用 Docker Compose（推荐）

创建 `docker-compose.yml` 文件：

```yaml
version: '3.5'

services:
  etcd:
    container_name: milvus-etcd
    image: quay.io/coreos/etcd:v3.5.5
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=**********
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/volumes/etcd:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 30s
      timeout: 20s
      retries: 3

  minio:
    container_name: milvus-minio
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    ports:
      - "9001:9001"
      - "9000:9000"
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/volumes/minio:/minio_data
    command: minio server /minio_data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  milvus:
    container_name: milvus-standalone
    image: milvusdb/milvus:v2.5.1
    command: ["milvus", "run", "standalone"]
    security_opt:
    - seccomp:unconfined
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/volumes/milvus:/var/lib/milvus
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/healthz"]
      interval: 30s
      start_period: 90s
      timeout: 20s
      retries: 3
    ports:
      - "19530:19530"
      - "9091:9091"
    depends_on:
      - "etcd"
      - "minio"

networks:
  default:
    name: milvus
```

启动服务：

```bash
# 在包含 docker-compose.yml 的目录中运行
docker-compose up -d
```

### 方法二：使用单个 Docker 容器（简化版）

```bash
# 拉取 Milvus 镜像
docker pull milvusdb/milvus:v2.5.1

# 运行 Milvus 容器
docker run -d \
  --name milvus-standalone \
  -p 19530:19530 \
  -p 9091:9091 \
  -v $(pwd)/volumes/milvus:/var/lib/milvus \
  milvusdb/milvus:v2.5.1 \
  milvus run standalone
```

## 验证安装

运行以下命令检查 Milvus 是否正常运行：

```bash
# 检查容器状态
docker ps

# 检查 Milvus 健康状态
curl http://localhost:9091/healthz
```

## 连接配置

在 Python 代码中使用以下配置连接到 Milvus：

```python
from pymilvus import MilvusClient

# 连接到本地 Docker 中的 Milvus
client = MilvusClient(uri="http://localhost:19530")
```

## 停止和清理

```bash
# 停止服务
docker-compose down

# 清理数据（可选）
docker-compose down -v
```

## 故障排除

1. **端口冲突**：确保端口 19530 和 9091 没有被其他服务占用
2. **内存不足**：Milvus 需要至少 4GB 内存
3. **权限问题**：确保 Docker 有足够的权限访问挂载的目录

## 使用 Attu（可选的 Web UI）

Attu 是 Milvus 的官方管理界面：

```bash
docker run -d \
  --name attu \
  -p 3000:3000 \
  zilliz/attu:latest
```

然后在浏览器中访问 `http://localhost:3000` 来管理 Milvus。
