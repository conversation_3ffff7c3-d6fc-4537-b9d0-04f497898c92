"""
01 - LangGraph 流式输出案例
最简单的流式输出演示
"""

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph, MessagesState, START, END

# 创建 LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0.7
)

def streaming_chat_node(state: MessagesState):
    """流式聊天节点"""
    messages = state["messages"]
    
    print("AI: ", end="", flush=True)
    
    # 使用 LLM 的流式输出
    response_chunks = []
    for chunk in llm.stream(messages):
        if chunk.content:
            response_chunks.append(chunk.content)
            # 实时输出每个块
            print(chunk.content, end="", flush=True)
    
    print()  # 换行
    
    # 返回完整的响应
    full_response = "".join(response_chunks)
    return {"messages": [AIMessage(content=full_response)]}

# 创建图
workflow = StateGraph(MessagesState)
workflow.add_node("chat", streaming_chat_node)
workflow.set_entry_point("chat")
workflow.add_edge("chat", END)

# 编译图
app = workflow.compile()

# 流式输出演示
print("=== LangGraph 流式输出演示 ===")
print("输入: 请给我讲一个关于人工智能的故事")

# 使用 invoke() 方法，但节点内部是流式的
result = app.invoke({"messages": [HumanMessage(content="请给我讲一个关于人工智能的故事")]})

print("\n=== 流式输出完成 ===")