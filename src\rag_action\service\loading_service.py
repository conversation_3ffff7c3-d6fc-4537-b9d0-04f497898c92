"""
文档加载服务
支持PDF文档的文本提取和处理
"""
import logging
import os
import re
from typing import Dict, List, Optional, Tuple
from pathlib import Path

try:
    import pymupdf  # PyMuPDF的正确导入方式
except ImportError:
    pymupdf = None

logger = logging.getLogger(__name__)


class LoadingService:
    """文档加载服务"""
    
    def __init__(self):
        self.total_pages = 0
        self.page_map = {}
        self.metadata = {}
    
    def load_pdf(self, file_path: str, method: str = "pymupdf") -> str:
        """
        加载PDF文档并提取文本
        
        Args:
            file_path: PDF文件路径
            method: 提取方法 (pymupdf)
            
        Returns:
            提取的文本内容
        """
        try:
            if method == "pymupdf" and pymupdf is not None:
                return self._load_with_pymupdf(file_path)
            else:
                raise ValueError(f"不支持的加载方法: {method} 或 PyMuPDF 未安装")
                
        except Exception as e:
            logger.error(f"PDF加载失败: {e}")
            raise
    
    def _load_with_pymupdf(self, file_path: str) -> str:
        """使用PyMuPDF加载PDF"""
        if pymupdf is None:
            raise ImportError("PyMuPDF未安装")

        doc = pymupdf.open(file_path)
        self.total_pages = len(doc)
        
        # 提取文档元数据
        self.metadata = {
            "title": doc.metadata.get("title", ""),
            "author": doc.metadata.get("author", ""),
            "subject": doc.metadata.get("subject", ""),
            "creator": doc.metadata.get("creator", ""),
            "producer": doc.metadata.get("producer", ""),
            "creation_date": doc.metadata.get("creationDate", ""),
            "modification_date": doc.metadata.get("modDate", "")
        }
        
        full_text = ""
        page_map = {}
        
        for page_num in range(len(doc)):
            page = doc[page_num]  # 使用索引访问页面
            page_text = page.get_text()
            
            # 清理文本
            page_text = self._clean_text(page_text)
            
            # 记录页面文本的起始位置
            start_pos = len(full_text)
            full_text += page_text + "\n\n"
            end_pos = len(full_text)
            
            page_map[page_num + 1] = {
                "start": start_pos,
                "end": end_pos,
                "text": page_text,
                "length": len(page_text)
            }
        
        self.page_map = page_map
        doc.close()
        
        return full_text.strip()
    
    def _clean_text(self, text: str) -> str:
        """清理提取的文本"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符和控制字符
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)
        
        # 处理常见的PDF提取问题
        text = text.replace('\uf0b7', '•')  # 替换项目符号
        text = text.replace('\uf020', ' ')   # 替换特殊空格
        
        return text.strip()
    
    def extract_title_from_content(self, content: str) -> str:
        """从内容中提取标题"""
        if not content:
            return "未命名文档"
        
        # 尝试从文档元数据获取标题
        if self.metadata.get("title"):
            return self.metadata["title"]
        
        # 从内容第一行提取标题
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if line and len(line) < 200:  # 标题通常不会太长
                return line
        
        # 如果没有找到合适的标题，使用默认值
        return "PDF文档"
    
    def get_total_pages(self) -> int:
        """获取总页数"""
        return self.total_pages
    
    def get_page_map(self) -> Dict:
        """获取页面映射"""
        return self.page_map
    
    def get_metadata(self) -> Dict:
        """获取文档元数据"""
        return self.metadata
    
    def get_page_content(self, page_number: int) -> Optional[str]:
        """获取指定页面的内容"""
        return self.page_map.get(page_number, {}).get("text")
    
    def find_page_by_position(self, position: int) -> int:
        """根据文本位置查找对应的页码"""
        for page_num, page_info in self.page_map.items():
            if page_info["start"] <= position < page_info["end"]:
                return page_num
        return 1  # 默认返回第一页
