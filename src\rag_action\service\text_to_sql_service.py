"""
Text-to-SQL服务
支持自然语言查询转换为SQL语句，基于Milvus向量检索的动态Few-Shot学习
"""
import logging
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import re

logger = logging.getLogger(__name__)

# 导入LangChain组件
try:
    from langchain_openai import ChatOpenAI
    from langchain_core.messages import HumanMessage, SystemMessage
    from langchain_core.prompts import ChatPromptTemplate
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    logger.warning("LangChain不可用，Text-to-SQL功能将受限")

try:
    from rag_action.core.config import settings
    from rag_action.service.text_to_sql_vectorstore import get_text_to_sql_vectorstore
except ImportError:
    try:
        from src.rag_action.core.config import settings
        from src.rag_action.service.text_to_sql_vectorstore import get_text_to_sql_vectorstore
    except ImportError:
        # 创建模拟对象用于测试
        class MockSettings:
            llm = type('obj', (object,), {
                'api_key': 'test-key',
                'base_url': 'https://api.openai.com/v1',
                'model_name': 'gpt-4o-mini'
            })()

        settings = MockSettings()

        def get_text_to_sql_vectorstore():
            from src.rag_action.service.text_to_sql_vectorstore import get_text_to_sql_vectorstore
            return get_text_to_sql_vectorstore()


class TextToSQLService:
    """Text-to-SQL服务 - 基于Milvus向量检索的动态Few-Shot学习"""

    def __init__(self):
        self.llm = None
        self.vectorstore = get_text_to_sql_vectorstore()

        # 初始化LLM
        if LANGCHAIN_AVAILABLE:
            try:
                self.llm = ChatOpenAI(
                    openai_api_key=settings.llm.api_key,
                    openai_api_base=settings.llm.base_url,
                    model_name=settings.llm.model_name,
                    temperature=0.1,  # 低温度确保SQL生成的准确性
                    max_tokens=1000
                )
                logger.info("Text-to-SQL LLM初始化成功")
            except Exception as e:
                logger.error(f"Text-to-SQL LLM初始化失败: {e}")

        logger.info("Text-to-SQL服务初始化完成（基于向量检索）")
    
    def _retrieve_relevant_ddl(self, question: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """检索与问题相关的DDL Schema信息"""
        try:
            ddl_schemas = self.vectorstore.search_ddl_schemas(question, top_k)
            logger.info(f"检索到{len(ddl_schemas)}个相关DDL Schema")
            return ddl_schemas
        except Exception as e:
            logger.error(f"DDL Schema检索失败: {e}")
            return []

    def _retrieve_relevant_columns(self, question: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """检索与问题相关的字段描述信息"""
        try:
            columns = self.vectorstore.search_column_descriptions(question, top_k)
            logger.info(f"检索到{len(columns)}个相关字段描述")
            return columns
        except Exception as e:
            logger.error(f"字段描述检索失败: {e}")
            return []

    def _retrieve_relevant_examples(self, question: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """检索与问题相关的Few-Shot示例"""
        try:
            examples = self.vectorstore.search_few_shot_examples(question, top_k)
            logger.info(f"检索到{len(examples)}个相关Few-Shot示例")
            return examples
        except Exception as e:
            logger.error(f"Few-Shot示例检索失败: {e}")
            return []

    def _build_text_to_sql_prompt(self, question: str) -> str:
        """基于三个细粒度集合并行构建动态Text-to-SQL提示词"""
        import concurrent.futures

        # 并行执行三个检索操作
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            # 提交三个并行任务
            futures = {
                'ddl': executor.submit(self._retrieve_relevant_ddl, question, 3),
                'columns': executor.submit(self._retrieve_relevant_columns, question, 5),
                'examples': executor.submit(self._retrieve_relevant_examples, question, 5)
            }

            # 收集结果，处理异常
            results = {}
            for key, future in futures.items():
                try:
                    results[key] = future.result(timeout=10)  # 10秒超时
                    logger.info(f"{key}检索完成，获得{len(results[key])}个结果")
                except Exception as e:
                    logger.error(f"{key}检索失败: {e}")
                    results[key] = []

        # 获取并行检索结果
        relevant_ddl = results['ddl']
        relevant_columns = results['columns']
        relevant_examples = results['examples']

        # 4. 构建DDL信息文本
        ddl_text = "数据库表结构定义：\n\n"
        for ddl in relevant_ddl:
            ddl_text += f"表名: {ddl['table_name']}\n"
            ddl_text += f"DDL语句:\n{ddl['ddl_statement']}\n"
            ddl_text += f"相似度: {ddl.get('similarity_score', 0.0):.3f}\n\n"

        # 5. 构建字段描述文本
        columns_text = "重要字段说明：\n\n"
        for col in relevant_columns:
            columns_text += f"表: {col['table_name']} | 字段: {col['column_name']}\n"
            columns_text += f"类型: {col['data_type']} | 描述: {col['description']}\n"
            if col.get('constraints'):
                columns_text += f"约束: {col['constraints']}\n"
            columns_text += f"相似度: {col.get('similarity_score', 0.0):.3f}\n\n"

        # 6. 构建Few-Shot示例文本
        examples_text = "成功案例示例:\n\n"
        for i, example in enumerate(relevant_examples, 1):
            examples_text += f"示例{i}:\n"
            examples_text += f"问题: {example['question']}\n"
            examples_text += f"SQL: {example['sql']}\n"
            examples_text += f"说明: {example['explanation']}\n"
            examples_text += f"相似度: {example.get('similarity_score', 0.0):.3f}\n\n"

        # 7. 构建完整的提示词
        prompt = f"""你是一个专业的SQL查询生成专家。请根据用户的自然语言问题生成准确的SQL查询语句。

{ddl_text}

{columns_text}

{examples_text}

重要规则和最佳实践:
1. 🔒 安全性: 只生成SELECT查询，严禁INSERT、UPDATE、DELETE、DROP、CREATE等修改操作
2. 📝 语法: 使用标准MySQL语法，确保SQL语句完整且可执行
3. 🎯 准确性: 仔细检查表名和列名，必须与schema完全匹配
4. 🔗 关联: 需要多表数据时使用JOIN，确保关联条件正确
5. 🔍 过滤: 使用LIKE进行模糊匹配，使用=进行精确匹配
6. 📊 排序: 时间相关查询使用ORDER BY created_at DESC/ASC
7. 📈 限制: 列表查询适当使用LIMIT避免返回过多数据
8. 🏷️ 别名: 为COUNT、SUM等聚合函数使用有意义的别名

常见查询模式:
- 统计数量: SELECT COUNT(*) as count FROM table_name
- 最新记录: SELECT * FROM table_name ORDER BY created_at DESC LIMIT n
- 模糊搜索: SELECT * FROM table_name WHERE column_name LIKE '%keyword%'
- 分组统计: SELECT column_name, COUNT(*) as count FROM table_name GROUP BY column_name
- 关联查询: SELECT t1.*, t2.* FROM table1 t1 JOIN table2 t2 ON t1.id = t2.foreign_id

用户问题: {question}

请仔细分析问题，参考上述示例，生成准确的SQL查询。返回格式如下:
{{
    "sql": "生成的SQL查询语句",
    "explanation": "查询说明"
}}
"""

        return prompt
    
    def generate_sql(self, question: str) -> Dict[str, Any]:
        """
        将自然语言问题转换为SQL查询
        
        Args:
            question: 自然语言问题
            
        Returns:
            包含SQL查询和相关信息的字典
        """
        if not self.llm:
            return {
                "success": False,
                "error": "LLM未初始化",
                "sql": "",
                "explanation": ""
            }
        
        try:
            # 构建提示词
            prompt = self._build_text_to_sql_prompt(question)
            
            # 调用LLM生成SQL
            response = self.llm.invoke([HumanMessage(content=prompt)])
            
            # 解析响应
            result = self._parse_sql_response(response.content)
            
            # 验证SQL语法
            validation_result = self._validate_sql(result.get("sql", ""))
            result.update(validation_result)
            
            return result
            
        except Exception as e:
            logger.error(f"SQL生成失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "sql": "",
                "explanation": ""
            }
    

    
    def _parse_sql_response(self, response: str) -> Dict[str, Any]:
        """解析LLM返回的SQL响应"""
        try:
            # 尝试解析JSON格式
            if response.strip().startswith('{') and response.strip().endswith('}'):
                result = json.loads(response)
                return {
                    "success": True,
                    "sql": result.get("sql", ""),
                    "explanation": result.get("explanation", ""),
                    "error": ""
                }
            
            # 如果不是JSON格式，尝试提取SQL
            sql_match = re.search(r'```sql\n(.*?)\n```', response, re.DOTALL)
            if sql_match:
                sql = sql_match.group(1).strip()
                return {
                    "success": True,
                    "sql": sql,
                    "explanation": "从代码块中提取的SQL",
                    "error": ""
                }
            
            # 尝试提取包含SELECT的行
            lines = response.split('\n')
            for line in lines:
                if 'SELECT' in line.upper():
                    return {
                        "success": True,
                        "sql": line.strip(),
                        "explanation": "提取的SQL语句",
                        "error": ""
                    }
            
            return {
                "success": False,
                "sql": "",
                "explanation": "",
                "error": "无法解析SQL响应"
            }
            
        except Exception as e:
            logger.error(f"解析SQL响应失败: {e}")
            return {
                "success": False,
                "sql": "",
                "explanation": "",
                "error": f"解析失败: {str(e)}"
            }
    
    def _validate_sql(self, sql: str) -> Dict[str, Any]:
        """验证SQL语法和安全性"""
        validation_result = {
            "is_valid": True,
            "warnings": [],
            "errors": []
        }
        
        if not sql:
            validation_result["is_valid"] = False
            validation_result["errors"].append("SQL语句为空")
            return validation_result
        
        sql_upper = sql.upper()
        
        # 检查是否只包含SELECT查询
        if not sql_upper.strip().startswith('SELECT'):
            validation_result["is_valid"] = False
            validation_result["errors"].append("只允许SELECT查询")
            return validation_result
        
        # 检查危险操作 - 使用词边界匹配避免误判
        import re
        dangerous_patterns = [
            r'\bDROP\b', r'\bDELETE\b', r'\bINSERT\b', r'\bUPDATE\b',
            r'\bALTER\b', r'\bCREATE\b', r'\bTRUNCATE\b'
        ]
        for pattern in dangerous_patterns:
            if re.search(pattern, sql_upper):
                keyword = pattern.replace(r'\b', '').replace('\\', '')
                validation_result["is_valid"] = False
                validation_result["errors"].append(f"不允许使用{keyword}操作")
                return validation_result
        
        # 检查表名是否存在（基于向量检索）
        try:
            # 使用向量存储检索所有Schema信息
            all_schemas = self.vectorstore.search_schema_info("", top_k=10)  # 获取所有Schema
            known_tables = [schema.get('table_name', '') for schema in all_schemas]

            for table_name in known_tables:
                if table_name and table_name in sql.lower():
                    validation_result["warnings"].append(f"使用了表: {table_name}")
        except Exception as e:
            logger.warning(f"表名验证失败: {e}")
        
        return validation_result
    
    def get_schema_info(self, query: str = "", top_k: int = 4) -> Dict[str, Any]:
        """获取数据库schema信息（基于向量检索）"""
        try:
            if query:
                # 基于查询检索相关Schema
                schemas = self._retrieve_relevant_schemas(query, top_k)
            else:
                # 获取所有Schema（用于兼容性）
                schemas = self.vectorstore.search_schema_info("数据库表结构", top_k=10)

            # 转换为原有格式以保持兼容性
            schema_info = {}
            for schema in schemas:
                table_name = schema.get('table_name', '')
                if table_name:
                    schema_info[table_name] = {
                        "table_name": table_name,
                        "description": schema.get('description', ''),
                        "ddl": schema.get('ddl', ''),
                        "columns": schema.get('columns', {})
                    }

            return schema_info
        except Exception as e:
            logger.error(f"获取Schema信息失败: {e}")
            return {}

    def get_few_shot_examples(self, query: str = "", top_k: int = 10) -> List[Dict[str, Any]]:
        """获取Few-Shot示例（基于向量检索）"""
        try:
            if query:
                # 基于查询检索相关示例
                examples = self._retrieve_relevant_examples(query, top_k)
            else:
                # 获取所有示例（用于兼容性）
                examples = self.vectorstore.search_few_shot_examples("SQL查询示例", top_k=25)

            # 转换为原有格式以保持兼容性
            formatted_examples = []
            for example in examples:
                formatted_example = {
                    "question": example.get('question', ''),
                    "sql": example.get('sql', ''),
                    "explanation": example.get('explanation', ''),
                    "category": example.get('category', 'general'),
                    "difficulty": example.get('difficulty', 'easy')
                }
                formatted_examples.append(formatted_example)

            return formatted_examples
        except Exception as e:
            logger.error(f"获取Few-Shot示例失败: {e}")
            return []

    def get_schema_info(self, query: str = "") -> List[Dict[str, Any]]:
        """获取Schema信息（向后兼容方法）"""
        try:
            # 组合DDL和字段信息
            ddl_schemas = self._retrieve_relevant_ddl(query, top_k=10)
            column_descriptions = self._retrieve_relevant_columns(query, top_k=20)

            # 按表名组织数据
            schema_dict = {}

            # 添加DDL信息
            for ddl in ddl_schemas:
                table_name = ddl['table_name']
                if table_name not in schema_dict:
                    schema_dict[table_name] = {
                        'table_name': table_name,
                        'ddl': ddl['ddl_statement'],
                        'description': f"表: {table_name}",
                        'columns': {}
                    }

            # 添加字段信息
            for col in column_descriptions:
                table_name = col['table_name']
                if table_name not in schema_dict:
                    schema_dict[table_name] = {
                        'table_name': table_name,
                        'ddl': '',
                        'description': f"表: {table_name}",
                        'columns': {}
                    }

                schema_dict[table_name]['columns'][col['column_name']] = {
                    'type': col['data_type'],
                    'description': col['description'],
                    'constraints': col.get('constraints', '')
                }

            return list(schema_dict.values())

        except Exception as e:
            logger.error(f"获取Schema信息失败: {e}")
            return []


def get_text_to_sql_service() -> TextToSQLService:
    """获取Text-to-SQL服务实例"""
    return TextToSQLService()
