import { http } from '../request'
import type { SystemHealth, SystemStats, TaskStatus } from '@/types'

/**
 * 系统相关API
 */
export const systemApi = {
  /**
   * 系统健康检查
   */
  getHealth(): Promise<SystemHealth> {
    return http.get('/health')
  },

  /**
   * 获取系统统计信息
   */
  getStats(): Promise<SystemStats> {
    return http.get('/stats')
  },

  /**
   * 获取服务状态
   */
  getServiceStatus(): Promise<{
    services: Record<string, {
      status: 'loaded' | 'loading' | 'error'
      load_time?: number
      error?: string
    }>
    total_services: number
    loaded_services: number
  }> {
    return http.get('/api/service/status')
  },

  /**
   * 获取任务状态
   */
  getTaskStatus(taskId: string): Promise<TaskStatus> {
    return http.get(`/task/${taskId}/status`)
  },

  /**
   * 取消任务
   */
  cancelTask(taskId: string): Promise<{ message: string }> {
    return http.post(`/task/${taskId}/cancel`)
  },

  /**
   * 获取任务列表
   */
  getTasks(params: {
    status?: 'pending' | 'processing' | 'completed' | 'failed'
    page?: number
    page_size?: number
  } = {}): Promise<{
    tasks: TaskStatus[]
    total: number
    page: number
    page_size: number
  }> {
    return http.get('/tasks', { params })
  },

  /**
   * 清理已完成的任务
   */
  cleanupTasks(): Promise<{ message: string; cleaned_count: number }> {
    return http.post('/tasks/cleanup')
  },

  /**
   * 获取系统日志
   */
  getLogs(params: {
    level?: 'DEBUG' | 'INFO' | 'WARNING' | 'ERROR'
    limit?: number
    offset?: number
    start_time?: string
    end_time?: string
  } = {}): Promise<{
    logs: Array<{
      timestamp: string
      level: string
      message: string
      module?: string
      function?: string
      line?: number
    }>
    total: number
  }> {
    return http.get('/logs', { params })
  },

  /**
   * 获取性能指标
   */
  getMetrics(): Promise<{
    cpu_usage: number
    memory_usage: number
    disk_usage: number
    api_response_times: Record<string, {
      avg: number
      min: number
      max: number
      count: number
    }>
    database_connections: {
      mysql: { active: number; idle: number }
      milvus: { active: number; idle: number }
    }
  }> {
    return http.get('/metrics')
  },

  /**
   * 数据库备份
   */
  backupDatabase(): Promise<{
    backup_id: string
    message: string
    backup_path: string
    size: number
  }> {
    return http.post('/backup/database')
  },

  /**
   * 获取备份列表
   */
  getBackups(): Promise<{
    backups: Array<{
      backup_id: string
      created_at: string
      size: number
      type: 'mysql' | 'milvus' | 'full'
      status: 'completed' | 'failed' | 'in_progress'
    }>
  }> {
    return http.get('/backups')
  },

  /**
   * 恢复数据库
   */
  restoreDatabase(backupId: string): Promise<{
    message: string
    restore_id: string
  }> {
    return http.post(`/restore/database/${backupId}`)
  },

  /**
   * 系统配置
   */
  getConfig(): Promise<{
    app: Record<string, any>
    mysql: Record<string, any>
    milvus: Record<string, any>
    embedding: Record<string, any>
    llm: Record<string, any>
  }> {
    return http.get('/config')
  },

  /**
   * 更新系统配置
   */
  updateConfig(config: Record<string, any>): Promise<{ message: string }> {
    return http.put('/config', config)
  },

  /**
   * 重启服务
   */
  restartService(serviceName: string): Promise<{ message: string }> {
    return http.post(`/service/${serviceName}/restart`)
  },

  /**
   * 清理缓存
   */
  clearCache(): Promise<{ message: string; cleared_items: number }> {
    return http.post('/cache/clear')
  }
}
