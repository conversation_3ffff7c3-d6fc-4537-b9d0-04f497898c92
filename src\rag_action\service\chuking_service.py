from datetime import datetime   
import logging
from langchain.text_splitter import RecursiveCharacterTextSplitter

logger = logging.getLogger(__name__)

class ChunkingService:


    def chunk_text(self, text: str, method: str, metadata: dict, page_map: list = None, chunk_size: int = 1000, return_metadata: bool = False) -> dict:

        
        try:
            ##判断page_map是否为空
            if not page_map:
                raise ValueError("page_map不能为空")

            # 调试信息
            logger.info(f"分块方法: {method}, page_map类型: {type(page_map)}, page_map内容: {page_map}")

            chunks = []
            total_pages = len(page_map)

            ##根据method选择分块方式
            if method == "by_pages":
                for page_data in page_map:
                    chunk_metadata = {
                        "chunk_id": len(chunks) + 1,
                        "page_number": page_data['page'],
                        "page_range": str(page_data['page']),
                        "word_count": len(page_data['text'].split())
                    }

                    chunks.append({
                        "content": page_data['text'],
                        "metadata": chunk_metadata
                    })
            elif method == "fixed_size":
                # 对每页内容进行固定大小分块
                for page_data in page_map:
                    page_chunks =self._fixed_size_chunking(page_data['text'], chunk_size)
                    for idx, chunk in enumerate(page_chunks, 1):
                        chunk_metadata = {
                            "chunk_id": len(chunks) + 1,
                            "page_number": page_data['page'],
                            "page_range": str(page_data['page']),
                            "word_count": len(chunk['text'].split())
                        }
                        chunks.append({
                            "content": chunk['text'],
                            "metadata": chunk_metadata
                        })
            elif method in ["by_paragraphs", "by_sententces"]:
                # 对每页内容进行段落或句子分块
                split_method = self._paragraph_chunks if method == "by_paragraphs" else self._sentence_chunks
                for page_data in page_map:
                    page_chunks = split_method(page_data['text'])
                    for chunk in page_chunks:
                        chunk_metadata = {
                            "chunk_id": len(chunks) + 1,  ##当前分块id
                            "page_number": page_data['page'],  ##当前页码
                            "page_range": str(page_data['page']),  ##当前页码范围
                            "word_count": len(chunk['text'].split())  ##当前分块字数
                        }
                        chunks.append({
                            "content": chunk['text'],
                            "metadata": chunk_metadata
                        })
            else:
                raise ValueError(f"不支持的分块方法: {method}")
            # 根据return_metadata参数决定返回格式
            if return_metadata:
                ##创建标准化的文档数据结构
                document_data = {
                   "filename": metadata.get("filename", ""),
                    "total_chunks": len(chunks),
                    "total_pages": total_pages,
                    "loading_method": metadata.get("loading_method", ""),
                    "chunking_method": method,
                    "timestamp": datetime.now().isoformat(),
                    "chunks": chunks
                }
                return document_data
            else:
                # 直接返回chunks列表，用于note_service
                return chunks
        except Exception as e:
            logger.error(f"分块失败: {e}")
            raise

    def _sentence_chunks(self, text: str) -> list[dict]:

        """
        使用 LangChain 的 RecursiveCharacterTextSplitter 将文本按句子进行分块。

        Args:
            text (str): 原始文本

        Returns:
            list[dict]: 每块为 {"text": chunk} 的字典列表
        """

        splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            separators=["。", "？", "！", "。", "？", "！"]
        )

        docs = splitter.create_documents([text])
        return [{"text": doc.page_content} for doc in docs]
    


    def _paragraph_chunks(self, text: str) -> list[dict]:
        """
        使用 LangChain 的 RecursiveCharacterTextSplitter 将文本按句子进行分块。

        Args:
            text (str): 原始文本

        Returns:
            list[dict]: 每块为 {"text": chunk} 的字典列表
        """
        splitter = RecursiveCharacterTextSplitter(
            separators = ["\n\n"], ##仅按段落切分
            chunk_size=10000, ##非常大，确保不再被二次切分
            chunk_overlap=0,  
        )

        docs = splitter.create_documents([text])
        return [{"text": doc.page_content} for doc in docs]
    
    def _fixed_size_chunking(self, text: str, chunk_size: int) -> list[dict]:
        """
        使用 LangChain 的 RecursiveCharacterTextSplitter 将文本按固定大小进行分块。

        Args:
            text (str): 原始文本
            chunk_size (int): 每块最大字符数

        Returns:
            list[dict]: 每块为 {"text": chunk} 的字典列表
        """
        splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=0,  # 如需上下文重叠，可改为 50~100
        )

        # LangChain 返回的是 Document 对象，提取 page_content 即可
        documents = splitter.create_documents([text])
        return [{"text": doc.page_content} for doc in documents]
