#!/usr/bin/env python
# coding: utf-8

# # DeepSeek Agent 实战：小红书爆款文案生成助手
# 
# 本 Notebook 将指导您如何使用 DeepSeek LLM 构建一个能够生成小红书爆款文案的智能 Agent。我们将从需求拆解开始，逐步定义 Agent 的系统提示词 (System Prompt)、外部工具 (Tools)，并实现其核心的工作流程，最终生成符合小红书平台特点的文案。
# 
# ## 1. 环境准备与DeepSeek API配置

import os
import json
import re
import time
import random
from langchain_openai import ChatOpenAI
from pydantic import SecretStr
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain.tools import tool

# mock 工具函数（如有需要可移到单独文件）
def mock_search_web(query: str) -> str:
    """模拟网页搜索工具，返回预设的搜索结果。"""
    print(f"[Tool Call] 模拟搜索网页：{query}")
    time.sleep(1) # 模拟网络延迟
    if "小红书美妆趋势" in query:
        return "近期小红书美妆流行'多巴胺穿搭'、'早C晚A'护肤理念、'伪素颜'妆容，热门关键词有#氛围感、#抗老、#屏障修复。"
    elif "保湿面膜" in query:
        return "小红书保湿面膜热门话题：沙漠干皮救星、熬夜急救面膜、水光肌养成。用户痛点：卡粉、泛红、紧绷感。"
    elif "深海蓝藻保湿面膜" in query:
        return "关于深海蓝藻保湿面膜的用户评价：普遍反馈补水效果好，吸收快，对敏感肌友好。有用户提到价格略高，但效果值得。"
    else:
        return f"未找到关于 '{query}' 的特定信息，但市场反馈通常关注产品成分、功效和用户体验。"
def mock_query_product_database(product_name: str) -> str:
    """模拟查询产品数据库，返回预设的产品信息。"""
    print(f"[Tool Call] 模拟查询产品数据库：{product_name}")
    time.sleep(0.5) # 模拟数据库查询延迟
    if "深海蓝藻保湿面膜" in product_name:
        return "深海蓝藻保湿面膜：核心成分为深海蓝藻提取物，富含多糖和氨基酸，能深层补水、修护肌肤屏障、舒缓敏感泛红。质地清爽不粘腻，适合所有肤质，尤其适合干燥、敏感肌。规格：25ml*5片。"
    elif "美白精华" in product_name:
        return "美白精华：核心成分是烟酰胺和VC衍生物，主要功效是提亮肤色、淡化痘印、改善暗沉。质地轻薄易吸收，适合需要均匀肤色的人群。"
    else:
        return f"产品数据库中未找到关于 '{product_name}' 的详细信息。"
def mock_generate_emoji(context: str) -> list:
    """模拟生成表情符号，根据上下文提供常用表情。"""
    print(f"[Tool Call] 模拟生成表情符号，上下文：{context}")
    time.sleep(0.2) # 模拟生成延迟
    if "补水" in context or "水润" in context or "保湿" in context:
        return ["💦", "💧", "🌊", "✨"]
    elif "惊喜" in context or "哇塞" in context or "爱了" in context:
        return ["💖", "😍", "🤩", "💯"]
    elif "熬夜" in context or "疲惫" in context:
        return ["😭", "😮‍💨", "😴", "💡"]
    elif "好物" in context or "推荐" in context:
        return ["✅", "👍", "⭐", "🛍️"]
    else:
        return random.sample(["✨", "🔥", "💖", "💯", "🎉", "👍", "🤩", "💧", "🌿"], k=min(5, len(context.split())))

SYSTEM_PROMPT = """
你是一个资深的小红书爆款文案专家，擅长结合最新潮流和产品卖点，创作引人入胜、高互动、高转化的笔记文案。
你的任务是根据用户提供的产品和需求，生成包含标题、正文、相关标签和表情符号的完整小红书笔记。
请始终采用'Thought-Action-Observation'模式进行推理和行动。文案风格需活泼、真诚、富有感染力。当完成任务后，请以JSON格式直接输出最终文案。
"""

def get_llm_client():
    api_key = os.getenv("OPENAI_API_KEY", "sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2")
    if not api_key:
        raise ValueError("请设置 OPENAI_API_KEY 环境变量")
    base_url = os.getenv("OPENAI_BASE_URL", "https://api.zhizengzeng.com/v1")
    model = os.getenv("OPENAI_MODEL", "gpt-4o-mini")
    return ChatOpenAI(
        api_key=SecretStr(api_key),
        base_url=base_url,
        model=model
    )

client = get_llm_client()

@tool
def search_web_tool(query: str) -> str:
    """搜索互联网上的实时信息。"""
    return mock_search_web(query)

@tool
def query_product_database_tool(product_name: str) -> str:
    """查询内部产品数据库。"""
    return mock_query_product_database(product_name)

@tool
def generate_emoji_tool(context: str) -> str:
    """根据内容生成表情符号。"""
    emojis = mock_generate_emoji(context)
    return " ".join(emojis)

def generate_rednote(product_name: str, tone_style: str = "活泼甜美", max_iterations: int = 5) -> str:
    """
    使用 Agent 生成小红书爆款文案。
    Args:
        product_name (str): 产品名称。
        tone_style (str): 文案风格。
        max_iterations (int): 最大推理轮数。
    Returns:
        str: 爆款文案（JSON字符串）。
    """
    print(f"\n🚀 启动小红书文案生成助手，产品：{product_name}，风格：{tone_style}\n")
    prompt = ChatPromptTemplate.from_messages([
        ("system", SYSTEM_PROMPT),
        MessagesPlaceholder(variable_name="chat_history"),
        ("human", "{input}"),
        MessagesPlaceholder(variable_name="agent_scratchpad"),
    ])
    tools = [search_web_tool, query_product_database_tool, generate_emoji_tool]
    agent = create_openai_tools_agent(client, tools, prompt)
    # 这里创建了一个 AgentExecutor 实例，用于实际执行智能体（agent）的推理和工具调用流程。
    # 参数说明：
    # - agent: 传入上面创建的 agent（即具备推理和调用外部工具能力的 LLM 代理）
    # - tools: 可用的工具列表（如网页搜索、产品数据库、表情生成等）
    # - verbose: 设置为 True，会在控制台详细输出 agent 的推理过程和每一步调用
    # - max_iterations: 限制 agent 推理的最大轮数，防止死循环
    agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True, max_iterations=max_iterations)
    user_input = f"请为产品「{product_name}」生成一篇小红书爆款文案。要求：语气{tone_style}，包含标题、正文、至少5个相关标签和5表情符号。请以完整的JSON格式输出，并确保JSON内容用markdown代码块包裹（例如：```json{{...}}```）。"
    try:
        # 是的，这里调用 agent_executor.invoke 时，Agent 会自动完成 Thought-Action-Observation（思考-行动-观察）推理流程。
        # 原理是：Agent 会根据系统提示词（System Prompt）和用户输入，结合历史对话内容，判断是否需要调用外部工具（如网页搜索、数据库、表情生成等）。
        # 当 LLM 认为需要获取外部信息时，会自动生成 Action（即调用某个工具），并将 Observation（工具返回结果）纳入下一轮推理。
        # 这个过程会循环进行，直到 LLM 判断任务已完成（即输出 Final Answer），或者达到 max_iterations 限制为止。
        response = agent_executor.invoke({
            "input": user_input,
            "chat_history": []
        })
        result = response["output"]
        json_string_match = re.search(r"```json\s*(\{.*\})\s*```", result, re.DOTALL)
        if json_string_match:
            extracted_json_content = json_string_match.group(1)
            try:
                final_response = json.loads(extracted_json_content)
                print("Agent: 任务完成，成功解析最终JSON文案。")
                return json.dumps(final_response, ensure_ascii=False, indent=2)
            except json.JSONDecodeError as e:
                print(f"Agent: 提取到JSON块但解析失败: {e}")
                print(f"尝试解析的字符串:\n{extracted_json_content}")
                return result
        else:
            try:
                final_response = json.loads(result)
                print("Agent: 任务完成，直接解析最终JSON文案。")
                return json.dumps(final_response, ensure_ascii=False, indent=2)
            except json.JSONDecodeError:
                print("Agent: 生成了非JSON格式内容，返回原始结果。")
                return result
    except Exception as e:
        print(f"调用 Agent 时发生错误: {e}")
        return "未能成功生成文案。"

def format_rednote_for_markdown(json_string: str) -> str:
    """
    将 JSON 格式的小红书文案转换为 Markdown 格式。
    """
    try:
        data = json.loads(json_string)
    except json.JSONDecodeError as e:
        return f"错误：无法解析 JSON 字符串 - {e}\n原始字符串：\n{json_string}"
    title = data.get("title", "无标题")
    body = data.get("body", "")
    hashtags = data.get("hashtags", [])
    markdown_output = f"## {title}\n\n{body}\n\n"
    if hashtags:
        hashtag_string = " ".join(hashtags)
        markdown_output += f"{hashtag_string}\n"
    return markdown_output.strip()

if __name__ == "__main__":
    # 示例：生成两种风格的文案
    product_name_1 = "深海蓝藻保湿面膜"
    tone_style_1 = "活泼甜美"
    result_1 = generate_rednote(product_name_1, tone_style_1)
    print("\n--- 生成的文案 1 ---")
    print(result_1)
    print("\n--- Markdown 格式 ---")
    print(format_rednote_for_markdown(result_1))

    product_name_2 = "美白精华"
    tone_style_2 = "知性温柔"
    result_2 = generate_rednote(product_name_2, tone_style_2)
    print("\n--- 生成的文案 2 ---")
    print(result_2)
    print("\n--- Markdown 格式 ---")
    print(format_rednote_for_markdown(result_2))
