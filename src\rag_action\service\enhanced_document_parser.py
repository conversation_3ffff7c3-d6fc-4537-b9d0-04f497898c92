"""
增强的文档解析服务
集成MinierU、Markdown解析、父子文档和上下文窗口功能
"""
import os
import logging
import tempfile
import json
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import hashlib

logger = logging.getLogger(__name__)

# 导入MinierU
try:
    from mineru.cli.common import read_fn
    from mineru.data.data_reader_writer import FileBasedDataWriter
    from mineru.utils.enum_class import MakeMode
    from mineru.backend.pipeline.pipeline_analyze import doc_analyze as pipeline_doc_analyze
    from mineru.backend.pipeline.pipeline_middle_json_mkcontent import union_make as pipeline_union_make
    from mineru.backend.pipeline.model_json_to_middle_json import result_to_middle_json as pipeline_result_to_middle_json
    from mineru.cli.common import prepare_env
    # 注意：convert_pdf_bytes_to_bytes_by_pypdfium2 可能不存在，先不导入
    MINERU_AVAILABLE = True
    logger.info("MinierU 可用")
except ImportError as e:
    MINERU_AVAILABLE = False
    logger.warning(f"MinierU 不可用，将使用备选解析器: {e}")
except Exception as e:
    MINERU_AVAILABLE = False
    logger.warning(f"MinierU 导入时出现其他错误: {e}")

# 导入Markdown解析
try:
    import markdown
    from markdown.extensions import toc, tables, codehilite
    MARKDOWN_AVAILABLE = True
except ImportError:
    MARKDOWN_AVAILABLE = False
    logger.warning("Markdown库不可用，请安装: pip install markdown")

# 导入LangChain文本分割器
try:
    from langchain.text_splitter import RecursiveCharacterTextSplitter, MarkdownHeaderTextSplitter
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    logger.warning("LangChain不可用")


class EnhancedDocumentParser:
    """增强的文档解析器，支持MinierU、Markdown解析、父子文档和上下文窗口"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.use_mineru = config.get("use_mineru", True) and MINERU_AVAILABLE
        self.chunk_size = config.get("chunk_size", 1000)
        self.chunk_overlap = config.get("chunk_overlap", 200)
        self.context_window_size = config.get("context_window_size", 3)  # 上下文窗口大小
        self.enable_parent_child = config.get("enable_parent_child", True)  # 启用父子文档
        self.parent_chunk_size = config.get("parent_chunk_size", 2000)  # 父文档块大小
        
        # 初始化文本分割器
        if LANGCHAIN_AVAILABLE:
            # 子文档分割器（较小的块）- 优化语义分块
            self.text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=self.chunk_size,
                chunk_overlap=self.chunk_overlap,
                length_function=len,
                # 优化：增加更多语义分隔符，优先在句子和段落边界切分
                separators=[
                    "\n\n",      # 段落分隔符（最高优先级）
                    "\n",        # 行分隔符
                    "。",        # 中文句号
                    "！",        # 中文感叹号
                    "？",        # 中文问号
                    ";",         # 英文分号
                    "；",        # 中文分号
                    ".",         # 英文句号
                    "!",         # 英文感叹号
                    "?",         # 英文问号
                    " ",         # 空格
                    ""           # 字符级别（最后选择）
                ]
            )

            # 父文档分割器（较大的块）
            self.parent_splitter = RecursiveCharacterTextSplitter(
                chunk_size=self.parent_chunk_size,
                chunk_overlap=self.chunk_overlap * 2,
                length_function=len,
                separators=["\n\n", "\n", "。", "！", "？", ";", "；", ".", "!", "?", " ", ""]
            )
            
            # Markdown标题分割器
            self.markdown_splitter = MarkdownHeaderTextSplitter(
                headers_to_split_on=[
                    ("#", "Header 1"),
                    ("##", "Header 2"),
                    ("###", "Header 3"),
                ]
            )
        
        # 智能分块配置
        self.min_chunk_size = config.get("min_chunk_size", 100)  # 最小块大小
        self.filter_meaningless_chunks = config.get("filter_meaningless_chunks", True)  # 过滤无意义块

        logger.info(f"增强文档解析器初始化: MinierU={'可用' if self.use_mineru else '不可用'}, 父子文档={'启用' if self.enable_parent_child else '禁用'}")
    
    def parse_pdf_with_mineru(self, file_path: str, filename: str = "") -> Dict[str, Any]:
        """
        使用MinierU解析PDF文件
        
        Args:
            file_path: PDF文件路径
            filename: 文件名
            
        Returns:
            解析结果字典
        """
        if not MINERU_AVAILABLE:
            raise ImportError("MinierU不可用")
        
        logger.info(f"使用MinierU解析PDF: {filename}")
        
        try:
            # 创建临时输出目录
            with tempfile.TemporaryDirectory() as temp_dir:
                output_dir = Path(temp_dir) / "output"
                output_dir.mkdir(exist_ok=True)
                
                # 读取PDF文件
                pdf_bytes = read_fn(file_path)
                pdf_bytes_list = [pdf_bytes]
                file_name_list = [Path(filename).stem]
                lang_list = ["ch"]  # 默认中文
                
                # 使用MinierU pipeline模式解析
                infer_results, all_image_lists, all_pdf_docs, lang_list, ocr_enabled_list = pipeline_doc_analyze(
                    pdf_bytes_list, 
                    lang_list, 
                    parse_method="auto",
                    formula_enable=True,
                    table_enable=True
                )
                
                # 处理解析结果
                model_list = infer_results[0]
                images_list = all_image_lists[0]
                pdf_doc = all_pdf_docs[0]
                _lang = lang_list[0]
                _ocr_enable = ocr_enabled_list[0]
                
                # 准备输出目录
                local_image_dir, local_md_dir = prepare_env(str(output_dir), file_name_list[0], "auto")
                image_writer = FileBasedDataWriter(local_image_dir)
                
                # 转换为中间JSON格式
                middle_json = pipeline_result_to_middle_json(
                    model_list, images_list, pdf_doc, image_writer, _lang, _ocr_enable, True
                )
                
                pdf_info = middle_json["pdf_info"]
                
                # 生成Markdown内容
                image_dir = str(os.path.basename(local_image_dir))
                markdown_content = pipeline_union_make(pdf_info, MakeMode.MM_MD, image_dir)
                
                # 生成内容列表
                content_list = pipeline_union_make(pdf_info, MakeMode.CONTENT_LIST, image_dir)
                
                # 处理pdf_info可能是列表的情况
                if isinstance(pdf_info, list):
                    total_pages = len(pdf_info)
                    pages_info = pdf_info
                else:
                    total_pages = len(pdf_info.get("pages", [])) if pdf_info else 0
                    pages_info = pdf_info.get("pages", []) if pdf_info else []

                return {
                    "content": markdown_content,
                    "markdown_content": markdown_content,
                    "content_list": content_list,
                    "pdf_info": pdf_info,
                    "metadata": {
                        "filename": filename,
                        "parser": "mineru",
                        "total_pages": total_pages,
                        "has_images": True,
                        "has_tables": True,
                        "has_formulas": True
                    }
                }
                
        except Exception as e:
            logger.error(f"MinierU解析失败: {e}")
            raise
    
    def parse_markdown_content(self, markdown_content: str) -> Dict[str, Any]:
        """
        解析Markdown内容，提取结构化信息
        
        Args:
            markdown_content: Markdown文本
            
        Returns:
            解析结果字典
        """
        if not MARKDOWN_AVAILABLE:
            logger.warning("Markdown库不可用，跳过Markdown解析")
            return {"html_content": markdown_content, "toc": []}
        
        try:
            # 配置Markdown扩展
            md = markdown.Markdown(
                extensions=[
                    'toc',
                    'tables', 
                    'codehilite',
                    'fenced_code',
                    'attr_list'
                ],
                extension_configs={
                    'toc': {
                        'title': '目录',
                        'anchorlink': True
                    },
                    'codehilite': {
                        'css_class': 'highlight'
                    }
                }
            )
            
            # 转换为HTML
            html_content = md.convert(markdown_content)
            
            # 提取目录
            toc = md.toc_tokens if hasattr(md, 'toc_tokens') else []
            
            return {
                "html_content": html_content,
                "toc": toc,
                "markdown_content": markdown_content
            }
            
        except Exception as e:
            logger.error(f"Markdown解析失败: {e}")
            return {"html_content": markdown_content, "toc": []}
    
    def create_parent_child_chunks(self, content: str, metadata: Dict[str, Any]) -> Tuple[List[Dict], List[Dict]]:
        """
        创建父子文档块
        
        Args:
            content: 文档内容
            metadata: 元数据
            
        Returns:
            (父文档块列表, 子文档块列表)
        """
        if not LANGCHAIN_AVAILABLE or not self.enable_parent_child:
            # 如果不支持或未启用父子文档，返回普通分块
            chunks = self._simple_chunk(content, metadata)
            return chunks, chunks
        
        try:
            # 首先创建子文档块（较小的块）
            child_chunks = self.text_splitter.split_text(content)
            child_docs = []

            for i, child_chunk in enumerate(child_chunks):
                child_id = f"{metadata.get('filename', 'doc')}_child_{i}"
                child_doc = {
                    "id": child_id,
                    "content": child_chunk,
                    "type": "child",
                    "chunk_index": i,
                    "parent_id": None,  # 暂时设为None，后续会更新
                    "metadata": {
                        **metadata,
                        "chunk_type": "child",
                        "chunk_size": len(child_chunk)
                    }
                }
                child_docs.append(child_doc)

            # 过滤无意义的子文档块
            child_docs = self._filter_chunks(child_docs)

            # 优化：只有当子文档数量>3时才创建父文档，减少冗余
            parent_docs = []
            if len(child_docs) > 3:
                # 创建父文档块（较大的块）
                parent_chunks = self.parent_splitter.split_text(content)

                for i, parent_chunk in enumerate(parent_chunks):
                    parent_id = f"{metadata.get('filename', 'doc')}_parent_{i}"
                    parent_doc = {
                        "id": parent_id,
                        "content": parent_chunk,
                        "type": "parent",
                        "chunk_index": i,
                        "metadata": {
                            **metadata,
                            "chunk_type": "parent",
                            "parent_id": parent_id,
                            "chunk_size": len(parent_chunk)
                        }
                    }
                    parent_docs.append(parent_doc)

                # 过滤无意义的父文档块
                parent_docs = self._filter_chunks(parent_docs)

                # 为子文档分配父文档ID
                for child_doc in child_docs:
                    parent_id = self._find_parent_for_child(child_doc["content"], parent_docs)
                    child_doc["parent_id"] = parent_id
                    child_doc["metadata"]["parent_id"] = parent_id
            else:
                # 子文档数量较少时，不创建父文档，避免冗余
                logger.info(f"子文档数量({len(child_docs)})较少，跳过父文档创建")

            logger.info(f"创建父子文档: {len(parent_docs)}个父文档, {len(child_docs)}个子文档")
            return parent_docs, child_docs
            
        except Exception as e:
            logger.error(f"创建父子文档失败: {e}")
            # 降级到普通分块
            chunks = self._simple_chunk(content, metadata)
            return chunks, chunks
    
    def _find_parent_for_child(self, child_content: str, parent_docs: List[Dict]) -> str:
        """找到子文档对应的父文档ID"""
        child_start = child_content[:100]  # 取前100个字符作为特征
        
        for parent_doc in parent_docs:
            if child_start in parent_doc["content"]:
                return parent_doc["id"]
        
        # 如果找不到，返回第一个父文档的ID
        return parent_docs[0]["id"] if parent_docs else "unknown"
    
    def create_context_windows(self, chunks: List[Dict]) -> List[Dict]:
        """
        为文档块创建上下文窗口
        
        Args:
            chunks: 文档块列表
            
        Returns:
            带上下文窗口的文档块列表
        """
        if self.context_window_size <= 0:
            return chunks
        
        try:
            enhanced_chunks = []
            
            for i, chunk in enumerate(chunks):
                # 获取上下文窗口
                start_idx = max(0, i - self.context_window_size)
                end_idx = min(len(chunks), i + self.context_window_size + 1)
                
                # 构建上下文
                context_before = []
                context_after = []
                
                for j in range(start_idx, i):
                    context_before.append(chunks[j]["content"])
                
                for j in range(i + 1, end_idx):
                    context_after.append(chunks[j]["content"])
                
                # 创建增强的块
                enhanced_chunk = chunk.copy()
                enhanced_chunk["context"] = {
                    "before": context_before,
                    "after": context_after,
                    "window_size": self.context_window_size
                }
                
                # 创建包含上下文的完整内容（用于检索）
                full_context = ""
                if context_before:
                    full_context += "...\n" + "\n".join(context_before[-1:]) + "\n"
                full_context += chunk["content"]
                if context_after:
                    full_context += "\n" + "\n".join(context_after[:1]) + "\n..."
                
                enhanced_chunk["content_with_context"] = full_context
                enhanced_chunks.append(enhanced_chunk)
            
            logger.info(f"为{len(chunks)}个文档块添加了上下文窗口")
            return enhanced_chunks
            
        except Exception as e:
            logger.error(f"创建上下文窗口失败: {e}")
            return chunks

    def _is_meaningful_chunk(self, content: str) -> bool:
        """
        判断文档块是否有意义

        Args:
            content: 文档块内容

        Returns:
            bool: 是否有意义
        """
        if not content or len(content.strip()) < self.min_chunk_size:
            return False

        # 移除空白字符后的内容
        clean_content = content.strip()

        # 检查是否只包含特殊字符、数字或极短的内容
        import re

        # 如果内容主要由特殊字符组成，认为无意义
        special_char_ratio = len(re.findall(r'[^\w\s\u4e00-\u9fff]', clean_content)) / len(clean_content)
        if special_char_ratio > 0.7:
            return False

        # 如果内容主要由数字组成，认为无意义
        digit_ratio = len(re.findall(r'\d', clean_content)) / len(clean_content)
        if digit_ratio > 0.8:
            return False

        # 如果内容过短且没有实际文字，认为无意义
        word_count = len(re.findall(r'[\w\u4e00-\u9fff]+', clean_content))
        if len(clean_content) < 200 and word_count < 5:
            return False

        # 检查是否只是页眉页脚等重复内容
        common_meaningless_patterns = [
            r'^第\s*\d+\s*页',  # 页码
            r'^\d+\s*$',       # 纯数字
            r'^[^\w\u4e00-\u9fff]*$',  # 只有特殊字符
            r'^(目录|索引|参考文献|附录)\s*$',  # 常见无意义标题
        ]

        for pattern in common_meaningless_patterns:
            if re.match(pattern, clean_content, re.IGNORECASE):
                return False

        return True

    def _filter_chunks(self, chunks: List[Dict]) -> List[Dict]:
        """
        过滤无意义的文档块

        Args:
            chunks: 原始文档块列表

        Returns:
            过滤后的文档块列表
        """
        if not self.filter_meaningless_chunks:
            return chunks

        filtered_chunks = []
        for chunk in chunks:
            if self._is_meaningful_chunk(chunk.get("content", "")):
                filtered_chunks.append(chunk)
            else:
                logger.debug(f"过滤无意义块: {chunk.get('content', '')[:50]}...")

        logger.info(f"块过滤: {len(chunks)} -> {len(filtered_chunks)} (过滤掉{len(chunks) - len(filtered_chunks)}个)")
        return filtered_chunks

    def _simple_chunk(self, content: str, metadata: Dict[str, Any]) -> List[Dict]:
        """简单分块方法（备选方案）"""
        chunks = []
        chunk_size = self.chunk_size
        
        for i in range(0, len(content), chunk_size):
            chunk_content = content[i:i + chunk_size]
            chunk = {
                "id": f"{metadata.get('filename', 'doc')}_{i // chunk_size}",
                "content": chunk_content,
                "type": "simple",
                "chunk_index": i // chunk_size,
                "metadata": {
                    **metadata,
                    "chunk_type": "simple",
                    "chunk_size": len(chunk_content)
                }
            }
            chunks.append(chunk)
        
        return chunks
    
    def parse_pdf(self, file_path: str, filename: str = "") -> Dict[str, Any]:
        """
        完整的PDF解析流程
        
        Args:
            file_path: PDF文件路径
            filename: 文件名
            
        Returns:
            完整的解析结果
        """
        try:
            # 1. 使用MinierU解析PDF
            if self.use_mineru:
                mineru_result = self.parse_pdf_with_mineru(file_path, filename)
                markdown_content = mineru_result["markdown_content"]
                base_metadata = mineru_result["metadata"]
            else:
                # 备选方案：使用PyMuPDF
                from .document_parser import get_document_parser
                fallback_parser = get_document_parser({"use_mineru": False})
                fallback_result = fallback_parser.parse_pdf(file_path, filename)
                markdown_content = fallback_result["content"]
                base_metadata = fallback_result["metadata"]
            
            # 2. 解析Markdown内容
            markdown_result = self.parse_markdown_content(markdown_content)
            
            # 3. 创建父子文档块
            metadata = {
                **base_metadata,
                "source": filename,
                "parser": "enhanced_mineru"
            }
            
            parent_chunks, child_chunks = self.create_parent_child_chunks(markdown_content, metadata)
            
            # 4. 为子文档添加上下文窗口
            child_chunks_with_context = self.create_context_windows(child_chunks)
            
            # 5. 构建最终结果
            result = {
                "content": markdown_content,
                "markdown_content": markdown_content,
                "html_content": markdown_result.get("html_content", ""),
                "toc": markdown_result.get("toc", []),
                "parent_chunks": parent_chunks,
                "child_chunks": child_chunks_with_context,
                "chunks": child_chunks_with_context,  # 默认使用子文档块
                "metadata": {
                    **metadata,
                    "total_parent_chunks": len(parent_chunks),
                    "total_child_chunks": len(child_chunks),
                    "context_window_size": self.context_window_size,
                    "parent_child_enabled": self.enable_parent_child
                }
            }
            
            logger.info(f"PDF解析完成: {filename}, 父文档块: {len(parent_chunks)}, 子文档块: {len(child_chunks)}")
            return result
            
        except Exception as e:
            logger.error(f"PDF解析失败: {e}")
            raise


def get_enhanced_document_parser(config: Dict[str, Any]) -> EnhancedDocumentParser:
    """获取增强文档解析器实例"""
    return EnhancedDocumentParser(config)
