# AI笔记系统配置文件
app:
  host: "0.0.0.0"
  port: 8000
  debug: true
  title: "AI笔记系统"
  description: "基于RAG的智能笔记管理系统"
  version: "1.0.0"

# MySQL数据库配置
mysql:
  host: "localhost"
  port: 3306
  user: "root"
  password: "root"
  database: "ai_notes"
  charset: "utf8mb4"
  echo: false  # 是否打印SQL语句

# Milvus向量数据库配置
milvus:
  host: "localhost"
  port: 19530
  collection_name: "note_embeddings"
  embedding_dim: 1536  # OpenAI embedding维度
  index_type: "HNSW"   # 索引类型
  metric_type: "COSINE"  # 相似度计算方式

# 向量嵌入配置
embedding:
  model: "openai"  # 支持 openai、bge、moonshot
  openai_api_key: "sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
  base_url: "https://api.zhizengzeng.com/v1"  # 如果使用代理或私有化部署，请修改此URL
  model_name: "text-embedding-3-small"  # OpenAI嵌入模型
  chunk_size: 1800     # 优化：增加到1800字符，提高语义完整性
  chunk_overlap: 270   # 优化：15%重叠，保持上下文连贯性
  max_tokens: 8000     # 最大token数

# BGE模型配置（备选）
bge:
  model_name: "BAAI/bge-large-zh-v1.5"
  device: "cpu"  # 或 "cuda"
  normalize_embeddings: true

# LLM配置（用于问答生成）
llm:
  provider: "openai"  # 支持 openai、moonshot、deepseek
  api_key: "sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
  base_url: "https://api.zhizengzeng.com/v1"
  model_name: "gpt-4o-mini"
  temperature: 0.7
  max_tokens: 2000

# 文件处理配置
file:
  upload_dir: "uploads"
  temp_dir: "temp"
  max_file_size: 50  # MB
  allowed_extensions: [".pdf"]

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/app.log"

# RAG检索配置
rag:
  top_k: 5  # 默认检索文档数量
  similarity_threshold: 0.7  # 相似度阈值
  max_context_length: 4000  # 最大上下文长度
  prompt_template: |
    基于以下相关文档内容，请回答用户的问题。如果文档中没有相关信息，请说明无法从提供的文档中找到答案。

    相关文档：
    {context}

    用户问题：{question}

    请提供准确、详细的回答：

# 文档解析配置
document_parsing:
  use_mineru: true  # 是否使用MinierU解析PDF
  mineru_config:
    output_format: "markdown"
    extract_images: true
    extract_tables: true
    ocr_enabled: true
  fallback_parser: "pymupdf"  # MinierU不可用时的备选解析器

# 检索前处理配置
retrieval_preprocessing:
  query_refinement:
    enabled: true
    model: "gpt-4o-mini"  # 用于查询重写的模型
    max_refinements: 2  # 最大重写次数

  self_query:
    enabled: true
    metadata_fields: ["tags", "source", "created_at"]  # 支持的元数据字段

  query_routing:
    enabled: true
    routes:
      - name: "rag_search"
        description: "普通问答和文档检索"
        keywords: ["什么", "如何", "为什么", "介绍", "解释"]
      - name: "sql_query"  # 暂不实现
        description: "数据库查询"
        keywords: ["统计", "数量", "列表", "查找"]

# 混合检索配置
ensemble_retrieval:
  enabled: true
  retrievers:
    vector:
      weight: 0.7
      top_k: 10
    bm25:
      weight: 0.3
      top_k: 10
      language: "chinese"
  final_top_k: 5

# 检索后处理配置
retrieval_postprocessing:
  reranking:
    enabled: true
    model: "colbert-ir/colbertv2"  # ColBERT重排模型
    top_k: 3  # 重排后保留的文档数量
    batch_size: 32

  context_compression:
    enabled: true
    max_tokens: 2000  # 压缩后的最大token数
