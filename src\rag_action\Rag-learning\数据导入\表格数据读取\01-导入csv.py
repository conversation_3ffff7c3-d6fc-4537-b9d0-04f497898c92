from langchain_community.document_loaders import CSVLoader

## 第一部分： 基本加载 csv文件并打印记录
file_path = "C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/黑神话悟空.csv"
loader = CSVLoader(file_path=file_path, encoding="utf-8")
data = loader.load()
print("示例1： 基本加载csv文件并打印前两条记录")
for record in data[:2]:
    print(record)
print("-"*100)

## 第二部分： 跳过标题行并使用自定义列名
loader = CSVLoader(
    file_path=file_path,
    encoding="utf-8",
    csv_args={
        "delimiter": ",",  # 指定分隔符为英文逗号
        "quotechar": '"',  # 指定引用字符为英文双引号
        # 指定自定义的列名，跳过原始csv文件的标题行，按顺序赋予每一列新的名称
        "fieldnames": ["种类", "名称", "说明", "等级"],
    }
)

data = loader.load()
print("示例2： 跳过标题行并使用自定义列名")
for record in data[:2]:
    print(record)
print("-"*100)

##第三部分： 指定 "Name" 列作为source_column
loader = CSVLoader(
    file_path=file_path,
    encoding="utf-8",
    source_column="Name",
)

data = loader.load()
print("示例3： 指定 'Name' 列作为source_column")
for record in data[:2]:
    print(record)
print("-"*100)


## 第四部分： 使用UnstructuredCSVLoader加载csv文件
from langchain_community.document_loaders import UnstructuredCSVLoader

loader = UnstructuredCSVLoader(file_path=file_path)
data = loader.load()
print("示例4： 使用UnstructuredCSVLoader加载csv文件")
for record in data[:2]:
    print(record)
print("-"*100)

