import asyncio
import json
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from langchain_mcp_adapters.client import MultiServerMCPClient
import os

def load_mcp_servers(path="mcp_services.json"):
    try:
        with open(path, "r") as f:
            raw = json.load(f)["mcpServers"]
    except FileNotFoundError: 
        raw = {
            "local-tools": {
                "type": "sse",
                "url": "http://localhost:8000"
            }
        }
    
    return {
        name: {
            "url": info["url"],
            "transport": "sse" if info["type"] == "sse" else "stdio"
        }
        for name, info in raw.items()
    }

async def main():
    print("🔧 加载MCP服务器配置...")
    service_config = load_mcp_servers()
    
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2")
    OPENAI_BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.zhizengzeng.com/v1")
    
    print("🔌 创建MCP客户端...")
    client = MultiServerMCPClient(service_config)
    
    print("📦 获取工具...")
    tools = await client.get_tools()
    print(f"✅ 成功加载 {len(tools)} 个工具")
    
    # 显示工具详情
    for i, tool in enumerate(tools):
        print(f"工具 {i+1}: {tool.name}")
        print(f"  描述: {tool.description}")
        print()
    
    llm = ChatOpenAI(
        api_key=OPENAI_API_KEY,
        base_url=OPENAI_BASE_URL,
        temperature=0,
        model="gpt-4o-mini"
    )
    
    print("🤖 创建LangGraph Agent...")
    # 使用LangGraph的create_react_agent
    agent = create_react_agent(llm, tools)
    
    print(f"✅ 已加载 {len(tools)} 个工具，输入自然语言指令（输入 exit 退出）：\n")
    print("示例指令:")
    print("- 计算 5 + 3")
    print("- 10 减去 4")
    print("- 请帮我计算 15 + 7")
    print()
    
    while True:
        user_input = input("👤 你说: ").strip()
        if user_input.lower() in {"exit", "quit"}:
            print("👋 再见！")
            break

        try:
            print("🔄 处理请求...")
            
            # 使用LangGraph agent
            result = await agent.ainvoke({
                "messages": [{"role": "user", "content": user_input}]
            })
            
            # 提取AI回复
            if isinstance(result, dict) and "messages" in result:
                messages = result["messages"]
                # 找到最后一条AI消息
                ai_messages = [msg for msg in messages if hasattr(msg, 'content') and msg.content and hasattr(msg, 'type') and msg.type == 'ai']
                if ai_messages:
                    print("🤖 结果:", ai_messages[-1].content)
                else:
                    print("🤖 结果:", result)
            else:
                print("🤖 结果:", result)
            
        except Exception as e:
            print("❌ 出错：", e)
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
