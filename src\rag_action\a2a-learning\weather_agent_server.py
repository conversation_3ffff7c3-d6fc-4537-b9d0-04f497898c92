# weather_agent_server.py
import time
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Dict, Any, Optional
import uvicorn
from python_a2a import A2AServer, AgentCard, AgentSkill, Task, TaskStatus, TaskState, Message, TextContent, MessageRole

class WeatherAgent(A2AServer):
    def __init__(self, port=5001):
        self.port = port
        agent_card = AgentCard(
            name="Weather Agent",
            description="提供指定城市的天气信息",
            url=f"http://localhost:{self.port}",
            version="1.0.0",
            skills=[
                AgentSkill(
                    name="GetCityWeather",
                    description="获取一个城市的当前天气",
                    tags=["weather", "location", "forecast"]
                )
            ]
        )
        super().__init__(agent_card=agent_card)

    def _get_simulated_weather(self, city):
        print(f"[WeatherAgent] 正在为城市 '{city}' 查询模拟天气...")
        time.sleep(1) # 模拟网络延迟
        weather_conditions = {
            "北京": "晴朗，25°C，微风。",
            "上海": "多云转小雨，22°C，东南风3级。",
            "广州": "雷阵雨，28°C，注意防范。",
            "深圳": "晴间多云，29°C，空气质量良。",
            "伦敦": "持续小雨，15°C，请带好雨具。",
            "巴黎": "阳光明媚，23°C，适合户外活动。"
        }
        return weather_conditions.get(city, f"抱歉，暂时无法获取'{city}'的天气信息。也许可以问问Ernie？")

    def handle_task(self, task: Task) -> Task:
        query_text = ""
        if task.message and task.message.content and hasattr(task.message.content, 'text'):
            query_text = task.message.content.text
        
        city = query_text # 假设查询文本直接是城市名
        
        # **核心逻辑：获取天气**
        weather_data = self._get_simulated_weather(city)

        print(f"[WeatherAgent] 为 '{city}' 获取到的天气: {weather_data}")

        task.artifacts = [{
            "parts": [{"type": "text", "text": weather_data}]
        }]
        task.status = TaskStatus(state=TaskState.COMPLETED)
        return task

# Pydantic models for request/response
class TaskRequest(BaseModel):
    jsonrpc: Optional[str] = None
    method: Optional[str] = None
    params: Optional[Dict[str, Any]] = None
    id: Optional[str] = None

class MessageRequest(BaseModel):
    content: Optional[Dict[str, Any]] = None
    message_id: Optional[str] = None

# ---- FastAPI App Setup ----
def create_app(agent_instance: WeatherAgent) -> FastAPI:
    app = FastAPI(
        title="Weather Agent Server",
        description="A2A协议天气智能体服务",
        version="1.0.0"
    )

    @app.get('/agent.json')
    async def get_agent_card():
        return agent_instance.agent_card.to_dict()

    @app.get('/a2a/agent.json')
    async def get_a2a_agent_card():
        """A2A标准兼容端点"""
        return agent_instance.agent_card.to_dict()

    @app.post('/tasks/send')
    async def handle_send_task(request: Dict[str, Any]):
        try:
            # 兼容JSON-RPC风格的请求
            if "jsonrpc" in request and "method" in request and request["method"] == "tasks/send":
                task_dict = request.get("params", {})
            else:
                task_dict = request
            
            task = Task.from_dict(task_dict)
            updated_task = agent_instance.handle_task(task)
            response_data = updated_task.to_dict()

            if "jsonrpc" in request:
                return {"jsonrpc": "2.0", "id": request.get("id"), "result": response_data}
            return response_data
        except Exception as e:
            error_response = {"code": -32603, "message": str(e)}
            if "jsonrpc" in request:
                return JSONResponse(
                    status_code=500,
                    content={"jsonrpc": "2.0", "id": request.get("id"), "error": error_response}
                )
            raise HTTPException(status_code=500, detail=error_response)
            
    @app.post('/')
    async def handle_root_message(request: Dict[str, Any]):
        """兼容旧版Message端点"""
        try:
            # 尝试将整个请求体作为Task处理
            if isinstance(request, dict) and "id" in request and "status" in request:
                return await handle_send_task(request)

            # 否则，尝试作为Message处理
            text_content = request.get("content", {}).get("text", "") if isinstance(request.get("content"), dict) else str(request)
            message = Message(content=TextContent(text=text_content), role=MessageRole.USER)
            # 模拟任务转换
            task = Task(id=f"task-{time.time_ns()}", message=message, status=TaskStatus(state=TaskState.PENDING))
            updated_task = agent_instance.handle_task(task)
            # 从Task结果中提取文本给Message响应
            response_text = "处理完成。"
            if updated_task.artifacts and updated_task.artifacts[0].get("parts"):
                response_text = updated_task.artifacts[0]["parts"][0].get("text", response_text)
            
            response_message = Message(
                content=TextContent(text=response_text),
                role=MessageRole.AGENT,
                message_id=f"response-{time.time_ns()}",
                parent_message_id=request.get("message_id")
            )
            return response_message.to_dict()
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))
            
    return app

if __name__ == "__main__":
    PORT = 5001
    weather_agent = WeatherAgent(port=PORT)
    app = create_app(weather_agent)
    print(f"WeatherAgent 正在启动，监听端口: http://localhost:{PORT}")
    uvicorn.run(app, host='0.0.0.0', port=PORT)