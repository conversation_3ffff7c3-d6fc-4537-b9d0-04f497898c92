import os
import json
from langchain_openai import ChatOpenAI
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain.tools import tool
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from pydantic import SecretStr

# 从环境变量中获取 API Key
api_key = os.getenv("OPENAI_API_KEY", "sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2")
if not api_key:
    raise ValueError("请设置 OPENAI_API_KEY 环境变量")

# 初始化 DeepSeek 客户端
llm = ChatOpenAI(
    api_key=SecretStr(api_key),
    base_url="https://api.zhizengzeng.com/v1",
    model="gpt-4o-mini", 
)

# 定义工具函数
@tool
def get_weather(location: str) -> str:
    """Get weather of a location, the user should supply a location first"""
    # 实际场景可以接入天气 API，这里模拟一个返回
    weather_info = {
        "location": location,
        "temperature": "22°C",
        "condition": "Sunny"
    }
    return json.dumps(weather_info, ensure_ascii=False)

# 创建提示模板
prompt = ChatPromptTemplate.from_messages([
    ("system", "你是一个有用的助手，可以使用工具来回答问题。"),
    MessagesPlaceholder(variable_name="chat_history"),
    ("human", "{input}"),
    MessagesPlaceholder(variable_name="agent_scratchpad"),
])

# 创建工具列表
tools =[get_weather]

# 创建agent
agent = create_openai_tools_agent(llm, tools, prompt)

# 创建agent执行器
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)

# 发送消息并处理函数调用
def send_messages(user_input, chat_history=None):
    if chat_history is None:
        chat_history = []
    response = agent_executor.invoke({
        "input": user_input,
        "chat_history": chat_history
    })
    return response["output"]

# 示例对话
if __name__ == "__main__":
    chat_history = []
    print("Assistant: 你好，我可以帮你查询天气。输入 q 退出。")
    while True:
        user_question = input("You: ")
        if user_question.strip().lower() == 'q':
            print("Assistant: 再见！")
            break
        reply = send_messages(user_question, chat_history)
        print("Assistant:", reply)
        # 将本轮对话加入历史
        chat_history.append({"role": "user", "content": user_question})
        chat_history.append({"role": "assistant", "content": reply})
