import asyncio
from typing import Literal
from langgraph.types import interrupt
from langgraph.graph import StateGraph, MessagesState, START, END
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import ToolNode
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI

# 初始化LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
)

# 定义工具
@tool
def send_email(to: str, subject: str, body: str) -> str:
    """发送邮件工具"""
    return f"邮件已发送到 {to}，主题：{subject}"

@tool
def delete_file(file_path: str) -> str:
    """删除文件工具"""
    return f"文件 {file_path} 已删除"

@tool
def transfer_money(from_account: str, to_account: str, amount: float) -> str:
    """转账工具"""
    return f"已从 {from_account} 转账 {amount} 元到 {to_account}"

# 工具列表
tools = [send_email, delete_file, transfer_money]
tool_node = ToolNode(tools)

# 绑定工具到LLM
llm_with_tools = llm.bind_tools(tools)

def agent_node(state: MessagesState):
    """AI代理节点"""
    response = llm_with_tools.invoke(state["messages"])
    return {"messages": [response]}

def human_approval_node(state: MessagesState):
    """人工审批节点"""
    last_message = state["messages"][-1]
    
    # 检查是否有工具调用
    if not hasattr(last_message, 'tool_calls') or not last_message.tool_calls:
        return {"messages": []}
    
    tool_calls = last_message.tool_calls
    
    for tool_call in tool_calls:
        tool_name = tool_call["name"]
        tool_args = tool_call["args"]
        
        # 显示工具调用信息
        approval_message = f"""
🔧 工具调用请求：
- 工具名称: {tool_name}
- 参数: {tool_args}
- 是否批准执行？
"""
        
        # 请求人工审批
        approval = interrupt({
            "message": approval_message,
            "tool_name": tool_name,
            "tool_args": tool_args,
            "options": ["approve", "reject", "modify"]
        })
        
        if approval == "reject":
            return {
                "messages": [{
                    "role": "assistant",
                    "content": f"❌ 工具调用 {tool_name} 被拒绝"
                }]
            }
        elif approval == "modify":
            # 请求修改参数
            new_args = interrupt({
                "message": f"请修改 {tool_name} 的参数：",
                "current_args": tool_args
            })
            
            # 更新工具调用参数
            tool_call["args"] = new_args
    
    return {"approved_tool_calls": tool_calls}

def should_continue(state: MessagesState) -> Literal["tools", "human_approval", "end"]:
    """判断下一步操作"""
    last_message = state["messages"][-1]
    
    # 检查用户是否想结束对话
    if hasattr(last_message, 'content'):
        content = last_message.content.lower()
        if any(word in content for word in ["bye", "goodbye", "再见", "结束"]):
            return "end"
    
    # 检查是否有工具调用
    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        return "human_approval"
    
    return "end"

def tools_node(state: MessagesState):
    """执行工具节点"""
    # 使用审批后的工具调用
    approved_calls = state.get("approved_tool_calls", [])
    
    if not approved_calls:
        return {"messages": []}
    
    # 执行工具调用
    results = []
    for tool_call in approved_calls:
        tool_name = tool_call["name"]
        tool_args = tool_call["args"]
        
        # 找到对应的工具并执行
        for tool in tools:
            if tool.name == tool_name:
                try:
                    result = tool.invoke(tool_args)
                    results.append({
                        "role": "tool",
                        "content": result,
                        "tool_call_id": tool_call["id"]
                    })
                except Exception as e:
                    results.append({
                        "role": "tool", 
                        "content": f"工具执行失败: {e}",
                        "tool_call_id": tool_call["id"]
                    })
                break
    
    return {"messages": results}

# 构建工具审批图
builder = StateGraph(MessagesState)
builder.add_node("agent", agent_node)
builder.add_node("human_approval", human_approval_node)
builder.add_node("tools", tools_node)

# 设置边
builder.add_edge(START, "agent")
builder.add_conditional_edges(
    "agent",
    should_continue,
    {
        "human_approval": "human_approval",
        "end": END
    }
)
builder.add_edge("human_approval", "tools")
builder.add_edge("tools", "agent")

# 编译图
approval_graph = builder.compile(checkpointer=MemorySaver())

def run_tool_approval_demo():
    """运行工具审批演示"""
    print("🛠️ 工具调用审批演示")
    print("=" * 40)
    
    config = {"configurable": {"thread_id": "tool_approval_demo"}}
    
    # 测试场景
    test_scenarios = [
        "请帮我发送一封邮件给 <EMAIL>，主题是'会议通知'，内容是'明天下午2点开会'",
        "请删除文件 /tmp/old_data.txt",
        "请从我的账户 123456 转账 1000 元到账户 789012"
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n📝 场景 {i}: {scenario}")
        print("-" * 40)
        
        try:
            # 启动处理
            result = approval_graph.invoke(
                {"messages": [{"role": "user", "content": scenario}]},
                config
            )
            
            # 模拟人工审批流程
            while True:
                try:
                    # 检查是否需要审批
                    state = approval_graph.get_state(config)
                    if state.next:
                        print(f"⏸️ 等待审批...")
                        
                        # 模拟审批决策
                        if "转账" in scenario:
                            approval = "reject"  # 拒绝转账
                            print(f"🚫 审批决定: {approval}")
                        elif "删除" in scenario:
                            approval = "modify"  # 修改删除路径
                            print(f"✏️ 审批决定: {approval}")
                        else:
                            approval = "approve"  # 批准邮件
                            print(f"✅ 审批决定: {approval}")
                        
                        # 继续执行
                        result = approval_graph.invoke(
                            {"__resume__": approval},
                            config
                        )
                        
                        # 如果是修改，提供新参数
                        if approval == "modify":
                            new_args = {"file_path": "/tmp/safe_to_delete.txt"}
                            print(f"📝 修改参数: {new_args}")
                            result = approval_graph.invoke(
                                {"__resume__": new_args},
                                config
                            )
                    else:
                        break
                        
                except Exception as e:
                    print(f"❌ 处理出错: {e}")
                    break
            
            print(f"✅ 场景 {i} 处理完成")
            
        except Exception as e:
            print(f"❌ 场景 {i} 执行失败: {e}")

# 交互式工具审批
async def interactive_tool_approval():
    """交互式工具审批"""
    print("\n🎮 交互式工具审批")
    print("=" * 30)
    print("输入您的请求，系统会在执行工具前请求您的审批")
    print("输入 'quit' 退出")
    
    config = {"configurable": {"thread_id": "interactive_approval"}}
    
    while True:
        try:
            user_input = input("\n👤 您的请求: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                break
            
            if not user_input:
                continue
            
            print(f"🔄 处理请求: {user_input}")
            
            # 启动处理
            async for event in approval_graph.astream(
                {"messages": [{"role": "user", "content": user_input}]},
                config
            ):
                print(f"📊 事件: {event}")
                
                # 处理中断
                if "__interrupt__" in event:
                    interrupt_info = event["__interrupt__"][0]
                    
                    if isinstance(interrupt_info["value"], dict):
                        # 审批请求
                        info = interrupt_info["value"]
                        print(f"\n{info['message']}")
                        
                        if "options" in info:
                            print(f"选项: {info['options']}")
                        
                        approval = input("您的决定: ").strip()
                        
                        # 继续执行
                        await approval_graph.ainvoke(
                            {"__resume__": approval},
                            config
                        )
                    else:
                        # 简单输入请求
                        response = input(f"{interrupt_info['value']}")
                        await approval_graph.ainvoke(
                            {"__resume__": response},
                            config
                        )
        
        except KeyboardInterrupt:
            print("\n👋 用户中断")
            break
        except Exception as e:
            print(f"❌ 处理出错: {e}")
    
    print("👋 交互式审批结束")

if __name__ == "__main__":
    print("🎯 工具调用审批完整演示")
    print("=" * 50)
    
    # 运行演示
    run_tool_approval_demo()
    
    # 运行交互式审批
    try:
        asyncio.run(interactive_tool_approval())
    except KeyboardInterrupt:
        print("\n👋 程序结束")