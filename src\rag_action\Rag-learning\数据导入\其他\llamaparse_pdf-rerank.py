# =============================================================================
# LlamaParse + LlamaIndex PDF 问答系统（带重排序，结构化检索）
# 详细流程讲解与注释
# =============================================================================

# -------------------------------
# 1. 导入依赖与环境变量加载
# -------------------------------
from llama_index.llms.openai import OpenAI                # OpenAI LLM 接口
from llama_index.embeddings.openai import OpenAIEmbedding # OpenAI 嵌入模型
from llama_index.core import VectorStoreIndex, Settings   # 向量索引与全局设置
from dotenv import load_dotenv                            # 加载 .env 环境变量

load_dotenv()   # 加载本地 .env 文件中的环境变量

# -------------------------------
# 2. 初始化 LLM 与嵌入模型
# -------------------------------
# 配置 OpenAI 大语言模型（用于后续问答生成）
llm = OpenAI(
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",  # API 密钥
    api_base="https://api.zhizengzeng.com/v1",                      # API 基础地址
    model="gpt-4o-mini"                                             # 使用的模型名称
)

# 配置 OpenAI 嵌入模型（用于文本向量化）
embed_model = OpenAIEmbedding(
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    api_base="https://api.zhizengzeng.com/v1",
    model="text-embedding-3-small"
)

# 设置全局 LLM 和嵌入模型，后续索引和检索会自动调用
Settings.llm = llm
Settings.embed_model = embed_model

# -------------------------------
# 3. 解析 PDF 文档为结构化 Markdown
# -------------------------------
from llama_parse import LlamaParse

# 创建 LlamaParse 解析器，指定 API Key 和输出类型为 markdown
parser = LlamaParse(
    api_key="llx-adfMAwWUmfNsv3xVtbc2lEkjvrkbNdWfBZdso2az99XCaUSE",
    result_type="markdown"  # 解析结果为 markdown 结构
)

# 加载并解析 PDF 文件，返回 markdown 文档对象列表
documents = parser.load_data(
    "C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/uber_10q_march_2022.pdf"
)
# 注：此处会自动上传 PDF 到 LlamaParse 云端解析，解析完成后返回结构化文档

# -------------------------------
# 4. Markdown 结构化节点解析
# -------------------------------
from llama_index.core.node_parser import MarkdownElementNodeParser

# 创建 MarkdownElementNodeParser，用于将 markdown 文档解析为结构化节点
node_parser = MarkdownElementNodeParser(
    llm=llm,           # 用于理解 markdown 结构的 LLM
    num_workers=8      # 并发线程数，加快解析速度
)

# 将文档对象解析为节点（每个节点对应 markdown 的一个结构单元，如标题、表格、段落等）
nodes = node_parser.get_nodes_from_documents(documents)

# 可选：进一步区分文本节点和索引节点（如目录、标题等）
text_nodes, index_nodes = node_parser.get_nodes_and_objects(nodes)
# text_nodes[0]  # 可查看第一个文本节点内容
# index_nodes[0] # 可查看第一个索引节点内容

# -------------------------------
# 5. 构建向量索引
# -------------------------------
# 方式一：递归结构化索引（文本节点+索引节点，支持结构化检索）
recursive_index = VectorStoreIndex(nodes=text_nodes + index_nodes)

# 方式二：原始文档索引（直接用解析后的 markdown 文档，适合简单检索）
raw_index = VectorStoreIndex.from_documents(documents)

# -------------------------------
# 6. 加载重排序器（Reranker）
# -------------------------------
from llama_index.postprocessor.flag_embedding_reranker import (
    FlagEmbeddingReranker
)

# 初始化 FlagEmbeddingReranker，用于对检索结果进行重排序
reranker = FlagEmbeddingReranker(
    top_n=5,  # 只保留重排序后得分最高的5个片段
    model="BAAI/bge-reranker-large",  # 使用的重排序模型
    # 该模型为 BAAI（智源研究院）开源的 bge-reranker-large，属于大规模中文/英文双语重排序模型。
    # 特点：
    # 1. 基于大规模语料训练，支持中英文双语，适合多语言场景。
    # 2. 能够对检索得到的文本片段进行语义相关性重排序，提升答案准确性。
    # 3. 在多项公开检索与重排序评测中表现优异，尤其适合问答、RAG 等场景。
    # 4. 支持大批量文本对的高效重排序，适合大规模文档检索系统。
)

# -------------------------------
# 7. 创建查询引擎
# -------------------------------
# 递归结构化检索引擎（支持结构化内容的深度检索+重排序）
recursive_query_engine = recursive_index.as_query_engine(
    similarity_top_k=15,                # 检索最相关的15个节点
    node_postprocessors=[reranker],     # 检索后进行重排序
    verbose=True                        # 输出详细检索过程
)

# 原始文档检索引擎（直接基于 markdown 文档内容检索+重排序）
raw_query_engine = raw_index.as_query_engine(
    similarity_top_k=3,                 # 检索最相关的3个节点
    node_postprocessors=[reranker]      # 检索后进行重排序
)

# -------------------------------
# 8. 示例问答与结果输出
# -------------------------------
# 可尝试多种英文财报类问题，以下为示例（只会用最后一个 query 变量）
query = "What is the change of free cash flow and what is the rate from the financial and operational highlights?"
query = "how many COVID-19 response initiatives in year 2021?"
query = "After the year of COVID-19, how much EBITDA profit improved?"

# 方式一：原始文档检索
response_1 = raw_query_engine.query(query)
print("\n************New LlamaParse+ Basic Query Engine************")
print(response_1)

# 展示检索到的原始文本片段
print("\n************Retrieved Text Chunks************")
for i, source_node in enumerate(response_1.source_nodes):
    print(f"\nChunk {i+1}:")
    print("Text content:")
    print(source_node.text)
    print("-" * 50)

# 方式二：递归结构化检索
response_2 = recursive_query_engine.query(query)
print("\n************New LlamaParse+ Recursive Retriever Query Engine************")
print(response_2)

# 展示检索到的结构化文本片段
print("\n************Retrieved Text Chunks************")
for i, source_node in enumerate(response_2.source_nodes):
    print(f"\nChunk {i+1}:")
    print("Text content:")
    print(source_node.text)
    print("-" * 50)

# =============================================================================
# 总结说明：
# 1. 本流程实现了 PDF → markdown 结构化 → 节点分解 → 向量化 → 检索问答的全流程。
# 2. 支持两种检索方式：原始内容检索（简单）、结构化递归检索（更强大）。
# 3. 结构化检索可利用文档的层级结构（如目录、标题、表格等），提升问答准确性。
# 4. 支持检索结果的重排序（Rerank），进一步提升答案相关性。
# 5. 适合财报、学术等结构化较强的 PDF 问答场景。
# 6. 可根据实际需求调整检索 top_k、节点解析方式、重排序模型等参数。