# AI笔记系统

基于RAG（检索增强生成）技术的智能笔记管理系统，支持PDF文档上传解析、笔记生成与管理、语义搜索与问答、标签归类等功能。

## 🚀 项目简介

AI笔记系统是一个现代化的个人知识管理工具，它结合了先进的AI技术来帮助用户更好地组织、搜索和利用他们的笔记内容。

### 核心特性

- **📄 PDF文档解析**：自动提取PDF文档内容并转换为Markdown格式
- **🤖 智能问答**：基于RAG技术的自然语言问答系统
- **🔍 语义搜索**：使用向量嵌入技术进行精准的语义搜索
- **🏷️ 标签管理**：灵活的标签系统支持笔记分类和筛选
- **📊 向量存储**：使用Milvus向量数据库进行高效的相似度搜索
- **💾 数据持久化**：MySQL数据库存储笔记元信息和结构化数据

## 🏗️ 技术架构

### 技术栈

- **后端框架**：FastAPI
- **数据库**：MySQL + Milvus
- **向量嵌入**：OpenAI Embedding / BGE
- **文档解析**：PyMuPDF
- **ORM**：SQLAlchemy
- **配置管理**：Pydantic + YAML
- **依赖管理**：uv

### 架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue3)   │────│  FastAPI 后端   │────│   MySQL 数据库   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              │
                       ┌─────────────────┐    ┌─────────────────┐
                       │  向量嵌入服务    │────│  Milvus 向量库   │
                       └─────────────────┘    └─────────────────┘
                              │
                              │
                       ┌─────────────────┐
                       │   LLM 服务      │
                       └─────────────────┘
```

## 📦 安装部署

### 环境要求

- Python 3.11+
- MySQL 8.0+
- Milvus 2.3+
- uv (Python包管理器)

### 快速开始

1. **克隆项目**
```bash
git clone <repository-url>
cd rag-action
```

2. **安装依赖**
```bash
# 创建虚拟环境
uv venv

# 激活虚拟环境 (Windows)
.venv\Scripts\activate

# 激活虚拟环境 (Linux/Mac)
source .venv/bin/activate

# 安装依赖
uv sync
```

3. **配置系统**
```bash
# 复制配置文件模板
cp config.yaml.example config.yaml

# 编辑配置文件，填入你的数据库连接信息和API密钥
vim config.yaml
```

4. **初始化数据库**
```bash
python run.py --init-db
```

5. **启动服务**
```bash
python run.py
```

服务启动后，访问以下地址：
- API文档：http://localhost:8000/docs
- ReDoc文档：http://localhost:8000/redoc
- 健康检查：http://localhost:8000/health

## ⚙️ 配置说明

### 配置文件结构

```yaml
# 应用配置
app:
  host: "0.0.0.0"
  port: 8000
  debug: true

# MySQL数据库配置
mysql:
  host: "localhost"
  port: 3306
  user: "root"
  password: "your_password"
  database: "ai_notes"

# Milvus向量数据库配置
milvus:
  host: "localhost"
  port: 19530
  collection_name: "note_embeddings"
  embedding_dim: 1536

# 向量嵌入配置
embedding:
  model: "openai"  # 支持 openai、bge
  openai_api_key: "sk-xxxx"
  base_url: "https://api.openai.com/v1"
  model_name: "text-embedding-3-small"

# LLM配置
llm:
  provider: "openai"
  api_key: "sk-xxxx"
  model_name: "gpt-3.5-turbo"
```

### 环境变量

你也可以通过环境变量来覆盖配置：

```bash
export OPENAI_API_KEY="your-api-key"
export MYSQL_PASSWORD="your-password"
export MILVUS_HOST="your-milvus-host"
```

## 🔧 开发指南

### 项目结构

```
src/rag_action/
├── core/                 # 核心模块
│   ├── config.py        # 配置管理
│   ├── database.py      # 数据库连接
│   └── init_db.py       # 数据库初始化
├── models/              # 数据模型
│   ├── database.py      # 数据库模型
│   └── schemas.py       # API模型
├── service/             # 业务服务
│   ├── embedding_service.py  # 嵌入服务
│   ├── vector_service.py     # 向量服务
│   ├── rag_service.py        # RAG服务
│   └── note_service.py       # 笔记服务
├── router/              # 路由模块
│   └── rag.py          # API路由
└── main.py             # 主应用
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_api.py

# 运行测试并生成覆盖率报告
pytest --cov=src/rag_action
```

### 代码规范

项目使用以下工具确保代码质量：

- **类型检查**：mypy
- **代码格式化**：black
- **导入排序**：isort
- **代码检查**：flake8

```bash
# 格式化代码
black src/
isort src/

# 类型检查
mypy src/

# 代码检查
flake8 src/
```

## 📚 API文档

### 核心接口

#### 1. 上传PDF文档
```http
POST /api/upload/pdf
Content-Type: multipart/form-data

file: PDF文件
tags: 标签（可选，逗号分隔）
```

#### 2. 创建手动笔记
```http
POST /api/note
Content-Type: application/json

{
  "title": "笔记标题",
  "content": "笔记内容（Markdown格式）",
  "tags": ["标签1", "标签2"]
}
```

#### 3. 获取笔记列表
```http
GET /api/notes?page=1&page_size=10&tag=标签名
```

#### 4. 获取笔记详情
```http
GET /api/note/{note_id}
```

#### 5. 智能问答
```http
GET /api/qa?q=你的问题&top_k=5
```

#### 6. 获取标签列表
```http
GET /api/tags
```

### 响应格式

所有API响应都遵循统一的格式：

```json
{
  "success": true,
  "data": {...},
  "message": "操作成功"
}
```

错误响应：
```json
{
  "error": "ErrorType",
  "message": "错误描述",
  "detail": "详细信息"
}
```

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL/Milvus服务是否启动
   - 验证配置文件中的连接信息
   - 确认网络连接和防火墙设置

2. **向量嵌入失败**
   - 检查OpenAI API密钥是否正确
   - 验证网络连接和API访问权限
   - 考虑使用本地BGE模型作为备选

3. **PDF解析失败**
   - 确认PDF文件格式正确
   - 检查文件大小是否超过限制
   - 验证PyMuPDF依赖是否正确安装

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log
```

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交Issue：[GitHub Issues](https://github.com/your-repo/issues)
- 邮箱：<EMAIL>

---

⭐ 如果这个项目对你有帮助，请给它一个星标！