# =============================================================================
# LlamaIndex PDF 问答系统（小窗口-大窗口检索）
# 详细流程讲解与注释
# =============================================================================

# 导入所需的库和模块
from llama_index.llms.openai import OpenAI  # OpenAI LLM 接口
from llama_index.embeddings.openai import OpenAIEmbedding  # OpenAI 向量嵌入接口
from llama_index.core import VectorStoreIndex, Settings  # 向量索引与全局设置
from llama_index.readers.file import PDFReader  # PDF 文件读取器
from llama_index.core.node_parser import SentenceWindowNodeParser  # 句子窗口节点解析器
from llama_index.core.postprocessor import MetadataReplacementPostProcessor  # 元数据后处理器
from llama_index.core.node_parser import SentenceSplitter  # 句子分割器

from dotenv import load_dotenv  # 用于加载环境变量
load_dotenv()   # 加载 .env 文件中的环境变量

import os

# =============================================================================
# 第一步：配置 LLM 和嵌入模型
# =============================================================================

# 初始化 OpenAI 大语言模型（LLM），用于后续的问答生成
llm = OpenAI(
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",  # API 密钥
    api_base="https://api.zhizengzeng.com/v1",  # API 基础地址
    model="gpt-4o-mini"  # 使用的模型名称
)

# 初始化 OpenAI 嵌入模型，用于将文本转为向量
embed_model = OpenAIEmbedding(
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    api_base="https://api.zhizengzeng.com/v1",
    model="text-embedding-3-small"
)

# 设置全局参数，便于后续索引和检索时自动调用
Settings.llm = llm
Settings.embed_model = embed_model
# 设置默认的节点分割器，这里按句子分割并设置分块大小与重叠
Settings.node_parser = SentenceSplitter(chunk_size=72, chunk_overlap=20)

# =============================================================================
# 第二步：构建句子窗口节点解析器
# =============================================================================

# 句子窗口节点解析器：将文档分割为句子，并为每个句子生成一个包含上下文窗口的节点
# window_size=3 表示每个节点包含当前句子及其前后各1个句子（共3句）
# window_metadata_key="window" 用于存储窗口文本
# original_text_metadata_key="original_text" 用于存储原始句子
node_parser = SentenceWindowNodeParser.from_defaults(
    window_size=3,
    window_metadata_key="window",
    original_text_metadata_key="original_text",
)

# =============================================================================
# 第三步：加载 PDF 文档并解析为节点
# =============================================================================

# 创建 PDF 文件读取器
loader = PDFReader()
documents = loader.load_data(file="C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/uber_10q_march_2022.pdf")

# 使用句子窗口节点解析器，将文档对象解析为节点（每个节点为一个句子窗口）
nodes = node_parser.get_nodes_from_documents(documents)

# =============================================================================
# 第四步：构建向量索引
# =============================================================================

# 基于节点列表构建向量索引（VectorStoreIndex）
# 每个节点会被嵌入为向量，便于后续的相似性检索
index = VectorStoreIndex(nodes)

# =============================================================================
# 第五步：创建查询引擎（带元数据窗口替换）
# =============================================================================

# 创建查询引擎，设置检索时返回最相似的3个节点
# node_postprocessors 用于后处理检索到的节点，这里用 MetadataReplacementPostProcessor
# 将检索结果的内容替换为节点的“window”窗口文本，便于用户看到上下文
query_engine = index.as_query_engine(
    similarity_top_k=3,  # 检索最相关的3个节点
    # 这里的 node_postprocessors 参数用于在检索后对节点内容进行处理。
    # MetadataReplacementPostProcessor 会将检索到的节点内容替换为节点元数据中 "window" 键对应的文本，
    # 这样用户看到的就是包含上下文窗口的内容，而不是单独的句子本身。
    node_postprocessors=[
        MetadataReplacementPostProcessor(target_metadata_key="window")
    ],
    verbose=True  # 输出详细检索过程
)

# =============================================================================
# 第六步：示例查询与结果展示
# =============================================================================

# 可以尝试多种英文财报类问题，以下为示例（只会用最后一个 query 变量）
query = "What is the change of free cash flow and what is the rate from the financial and operational highlights?"
query = "how much COVID-19 response initiatives in millions in year 2021?" # 这个问题LangChain能答，LlamaIndex不一定能答
query = "What is the Adjusted EBITDA loss in year COVID-19?"
query = "how much is the Loss from operations for the period ended March 31, 2021?"
query = "how much is the Loss from operations for 2022?"

# 执行查询，得到 LlamaIndex 的响应对象
response = query_engine.query(query)

# 打印 LlamaIndex 的问答结果
print("\n************LlamaIndex Query Response************")
print(response)

# =============================================================================
# 第七步：展示检索到的原始文本片段及其上下文窗口
# =============================================================================

print("\n************Retrieved Text Chunks************")
for i, source_node in enumerate(response.source_nodes):
    print(f"\nChunk {i+1}:")
    print("Original sentence:")  # 显示检索到的原始句子
    print(source_node.node.metadata["original_text"])
    print("\nContext window:")   # 显示该句子的上下文窗口（前后句）
    print(source_node.node.metadata["window"])
    print("-" * 50)

# =============================================================================
# 总结：
# 1. 本流程实现了 PDF 文档的自动分句、窗口化、向量化、检索与问答。
# 2. 通过窗口机制，检索结果不仅包含命中句，还能看到上下文，提升答案准确性。
# 3. 适合财报、学术等结构化较强的英文 PDF 问答场景。
# 4. 可根据实际需求调整窗口大小、分块参数、检索数量等。