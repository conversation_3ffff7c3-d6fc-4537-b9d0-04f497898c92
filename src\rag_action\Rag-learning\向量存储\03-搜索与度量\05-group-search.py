from pymilvus import MilvusClient, DataType
import random

"""
本示例详细演示如何在 Milvus 中进行分组向量搜索（Group Search），并说明分组的实际作用。

【分组搜索的作用】
在实际的向量检索场景中，常常会遇到如下需求：我们希望在大规模的分片（chunk）或段落级别的向量库中，检索出与查询向量最相似的内容，但又不希望结果全部来自同一个文档（docId），而是希望每个文档只返回最相关的若干个片段。此时，分组搜索（group by）就非常有用。它可以按照某个字段（如文档ID）对搜索结果进行分组，每组返回最相似的若干条数据，保证结果的多样性和覆盖面。

【流程详解】
1. 连接 Milvus 服务，准备客户端。
2. 定义集合 schema，包括主键、向量字段、文档ID（docId）、内容分片（chunk）等。
3. 创建集合。
4. 生成并插入模拟数据：每条数据属于某个文档（docId），并有自己的向量和内容片段（chunk）。
5. 创建向量索引，加速后续检索。
6. 加载集合到内存，准备搜索。
7. 进行分组搜索：分别演示基本分组和配置组大小的分组搜索。
8. 清理资源。

下面是完整代码及注释：
"""

# 1. 设置 Milvus 客户端
client = MilvusClient(uri="http://localhost:19530")
COLLECTION_NAME = "group_search_demo"

# 2. 如果集合已存在，则删除，保证流程可重复
if client.has_collection(COLLECTION_NAME):
    client.drop_collection(COLLECTION_NAME)

# 3. 创建 schema，包含主键、向量、文档ID和内容分片
schema = MilvusClient.create_schema(auto_id=False, enable_dynamic_field=True)
schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True)
schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=128)
schema.add_field(field_name="docId", datatype=DataType.INT64)
schema.add_field(field_name="chunk", datatype=DataType.VARCHAR, max_length=100)

# 4. 创建集合
client.create_collection(collection_name=COLLECTION_NAME, schema=schema)

# 5. 生成并插入模拟数据
num_vectors = 1000
vectors = [[random.random() for _ in range(128)] for _ in range(num_vectors)]
ids = list(range(num_vectors))
doc_ids = [random.randint(1, 100) for _ in range(num_vectors)]  # 假设有100个文档
chunks = [f"chunk_{random.randint(1, 1000)}" for _ in range(num_vectors)]
entities = [{"id": ids[i], "vector": vectors[i], "docId": doc_ids[i], "chunk": chunks[i]} for i in range(num_vectors)]

client.insert(collection_name=COLLECTION_NAME, data=entities)
client.flush(collection_name=COLLECTION_NAME)

# 6. 创建向量索引
index_params = MilvusClient.prepare_index_params()
index_params.add_index(
    field_name="vector",
    metric_type="L2",
    index_type="FLAT",
    index_name="vector_index",
    params={}
)
client.create_index(
    collection_name=COLLECTION_NAME,
    index_params=index_params,
    sync=True
)

# 7. 加载集合到内存
client.load_collection(collection_name=COLLECTION_NAME)

# 8. 基本分组搜索示例
print("\n=== 基本分组搜索 ===")
query_vector = [random.random() for _ in range(128)]
# 按 docId 分组，每组只返回最相似的一个结果，最多返回5个不同文档的结果
results = client.search(
    collection_name=COLLECTION_NAME,
    data=[query_vector],
    anns_field="vector",
    limit=5,  # 返回5个不同的文档组
    group_by_field="docId",  # 按文档ID分组
    output_fields=["docId", "chunk"]
)

print("基本分组搜索结果（每个文档只返回最相似的一个片段）:")
for hits in results:
    for hit in hits:
        print(f"文档ID: {hit['entity']['docId']}, 块: {hit['entity']['chunk']}, 距离: {hit['distance']}")

# 9. 配置组大小的分组搜索示例
print("\n=== 配置组大小的分组搜索 ===")
# 按 docId 分组，每组返回2个最相似的片段，最多返回3个文档组
results = client.search(
    collection_name=COLLECTION_NAME,
    data=[query_vector],
    anns_field="vector",
    limit=3,  # 返回3个不同的文档组
    group_by_field="docId",
    group_size=2,  # 每个组返回2个最相似的结果
    strict_group_size=True,  # 严格确保每个组有2个结果
    output_fields=["docId", "chunk"]
)

print("配置组大小的分组搜索结果（每个文档返回2个最相似的片段）:")
for hits in results:
    print(f"\n文档组 {hits[0]['entity']['docId']} 的结果:")
    for hit in hits:
        print(f"块: {hit['entity']['chunk']}, 距离: {hit['distance']}")

# 10. 清理资源
client.release_collection(collection_name=COLLECTION_NAME)
