# AI笔记系统前端

基于Vue 3 + TypeScript + Element Plus构建的现代化AI笔记管理系统前端界面。

## 🚀 技术栈

- **框架**: Vue 3 + Composition API
- **构建工具**: Vite
- **语言**: TypeScript
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **样式**: SCSS
- **代码规范**: ESLint + Prettier

## 📁 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口
│   │   ├── modules/       # 分模块的API
│   │   └── request.ts     # 请求封装
│   ├── components/        # 通用组件
│   │   ├── Layout/        # 布局组件
│   │   └── Upload/        # 上传组件
│   ├── stores/            # Pinia状态管理
│   │   └── modules/       # 分模块的store
│   ├── styles/            # 样式文件
│   ├── types/             # TypeScript类型定义
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   │   ├── Dashboard/     # 仪表盘
│   │   ├── Notes/         # 笔记管理
│   │   ├── Upload/        # 文档上传
│   │   ├── Query/         # 智能问答
│   │   └── System/        # 系统监控
│   ├── router/            # 路由配置
│   ├── App.vue           # 根组件
│   └── main.ts           # 入口文件
├── package.json          # 项目配置
├── vite.config.ts        # Vite配置
├── tsconfig.json         # TypeScript配置
└── README.md            # 项目说明
```

## 🛠️ 开发环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

## 📦 安装依赖

```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

## 🚀 启动开发服务器

```bash
# 使用npm
npm run dev

# 或使用yarn
yarn dev
```

开发服务器将在 `http://localhost:3000` 启动，并自动代理API请求到后端服务器 `http://localhost:8000`。

## 🏗️ 构建生产版本

```bash
# 使用npm
npm run build

# 或使用yarn
yarn build
```

构建产物将输出到 `dist` 目录。

## 🔍 代码检查和格式化

```bash
# 代码检查
npm run lint

# 代码格式化
npm run format
```

## 📱 核心功能

### 1. 仪表盘 (Dashboard)
- 系统概览和统计信息
- 笔记数量、标签统计
- 来源分布图表
- 月度趋势分析
- 快速操作入口

### 2. 文档上传 (Upload)
- 拖拽上传支持
- 批量文件上传
- 实时进度显示
- 支持PDF、Markdown等格式
- 上传配置和历史记录

### 3. 笔记管理 (Notes)
- 笔记列表和搜索
- 网格/列表视图切换
- 标签筛选和分类
- 笔记详情查看
- 编辑和删除操作

### 4. 智能问答 (Query)
- 自然语言问答
- 基于笔记内容的智能回答
- 查询历史记录
- 相关来源展示
- 高级查询配置

### 5. 系统监控 (System)
- 系统健康状态
- 服务运行状态
- 资源使用监控
- 数据库连接状态
- API响应时间统计

## 🎨 主题和样式

系统支持亮色/暗色主题切换，并提供响应式设计：

- **亮色主题**: 默认主题，适合日间使用
- **暗色主题**: 护眼主题，适合夜间使用
- **自动切换**: 跟随系统主题设置

### 响应式断点
- **移动端**: < 768px
- **平板**: 768px - 1024px
- **桌面**: > 1024px

## 🔧 配置说明

### Vite配置 (vite.config.ts)
- 自动导入Vue和Element Plus组件
- 路径别名配置
- 开发服务器代理设置
- 构建优化配置

### TypeScript配置 (tsconfig.json)
- 严格类型检查
- 路径映射
- 现代ES特性支持

### ESLint配置 (.eslintrc.cjs)
- Vue 3推荐规则
- TypeScript支持
- Prettier集成

## 🌐 API集成

前端通过Axios与后端FastAPI服务进行通信：

- **基础URL**: `/api` (开发环境代理到 `http://localhost:8000`)
- **请求拦截**: 自动添加认证头、显示加载状态
- **响应拦截**: 统一错误处理、消息提示
- **超时设置**: 60秒请求超时

### API模块
- `notesApi`: 笔记相关接口
- `queryApi`: 查询相关接口
- `systemApi`: 系统相关接口

## 📊 状态管理

使用Pinia进行状态管理，按功能模块划分：

- `appStore`: 应用全局状态（主题、侧边栏等）
- `notesStore`: 笔记数据管理
- `queryStore`: 查询状态管理
- `uploadStore`: 上传状态管理
- `systemStore`: 系统监控数据

## 🔒 类型安全

项目使用TypeScript提供完整的类型支持：

- API响应类型定义
- 组件Props类型
- Store状态类型
- 工具函数类型

## 🚀 性能优化

- **代码分割**: 路由级别的懒加载
- **组件缓存**: keep-alive缓存页面组件
- **资源优化**: 图片懒加载、文件压缩
- **请求优化**: 防抖搜索、请求缓存

## 🧪 开发建议

1. **组件开发**: 遵循Vue 3 Composition API最佳实践
2. **样式规范**: 使用SCSS变量和混入，保持样式一致性
3. **类型定义**: 为所有API和组件定义准确的TypeScript类型
4. **错误处理**: 合理处理异步操作的错误情况
5. **用户体验**: 提供加载状态、错误提示和空状态处理

## 🐛 常见问题

### 1. 开发服务器启动失败
- 检查Node.js版本是否 >= 18.0.0
- 清除node_modules并重新安装依赖
- 检查端口3000是否被占用

### 2. API请求失败
- 确认后端服务器在8000端口正常运行
- 检查网络连接和防火墙设置
- 查看浏览器控制台的错误信息

### 3. 构建失败
- 检查TypeScript类型错误
- 确认所有依赖都已正确安装
- 查看构建日志中的具体错误信息

## 📄 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

1. Fork本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request
