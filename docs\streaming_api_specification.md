# 流式问答API规范

## 概述

本文档定义了智能问答系统流式输出的数据格式规范，确保前后端数据传输的一致性和完整性。

## 流式输出数据格式

### 基础数据结构

所有流式输出的数据块都遵循以下基础结构：

```json
{
  "type": "string",           // 数据块类型
  "content": "string",        // 内容数据
  "finished": boolean,        // 是否为最后一个数据块
  "processing_time": float,   // 处理时间（可选，仅在finished=true时提供）
  "metadata": object          // 元数据（可选）
}
```

### 数据块类型定义

#### 1. 检索阶段

**检索开始**
```json
{
  "type": "retrieval_start",
  "content": "正在搜索相关知识...",
  "finished": false
}
```

**检索完成**
```json
{
  "type": "retrieval_complete",
  "content": "找到 3 个相关文档",
  "finished": false,
  "sources_count": 3
}
```

#### 2. 生成阶段

**生成开始**
```json
{
  "type": "generation_start",
  "content": "正在生成回答...",
  "finished": false
}
```

**答案块**
```json
{
  "type": "answer_chunk",
  "content": "这是答案的一部分",
  "finished": false
}
```

**答案完成**
```json
{
  "type": "answer_complete",
  "content": "完整的答案内容",
  "finished": true,
  "processing_time": 5.234,
  "sources": [...],
  "metadata": {
    "intent": "knowledge_search",
    "routing_confidence": 0.85,
    "sources_count": 3,
    "token_count": 150
  }
}
```

#### 3. 数据库查询阶段

**数据库查询开始**
```json
{
  "type": "database_query_start",
  "content": "正在分析数据库查询...",
  "finished": false
}
```

**数据库查询完成**
```json
{
  "type": "database_query_complete",
  "content": "查询完成，找到 5 条记录",
  "finished": false
}
```

#### 4. 错误处理

**错误信息**
```json
{
  "type": "error",
  "content": "处理过程中出现错误: 具体错误信息",
  "finished": true,
  "processing_time": 2.156,
  "metadata": {
    "error": "具体错误信息",
    "intent": "knowledge_search"
  }
}
```

### 问答意图类型

系统支持以下问答意图类型：

1. **knowledge_search** - 知识库检索
2. **simple_conversation** - 简单对话
3. **context_based_answer** - 基于上下文回答
4. **database_query** - 数据库查询
5. **hybrid_query** - 混合查询

### 元数据字段说明

#### 通用元数据字段

- `intent`: 问答意图类型
- `routing_confidence`: 路由置信度 (0-1)
- `token_count`: 生成的token数量
- `processing_time`: 总处理时间（秒）

#### 知识检索特定字段

- `sources_count`: 检索到的文档数量
- `sources`: 来源文档列表

#### 数据库查询特定字段

- `sql`: 执行的SQL语句
- `row_count`: 查询结果行数
- `execution_time`: SQL执行时间
- `retry_count`: 重试次数

## 流式传输协议

### HTTP流式响应

使用HTTP的分块传输编码（Transfer-Encoding: chunked）：

```http
HTTP/1.1 200 OK
Content-Type: application/json; charset=utf-8
Transfer-Encoding: chunked
Cache-Control: no-cache
Connection: keep-alive

data: {"type":"retrieval_start","content":"正在搜索相关知识...","finished":false}

data: {"type":"retrieval_complete","content":"找到 3 个相关文档","finished":false,"sources_count":3}

data: {"type":"generation_start","content":"正在生成回答...","finished":false}

data: {"type":"answer_chunk","content":"根据检索到的文档","finished":false}

data: {"type":"answer_complete","content":"完整答案","finished":true,"processing_time":5.234}
```

### Server-Sent Events (SSE)

使用SSE协议进行流式传输：

```http
HTTP/1.1 200 OK
Content-Type: text/event-stream; charset=utf-8
Cache-Control: no-cache
Connection: keep-alive

data: {"type":"retrieval_start","content":"正在搜索相关知识...","finished":false}

data: {"type":"answer_chunk","content":"答案片段","finished":false}

data: {"type":"answer_complete","content":"完整答案","finished":true}

event: close
data: 
```

## 错误处理规范

### 错误类型

1. **系统错误** - 服务器内部错误
2. **网络错误** - 网络连接问题
3. **超时错误** - 请求处理超时
4. **验证错误** - 输入参数验证失败

### 错误响应格式

```json
{
  "type": "error",
  "content": "用户友好的错误信息",
  "finished": true,
  "processing_time": 1.234,
  "metadata": {
    "error": "详细的错误信息",
    "error_code": "SYSTEM_ERROR",
    "intent": "knowledge_search"
  }
}
```

## 中文字符处理

### UTF-8编码保证

- 所有文本内容使用UTF-8编码
- 确保流式传输不会截断中文字符
- 在发送前验证UTF-8序列的完整性

### 字符完整性检查

```python
def _is_complete_utf8_sequence(self, text: str) -> bool:
    """检查文本是否为完整的UTF-8序列"""
    try:
        text.encode('utf-8').decode('utf-8')
        return True
    except UnicodeDecodeError:
        return False
```

## 性能考虑

### 缓冲策略

- 答案块大小建议在10-100字符之间
- 避免过于频繁的小块传输
- 在网络条件较差时适当增加缓冲

### 超时设置

- 单个数据块传输超时：30秒
- 整个问答流程超时：5分钟
- 网络重连超时：10秒

## 兼容性说明

### 向后兼容

- 保持传统的一次性响应接口
- 新增流式接口不影响现有功能
- 客户端可以选择使用流式或非流式接口

### 浏览器支持

- 支持现代浏览器的Fetch API
- 兼容Server-Sent Events
- 提供WebSocket备选方案
