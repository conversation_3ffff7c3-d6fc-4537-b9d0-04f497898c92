<template>
  <div class="settings-page">
    <div class="page-header">
      <h1 class="page-title">系统设置</h1>
      <p class="page-subtitle">配置系统参数和用户偏好</p>
    </div>

    <div class="settings-content">
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane label="基本设置" name="basic">
          <div class="settings-section">
            <h3>外观设置</h3>
            <el-form label-width="120px">
              <el-form-item label="主题模式">
                <el-radio-group v-model="settings.theme" @change="updateTheme">
                  <el-radio label="light">浅色主题</el-radio>
                  <el-radio label="dark">深色主题</el-radio>
                  <el-radio label="auto">跟随系统</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="语言">
                <el-select v-model="settings.language" @change="updateSettings">
                  <el-option label="简体中文" value="zh-CN" />
                  <el-option label="English" value="en-US" />
                </el-select>
              </el-form-item>
            </el-form>
          </div>

          <div class="settings-section">
            <h3>功能设置</h3>
            <el-form label-width="120px">
              <el-form-item label="分页大小">
                <el-input-number
                  v-model="settings.pageSize"
                  :min="10"
                  :max="100"
                  :step="10"
                  @change="updateSettings"
                />
              </el-form-item>
              
              <el-form-item label="自动保存">
                <el-switch
                  v-model="settings.autoSave"
                  @change="updateSettings"
                />
              </el-form-item>
              
              <el-form-item label="通知提醒">
                <el-switch
                  v-model="settings.notifications"
                  @change="updateSettings"
                />
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane label="关于系统" name="about">
          <div class="about-section">
            <div class="system-info">
              <h3>AI笔记系统</h3>
              <p class="version">版本: 1.0.0</p>
              <p class="description">
                基于RAG技术的智能笔记管理系统，支持文档解析、向量检索和智能问答。
              </p>
            </div>

            <div class="tech-stack">
              <h4>技术栈</h4>
              <ul>
                <li>前端: Vue 3 + TypeScript + Element Plus</li>
                <li>后端: FastAPI + Python</li>
                <li>数据库: MySQL + Milvus</li>
                <li>AI模型: OpenAI GPT + Embedding</li>
              </ul>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useAppStore } from '@/stores'

const appStore = useAppStore()
const activeTab = ref('basic')

const settings = reactive({
  theme: 'light',
  language: 'zh-CN',
  pageSize: 20,
  autoSave: true,
  notifications: true
})

const updateTheme = (theme: string) => {
  appStore.setTheme(theme as 'light' | 'dark' | 'auto')
}

const updateSettings = () => {
  appStore.updateSettings(settings)
  ElMessage.success('设置已保存')
}

onMounted(() => {
  // 加载当前设置
  Object.assign(settings, appStore.settings)
  settings.theme = appStore.theme
})
</script>

<style lang="scss" scoped>
.settings-page {
  padding: var(--spacing-lg);
  max-width: 1000px;
  margin: 0 auto;
  
  .page-header {
    margin-bottom: var(--spacing-xl);
    
    .page-title {
      font-size: 28px;
      font-weight: var(--font-weight-bold);
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
    }
    
    .page-subtitle {
      color: var(--text-secondary);
      font-size: var(--font-size-lg);
    }
  }
  
  .settings-content {
    .settings-section {
      margin-bottom: var(--spacing-xl);
      
      h3 {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-medium);
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--border-lighter);
      }
    }
    
    .about-section {
      .system-info {
        margin-bottom: var(--spacing-xl);
        
        h3 {
          font-size: 24px;
          font-weight: var(--font-weight-bold);
          color: var(--primary-color);
          margin-bottom: var(--spacing-md);
        }
        
        .version {
          font-size: var(--font-size-lg);
          color: var(--text-secondary);
          margin-bottom: var(--spacing-md);
        }
        
        .description {
          line-height: 1.6;
          color: var(--text-regular);
        }
      }
      
      .tech-stack {
        h4 {
          font-size: var(--font-size-lg);
          font-weight: var(--font-weight-medium);
          color: var(--text-primary);
          margin-bottom: var(--spacing-md);
        }
        
        ul {
          list-style: none;
          padding: 0;
          
          li {
            padding: var(--spacing-sm) 0;
            color: var(--text-regular);
            position: relative;
            padding-left: var(--spacing-md);
            
            &::before {
              content: '•';
              color: var(--primary-color);
              position: absolute;
              left: 0;
            }
          }
        }
      }
    }
  }
}

@include respond-below(md) {
  .settings-page {
    padding: var(--spacing-md);
  }
}
</style>
