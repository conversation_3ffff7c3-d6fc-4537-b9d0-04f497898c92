from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, MessagesState, START
from langgraph.checkpoint.memory import MemorySaver

memory = MemorySaver()

llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini", temperature=0)


def call_model(state: MessagesState):
    response = llm.invoke(state["messages"])
    return {"messages": response}


builder = StateGraph(MessagesState)
builder.add_node("call_model", call_model)
builder.add_edge(START, "call_model")
graph = builder.compile(checkpointer=memory)

# 配置参数，指定thread_id
config = {"configurable": {"thread_id": "conversation-1"}}

print("=== 第一轮对话 ===")
input_messages = {"messages": [{"role": "user", "content": "你好，我叫派大星"}]}
for chunk in graph.stream(input_messages, config):
    # 🔧 修复：正确访问chunk结构
    print(f"Chunk结构: {list(chunk.keys())}")  # 调试信息
    if "call_model" in chunk:
        chunk["call_model"]["messages"].pretty_print()

print("\n=== 第二轮对话 ===")
input_messages = {"messages": [{"role": "user", "content": "我叫什么？"}]}
for chunk in graph.stream(input_messages, config):
    if "call_model" in chunk:
        chunk["call_model"]["messages"].pretty_print()

print("\n=== 测试不同线程 ===")
config_2 = {"configurable": {"thread_id": "conversation-2"}}
input_messages = {"messages": [{"role": "user", "content": "我叫什么？"}]}
for chunk in graph.stream(input_messages, config_2):
    if "call_model" in chunk:
        chunk["call_model"]["messages"].pretty_print()
