<template>
  <div class="streaming-test-page">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <h2>🌊 流式问答功能测试</h2>
          <el-tag :type="isConnected ? 'success' : 'danger'">
            {{ isConnected ? '后端已连接' : '后端未连接' }}
          </el-tag>
        </div>
      </template>

      <!-- 测试控制区域 -->
      <div class="test-controls">
        <el-form :model="testForm" label-width="120px">
          <el-form-item label="测试问题">
            <el-input
              v-model="testForm.question"
              type="textarea"
              :rows="3"
              placeholder="输入您的问题，例如：什么是数据导入？"
              :disabled="isStreaming"
            />
          </el-form-item>
          
          <el-form-item label="接口类型">
            <el-radio-group v-model="testForm.apiType" :disabled="isStreaming">
              <el-radio value="get">GET 流式接口</el-radio>
              <el-radio value="post">POST 流式接口</el-radio>
              <el-radio value="traditional">传统接口</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item>
            <el-button 
              type="primary" 
              @click="startTest"
              :loading="isStreaming"
              :disabled="!testForm.question.trim() || !isConnected"
            >
              {{ isStreaming ? '正在测试...' : '开始测试' }}
            </el-button>
            
            <el-button 
              @click="stopTest"
              :disabled="!isStreaming"
            >
              停止测试
            </el-button>
            
            <el-button @click="clearResults">
              清空结果
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 进度指示器 -->
      <div v-if="currentStage" class="progress-indicator">
        <el-alert
          :title="currentStage"
          :description="stageMessage"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 流式输出显示区域 -->
      <div v-if="streamingAnswer || isStreaming" class="streaming-output">
        <h3>🤖 AI 回答</h3>
        <div class="answer-container">
          <div class="answer-text">
            {{ streamingAnswer }}
            <span v-if="isStreaming" class="cursor">▋</span>
          </div>
        </div>
      </div>

      <!-- 完整响应显示 -->
      <div v-if="finalResponse" class="final-response">
        <h3>📊 响应详情</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="处理时间">
            {{ finalResponse.processing_time?.toFixed(2) }}ms
          </el-descriptions-item>
          <el-descriptions-item label="答案长度">
            {{ finalResponse.answer?.length || 0 }} 字符
          </el-descriptions-item>
          <el-descriptions-item label="来源文档">
            {{ finalResponse.sources?.length || 0 }} 个
          </el-descriptions-item>
          <el-descriptions-item label="Token数量">
            {{ finalResponse.metadata?.token_count || 'N/A' }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 来源文档 -->
        <div v-if="finalResponse.sources?.length" class="sources-section">
          <h4>📚 参考来源</h4>
          <el-collapse>
            <el-collapse-item 
              v-for="(source, index) in finalResponse.sources" 
              :key="index"
              :title="`文档 ${index + 1}: ${source.note_title}`"
            >
              <div class="source-content">
                <p><strong>相似度:</strong> {{ (source.similarity_score * 100).toFixed(1) }}%</p>
                <p><strong>内容:</strong></p>
                <div class="source-text">{{ source.content }}</div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>

      <!-- 性能统计 -->
      <div v-if="performanceStats" class="performance-stats">
        <h3>⚡ 性能统计</h3>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="首块响应时间" :value="performanceStats.firstChunkTime" suffix="ms" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="总响应时间" :value="performanceStats.totalTime" suffix="ms" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="数据块数量" :value="performanceStats.chunkCount" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="平均速度" :value="performanceStats.avgSpeed" suffix="字符/秒" />
          </el-col>
        </el-row>
      </div>

      <!-- 错误显示 -->
      <div v-if="errorMessage" class="error-section">
        <el-alert
          title="测试失败"
          :description="errorMessage"
          type="error"
          show-icon
          closable
          @close="errorMessage = ''"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const isConnected = ref(false)
const isStreaming = ref(false)
const currentStage = ref('')
const stageMessage = ref('')
const streamingAnswer = ref('')
const finalResponse = ref<any>(null)
const performanceStats = ref<any>(null)
const errorMessage = ref('')

const testForm = ref({
  question: '什么是数据导入？',
  apiType: 'get'
})

// 性能监控
let startTime = 0
let firstChunkTime = 0
let chunkCount = 0

// 检查后端连接状态
const checkBackendConnection = async () => {
  try {
    const response = await fetch('http://localhost:8000/health')
    isConnected.value = response.ok
  } catch (error) {
    isConnected.value = false
  }
}

// 开始测试
const startTest = async () => {
  if (!testForm.value.question.trim()) {
    ElMessage.warning('请输入测试问题')
    return
  }

  // 重置状态
  isStreaming.value = true
  currentStage.value = ''
  stageMessage.value = ''
  streamingAnswer.value = ''
  finalResponse.value = null
  performanceStats.value = null
  errorMessage.value = ''
  
  // 重置性能统计
  startTime = Date.now()
  firstChunkTime = 0
  chunkCount = 0

  try {
    if (testForm.value.apiType === 'traditional') {
      await testTraditionalAPI()
    } else {
      await testStreamingAPI()
    }
  } catch (error: any) {
    errorMessage.value = error.message || '测试失败'
    isStreaming.value = false
  }
}

// 测试传统API
const testTraditionalAPI = async () => {
  try {
    const response = await fetch(`http://localhost:8000/api/query/intelligent?query=${encodeURIComponent(testForm.value.question)}`, {
      method: 'POST'
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()
    
    // 模拟打字机效果显示结果
    const answer = result.answer || ''
    let index = 0
    const typeInterval = setInterval(() => {
      if (index < answer.length) {
        streamingAnswer.value += answer[index]
        index++
      } else {
        clearInterval(typeInterval)
        finalResponse.value = result
        isStreaming.value = false
        
        // 计算性能统计
        const totalTime = Date.now() - startTime
        performanceStats.value = {
          firstChunkTime: 0,
          totalTime,
          chunkCount: 1,
          avgSpeed: (answer.length / (totalTime / 1000)).toFixed(1)
        }
      }
    }, 50)

  } catch (error: any) {
    throw error
  }
}

// 测试流式API
const testStreamingAPI = async () => {
  const url = testForm.value.apiType === 'get' 
    ? `http://localhost:8000/api/query/intelligent/stream?query=${encodeURIComponent(testForm.value.question)}`
    : 'http://localhost:8000/api/query/intelligent/stream'

  const options: RequestInit = {
    method: testForm.value.apiType === 'get' ? 'GET' : 'POST',
    headers: {
      'Accept': 'text/event-stream',
      ...(testForm.value.apiType === 'post' && {
        'Content-Type': 'application/json'
      })
    },
    ...(testForm.value.apiType === 'post' && {
      body: JSON.stringify({
        message: testForm.value.question,
        top_k: 5,
        stream: true
      })
    })
  }

  const response = await fetch(url, options)
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
  }

  const reader = response.body?.getReader()
  if (!reader) {
    throw new Error('无法获取响应流')
  }

  const decoder = new TextDecoder()

  try {
    while (true) {
      const { done, value } = await reader.read()
      if (done) break

      const chunk = decoder.decode(value, { stream: true })
      const lines = chunk.split('\n')

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6))
            handleStreamChunk(data)
          } catch (parseError) {
            console.warn('解析SSE数据失败:', parseError)
          }
        }
      }
    }
  } finally {
    reader.releaseLock()
  }
}

// 处理流式数据块
const handleStreamChunk = (data: any) => {
  const { type, content, finished } = data

  // 记录首块时间
  if (firstChunkTime === 0) {
    firstChunkTime = Date.now() - startTime
  }

  chunkCount++

  switch (type) {
    case 'retrieval_start':
      currentStage.value = '🔍 检索阶段'
      stageMessage.value = content
      break
    
    case 'retrieval_complete':
      currentStage.value = '✅ 检索完成'
      stageMessage.value = content
      break
    
    case 'generation_start':
      currentStage.value = '🤖 生成阶段'
      stageMessage.value = content
      break
    
    case 'answer_chunk':
      streamingAnswer.value += content
      break
    
    case 'answer_complete':
      finalResponse.value = {
        answer: content,
        sources: data.sources || [],
        processing_time: data.processing_time || 0,
        metadata: data.metadata || {}
      }
      
      // 计算性能统计
      const totalTime = Date.now() - startTime
      performanceStats.value = {
        firstChunkTime,
        totalTime,
        chunkCount,
        avgSpeed: (streamingAnswer.value.length / (totalTime / 1000)).toFixed(1)
      }
      
      currentStage.value = ''
      stageMessage.value = ''
      isStreaming.value = false
      break
    
    case 'error':
      throw new Error(content)
  }
}

// 停止测试
const stopTest = () => {
  isStreaming.value = false
  currentStage.value = ''
  stageMessage.value = ''
}

// 清空结果
const clearResults = () => {
  streamingAnswer.value = ''
  finalResponse.value = null
  performanceStats.value = null
  errorMessage.value = ''
  currentStage.value = ''
  stageMessage.value = ''
}

// 生命周期
onMounted(() => {
  checkBackendConnection()
  // 定期检查连接状态
  const interval = setInterval(checkBackendConnection, 10000)
  onUnmounted(() => clearInterval(interval))
})
</script>

<style scoped>
.streaming-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-controls {
  margin-bottom: 20px;
}

.progress-indicator {
  margin-bottom: 20px;
}

.streaming-output {
  margin-bottom: 20px;
}

.answer-container {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 16px;
  min-height: 100px;
  max-height: 400px;
  overflow-y: auto;
}

.answer-text {
  font-family: 'Consolas', 'Monaco', monospace;
  line-height: 1.6;
  white-space: pre-wrap;
}

.cursor {
  animation: blink 1s infinite;
  color: #409eff;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.final-response {
  margin-bottom: 20px;
}

.sources-section {
  margin-top: 20px;
}

.source-content {
  padding: 10px;
}

.source-text {
  background: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
}

.performance-stats {
  margin-bottom: 20px;
}

.error-section {
  margin-top: 20px;
}
</style>
