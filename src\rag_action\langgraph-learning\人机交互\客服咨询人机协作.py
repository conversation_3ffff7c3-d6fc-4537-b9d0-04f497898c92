"""
LangGraph 人机交互完整示例：智能客服咨询场景
展示AI客服在关键决策点暂停，等待人工客服介入的真实应用场景
"""

import asyncio
from typing import Dict, List, Any, TypedDict, Literal
from langgraph.types import interrupt
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage

# 初始化LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0.7
)

# 定义客服状态
class CustomerServiceState(TypedDict):
    messages: List[Dict[str, str]]  # 对话历史
    customer_info: Dict[str, Any]   # 客户信息
    issue_type: str                 # 问题类型
    urgency_level: str             # 紧急程度
    requires_human: bool           # 是否需要人工介入
    resolution_status: str         # 解决状态
    conversation_log: List[str]    # 对话日志

def analyze_customer_issue(state: CustomerServiceState) -> CustomerServiceState:
    """AI分析客户问题 - 优化版：更智能的判断逻辑"""
    print("\n🤖 [AI客服] 正在智能分析您的问题...")
    
    # 获取最新的客户消息
    if not state["messages"]:
        return state
    
    last_message = state["messages"][-1]["content"]
    
    # 优化的AI分析提示词 - 更精确的判断标准
    analysis_prompt = f"""
作为智能客服系统，请仔细分析以下客户问题并做出准确判断：

客户问题：{last_message}

请根据以下标准进行分析：

【问题类型判断】：
- 技术支持：软件使用、功能咨询、操作指导、常见故障
- 账户问题：登录异常、密码重置、账户被盗、权限问题  
- 退款申请：产品退款、服务退费、订单取消
- 投诉建议：服务不满、产品质量、流程问题
- 其他：一般咨询、信息查询

【紧急程度判断】：
- 低：一般咨询、使用指导
- 中：功能问题、轻微故障
- 高：影响使用、数据问题
- 紧急：安全威胁、资金风险、严重故障

【人工介入判断标准】（只有以下情况才需要人工）：
需要人工：
- 账户安全问题（被盗、异常登录、资金安全）
- 退款申请（涉及资金处理）
- 严重投诉（服务质量、产品缺陷）
- 复杂技术问题（系统故障、数据丢失）
- 法律纠纷或争议

不需要人工（AI可独立处理）：
- 常见技术问题（使用方法、功能介绍）
- 一般咨询（产品信息、操作指导）
- 轻微问题（界面问题、小bug）
- 建议反馈（功能建议、体验反馈）

请严格按照以下格式回答：
问题类型：[具体类型]
紧急程度：[低/中/高/紧急]
需要人工：[是/否]
判断理由：[简要说明为什么需要或不需要人工介入]
"""
    
    response = llm.invoke([HumanMessage(content=analysis_prompt)])
    analysis_result = response.content
    
    print(f"📊 [AI分析结果]\n{analysis_result}")
    
    # 优化的解析逻辑 - 更准确的判断
    issue_type = "其他"
    urgency_level = "中"
    requires_human = False
    
    # 解析问题类型
    if "技术支持" in analysis_result:
        issue_type = "技术支持"
        # 技术支持默认AI处理，除非明确标注为复杂问题
        requires_human = "复杂技术问题" in analysis_result or "系统故障" in analysis_result or "数据丢失" in analysis_result
    elif "账户问题" in analysis_result:
        issue_type = "账户问题"
        # 账户问题根据具体情况判断
        if any(keyword in analysis_result for keyword in ["被盗", "安全", "异常登录", "资金"]):
            requires_human = True
        else:
            requires_human = False  # 简单的账户问题AI可以处理
    elif "退款" in analysis_result:
        issue_type = "退款申请"
        requires_human = True  # 退款涉及资金，需要人工处理
    elif "投诉" in analysis_result:
        issue_type = "投诉建议"
        # 区分严重投诉和一般建议
        if any(keyword in analysis_result for keyword in ["严重", "质量", "服务不满", "纠纷"]):
            requires_human = True
            urgency_level = "高"
        else:
            requires_human = False  # 一般建议AI可以处理
    
    # 解析紧急程度
    if "紧急" in analysis_result:
        urgency_level = "紧急"
        requires_human = True  # 紧急问题都需要人工
    elif "高" in analysis_result:
        urgency_level = "高"
    elif "低" in analysis_result:
        urgency_level = "低"
    
    # 解析人工介入判断
    if "需要人工：是" in analysis_result:
        requires_human = True
    elif "需要人工：否" in analysis_result:
        requires_human = False
    
    # 记录判断结果
    judgment_log = f"AI智能判断：{issue_type}（{urgency_level}优先级）- {'需要人工介入' if requires_human else 'AI独立处理'}"
    
    return {
        **state,
        "issue_type": issue_type,
        "urgency_level": urgency_level,
        "requires_human": requires_human,
        "conversation_log": state["conversation_log"] + [judgment_log]
    }

def ai_initial_response(state: CustomerServiceState) -> CustomerServiceState:
    """AI提供初步回复 - 优化版：更详细和有针对性的回复"""
    print(f"\n🤖 [AI客服] 处理{state['issue_type']}问题...")
    
    # 根据问题类型和是否需要人工介入提供不同的回复
    if state["issue_type"] == "技术支持":
        if state["requires_human"]:
            ai_response = """
感谢您联系技术支持！我了解到您遇到了复杂的技术问题。

这类问题需要我们的技术专家来详细分析和解决。
我正在为您联系专业的技术工程师，他们会为您提供深入的技术支持。
请稍等片刻...
"""
        else:
            ai_response = """
感谢您联系我们的技术支持！我是AI客服助手，很高兴为您解答技术问题。

针对您的问题，我为您提供以下解决方案：

🔧 常见解决步骤：
1. 检查网络连接是否稳定
2. 清除浏览器缓存和Cookie
3. 尝试重启应用程序
4. 确认软件版本是否为最新

💡 如果问题仍然存在，请尝试：
- 检查系统兼容性
- 临时关闭防火墙或杀毒软件
- 以管理员权限运行程序

这些步骤通常能解决90%的常见技术问题。如果仍有疑问，我随时为您提供进一步帮助！
"""
    
    elif state["issue_type"] == "账户问题":
        if state["requires_human"]:
            ai_response = """
您好！我检测到您的账户可能存在安全风险。

出于保护您账户安全的考虑，这类问题需要我们的安全专员来处理。
我正在为您安排专业的账户安全顾问，他们会：
- 立即检查您的账户状态
- 协助您进行安全验证
- 提供账户保护建议

请稍等片刻，安全专员马上为您服务...
"""
        else:
            ai_response = """
您好！关于您的账户问题，我来为您提供帮助。

🔐 常见账户问题解决方案：
- 忘记密码：点击登录页面"忘记密码"，通过邮箱或手机重置
- 登录困难：清除浏览器缓存，尝试使用其他浏览器
- 个人信息修改：登录后进入"账户设置"页面进行修改
- 绑定手机/邮箱：在安全设置中可以更新联系方式

📱 如需立即重置密码，我可以为您发送重置链接到注册邮箱。
请问还有其他账户相关问题需要帮助吗？
"""
    
    elif state["issue_type"] == "退款申请":
        ai_response = """
感谢您联系我们关于退款事宜。

我理解您希望申请退款，这需要我们的财务专员来为您处理。
退款申请涉及订单审核和资金处理，我正在为您转接退款专员。

他们会为您：
✅ 核实订单信息和退款条件
✅ 处理退款申请流程
✅ 告知具体的退款时间

请稍等片刻...
"""
    
    elif state["issue_type"] == "投诉建议":
        if state["requires_human"]:
            ai_response = """
非常感谢您的反馈！我们高度重视您提出的问题。

您的投诉我们会认真对待并优先处理。我正在为您联系客户关系主管，
他们会：
🎯 详细了解问题的具体情况
🎯 制定针对性的解决方案
🎯 跟进处理进度直到问题解决

请稍等片刻，专业客服马上为您服务...
"""
        else:
            ai_response = """
感谢您的宝贵建议！我们非常重视用户的反馈意见。

📝 您的建议已经记录，我们会：
- 将您的建议转达给相关产品团队
- 在后续版本中考虑您的改进建议
- 持续优化产品和服务体验

🌟 如果您还有其他建议或需要帮助的地方，请随时告诉我！
我们致力于为您提供更好的服务体验。
"""
    
    else:
        ai_response = """
感谢您联系我们！我是AI客服助手，很高兴为您服务。

我正在分析您的问题，请稍等片刻...
我会为您提供最合适的帮助和解决方案。

如果您有任何疑问，请随时告诉我！
"""
    
    print(f"💬 [AI回复]\n{ai_response}")
    
    return {
        **state,
        "messages": state["messages"] + [{"role": "assistant", "content": ai_response}],
        "conversation_log": state["conversation_log"] + ["AI提供专业回复"]
    }

def human_intervention_node(state: CustomerServiceState) -> CustomerServiceState:
    """人工客服介入节点 - 修复版：正确的状态管理"""
    print(f"\n⚠️ [系统提示] 检测到{state['urgency_level']}优先级的{state['issue_type']}问题")
    print("🔄 [系统] 正在请求人工客服介入...")
    
    # 构建人工客服的上下文信息
    context_info = f"""
=== 客服工作台 ===
客户问题类型：{state['issue_type']}
紧急程度：{state['urgency_level']}
对话历史：{len(state['messages'])}条消息

最新客户消息：{state['messages'][-2]['content'] if len(state['messages']) >= 2 else '无'}
AI初步回复：{state['messages'][-1]['content'] if state['messages'] else '无'}

请作为专业客服代表回复客户：
"""
    
    # 使用interrupt暂停，等待人工客服输入
    human_response = interrupt({
        "type": "human_intervention",
        "context": context_info,
        "prompt": "👨‍💼 [人工客服] 请输入您的专业回复："
    })
    
    print(f"✅ [人工客服] {human_response}")
    
    # 修复：人工处理完成后，状态应该是"已完成"而不是"人工处理中"
    return {
        **state,
        "messages": state["messages"] + [{"role": "human_agent", "content": human_response}],
        "conversation_log": state["conversation_log"] + ["人工客服完成处理"],
        "resolution_status": "人工处理完成"  # 🔧 修复：明确表示人工处理已完成
    }

def collect_customer_feedback(state: CustomerServiceState) -> CustomerServiceState:
    """收集客户反馈 - 修复版：正确设置最终状态"""
    print("\n📝 [系统] 收集客户满意度反馈...")
    
    feedback_prompt = """
感谢您使用我们的客服服务！

为了不断改进服务质量，请您对本次服务进行评价：
1. 非常满意
2. 满意  
3. 一般
4. 不满意
5. 非常不满意

请输入数字1-5：
"""
    
    # 收集客户反馈
    feedback = interrupt({
        "type": "feedback_collection",
        "prompt": feedback_prompt
    })
    
    feedback_text = {
        "1": "非常满意", "2": "满意", "3": "一般", 
        "4": "不满意", "5": "非常不满意"
    }.get(feedback, "未评价")
    
    print(f"⭐ [客户反馈] {feedback_text}")
    
    # 修复：反馈收集完成后，状态应该是"已完成"
    return {
        **state,
        "customer_info": {**state["customer_info"], "satisfaction": feedback_text},
        "conversation_log": state["conversation_log"] + [f"客户反馈：{feedback_text}"],
        "resolution_status": "已完成"  # 🔧 修复：设置最终完成状态
    }

def route_decision(state: CustomerServiceState) -> Literal["human_intervention", "feedback", "end"]:
    """优化的路由决策：修复状态判断逻辑"""
    
    print(f"\n🎯 [路由决策] 分析当前状态...")
    print(f"   问题类型: {state['issue_type']}")
    print(f"   需要人工: {state['requires_human']}")
    print(f"   处理状态: {state['resolution_status']}")
    
    # 修复：更精确的状态判断
    if state["resolution_status"] == "已完成":
        print("   决策: 流程已完成 -> 结束")
        return "end"
    
    # 如果人工已经处理完成，收集反馈
    if state["resolution_status"] == "人工处理完成":
        print("   决策: 人工处理完成 -> 收集反馈")
        return "feedback"
    
    # 如果AI已经处理完成，收集反馈
    if state["resolution_status"] == "AI处理完成":
        print("   决策: AI处理完成 -> 收集反馈")
        return "feedback"
    
    # 如果需要人工处理且还未开始人工处理
    if state["requires_human"] and state["resolution_status"] == "处理中":
        print("   决策: 需要人工介入 -> 转人工客服")
        return "human_intervention"
    
    # AI可以独立处理的问题，先跟进再收集反馈
    if not state["requires_human"] and state["resolution_status"] == "处理中":
        print("   决策: AI独立处理 -> AI跟进服务")
        return "feedback"  # 这里应该先到ai_followup，但为了简化流程直接到feedback
    
    # 默认结束
    print("   决策: 默认结束流程")
    return "end"

def ai_followup_response(state: CustomerServiceState) -> CustomerServiceState:
    """AI跟进回复节点 - 修复版：正确的状态管理"""
    print(f"\n🤖 [AI客服] 为您提供跟进服务...")
    
    followup_message = """
希望我的回复对您有帮助！

🤝 如果您还有其他问题或需要进一步协助，请随时告诉我。
📞 如果问题比较复杂，我也可以为您转接人工客服。
⭐ 我们致力于为您提供最好的服务体验！

请问还有什么可以帮助您的吗？
"""
    
    print(f"💬 [AI跟进] {followup_message}")
    
    return {
        **state,
        "messages": state["messages"] + [{"role": "assistant", "content": followup_message}],
        "conversation_log": state["conversation_log"] + ["AI提供跟进服务"],
        "resolution_status": "AI处理完成"  # 🔧 修复：明确AI处理完成状态
    }

def final_summary(state: CustomerServiceState) -> CustomerServiceState:
    """生成服务总结 - 增强版：更详细的处理分析"""
    print("\n📋 [服务总结]")
    print("=" * 50)
    print(f"问题类型：{state['issue_type']}")
    print(f"紧急程度：{state['urgency_level']}")
    print(f"处理方式：{'人工处理' if state['requires_human'] else 'AI独立处理'}")
    print(f"处理状态：{state['resolution_status']}")
    print(f"客户满意度：{state['customer_info'].get('satisfaction', '未评价')}")
    print(f"对话轮次：{len(state['messages'])}轮")
    
    # 分析处理效率
    if state['requires_human']:
        print(f"✅ 正确识别复杂问题，及时转接人工处理")
    else:
        print(f"✅ AI成功独立解决问题，提高服务效率")
    
    print("\n📝 完整对话日志：")
    for i, log in enumerate(state["conversation_log"], 1):
        print(f"  {i}. {log}")
    print("=" * 50)
    
    return state

# 构建修复后的客服工作流图
def build_customer_service_graph():
    """构建修复后的客服工作流图 - 清晰的状态流转"""
    
    builder = StateGraph(CustomerServiceState)
    
    # 添加节点
    builder.add_node("analyze", analyze_customer_issue)
    builder.add_node("ai_response", ai_initial_response)
    builder.add_node("ai_followup", ai_followup_response)
    builder.add_node("human_intervention", human_intervention_node)
    builder.add_node("feedback", collect_customer_feedback)
    builder.add_node("summary", final_summary)
    
    # 设置流程 - 修复版：清晰的路径定义
    builder.add_edge(START, "analyze")
    builder.add_edge("analyze", "ai_response")
    
    # 主要的条件路由 - 从ai_response开始分流
    builder.add_conditional_edges(
        "ai_response",
        route_decision,
        {
            "human_intervention": "human_intervention",
            "feedback": "ai_followup",  # AI处理的问题先跟进
            "end": "summary"
        }
    )
    
    # AI跟进后的路由
    builder.add_conditional_edges(
        "ai_followup",
        lambda state: "feedback",  # AI跟进后直接收集反馈
        {"feedback": "feedback"}
    )
    
    # 人工处理后的路由
    builder.add_conditional_edges(
        "human_intervention", 
        lambda state: "feedback",  # 人工处理后直接收集反馈
        {"feedback": "feedback"}
    )
    
    # 反馈收集后的路由
    builder.add_conditional_edges(
        "feedback",
        lambda state: "summary",  # 反馈后直接生成总结
        {"summary": "summary"}
    )
    
    # 总结后结束
    builder.add_edge("summary", END)
    
    return builder.compile(checkpointer=MemorySaver())

# 客服场景演示 - 更新测试场景
async def run_customer_service_demo():
    """运行客服场景演示 - 展示优化后的AI判断能力"""
    print("🎯 智能客服人机协作演示 - 优化版")
    print("=" * 60)
    print("展示AI如何智能判断：哪些问题可以独立处理，哪些需要人工介入")
    print("=" * 60)
    
    graph = build_customer_service_graph()
    
    # 优化的测试场景 - 展示不同的处理路径
    test_scenarios = [
        {
            "name": "简单技术问题（AI独立处理）",
            "customer_message": "你好，我忘记了怎么修改个人资料，能教我一下操作步骤吗？",
            "description": "常见操作咨询，AI可以独立提供标准答案",
            "expected": "AI独立处理"
        },
        {
            "name": "复杂技术故障（需要人工）", 
            "customer_message": "我的数据突然全部丢失了，系统显示数据库连接错误，这是什么情况？",
            "description": "涉及数据丢失的严重技术问题，需要技术专家处理",
            "expected": "转人工处理"
        },
        {
            "name": "账户安全问题（需要人工）",
            "customer_message": "我发现有人在异地登录我的账户，还修改了我的密码，请立即帮我处理！",
            "description": "账户安全威胁，需要安全专员立即介入",
            "expected": "转人工处理"
        },
        {
            "name": "一般账户咨询（AI独立处理）",
            "customer_message": "请问怎么绑定新的手机号码？我想更新一下联系方式",
            "description": "常见账户操作咨询，AI可以提供标准流程",
            "expected": "AI独立处理"
        },
        {
            "name": "退款申请（需要人工）",
            "customer_message": "我昨天购买的VIP服务想要退款，因为功能不符合我的需求",
            "description": "涉及资金处理，需要财务专员审核",
            "expected": "转人工处理"
        },
        {
            "name": "产品建议（AI独立处理）",
            "customer_message": "建议你们在首页增加一个快速搜索功能，这样会更方便用户使用",
            "description": "产品改进建议，AI可以记录并回复",
            "expected": "AI独立处理"
        },
        {
            "name": "严重投诉（需要人工）",
            "customer_message": "你们的产品质量太差了！我要投诉！已经影响到我的工作了，必须给我一个说法！",
            "description": "严重服务投诉，需要客户关系专员处理",
            "expected": "转人工处理"
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n🎬 场景 {i}：{scenario['name']}")
        print(f"📝 场景描述：{scenario['description']}")
        print(f"🎯 预期处理：{scenario['expected']}")
        print(f"👤 客户问题：{scenario['customer_message']}")
        print("-" * 60)
        
        # 配置
        config = {"configurable": {"thread_id": f"optimized_service_{i}"}}
        
        # 初始状态
        initial_state = {
            "messages": [{"role": "user", "content": scenario['customer_message']}],
            "customer_info": {"scenario": scenario['name']},
            "issue_type": "",
            "urgency_level": "",
            "requires_human": False,
            "resolution_status": "处理中",
            "conversation_log": ["客户发起咨询"]
        }
        
        try:
            # 执行工作流
            async for event in graph.astream(initial_state, config):
                # 处理中断事件
                if "__interrupt__" in event:
                    interrupt_data = event["__interrupt__"]
                    
                    if isinstance(interrupt_data, tuple) and len(interrupt_data) > 0:
                        interrupt_obj = interrupt_data[0]
                        interrupt_value = interrupt_obj.value
                        
                        if isinstance(interrupt_value, dict):
                            context = interrupt_value.get("context", "")
                            prompt = interrupt_value.get("prompt", "请输入：")
                            interrupt_type = interrupt_value.get("type", "unknown")
                            
                            if context:
                                print(f"\n{context}")
                            
                            if interrupt_type == "human_intervention":
                                # 根据场景类型提供模拟的人工回复
                                if "数据丢失" in scenario['customer_message']:
                                    response = "我是技术专家李工程师。数据丢失是严重问题，我立即为您检查数据库状态并尝试恢复。请提供您的用户ID，我会优先处理您的情况。"
                                elif "异地登录" in scenario['customer_message']:
                                    response = "我是安全专员张主管。我立即为您冻结账户并启动安全审查。请提供身份验证信息，我们会在15分钟内恢复您的账户安全。"
                                elif "退款" in scenario['customer_message']:
                                    response = "我是财务专员王客服。我查看了您的购买记录，VIP服务确实可以在7天内退款。我现在就为您处理，预计2-3个工作日退款到账。"
                                elif "投诉" in scenario['customer_message']:
                                    response = "我是客户关系主管刘经理。非常抱歉给您带来不好的体验！我会亲自跟进您的问题，并在24小时内给您满意的解决方案。"
                                else:
                                    response = "我是专业客服，很高兴为您提供人工服务。让我详细了解您的问题并为您解决。"
                                
                                print(f"👨‍💼 [人工客服] {response}")
                                
                            elif interrupt_type == "feedback_collection":
                                # 根据处理方式模拟不同的客户反馈
                                if scenario['expected'] == "AI独立处理":
                                    response = "1"  # 非常满意
                                    print("👤 [客户] AI客服回答得很详细，问题解决了，非常满意！")
                                else:
                                    response = "2"  # 满意
                                    print("👤 [客户] 人工客服很专业，问题得到了妥善处理，满意！")
                            else:
                                response = input(f"{prompt}")
                            
                            # 恢复执行
                            await graph.ainvoke({"__resume__": response}, config)
                        else:
                            response = input(f"\n{interrupt_value}")
                            await graph.ainvoke({"__resume__": response}, config)
            
            print(f"\n✅ 场景 {i} 处理完成")
            
            # 验证处理结果是否符合预期
            final_state = graph.get_state(config)
            if final_state.values:
                actual_requires_human = final_state.values.get("requires_human", False)
                expected_human = scenario['expected'] == "转人工处理"
                
                if actual_requires_human == expected_human:
                    print(f"🎯 ✅ 判断正确：{scenario['expected']}")
                else:
                    print(f"🎯 ❌ 判断偏差：预期{scenario['expected']}，实际{'转人工处理' if actual_requires_human else 'AI独立处理'}")
            
            # 添加场景间的分隔
            if i < len(test_scenarios):
                print("\n" + "🔄" * 15 + " 下一个场景 " + "🔄" * 15)
                await asyncio.sleep(0.5)
                
        except Exception as e:
            print(f"❌ 场景 {i} 执行失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 展示优化效果总结
    print(f"\n🎉 演示完成！优化效果总结：")
    print("=" * 50)
    print("✅ AI独立处理：简单技术问题、常规咨询、产品建议")
    print("✅ 人工介入处理：复杂故障、安全问题、退款申请、严重投诉")
    print("✅ 智能路由：根据问题复杂度和敏感性自动判断")
    print("✅ 流程优化：避免不必要的重复分析和循环")

# 交互式客服演示
async def interactive_customer_service():
    """交互式客服演示"""
    print("\n🎮 交互式客服演示")
    print("=" * 40)
    print("您现在可以作为客户提出问题，体验完整的客服流程")
    print("输入 'quit' 退出演示")
    
    graph = build_customer_service_graph()
    
    session_count = 0
    
    while True:
        try:
            session_count += 1
            print(f"\n🆕 第 {session_count} 次咨询")
            print("-" * 30)
            
            customer_input = input("👤 [您作为客户] 请描述您遇到的问题: ").strip()
            
            if customer_input.lower() in ['quit', 'exit', '退出', 'q']:
                print("👋 感谢使用客服系统，再见！")
                break
            
            if not customer_input:
                print("⚠️ 请输入您的问题")
                continue
            
            print(f"\n🔄 [系统] 正在处理您的咨询...")
            
            # 配置
            config = {"configurable": {"thread_id": f"interactive_session_{session_count}"}}
            
            # 初始状态
            initial_state = {
                "messages": [{"role": "user", "content": customer_input}],
                "customer_info": {"session": session_count},
                "issue_type": "",
                "urgency_level": "",
                "requires_human": False,
                "resolution_status": "处理中",
                "conversation_log": ["客户发起咨询"]
            }
            
            # 执行工作流
            async for event in graph.astream(initial_state, config):
                # 处理中断
                if "__interrupt__" in event:
                    interrupt_data = event["__interrupt__"]
                    
                    if isinstance(interrupt_data, tuple) and len(interrupt_data) > 0:
                        interrupt_obj = interrupt_data[0]
                        interrupt_value = interrupt_obj.value
                        
                        if isinstance(interrupt_value, dict):
                            context = interrupt_value.get("context", "")
                            prompt = interrupt_value.get("prompt", "请输入：")
                            interrupt_type = interrupt_value.get("type", "unknown")
                            
                            if context:
                                print(f"\n{context}")
                            
                            if interrupt_type == "human_intervention":
                                print("⏰ [系统提示] 现在您需要扮演人工客服角色")
                                response = input(f"{prompt}")
                            elif interrupt_type == "feedback_collection":
                                print("⏰ [系统提示] 现在您需要作为客户进行满意度评价")
                                response = input(f"{prompt}")
                            else:
                                response = input(f"{prompt}")
                            
                            # 恢复执行
                            await graph.ainvoke({"__resume__": response}, config)
                        else:
                            response = input(f"\n{interrupt_value}")
                            await graph.ainvoke({"__resume__": response}, config)
            
            print(f"\n✅ 第 {session_count} 次咨询处理完成")
            print("=" * 40)
            
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出系统")
            break
        except Exception as e:
            print(f"❌ 处理出错: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    print("🎯 LangGraph 人机交互完整演示")
    print("智能客服场景：展示AI与人工客服的协作流程")
    print("=" * 60)
    
    print("\n选择演示模式:")
    print("1. 自动场景演示（预设4个典型客服场景）")
    print("2. 交互式演示（您可以输入真实问题体验）")
    
    choice = input("\n请选择 (1-2): ").strip()
    
    try:
        if choice == "1":
            print("\n🚀 启动自动场景演示...")
            asyncio.run(run_customer_service_demo())
        elif choice == "2":
            print("\n🚀 启动交互式演示...")
            asyncio.run(interactive_customer_service())
        else:
            print("默认运行自动场景演示")
            asyncio.run(run_customer_service_demo())
            
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🎉 演示结束！")
    print("💡 通过这个示例，您可以看到LangGraph如何实现：")
    print("   • AI自动分析和初步处理")
    print("   • 关键节点的人工介入")
    print("   • 完整的客服工作流程")
    print("   • 真实的人机协作场景")


