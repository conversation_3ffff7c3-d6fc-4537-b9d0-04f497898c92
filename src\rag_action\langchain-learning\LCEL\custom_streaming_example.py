"""
自定义流输出函数示例
"""

from langchain_core.output_parsers import StrOutputParser
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableLambda
import asyncio
import time
import json

# 创建 LLM
llm = ChatOpenAI(
    base_url="https://api.zhizengzeng.com/v1",
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    model="gpt-4o-mini",
    temperature=0
)

def custom_stream_processor():
    """自定义流处理器"""
    
    def process_stream(chunk):
        """处理流式数据"""
        if hasattr(chunk, 'content'):
            # 添加时间戳
            timestamp = time.strftime("%H:%M:%S")
            return f"[{timestamp}] {chunk.content}"
        return str(chunk)
    
    return RunnableLambda(process_stream)

def word_by_word_stream():
    """逐字流输出"""
    
    def word_stream(text):
        """将文本按词分割并逐词输出"""
        words = text.split()
        for word in words:
            # yield 是 Python 中的生成器（generator）关键字，用于在函数中返回一个值，并在下次迭代时从当前位置继续执行。
            # 这里每次 yield 一个单词，外部可以逐步获取每个单词，实现流式输出。
            yield word + " "
            time.sleep(0.1)  # 模拟打字效果
    
    return RunnableLambda(word_stream)

def formatted_stream():
    """格式化流输出"""
    
    def format_stream(chunk):
        """格式化流输出"""
        if hasattr(chunk, 'content'):
            content = chunk.content
            # 添加格式
            formatted = f"🤖 AI: {content}"
            return formatted
        return str(chunk)
    
    return RunnableLambda(format_stream)

def progress_stream():
    """带进度条的流输出"""
    
    def progress_stream_func(chunk):
        """带进度的流输出"""
        if hasattr(chunk, 'content'):
            content = chunk.content
            # 计算进度（简单估算）
            progress = len(content) / 100  # 假设总长度100字符
            progress_bar = "█" * int(progress * 20) + "░" * (20 - int(progress * 20))
            return f"[{progress_bar}] {progress:.1%} - {content}"
        return str(chunk)
    
    return RunnableLambda(progress_stream_func)

def json_stream():
    """JSON格式流输出"""
    
    def json_stream_func(chunk):
        """JSON格式流输出"""
        if hasattr(chunk, 'content'):
            data = {
                "timestamp": time.time(),
                "content": chunk.content,
                "type": "ai_response"
            }
            return json.dumps(data, ensure_ascii=False)
        return json.dumps({"error": "无效数据"})
    
    return RunnableLambda(json_stream_func)

def color_stream():
    """彩色流输出"""
    
    def color_stream_func(chunk):
        """彩色流输出"""
        if hasattr(chunk, 'content'):
            content = chunk.content
            # 使用ANSI颜色代码
            colored = f"\033[32m{content}\033[0m"  # 绿色
            return colored
        return str(chunk)
    
    return RunnableLambda(color_stream_func)

def basic_streaming_example():
    """基础流输出示例"""
    
    print("=== 基础流输出示例 ===")
    
    chain = (
        ChatPromptTemplate.from_template("请详细解释：{topic}") |
        llm |
        StrOutputParser() |
        custom_stream_processor()
    )
    
    print("开始流式处理...")
    for chunk in chain.stream({"topic": "人工智能"}):
        print(chunk, end="", flush=True)
    print("\n流式处理完成")

def word_by_word_example():
    """逐字流输出示例"""
    
    print("\n=== 逐字流输出示例 ===")
    
    chain = (
        ChatPromptTemplate.from_template("请简要介绍：{topic}") |
        llm |
        StrOutputParser() |
        word_by_word_stream()
    )
    
    print("开始逐字输出...")
    for chunk in chain.stream({"topic": "机器学习"}):
        print(chunk, end="", flush=True)
    print("\n逐字输出完成")

def formatted_streaming_example():
    """格式化流输出示例"""
    
    print("\n=== 格式化流输出示例 ===")
    
    chain = (
        ChatPromptTemplate.from_template("请回答：{question}") |
        llm |
        StrOutputParser() |
        formatted_stream()
    )
    
    print("开始格式化流输出...")
    for chunk in chain.stream({"question": "什么是深度学习？"}):
        print(chunk, end="", flush=True)
    print("\n格式化流输出完成")

def progress_streaming_example():
    """进度流输出示例"""
    
    print("\n=== 进度流输出示例 ===")
    
    chain = (
        ChatPromptTemplate.from_template("请详细说明：{topic}") |
        llm |
        StrOutputParser() |
        progress_stream()
    )
    
    print("开始进度流输出...")
    for chunk in chain.stream({"topic": "区块链技术"}):
        print(chunk, end="", flush=True)
    print("\n进度流输出完成")

def json_streaming_example():
    """JSON流输出示例"""
    
    print("\n=== JSON流输出示例 ===")
    
    chain = (
        ChatPromptTemplate.from_template("请回答：{question}") |
        llm |
        StrOutputParser() |
        json_stream()
    )
    
    print("开始JSON流输出...")
    for chunk in chain.stream({"question": "什么是云计算？"}):
        print(chunk)
    print("JSON流输出完成")

def color_streaming_example():
    """彩色流输出示例"""
    
    print("\n=== 彩色流输出示例 ===")
    
    chain = (
        ChatPromptTemplate.from_template("请解释：{topic}") |
        llm |
        StrOutputParser() |
        color_stream()
    )
    
    print("开始彩色流输出...")
    for chunk in chain.stream({"topic": "物联网"}):
        print(chunk, end="", flush=True)
    print("\n彩色流输出完成")

def async_streaming_example():
    """异步流输出示例"""
    
    print("\n=== 异步流输出示例 ===")
    
    async def async_stream():
        chain = (
            ChatPromptTemplate.from_template("请介绍：{topic}") |
            llm |
            StrOutputParser() |
            custom_stream_processor()
        )
        
        print("开始异步流输出...")
        async for chunk in chain.astream({"topic": "大数据"}):
            print(chunk, end="", flush=True)
        print("\n异步流输出完成")
    
    asyncio.run(async_stream())

def complex_streaming_example():
    """复杂流输出示例"""
    
    print("\n=== 复杂流输出示例 ===")
    
    def complex_stream_processor():
        """复杂的流处理器"""
        def process(chunk):
            if hasattr(chunk, 'content'):
                content = chunk.content
                # 添加多种格式
                timestamp = time.strftime("%H:%M:%S")
                word_count = len(content.split())
                formatted = f"[{timestamp}] 字数:{word_count} | {content}"
                return formatted
            return str(chunk)
        
        return RunnableLambda(process)
    
    chain = (
        ChatPromptTemplate.from_template("请详细分析：{topic}") |
        llm |
        StrOutputParser() |
        complex_stream_processor()
    )
    
    print("开始复杂流输出...")
    for chunk in chain.stream({"topic": "人工智能的未来发展"}):
        print(chunk, end="", flush=True)
    print("\n复杂流输出完成")

def main():
    """主函数"""
    basic_streaming_example()
    word_by_word_example()
    formatted_streaming_example()
    progress_streaming_example()
    json_streaming_example()
    color_streaming_example()
    async_streaming_example()
    complex_streaming_example()

if __name__ == "__main__":
    main() 