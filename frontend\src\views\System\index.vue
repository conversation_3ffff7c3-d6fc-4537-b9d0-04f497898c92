<template>
  <div class="system-page">
    <div class="page-header">
      <h1 class="page-title">系统监控</h1>
      <div class="header-actions">
        <el-switch
          v-model="autoRefresh"
          @change="toggleAutoRefresh"
          active-text="自动刷新"
          inactive-text="手动刷新"
        />
        <el-button @click="refreshAll" :loading="refreshing">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 系统状态卡片 -->
    <div class="status-cards">
      <div class="status-card" :class="systemStatusClass">
        <div class="card-icon">
          <el-icon><Monitor /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">系统状态</div>
          <div class="card-value">{{ systemStatusText }}</div>
          <div class="card-time">{{ lastUpdateTime }}</div>
        </div>
      </div>

      <div class="status-card">
        <div class="card-icon cpu">
          <el-icon><Monitor /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">CPU使用率</div>
          <div class="card-value">{{ cpuUsage }}%</div>
          <el-progress :percentage="cpuUsage" :show-text="false" />
        </div>
      </div>

      <div class="status-card">
        <div class="card-icon memory">
          <el-icon><Monitor /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">内存使用率</div>
          <div class="card-value">{{ memoryUsage }}%</div>
          <el-progress :percentage="memoryUsage" :show-text="false" />
        </div>
      </div>

      <div class="status-card">
        <div class="card-icon disk">
          <el-icon><Monitor /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">磁盘使用率</div>
          <div class="card-value">{{ diskUsage }}%</div>
          <el-progress :percentage="diskUsage" :show-text="false" />
        </div>
      </div>
    </div>

    <!-- 服务状态 -->
    <div class="services-section">
      <div class="section-header">
        <h3>服务状态</h3>
        <div class="service-summary">
          <el-tag type="success">运行中: {{ serviceStats.loaded }}</el-tag>
          <el-tag type="warning">加载中: {{ serviceStats.loading }}</el-tag>
          <el-tag type="danger">异常: {{ serviceStats.error }}</el-tag>
        </div>
      </div>
      
      <div class="services-grid">
        <div
          v-for="(service, name) in services"
          :key="name"
          class="service-card"
          :class="service.status"
        >
          <div class="service-header">
            <div class="service-name">{{ getServiceDisplayName(name) }}</div>
            <el-tag
              :type="getServiceTagType(service.status)"
              size="small"
            >
              {{ getServiceStatusText(service.status) }}
            </el-tag>
          </div>
          
          <div class="service-details">
            <div v-if="service.load_time" class="service-time">
              加载时间: {{ service.load_time }}ms
            </div>
            <div v-if="service.error" class="service-error">
              错误: {{ service.error }}
            </div>
          </div>
          
          <div class="service-actions">
            <el-button
              type="text"
              size="small"
              @click="restartService(name)"
              :disabled="service.status === 'loading'"
            >
              重启
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据库连接状态 -->
    <div class="database-section">
      <div class="section-header">
        <h3>数据库连接</h3>
      </div>
      
      <div class="database-grid">
        <div class="db-card">
          <div class="db-header">
            <div class="db-name">MySQL</div>
            <div class="db-status" :class="{ online: mysqlConnections.active > 0 }">
              {{ mysqlConnections.active > 0 ? '在线' : '离线' }}
            </div>
          </div>
          <div class="db-stats">
            <div class="stat-item">
              <span class="stat-label">活跃连接:</span>
              <span class="stat-value">{{ mysqlConnections.active }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">空闲连接:</span>
              <span class="stat-value">{{ mysqlConnections.idle }}</span>
            </div>
          </div>
        </div>

        <div class="db-card">
          <div class="db-header">
            <div class="db-name">Milvus</div>
            <div class="db-status" :class="{ online: milvusConnections.active > 0 }">
              {{ milvusConnections.active > 0 ? '在线' : '离线' }}
            </div>
          </div>
          <div class="db-stats">
            <div class="stat-item">
              <span class="stat-label">活跃连接:</span>
              <span class="stat-value">{{ milvusConnections.active }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">空闲连接:</span>
              <span class="stat-value">{{ milvusConnections.idle }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- API响应时间 -->
    <div class="api-section">
      <div class="section-header">
        <h3>API响应时间</h3>
      </div>
      
      <div class="api-table">
        <el-table :data="apiResponseTimes" stripe>
          <el-table-column prop="endpoint" label="接口" min-width="200" />
          <el-table-column prop="avg" label="平均响应时间(ms)" width="150">
            <template #default="{ row }">
              <span :class="getResponseTimeClass(row.avg)">{{ row.avg.toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="min" label="最小值(ms)" width="120">
            <template #default="{ row }">{{ row.min.toFixed(2) }}</template>
          </el-table-column>
          <el-table-column prop="max" label="最大值(ms)" width="120">
            <template #default="{ row }">{{ row.max.toFixed(2) }}</template>
          </el-table-column>
          <el-table-column prop="count" label="请求次数" width="120" />
        </el-table>
      </div>
    </div>

    <!-- 系统操作 -->
    <div class="operations-section">
      <div class="section-header">
        <h3>系统操作</h3>
      </div>
      
      <div class="operations-grid">
        <div class="operation-card">
          <div class="operation-icon">
            <el-icon><FolderAdd /></el-icon>
          </div>
          <div class="operation-content">
            <h4>数据备份</h4>
            <p>创建系统数据备份</p>
            <el-button type="primary" @click="createBackup" :loading="backupLoading">
              创建备份
            </el-button>
          </div>
        </div>

        <div class="operation-card">
          <div class="operation-icon">
            <el-icon><Delete /></el-icon>
          </div>
          <div class="operation-content">
            <h4>清理缓存</h4>
            <p>清理系统缓存数据</p>
            <el-button @click="clearCache" :loading="cacheLoading">
              清理缓存
            </el-button>
          </div>
        </div>

        <div class="operation-card">
          <div class="operation-icon">
            <el-icon><DocumentCopy /></el-icon>
          </div>
          <div class="operation-content">
            <h4>查看日志</h4>
            <p>查看系统运行日志</p>
            <el-button @click="viewLogs">
              查看日志
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useSystemStore } from '@/stores'
import { timeAgo } from '@/utils'
import {
  Refresh,
  Monitor,
  FolderAdd,
  Delete,
  DocumentCopy
} from '@element-plus/icons-vue'

const router = useRouter()
const systemStore = useSystemStore()

// 响应式数据
const autoRefresh = ref(false)
const refreshing = ref(false)
const backupLoading = ref(false)
const cacheLoading = ref(false)
const lastUpdateTime = ref('')

// 计算属性
const systemStatusText = computed(() => {
  const status = systemStore.systemStatus
  const statusMap = {
    healthy: '正常',
    degraded: '降级',
    unhealthy: '异常',
    unknown: '未知'
  }
  return statusMap[status] || '未知'
})

const systemStatusClass = computed(() => {
  const status = systemStore.systemStatus
  return `status-${status}`
})

const services = computed(() => systemStore.services)
const serviceStats = computed(() => systemStore.serviceStats)

const cpuUsage = computed(() => systemStore.metrics?.cpu_usage || 0)
const memoryUsage = computed(() => systemStore.metrics?.memory_usage || 0)
const diskUsage = computed(() => systemStore.metrics?.disk_usage || 0)

const mysqlConnections = computed(() => 
  systemStore.metrics?.database_connections?.mysql || { active: 0, idle: 0 }
)
const milvusConnections = computed(() => 
  systemStore.metrics?.database_connections?.milvus || { active: 0, idle: 0 }
)

const apiResponseTimes = computed(() => {
  const times = systemStore.metrics?.api_response_times || {}
  return Object.entries(times).map(([endpoint, data]) => ({
    endpoint,
    ...data
  }))
})

// 方法
const getServiceDisplayName = (name: string) => {
  const nameMap = {
    document_parser: '文档解析服务',
    embedding_service: '向量嵌入服务',
    llm_service: 'LLM服务',
    vector_service: '向量检索服务',
    reranker_service: '重排序服务'
  }
  return nameMap[name] || name
}

const getServiceStatusText = (status: string) => {
  const statusMap = {
    loaded: '运行中',
    loading: '加载中',
    error: '异常'
  }
  return statusMap[status] || status
}

const getServiceTagType = (status: string) => {
  const typeMap = {
    loaded: 'success',
    loading: 'warning',
    error: 'danger'
  }
  return typeMap[status] || 'info'
}

const getResponseTimeClass = (time: number) => {
  if (time < 100) return 'text-success'
  if (time < 500) return 'text-warning'
  return 'text-danger'
}

const refreshAll = async () => {
  refreshing.value = true
  try {
    await systemStore.refreshAll()
    lastUpdateTime.value = timeAgo(new Date().toISOString())
  } finally {
    refreshing.value = false
  }
}

const toggleAutoRefresh = (enabled: boolean) => {
  if (enabled) {
    systemStore.enableAutoRefresh(30) // 30秒间隔
  } else {
    systemStore.disableAutoRefresh()
  }
}

const restartService = async (serviceName: string) => {
  try {
    await systemStore.restartService(serviceName)
  } catch (error) {
    ElMessage.error('重启服务失败')
  }
}

const createBackup = async () => {
  backupLoading.value = true
  try {
    await systemStore.createBackup()
  } finally {
    backupLoading.value = false
  }
}

const clearCache = async () => {
  cacheLoading.value = true
  try {
    await systemStore.clearCache()
  } finally {
    cacheLoading.value = false
  }
}

const viewLogs = () => {
  router.push('/system/logs')
}

// 生命周期
onMounted(() => {
  refreshAll()
})

onUnmounted(() => {
  systemStore.disableAutoRefresh()
})
</script>

<style lang="scss" scoped>
.system-page {
  padding: var(--spacing-lg);
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    
    .page-title {
      font-size: 28px;
      font-weight: var(--font-weight-bold);
      color: var(--text-primary);
    }
    
    .header-actions {
      display: flex;
      gap: var(--spacing-md);
      align-items: center;
    }
  }
  
  .status-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    
    .status-card {
      background: var(--bg-color);
      border-radius: var(--border-radius-base);
      padding: var(--spacing-lg);
      box-shadow: var(--shadow-base);
      display: flex;
      align-items: center;
      
      &.status-healthy {
        border-left: 4px solid var(--success-color);
      }
      
      &.status-degraded {
        border-left: 4px solid var(--warning-color);
      }
      
      &.status-unhealthy {
        border-left: 4px solid var(--danger-color);
      }
      
      .card-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        margin-right: var(--spacing-md);
        background: rgba(64, 158, 255, 0.1);
        color: var(--primary-color);
        
        &.cpu {
          background: rgba(103, 194, 58, 0.1);
          color: var(--success-color);
        }
        
        &.memory {
          background: rgba(230, 162, 60, 0.1);
          color: var(--warning-color);
        }
        
        &.disk {
          background: rgba(245, 108, 108, 0.1);
          color: var(--danger-color);
        }
      }
      
      .card-content {
        flex: 1;
        
        .card-title {
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
          margin-bottom: var(--spacing-sm);
        }
        
        .card-value {
          font-size: 24px;
          font-weight: var(--font-weight-bold);
          color: var(--text-primary);
          margin-bottom: var(--spacing-sm);
        }
        
        .card-time {
          font-size: var(--font-size-xs);
          color: var(--text-placeholder);
        }
      }
    }
  }
  
  .services-section,
  .database-section,
  .api-section,
  .operations-section {
    margin-bottom: var(--spacing-xl);
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      
      h3 {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-medium);
        color: var(--text-primary);
      }
      
      .service-summary {
        display: flex;
        gap: var(--spacing-sm);
      }
    }
  }
  
  .services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    
    .service-card {
      background: var(--bg-color);
      border-radius: var(--border-radius-base);
      padding: var(--spacing-lg);
      box-shadow: var(--shadow-base);
      
      &.loaded {
        border-left: 4px solid var(--success-color);
      }
      
      &.loading {
        border-left: 4px solid var(--warning-color);
      }
      
      &.error {
        border-left: 4px solid var(--danger-color);
      }
      
      .service-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-md);
        
        .service-name {
          font-weight: var(--font-weight-medium);
          color: var(--text-primary);
        }
      }
      
      .service-details {
        margin-bottom: var(--spacing-md);
        
        .service-time {
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
        }
        
        .service-error {
          font-size: var(--font-size-sm);
          color: var(--danger-color);
        }
      }
      
      .service-actions {
        text-align: right;
      }
    }
  }
  
  .database-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    
    .db-card {
      background: var(--bg-color);
      border-radius: var(--border-radius-base);
      padding: var(--spacing-lg);
      box-shadow: var(--shadow-base);
      
      .db-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-md);
        
        .db-name {
          font-weight: var(--font-weight-medium);
          color: var(--text-primary);
        }
        
        .db-status {
          padding: 2px 8px;
          border-radius: 12px;
          font-size: var(--font-size-xs);
          background: var(--danger-color);
          color: white;
          
          &.online {
            background: var(--success-color);
          }
        }
      }
      
      .db-stats {
        .stat-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: var(--spacing-sm);
          
          .stat-label {
            color: var(--text-secondary);
          }
          
          .stat-value {
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
          }
        }
      }
    }
  }
  
  .api-table {
    background: var(--bg-color);
    border-radius: var(--border-radius-base);
    box-shadow: var(--shadow-base);
    overflow: hidden;
  }
  
  .operations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    
    .operation-card {
      background: var(--bg-color);
      border-radius: var(--border-radius-base);
      padding: var(--spacing-lg);
      box-shadow: var(--shadow-base);
      text-align: center;
      
      .operation-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        margin: 0 auto var(--spacing-md);
      }
      
      .operation-content {
        h4 {
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-medium);
          color: var(--text-primary);
          margin-bottom: var(--spacing-sm);
        }
        
        p {
          color: var(--text-secondary);
          margin-bottom: var(--spacing-md);
        }
      }
    }
  }
}

@include respond-below(md) {
  .system-page {
    padding: var(--spacing-md);
    
    .page-header {
      flex-direction: column;
      gap: var(--spacing-md);
      align-items: stretch;
    }
    
    .status-cards {
      grid-template-columns: 1fr;
    }
    
    .services-grid,
    .database-grid,
    .operations-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
