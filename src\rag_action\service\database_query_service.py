"""
数据库查询执行服务
安全执行SQL查询并返回结果
"""
import logging
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import pandas as pd

logger = logging.getLogger(__name__)

# 导入数据库连接
try:
    from rag_action.core.database import get_db, SessionLocal
    from sqlalchemy import text
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False
    logger.warning("数据库连接不可用")

from .text_to_sql_service import get_text_to_sql_service


class DatabaseQueryService:
    """数据库查询执行服务"""
    
    def __init__(self):
        self.text_to_sql_service = get_text_to_sql_service()
        self.max_rows = 1000  # 最大返回行数限制
        self.timeout_seconds = 30  # 查询超时时间
        
        logger.info("数据库查询服务初始化完成")
    
    def execute_natural_language_query(self, question: str) -> Dict[str, Any]:
        """
        执行自然语言查询
        
        Args:
            question: 自然语言问题
            
        Returns:
            查询结果字典
        """
        try:
            # 1. 生成SQL
            sql_result = self.text_to_sql_service.generate_sql(question)
            
            if not sql_result.get("success", False):
                return {
                    "success": False,
                    "error": sql_result.get("error", "SQL生成失败"),
                    "question": question,
                    "sql": "",
                    "data": [],
                    "summary": ""
                }
            
            sql = sql_result.get("sql", "")
            explanation = sql_result.get("explanation", "")
            
            # 2. 执行SQL查询
            execution_result = self.execute_sql(sql)
            
            # 3. 构建完整结果
            result = {
                "success": execution_result["success"],
                "question": question,
                "sql": sql,
                "explanation": explanation,
                "data": execution_result.get("data", []),
                "columns": execution_result.get("columns", []),
                "row_count": execution_result.get("row_count", 0),
                "execution_time": execution_result.get("execution_time", 0),
                "summary": self._generate_result_summary(question, execution_result),
                "error": execution_result.get("error", "")
            }
            
            return result
            
        except Exception as e:
            logger.error(f"自然语言查询执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "question": question,
                "sql": "",
                "data": [],
                "summary": ""
            }
    
    def execute_sql(self, sql: str) -> Dict[str, Any]:
        """
        安全执行SQL查询
        
        Args:
            sql: SQL查询语句
            
        Returns:
            执行结果字典
        """
        if not DATABASE_AVAILABLE:
            return {
                "success": False,
                "error": "数据库连接不可用",
                "data": [],
                "columns": [],
                "row_count": 0,
                "execution_time": 0
            }
        
        try:
            start_time = datetime.now()
            
            # 安全性检查
            safety_check = self._check_sql_safety(sql)
            if not safety_check["is_safe"]:
                return {
                    "success": False,
                    "error": f"SQL安全检查失败: {safety_check['reason']}",
                    "data": [],
                    "columns": [],
                    "row_count": 0,
                    "execution_time": 0
                }
            
            # 添加LIMIT限制
            limited_sql = self._add_limit_to_sql(sql)
            
            # 执行查询
            db = SessionLocal()
            try:
                result = db.execute(text(limited_sql))
                
                # 获取列名
                columns = list(result.keys()) if result.keys() else []
                
                # 获取数据
                rows = result.fetchall()
                data = [dict(row._mapping) for row in rows]
                
                # 转换数据类型（处理datetime等特殊类型）
                data = self._convert_data_types(data)
                
                end_time = datetime.now()
                execution_time = (end_time - start_time).total_seconds()
                
                return {
                    "success": True,
                    "data": data,
                    "columns": columns,
                    "row_count": len(data),
                    "execution_time": execution_time,
                    "sql_executed": limited_sql,
                    "error": ""
                }
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"SQL执行失败: {e}")
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            return {
                "success": False,
                "error": str(e),
                "data": [],
                "columns": [],
                "row_count": 0,
                "execution_time": execution_time
            }
    
    def _check_sql_safety(self, sql: str) -> Dict[str, Any]:
        """检查SQL安全性"""
        sql_upper = sql.upper().strip()
        
        # 只允许SELECT查询
        if not sql_upper.startswith('SELECT'):
            return {
                "is_safe": False,
                "reason": "只允许SELECT查询"
            }
        
        # 禁止的关键词
        forbidden_keywords = [
            'DROP', 'DELETE', 'INSERT', 'UPDATE', 'ALTER', 'CREATE', 
            'TRUNCATE', 'REPLACE', 'MERGE', 'CALL', 'EXEC', 'EXECUTE'
        ]
        
        for keyword in forbidden_keywords:
            if keyword in sql_upper:
                return {
                    "is_safe": False,
                    "reason": f"禁止使用关键词: {keyword}"
                }
        
        # 检查是否包含危险函数
        dangerous_functions = [
            'LOAD_FILE', 'INTO OUTFILE', 'INTO DUMPFILE',
            'SYSTEM', 'SHELL', 'EXEC'
        ]
        
        for func in dangerous_functions:
            if func in sql_upper:
                return {
                    "is_safe": False,
                    "reason": f"禁止使用函数: {func}"
                }
        
        return {
            "is_safe": True,
            "reason": "SQL安全检查通过"
        }
    
    def _add_limit_to_sql(self, sql: str) -> str:
        """为SQL添加LIMIT限制"""
        sql_upper = sql.upper()
        
        # 如果已经有LIMIT，检查是否超过最大限制
        if 'LIMIT' in sql_upper:
            # 提取LIMIT值
            import re
            limit_match = re.search(r'LIMIT\s+(\d+)', sql_upper)
            if limit_match:
                limit_value = int(limit_match.group(1))
                if limit_value > self.max_rows:
                    # 替换为最大限制
                    sql = re.sub(r'LIMIT\s+\d+', f'LIMIT {self.max_rows}', sql, flags=re.IGNORECASE)
            return sql
        
        # 如果没有LIMIT，添加限制
        if sql.rstrip().endswith(';'):
            sql = sql.rstrip()[:-1]  # 移除分号
        
        return f"{sql} LIMIT {self.max_rows}"
    
    def _convert_data_types(self, data: List[Dict]) -> List[Dict]:
        """转换数据类型为JSON可序列化的格式"""
        converted_data = []
        
        for row in data:
            converted_row = {}
            for key, value in row.items():
                if isinstance(value, datetime):
                    converted_row[key] = value.isoformat()
                elif hasattr(value, '__dict__'):  # 复杂对象
                    converted_row[key] = str(value)
                else:
                    converted_row[key] = value
            converted_data.append(converted_row)
        
        return converted_data
    
    def _generate_result_summary(self, question: str, execution_result: Dict) -> str:
        """生成查询结果摘要"""
        if not execution_result.get("success", False):
            return f"查询失败: {execution_result.get('error', '未知错误')}"
        
        row_count = execution_result.get("row_count", 0)
        execution_time = execution_result.get("execution_time", 0)
        
        if row_count == 0:
            return "查询成功，但没有找到匹配的数据。"
        
        summary = f"查询成功，找到 {row_count} 条记录"
        
        if execution_time > 0:
            summary += f"，执行时间 {execution_time:.3f} 秒"
        
        # 根据数据类型生成更详细的摘要
        data = execution_result.get("data", [])
        if data and len(data) > 0:
            first_row = data[0]
            
            # 如果是统计查询（只有一行且包含count等）
            if row_count == 1:
                for key, value in first_row.items():
                    if 'count' in key.lower() or 'total' in key.lower() or 'sum' in key.lower():
                        summary += f"。{key}: {value}"
            
            # 如果是列表查询
            elif row_count > 1:
                columns = execution_result.get("columns", [])
                if len(columns) > 0:
                    summary += f"。主要字段: {', '.join(columns[:3])}"
                    if len(columns) > 3:
                        summary += f" 等{len(columns)}个字段"
        
        return summary + "。"
    
    def get_table_info(self, table_name: str = None) -> Dict[str, Any]:
        """获取表结构信息"""
        schema_info = self.text_to_sql_service.get_schema_info()
        
        if table_name:
            return schema_info.get(table_name, {})
        
        return schema_info
    
    def get_sample_queries(self) -> List[Dict[str, Any]]:
        """获取示例查询"""
        return self.text_to_sql_service.get_few_shot_examples()
    
    def validate_sql(self, sql: str) -> Dict[str, Any]:
        """验证SQL语法"""
        return self.text_to_sql_service._validate_sql(sql)

    def execute_sql_safely(self, sql: str) -> Dict[str, Any]:
        """
        安全执行SQL查询
        专门为Text-to-SQL Agent设计的执行方法

        Args:
            sql: SQL查询语句

        Returns:
            Dict: 执行结果
        """
        if not DATABASE_AVAILABLE:
            return {
                "success": False,
                "data": [],
                "columns": [],
                "row_count": 0,
                "execution_time": 0,
                "error": "数据库连接不可用"
            }

        start_time = datetime.now()

        try:
            # 1. SQL安全验证
            validation_result = self.text_to_sql_service._validate_sql(sql)
            if not validation_result.get("is_valid", False):
                return {
                    "success": False,
                    "data": [],
                    "columns": [],
                    "row_count": 0,
                    "execution_time": 0,
                    "error": f"SQL验证失败: {', '.join(validation_result.get('errors', []))}"
                }

            # 2. 添加安全限制
            limited_sql = self._add_safety_limits(sql)

            # 3. 执行查询
            db = SessionLocal()
            try:
                result = db.execute(text(limited_sql))

                # 获取列名
                columns = list(result.keys()) if hasattr(result, 'keys') else []

                # 获取数据
                data = []
                for row in result:
                    if hasattr(row, '_asdict'):
                        data.append(row._asdict())
                    else:
                        data.append(dict(row))

                end_time = datetime.now()
                execution_time = (end_time - start_time).total_seconds()

                return {
                    "success": True,
                    "data": data,
                    "columns": columns,
                    "row_count": len(data),
                    "execution_time": execution_time,
                    "error": ""
                }
            finally:
                db.close()

        except Exception as e:
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            error_msg = str(e)
            logger.error(f"SQL执行失败: {error_msg}")

            return {
                "success": False,
                "data": [],
                "columns": [],
                "row_count": 0,
                "execution_time": execution_time,
                "error": error_msg
            }

    def _add_safety_limits(self, sql: str) -> str:
        """为SQL添加安全限制"""
        sql = sql.strip()

        # 如果SQL已经有LIMIT，不再添加
        if 'LIMIT' in sql.upper():
            return sql

        # 为SELECT查询添加LIMIT限制
        if sql.upper().startswith('SELECT'):
            # 在最后添加LIMIT
            if sql.endswith(';'):
                sql = sql[:-1] + ' LIMIT 1000;'
            else:
                sql = sql + ' LIMIT 1000'

        return sql


def get_database_query_service() -> DatabaseQueryService:
    """获取数据库查询服务实例"""
    return DatabaseQueryService()
