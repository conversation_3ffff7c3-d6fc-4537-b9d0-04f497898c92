import { http } from '../request'
import type { QueryRequest, QueryResponse, Conversation, ConversationSummary } from '@/types'

/**
 * 查询相关API
 */
export const queryApi = {


  /**
   * 流式智能问答 (GET方式)
   */
  intelligentQueryStream(params: {
    query: string
    conversation_id?: string
  }, callbacks: {
    onProgress?: (stage: string, message: string) => void
    onChunk?: (chunk: string) => void
    onComplete?: (response: QueryResponse) => void
    onError?: (error: any) => void
  }): void {
    // 构建查询参数
    const queryParams = new URLSearchParams()
    queryParams.append('query', params.query)
    if (params.conversation_id) {
      queryParams.append('conversation_id', params.conversation_id)
    }

    // 使用Server-Sent Events进行流式请求
    const eventSource = new EventSource(`/api/query/intelligent/stream?${queryParams.toString()}`)

    let fullAnswer = ''

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        const { type, content, finished } = data

        switch (type) {
          case 'retrieval_start':
            callbacks.onProgress?.('retrieval', content)
            break

          case 'retrieval_complete':
            callbacks.onProgress?.('retrieval', `${content} (找到 ${data.sources_count || 0} 个文档)`)
            break

          case 'generation_start':
            callbacks.onProgress?.('generation', content)
            break

          case 'answer_chunk':
            fullAnswer += content
            callbacks.onChunk?.(content)
            break

          case 'answer_complete':
            eventSource.close()
            callbacks.onComplete?.({
              answer: content,
              sources: data.sources || [],
              query_time: data.processing_time || 0,
              total_tokens: data.metadata?.token_count,
              conversation_id: data.conversation_id,
              message_id: data.message_id,
              processing_time: data.processing_time,
              metadata: data.metadata
            })
            break

          case 'session_complete':
            // 会话完成，包含额外的会话信息
            break

          case 'error':
            eventSource.close()
            callbacks.onError?.(new Error(content))
            break

          default:
            console.warn('未知的流式数据类型:', type)
        }
      } catch (error) {
        console.error('解析SSE数据失败:', error)
        callbacks.onError?.(error)
      }
    }

    eventSource.onerror = (error) => {
      eventSource.close()
      callbacks.onError?.(error)
    }
  },

  /**
   * 流式智能问答 (POST方式)
   */
  intelligentQueryStreamPost(params: {
    query: string
    conversation_id?: string
    top_k?: number
    note_ids?: number[]
    stream?: boolean
  }, callbacks: {
    onProgress?: (stage: string, message: string) => void
    onChunk?: (chunk: string) => void
    onComplete?: (response: QueryResponse) => void
    onError?: (error: any) => void
  }): void {
    // 使用fetch进行POST流式请求
    fetch('/api/query/intelligent/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
      },
      body: JSON.stringify({
        query: params.query,
        conversation_id: params.conversation_id,
        top_k: params.top_k || 5,
        note_ids: params.note_ids,
        stream: params.stream !== false
      })
    }).then(response => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      let fullAnswer = ''
      const decoder = new TextDecoder()

      const readStream = async () => {
        try {
          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            const chunk = decoder.decode(value, { stream: true })
            const lines = chunk.split('\n')

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const data = JSON.parse(line.slice(6))
                  const { type, content, finished } = data

                  // 记录conversation_id接收
                  if (data.conversation_id) {
                    console.log(`收到conversation_id [${type}]:`, data.conversation_id)
                  }

                  switch (type) {
                    case 'retrieval_start':
                      callbacks.onProgress?.('retrieval', content)
                      break

                    case 'retrieval_complete':
                      callbacks.onProgress?.('retrieval', `${content} (找到 ${data.sources_count || 0} 个文档)`)
                      break

                    case 'generation_start':
                      callbacks.onProgress?.('generation', content)
                      break

                    case 'answer_chunk':
                      fullAnswer += content
                      callbacks.onChunk?.(content)
                      break

                    case 'answer_complete':
                      callbacks.onComplete?.({
                        answer: content,
                        sources: data.sources || [],
                        query_time: data.processing_time || 0,
                        total_tokens: data.metadata?.token_count,
                        conversation_id: data.conversation_id,
                        message_id: data.message_id,
                        processing_time: data.processing_time,
                        metadata: data.metadata
                      })
                      break

                    case 'session_complete':
                      // 处理session_complete事件，获取conversation_id
                      console.log('收到session_complete事件:', data)
                      callbacks.onComplete?.({
                        answer: fullAnswer, // 使用累积的完整答案
                        sources: data.sources || [],
                        query_time: data.processing_time || 0,
                        total_tokens: data.metadata?.token_count,
                        conversation_id: data.conversation_id, // 关键：这里有conversation_id
                        message_id: data.message_id,
                        user_message_id: data.user_message_id,
                        processing_time: data.processing_time,
                        metadata: data.metadata
                      })
                      return

                    case 'error':
                      callbacks.onError?.(new Error(content))
                      return
                  }
                } catch (parseError) {
                  // 忽略解析错误
                }
              }
            }
          }
        } catch (error) {
          callbacks.onError?.(error)
        }
      }

      readStream()
    }).catch(error => {
      callbacks.onError?.(error)
    })
  },



  /**
   * 数据库查询执行
   */
  databaseQuery(params: { question: string }): Promise<{
    sql: string
    result: string
    data?: any[]
    execution_time: number
  }> {
    return http.post('/query/database', null, { params })
  },

  /**
   * 获取数据库表结构
   */
  getTableSchema(): Promise<{
    tables: Array<{
      table_name: string
      description: string
      columns: Array<{
        column_name: string
        data_type: string
        description: string
        is_primary_key: boolean
      }>
    }>
  }> {
    return http.get('/schema/tables')
  },

  /**
   * 获取SQL示例
   */
  getSqlExamples(): Promise<{
    examples: Array<{
      question: string
      sql: string
      description: string
      category: string
    }>
  }> {
    return http.get('/examples/sql')
  },

  /**
   * 向量搜索
   */
  vectorSearch(params: {
    query: string
    top_k?: number
    note_ids?: number[]
  }): Promise<{
    results: Array<{
      note_id: number
      note_title: string
      content: string
      score: number
      page_number?: number
    }>
    query_time: number
  }> {
    return http.post('/search/vector', null, { params })
  },

  /**
   * 混合搜索
   */
  hybridSearch(params: {
    query: string
    top_k?: number
    note_ids?: number[]
    vector_weight?: number
    bm25_weight?: number
  }): Promise<{
    results: Array<{
      note_id: number
      note_title: string
      content: string
      score: number
      page_number?: number
      source: 'vector' | 'bm25' | 'hybrid'
    }>
    query_time: number
  }> {
    return http.post('/search/hybrid', null, { params })
  },

  /**
   * 获取会话列表
   */
  getConversations(limit: number = 50): Promise<any> {
    return http.get(`/conversations?limit=${limit}`)
  },

  /**
   * 获取会话详情
   */
  getConversation(conversationId: string): Promise<any> {
    return http.get(`/conversations/${conversationId}`)
  },

  /**
   * 删除会话
   */
  deleteConversation(conversationId: string): Promise<any> {
    return http.delete(`/conversations/${conversationId}`)
  },

  /**
   * 清空所有会话
   */
  clearAllConversations(): Promise<any> {
    return http.delete('/conversations')
  },

  /**
   * 获取会话统计
   */
  getConversationStats(): Promise<any> {
    return http.get('/conversations/stats')
  }
}
