#!/usr/bin/env python3
"""
高级PDF转Markdown转换脚本
参考 minerU_pdf2markdown.py 的实现方式
支持命令行参数，提供精确控制
"""
import os
import json
import argparse
from pathlib import Path
from loguru import logger

# 导入mineru相关模块
from mineru.cli.common import convert_pdf_bytes_to_bytes_by_pypdfium2, prepare_env, read_fn
from mineru.data.data_reader_writer import FileBasedDataWriter
from mineru.utils.draw_bbox import draw_layout_bbox, draw_span_bbox
from mineru.utils.enum_class import MakeMode
from mineru.backend.vlm.vlm_analyze import doc_analyze as vlm_doc_analyze
from mineru.backend.vlm.vlm_middle_json_mkcontent import union_make as vlm_union_make
from mineru.backend.pipeline.pipeline_analyze import doc_analyze as pipeline_doc_analyze
from mineru.backend.pipeline.pipeline_middle_json_mkcontent import union_make as pipeline_union_make
from mineru.backend.pipeline.model_json_to_middle_json import result_to_middle_json as pipeline_result_to_middle_json

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="高级PDF转Markdown转换工具")
    
    parser.add_argument("--input-dir", "-i", default="pdfs", 
                       help="输入PDF文件目录 (默认: pdfs)")
    parser.add_argument("--output-dir", "-o", default="output", 
                       help="输出目录 (默认: output)")
    parser.add_argument("--lang", "-l", default="ch", 
                       choices=["ch", "en", "korean", "japan", "chinese_cht"],
                       help="文档语言 (默认: ch)")
    parser.add_argument("--backend", "-b", default="pipeline",
                       choices=["pipeline", "vlm-transformers", "vlm-sglang-engine", "vlm-sglang-client"],
                       help="转换后端 (默认: pipeline)")
    parser.add_argument("--method", "-m", default="auto",
                       choices=["auto", "txt", "ocr"],
                       help="解析方法 (默认: auto)")
    parser.add_argument("--server-url", "-s", 
                       help="VLM服务器URL (仅用于vlm-sglang-client)")
    parser.add_argument("--start-page", type=int, default=0,
                       help="开始页面 (默认: 0)")
    parser.add_argument("--end-page", type=int, default=None,
                       help="结束页面 (默认: 全部)")
    parser.add_argument("--no-formula", action="store_true",
                       help="禁用公式解析")
    parser.add_argument("--no-table", action="store_true",
                       help="禁用表格解析")
    parser.add_argument("--draw-bbox", action="store_true",
                       help="绘制边界框")
    
    return parser.parse_args()

def main():
    """高级转换主函数"""
    args = parse_arguments()
    
    print("🚀 开始高级PDF转Markdown转换...")
    print(f"📋 配置信息:")
    print(f"   - 输入目录: {args.input_dir}")
    print(f"   - 输出目录: {args.output_dir}")
    print(f"   - 语言: {args.lang}")
    print(f"   - 后端: {args.backend}")
    print(f"   - 方法: {args.method}")
    print(f"   - 页面范围: {args.start_page}-{args.end_page or '全部'}")
    
    # 设置路径
    __dir__ = os.path.dirname(os.path.abspath(__file__))
    pdf_files_dir = os.path.join(__dir__, args.input_dir)
    output_dir = os.path.join(__dir__, args.output_dir)
    
    # 确保目录存在
    os.makedirs(pdf_files_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)
    
    # 收集PDF文件
    pdf_suffixes = [".pdf"]
    doc_path_list = []
    
    for doc_path in Path(pdf_files_dir).glob('*'):
        if doc_path.suffix.lower() in pdf_suffixes:
            doc_path_list.append(doc_path)
    
    print(f"\n📁 找到 {len(doc_path_list)} 个PDF文件:")
    for path in doc_path_list:
        print(f"   - {path.name}")
    
    if not doc_path_list:
        print("❌ 未找到PDF文件！")
        return
    
    # 开始转换
    try:
        print("\n🔄 开始转换...")
        
        # 准备数据
        file_name_list = []
        pdf_bytes_list = []
        lang_list = []
        
        for path in doc_path_list:
            file_name = str(Path(path).stem)
            pdf_bytes = read_fn(path)
            file_name_list.append(file_name)
            pdf_bytes_list.append(pdf_bytes)
            lang_list.append(args.lang)
        
        # 根据后端选择转换方法
        if args.backend == "pipeline":
            parse_with_pipeline_advanced(
                output_dir=output_dir,
                pdf_file_names=file_name_list,
                pdf_bytes_list=pdf_bytes_list,
                p_lang_list=lang_list,
                parse_method=args.method,
                formula_enable=not args.no_formula,
                table_enable=not args.no_table,
                start_page_id=args.start_page,
                end_page_id=args.end_page,
                draw_bbox=args.draw_bbox
            )
        else:
            parse_with_vlm_advanced(
                output_dir=output_dir,
                pdf_file_names=file_name_list,
                pdf_bytes_list=pdf_bytes_list,
                backend=args.backend,
                server_url=args.server_url,
                start_page_id=args.start_page,
                end_page_id=args.end_page,
                draw_bbox=args.draw_bbox
            )
        
        print("✅ 转换完成！")
        print(f"📂 输出目录: {output_dir}")
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        logger.exception(e)

def parse_with_pipeline_advanced(
    output_dir,
    pdf_file_names: list[str],
    pdf_bytes_list: list[bytes],
    p_lang_list: list[str],
    parse_method="auto",
    formula_enable=True,
    table_enable=True,
    start_page_id=0,
    end_page_id=None,
    draw_bbox=False
):
    """使用pipeline模式高级解析PDF"""
    
    # 处理页面范围
    for idx, pdf_bytes in enumerate(pdf_bytes_list):
        new_pdf_bytes = convert_pdf_bytes_to_bytes_by_pypdfium2(pdf_bytes, start_page_id, end_page_id)
        pdf_bytes_list[idx] = new_pdf_bytes

    # 使用pipeline进行分析
    infer_results, all_image_lists, all_pdf_docs, lang_list, ocr_enabled_list = pipeline_doc_analyze(
        pdf_bytes_list, 
        p_lang_list, 
        parse_method=parse_method, 
        formula_enable=formula_enable,
        table_enable=table_enable
    )

    # 处理每个PDF文件
    for idx, model_list in enumerate(infer_results):
        model_json = model_list.copy()
        pdf_file_name = pdf_file_names[idx]
        
        # 准备环境
        local_image_dir, local_md_dir = prepare_env(output_dir, pdf_file_name, parse_method)
        image_writer, md_writer = FileBasedDataWriter(local_image_dir), FileBasedDataWriter(local_md_dir)

        images_list = all_image_lists[idx]
        pdf_doc = all_pdf_docs[idx]
        _lang = lang_list[idx]
        _ocr_enable = ocr_enabled_list[idx]
        
        # 转换为中间JSON格式
        middle_json = pipeline_result_to_middle_json(
            model_list, images_list, pdf_doc, image_writer, _lang, _ocr_enable, formula_enable
        )

        pdf_info = middle_json["pdf_info"]
        pdf_bytes = pdf_bytes_list[idx]

        # 绘制边界框
        if draw_bbox:
            draw_layout_bbox(pdf_info, pdf_bytes, local_md_dir, f"{pdf_file_name}_layout.pdf")
            draw_span_bbox(pdf_info, pdf_bytes, local_md_dir, f"{pdf_file_name}_span.pdf")

        # 生成Markdown文件
        image_dir = str(os.path.basename(local_image_dir))
        md_content_str = pipeline_union_make(pdf_info, MakeMode.MM_MD, image_dir)
        md_writer.write_string(f"{pdf_file_name}.md", md_content_str)

        # 生成内容列表
        content_list = pipeline_union_make(pdf_info, MakeMode.CONTENT_LIST, image_dir)
        md_writer.write_string(
            f"{pdf_file_name}_content_list.json",
            json.dumps(content_list, ensure_ascii=False, indent=4)
        )

        # 生成中间JSON
        md_writer.write_string(
            f"{pdf_file_name}_middle.json",
            json.dumps(middle_json, ensure_ascii=False, indent=4)
        )

        # 生成模型输出
        md_writer.write_string(
            f"{pdf_file_name}_model.json",
            json.dumps(model_json, ensure_ascii=False, indent=4)
        )

        logger.info(f"输出目录: {local_md_dir}")

def parse_with_vlm_advanced(
    output_dir,
    pdf_file_names: list[str],
    pdf_bytes_list: list[bytes],
    backend="transformers",
    server_url=None,
    start_page_id=0,
    end_page_id=None,
    draw_bbox=False
):
    """使用VLM模式高级解析PDF"""
    
    # 处理backend名称
    if backend.startswith("vlm-"):
        backend = backend[4:]
    
    parse_method = "vlm"
    
    # 处理每个PDF文件
    for idx, pdf_bytes in enumerate(pdf_bytes_list):
        pdf_file_name = pdf_file_names[idx]
        
        # 处理页面范围
        pdf_bytes = convert_pdf_bytes_to_bytes_by_pypdfium2(pdf_bytes, start_page_id, end_page_id)
        
        # 准备环境
        local_image_dir, local_md_dir = prepare_env(output_dir, pdf_file_name, parse_method)
        image_writer, md_writer = FileBasedDataWriter(local_image_dir), FileBasedDataWriter(local_md_dir)
        
        # 使用VLM进行分析
        middle_json, infer_result = vlm_doc_analyze(
            pdf_bytes, 
            image_writer=image_writer, 
            backend=backend, 
            server_url=server_url
        )

        pdf_info = middle_json["pdf_info"]

        # 绘制边界框
        if draw_bbox:
            draw_layout_bbox(pdf_info, pdf_bytes, local_md_dir, f"{pdf_file_name}_layout.pdf")

        # 生成Markdown文件
        image_dir = str(os.path.basename(local_image_dir))
        md_content_str = vlm_union_make(pdf_info, MakeMode.MM_MD, image_dir)
        md_writer.write_string(f"{pdf_file_name}.md", md_content_str)

        # 生成内容列表
        content_list = vlm_union_make(pdf_info, MakeMode.CONTENT_LIST, image_dir)
        md_writer.write_string(
            f"{pdf_file_name}_content_list.json",
            json.dumps(content_list, ensure_ascii=False, indent=4)
        )

        # 生成中间JSON
        md_writer.write_string(
            f"{pdf_file_name}_middle.json",
            json.dumps(middle_json, ensure_ascii=False, indent=4)
        )

        # 生成模型输出
        model_output = ("\n" + "-" * 50 + "\n").join(infer_result)
        md_writer.write_string(f"{pdf_file_name}_model_output.txt", model_output)

        logger.info(f"输出目录: {local_md_dir}")

if __name__ == "__main__":
    main() 