"""
Pydantic v2 OutputParser + LCEL 示例
使用 LangChain Expression Language 构建处理链
"""

from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict
from langchain.output_parsers import PydanticOutputParser
from langchain.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from langchain.schema import BaseOutputParser
from langchain_core.runnables import RunnablePassthrough

class UserProfile(BaseModel):
    """单个用户档案模型"""
    
    model_config = ConfigDict(
        extra="forbid",
        str_strip_whitespace=True,
    )
    
    name: str = Field(description="用户姓名")
    age: int = Field(description="用户年龄", ge=0, le=150)
    email: str = Field(description="邮箱地址")
    interests: List[str] = Field(description="兴趣爱好列表")
    bio: Optional[str] = Field(description="个人简介", default=None)

class UserProfileList(BaseModel):
    """用户档案列表模型"""
    
    model_config = ConfigDict(
        extra="forbid",
        str_strip_whitespace=True,
    )
    
    users: List[UserProfile] = Field(description="用户档案列表")

def single_object_lcel_example():
    """单个对象 LCEL 示例"""
    
    print("=== 单个对象 LCEL 示例 ===")
    
    # 创建 LLM
    llm = ChatOpenAI(
        temperature=0,
        base_url="https://api.zhizengzeng.com/v1",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
    )
    
    # 创建解析器
    parser = PydanticOutputParser(pydantic_object=UserProfile)
    
    # 创建提示模板
    prompt = PromptTemplate(
        template="请根据以下信息创建用户档案：\n{format_instructions}\n\n用户信息：{user_info}",
        input_variables=["user_info"],
        partial_variables={"format_instructions": parser.get_format_instructions()}
    )
    
    # 构建 LCEL 链
    chain = prompt | llm | parser
    
    # 示例用户信息
    user_info = """
    姓名：张三
    年龄：28岁
    邮箱：<EMAIL>
    个人简介：热爱技术的软件工程师
    兴趣爱好：编程、阅读、游泳
    """
    
    # 执行链
    try:
        result = chain.invoke({"user_info": user_info})
        print("LCEL 链执行成功:")
        print(f"姓名: {result.name}")
        print(f"年龄: {result.age}")
        print(f"邮箱: {result.email}")
        print(f"兴趣爱好: {result.interests}")
        print(f"个人简介: {result.bio}")
        
    except Exception as e:
        print(f"执行失败: {e}")

def list_lcel_example():
    """列表 LCEL 示例"""
    
    print("\n=== 列表 LCEL 示例 ===")
    
    # 创建 LLM
    llm = ChatOpenAI(
        temperature=0,
        base_url="https://api.zhizengzeng.com/v1",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
    )
    
    # 创建解析器
    parser = PydanticOutputParser(pydantic_object=UserProfileList)
    
    # 创建提示模板
    prompt = PromptTemplate(
        template="请根据以下信息创建用户档案列表：\n{format_instructions}\n\n用户信息：{user_info}",
        input_variables=["user_info"],
        partial_variables={"format_instructions": parser.get_format_instructions()}
    )
    
    # 构建 LCEL 链
    chain = prompt | llm | parser
    
    # 示例多个用户信息
    user_info = """
    用户1：李四，25岁，<EMAIL>，爱好：音乐、电影，简介：音乐爱好者
    用户2：王五，30岁，<EMAIL>，爱好：摄影、旅行，简介：摄影师
    用户3：赵六，27岁，<EMAIL>，爱好：健身、烹饪，简介：健身教练
    """
    
    # 执行链
    try:
        result = chain.invoke({"user_info": user_info})
        print("LCEL 链执行成功:")
        print(f"用户总数: {len(result.users)}")
        
        for i, user in enumerate(result.users, 1):
            print(f"\n用户 {i}:")
            print(f"  姓名: {user.name}")
            print(f"  年龄: {user.age}")
            print(f"  邮箱: {user.email}")
            print(f"  兴趣爱好: {user.interests}")
            print(f"  个人简介: {user.bio}")
        
    except Exception as e:
        print(f"执行失败: {e}")

def complex_lcel_example():
    """复杂 LCEL 示例 - 包含多个处理步骤"""
    
    print("\n=== 复杂 LCEL 示例 ===")
    
    # 创建 LLM
    llm = ChatOpenAI(
        temperature=0,
        base_url="https://api.zhizengzeng.com/v1",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
    )
    
    # 创建解析器
    parser = PydanticOutputParser(pydantic_object=UserProfile)
    
    # 创建多个提示模板
    format_prompt = PromptTemplate(
        template="请格式化以下用户信息：\n{user_info}",
        input_variables=["user_info"]
    )
    
    profile_prompt = PromptTemplate(
        template="请根据格式化后的信息创建用户档案：\n{format_instructions}\n\n格式化信息：{formatted_info}",
        input_variables=["formatted_info"],
        partial_variables={"format_instructions": parser.get_format_instructions()}
    )
    
    # 构建复杂的 LCEL 链
    chain = (
        {"user_info": RunnablePassthrough()} 
        | format_prompt 
        | llm 
        | {"formatted_info": RunnablePassthrough()} 
        | profile_prompt 
        | llm 
        | parser
    )
    
    # 示例用户信息
    user_info = """
    张三，28岁，<EMAIL>，编程、阅读、游泳，热爱技术的软件工程师
    """
    
    # 执行链
    try:
        result = chain.invoke(user_info)
        print("复杂 LCEL 链执行成功:")
        print(f"姓名: {result.name}")
        print(f"年龄: {result.age}")
        print(f"邮箱: {result.email}")
        print(f"兴趣爱好: {result.interests}")
        print(f"个人简介: {result.bio}")
        
    except Exception as e:
        print(f"执行失败: {e}")

def streaming_lcel_example():
    """流式 LCEL 示例"""
    
    print("\n=== 流式 LCEL 示例 ===")
    
    # 创建 LLM
    llm = ChatOpenAI(
        temperature=0,
        base_url="https://api.zhizengzeng.com/v1",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2"
    )
    
    # 创建解析器
    parser = PydanticOutputParser(pydantic_object=UserProfile)
    
    # 创建提示模板
    prompt = PromptTemplate(
        template="请根据以下信息创建用户档案：\n{format_instructions}\n\n用户信息：{user_info}",
        input_variables=["user_info"],
        partial_variables={"format_instructions": parser.get_format_instructions()}
    )
    
    # 构建 LCEL 链
    chain = prompt | llm | parser
    
    # 示例用户信息
    user_info = """
    姓名：张三
    年龄：28岁
    邮箱：<EMAIL>
    个人简介：热爱技术的软件工程师
    兴趣爱好：编程、阅读、游泳
    """
    
    # 流式执行
    print("开始流式处理...")
    try:
        for chunk in chain.stream({"user_info": user_info}):
            print(f"处理中: {chunk}")
        
        print("流式处理完成")
        
    except Exception as e:
        print(f"流式处理失败: {e}")

def main():
    """主函数"""
    single_object_lcel_example()
    list_lcel_example()
    complex_lcel_example()
    streaming_lcel_example()

if __name__ == "__main__":
    main() 