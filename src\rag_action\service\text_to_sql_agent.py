"""
Text-to-SQL Agent服务
具备错误识别和自动纠错功能的智能SQL生成代理
"""
import logging
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from rag_action.service.text_to_sql_service import get_text_to_sql_service
from rag_action.service.database_query_service import get_database_query_service

logger = logging.getLogger(__name__)

@dataclass
class SQLExecutionResult:
    """SQL执行结果"""
    success: bool
    data: List[Dict[str, Any]] = None
    error: str = ""
    sql: str = ""
    execution_time: float = 0
    row_count: int = 0
    retry_count: int = 0
    error_analysis: str = ""
    fix_suggestion: str = ""

class TextToSQLAgent:
    """
    Text-to-SQL智能代理
    具备错误识别、分析和自动纠错功能
    """
    
    def __init__(self):
        self.text_to_sql_service = get_text_to_sql_service()
        self.db_query_service = get_database_query_service()
        # 延迟导入LLM服务以避免循环导入
        self.llm_service = None
        self.max_retries = 3
        
        # 常见错误模式和修复策略
        self.error_patterns = {
            "table_not_found": {
                "keywords": ["table", "doesn't exist", "not found", "unknown table"],
                "fix_strategy": "check_table_names"
            },
            "column_not_found": {
                "keywords": ["column", "doesn't exist", "unknown column", "not found"],
                "fix_strategy": "check_column_names"
            },
            "syntax_error": {
                "keywords": ["syntax error", "near", "unexpected", "invalid syntax"],
                "fix_strategy": "fix_syntax"
            },
            "permission_denied": {
                "keywords": ["permission denied", "access denied", "not allowed"],
                "fix_strategy": "simplify_query"
            },
            "data_type_error": {
                "keywords": ["data type", "type mismatch", "conversion failed"],
                "fix_strategy": "fix_data_types"
            }
        }
        
        logger.info("Text-to-SQL Agent初始化完成")

    def _get_llm_service(self):
        """延迟获取LLM服务以避免循环导入"""
        if self.llm_service is None:
            from rag_action.service.rag_service import LLMService
            self.llm_service = LLMService()
        return self.llm_service
    
    async def execute_natural_language_query_with_retry(self, question: str) -> SQLExecutionResult:
        """
        执行自然语言查询，具备自动重试和纠错功能
        
        Args:
            question: 自然语言问题
            
        Returns:
            SQLExecutionResult: 执行结果，包含重试信息
        """
        start_time = time.time()
        retry_count = 0
        last_error = ""
        sql_history = []
        
        logger.info(f"开始执行自然语言查询: {question}")
        
        while retry_count <= self.max_retries:
            try:
                # 1. 生成SQL
                if retry_count == 0:
                    # 首次生成
                    logger.info(f"🎯 首次生成SQL: {question}")
                    sql_result = self.text_to_sql_service.generate_sql(question)
                else:
                    # 基于错误进行SQL纠错
                    logger.info(f"🔧 第{retry_count + 1}次尝试: 基于错误进行SQL纠错")
                    sql_result = await self._regenerate_sql_with_error_context(
                        question, last_error, sql_history
                    )
                
                if not sql_result.get("success", False):
                    last_error = sql_result.get("error", "SQL生成失败")
                    logger.warning(f"SQL生成失败 (尝试 {retry_count + 1}): {last_error}")
                    retry_count += 1
                    continue
                
                sql = sql_result.get("sql", "")
                sql_history.append(sql)
                
                # 2. 执行SQL
                execution_result = self.db_query_service.execute_sql_safely(sql)
                
                if execution_result.get("success", False):
                    # 执行成功
                    total_time = time.time() - start_time
                    logger.info(f"查询成功执行 (尝试 {retry_count + 1}): {sql}")
                    
                    return SQLExecutionResult(
                        success=True,
                        data=execution_result.get("data", []),
                        sql=sql,
                        execution_time=total_time,
                        row_count=execution_result.get("row_count", 0),
                        retry_count=retry_count
                    )
                else:
                    # 执行失败，准备纠错
                    last_error = execution_result.get("error", "SQL执行失败")
                    logger.warning(f"❌ SQL执行失败 (尝试 {retry_count + 1}): {last_error}")
                    logger.info(f"失败的SQL: {sql}")

                    # 如果还有重试机会，准备纠错
                    if retry_count < self.max_retries:
                        logger.info(f"🔧 准备进行SQL纠错，剩余重试次数: {self.max_retries - retry_count}")

                    retry_count += 1
                    
            except Exception as e:
                last_error = str(e)
                logger.error(f"查询执行异常 (尝试 {retry_count + 1}): {e}")
                retry_count += 1
        
        # 所有重试都失败了
        total_time = time.time() - start_time
        logger.error(f"查询最终失败，已重试{self.max_retries}次: {last_error}")
        
        return SQLExecutionResult(
            success=False,
            error=last_error,
            sql=sql_history[-1] if sql_history else "",
            execution_time=total_time,
            retry_count=self.max_retries,
            error_analysis=await self._analyze_sql_error(sql_history[-1] if sql_history else "", last_error)
        )
    
    async def _regenerate_sql_with_error_context(self, question: str, error: str, sql_history: List[str]) -> Dict[str, Any]:
        """
        基于错误上下文重新生成SQL - 真正的SQL纠错

        Args:
            question: 原始问题
            error: 错误信息
            sql_history: 之前尝试的SQL历史

        Returns:
            Dict: SQL生成结果
        """
        try:
            failed_sql = sql_history[-1] if sql_history else ""

            logger.info(f"🔧 开始SQL纠错: 分析错误并修复SQL")
            logger.info(f"原始问题: {question}")
            logger.info(f"失败SQL: {failed_sql}")
            logger.info(f"错误信息: {error}")

            # 构建专门的SQL纠错提示
            fix_prompt = f"""你是一个SQL纠错专家。用户的SQL查询执行失败了，你需要分析错误并生成修复后的SQL。

原始问题: {question}

失败的SQL:
{failed_sql}

错误信息:
{error}

数据库表结构:
{self.text_to_sql_service.get_schema_info()}

之前尝试过的SQL:
{chr(10).join([f"{i+1}. {sql}" for i, sql in enumerate(sql_history)])}

请分析错误原因并生成修复后的SQL。常见错误类型和修复方法:

1. 表名错误: 检查表名是否正确 (notes, note_tags, note_chunks)
2. 列名错误: 检查列名是否存在于对应表中
3. 语法错误: 修复SQL语法问题
4. 数据类型错误: 确保数据类型匹配
5. 权限问题: 简化查询，只使用SELECT语句

要求:
- 只返回修复后的SQL语句
- 不要包含任何解释或markdown格式
- 确保SQL语法正确
- 使用正确的表名和列名
- 避免重复之前失败的错误

修复后的SQL:"""

            llm_service = self._get_llm_service()
            response, _ = llm_service.generate_response(fix_prompt)
            fixed_sql = response.strip()

            # 清理SQL（移除可能的markdown标记）
            if "```sql" in fixed_sql:
                fixed_sql = fixed_sql.split("```sql")[1].split("```")[0].strip()
            elif "```" in fixed_sql:
                fixed_sql = fixed_sql.split("```")[1].strip()

            # 移除可能的前缀文本
            lines = fixed_sql.split('\n')
            sql_lines = []
            found_sql = False
            for line in lines:
                line = line.strip()
                if line.upper().startswith('SELECT') or found_sql:
                    found_sql = True
                    sql_lines.append(line)
                elif line and not any(word in line.lower() for word in ['修复', '分析', '错误', '原因', '这里', '以下']):
                    sql_lines.append(line)

            if sql_lines:
                fixed_sql = ' '.join(sql_lines).strip()

            logger.info(f"🔧 生成修复后的SQL: {fixed_sql}")

            # 验证修复后的SQL
            validation_result = self.text_to_sql_service._validate_sql(fixed_sql)
            is_valid = validation_result.get("is_valid", False)

            if is_valid:
                logger.info(f"✅ 修复后的SQL验证通过")
            else:
                logger.warning(f"❌ 修复后的SQL验证失败: {validation_result.get('errors', [])}")

            return {
                "success": is_valid,
                "sql": fixed_sql,
                "explanation": f"SQL纠错: 修复了错误 '{error[:100]}...'",
                "error": "" if is_valid else f"修复后的SQL仍然无效: {', '.join(validation_result.get('errors', []))}",
                "is_valid": is_valid,
                "warnings": validation_result.get("warnings", [])
            }

        except Exception as e:
            logger.error(f"❌ SQL纠错过程失败: {e}")
            return {
                "success": False,
                "sql": "",
                "error": f"SQL纠错过程出错: {str(e)}",
                "is_valid": False
            }
    
    async def _analyze_sql_error(self, sql: str, error: str) -> str:
        """
        简单的SQL错误分析（用于日志记录）

        Args:
            sql: 失败的SQL
            error: 错误信息

        Returns:
            str: 简要错误分析
        """
        try:
            # 基于错误模式匹配
            error_lower = error.lower()
            matched_patterns = []

            for pattern_name, pattern_info in self.error_patterns.items():
                for keyword in pattern_info["keywords"]:
                    if keyword in error_lower:
                        matched_patterns.append(pattern_name)
                        break

            if matched_patterns:
                return f"错误类型: {', '.join(matched_patterns)}"
            else:
                return f"未知错误类型: {error[:100]}..."

        except Exception as e:
            logger.error(f"错误分析失败: {e}")
            return f"错误分析失败: {str(e)}"
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """获取执行统计信息"""
        # 这里可以添加统计信息收集逻辑
        return {
            "max_retries": self.max_retries,
            "error_patterns_count": len(self.error_patterns),
            "agent_status": "active"
        }

# 单例模式
_text_to_sql_agent = None

def get_text_to_sql_agent() -> TextToSQLAgent:
    """获取Text-to-SQL Agent实例"""
    global _text_to_sql_agent
    if _text_to_sql_agent is None:
        _text_to_sql_agent = TextToSQLAgent()
    return _text_to_sql_agent
