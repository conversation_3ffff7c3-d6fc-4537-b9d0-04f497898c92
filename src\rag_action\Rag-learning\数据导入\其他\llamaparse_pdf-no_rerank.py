# =============================================================================
# LlamaParse + LlamaIndex PDF 问答系统（无重排序，递归结构化检索）
# 详细流程讲解与注释
# =============================================================================

# -------------------------------
# 1. 环境变量与依赖加载
# -------------------------------
from llama_index.llms.openai import OpenAI                # OpenAI LLM 接口
from llama_index.embeddings.openai import OpenAIEmbedding # OpenAI 嵌入模型
from llama_index.core import VectorStoreIndex, Settings   # 向量索引与全局设置
from dotenv import load_dotenv                           # 加载 .env 环境变量
import os

load_dotenv()   # 加载本地 .env 文件中的环境变量

# 设置 LlamaParse 云服务的 API Key（可直接写死或用环境变量）
os.environ["LLAMA_CLOUD_API_KEY"] = "llx-adfMAwWUmfNsv3xVtbc2lEkjvrkbNdWfBZdso2az99XCaUSE"

# -------------------------------
# 2. 初始化 LLM 与嵌入模型
# -------------------------------
# 配置 OpenAI 大语言模型（用于后续问答生成）
llm = OpenAI(
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",  # API 密钥
    api_base="https://api.zhizengzeng.com/v1",                      # API 基础地址
    model="gpt-4o-mini"                                             # 使用的模型名称
)

# 配置 OpenAI 嵌入模型（用于文本向量化）
embed_model = OpenAIEmbedding(
    api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
    api_base="https://api.zhizengzeng.com/v1",
    model="text-embedding-3-small"
)

# 设置全局 LLM 和嵌入模型，后续索引和检索会自动调用
Settings.llm = llm
Settings.embed_model = embed_model

# -------------------------------
# 3. 解析 PDF 文档为结构化 Markdown
# -------------------------------
from llama_parse import LlamaParse

# 创建 LlamaParse 解析器，指定 API Key 和输出类型为 markdown
parser = LlamaParse(
    api_key=os.getenv("LLAMA_CLOUD_API_KEY"),
    result_type="markdown"  # 解析结果为 markdown 结构
)

# 加载并解析 PDF 文件，返回 markdown 文档对象列表
documents = parser.load_data(
    "C:/Users/<USER>/Downloads/zhizhuxia/bge-small-zh/uber_10q_march_2022.pdf"
)
# 注：此处会自动上传 PDF 到 LlamaParse 云端解析，解析完成后返回结构化文档

# -------------------------------
# 4. Markdown 结构化节点解析
# -------------------------------
from llama_index.core.node_parser import MarkdownElementNodeParser

# 创建 MarkdownElementNodeParser，将 markdown 文档进一步分解为结构化节点
node_parser = MarkdownElementNodeParser(
    llm=llm,           # 用于理解 markdown 结构的 LLM
    num_workers=8      # 并发线程数，加快解析速度
)

# 将文档对象解析为节点（每个节点对应 markdown 的一个结构单元，如标题、表格、段落等）
nodes = node_parser.get_nodes_from_documents(documents)

# 可选：进一步区分文本节点和索引节点（如目录、标题等）
text_nodes, index_nodes = node_parser.get_nodes_and_objects(nodes)
# text_nodes[0]  # 可查看第一个文本节点内容
# index_nodes[0] # 可查看第一个索引节点内容

# -------------------------------
# 5. 构建向量索引
# -------------------------------
# 方式一：递归结构化索引（文本节点+索引节点，支持结构化检索）
recursive_index = VectorStoreIndex(nodes=text_nodes + index_nodes)

# 方式二：原始文档索引（直接用解析后的 markdown 文档，适合简单检索）
raw_index = VectorStoreIndex.from_documents(documents)

# -------------------------------
# 6. 创建查询引擎
# -------------------------------
# 递归结构化检索引擎（支持结构化内容的深度检索）
recursive_query_engine = recursive_index.as_query_engine(
    similarity_top_k=15,  # 检索最相关的15个节点
    verbose=True          # 输出详细检索过程
)

# 原始文档检索引擎（直接基于 markdown 文档内容检索）
raw_query_engine = raw_index.as_query_engine(
    similarity_top_k=15,
)

# -------------------------------
# 7. 示例问答与结果输出
# -------------------------------
# 可尝试多种英文财报类问题，以下为示例
query = "What is the change of free cash flow and what is the rate from the financial and operational highlights?"
query = "how many COVID-19 response initiatives in year 2021?"

# 方式一：原始文档检索
response_1 = raw_query_engine.query(query)
print("\n************New LlamaParse+ Basic Query Engine************")
print(response_1)

# 方式二：递归结构化检索
response_2 = recursive_query_engine.query(query)
print("\n************New LlamaParse+ Recursive Retriever Query Engine************")
print(response_2)

# =============================================================================
# 总结说明：
# 1. 本流程实现了 PDF → markdown 结构化 → 节点分解 → 向量化 → 检索问答的全流程。
# 2. 支持两种检索方式：原始内容检索（简单）、结构化递归检索（更强大）。
# 3. 结构化检索可利用文档的层级结构（如目录、标题、表格等），提升问答准确性。
# 4. 适合财报、学术等结构化较强的 PDF 问答场景。
# 5. 可根据实际需求调整检索 top_k、节点解析方式等参数。