"""
超简单自定义解析器示例
"""

from langchain_core.output_parsers import BaseOutputParser
import json

class SimpleParser(BaseOutputParser[str]):
    """最简单的自定义解析器"""
    
    def parse(self, text: str) -> str:
        """解析文本"""
        
        # 如果文本是 JSON 格式，提取其中的 name 字段
        try:
            if text.startswith('{'):
                data = json.loads(text)
                return data.get('name', '解析失败')
        except:
            pass
        
        # 否则返回原文本
        return text

def main():
    """主函数"""
    
    # 创建解析器
    parser = SimpleParser()
    
    # 测试
    test_cases = [
        '{"name": "张三", "age": 28}',
        '{"name": "李四"}',
        '普通文本'
    ]
    
    print("=== 超简单自定义解析器 ===")
    
    for test_case in test_cases:
        result = parser.parse(test_case)
        print(f"输入: {test_case}")
        print(f"输出: {result}")
        print()

if __name__ == "__main__":
    main() 