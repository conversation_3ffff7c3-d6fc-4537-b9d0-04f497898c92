"""
LCEL 事件监听机制示例 - 修复版
"""
"""
LCEL（LangChain Expression Language）事件监听是一种用于追踪和响应链式调用（Chain）执行过程中的各类事件的机制。通过事件监听器（Callback Handler），开发者可以在链的不同阶段（如链开始、链结束、LLM调用开始、LLM调用结束、出错等）捕获事件，并自定义处理逻辑。

核心原理：
- LCEL事件监听器本质上是继承自`BaseCallbackHandler`的类，重写其中的事件方法（如`on_chain_start`、`on_chain_end`、`on_llm_start`、`on_llm_end`等）。
- 在链执行过程中，LangChain会自动在合适的时机调用这些事件方法，将当前的上下文信息（如输入、输出、异常等）传递给监听器。
- 可以同时注册多个监听器，实现日志记录、性能监控、异常报警等多种功能。

常见应用场景包括：
1. **日志记录**：记录每次链调用的输入、输出、提示词、响应内容等，方便后续排查和分析。
2. **性能监控**：统计每个环节的耗时，发现性能瓶颈。
3. **异常报警**：在链或LLM调用出错时，自动发送报警通知（如邮件、钉钉、企业微信等）。
4. **可视化追踪**：与LangSmith等可视化平台集成，追踪链路执行流程，便于调试和优化。
5. **自定义行为**：如自动保存中间结果、动态调整参数、收集用户反馈等。

通过LCEL事件监听，开发者可以实现对链式应用的全流程可观测性和可控性，大大提升了生产级应用的可维护性和可靠性。
"""


from langchain_core.output_parsers import StrOutputParser
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough
from langchain_core.callbacks import BaseCallbackHandler
import asyncio
import time
from typing import Any, Dict, List, Optional

# 1. 基础事件监听器 - 修复版
class BasicCallbackHandler(BaseCallbackHandler):
    """基础事件监听器 - 修复版"""
    
    def on_chain_start(self, serialized: Dict[str, Any], inputs: Dict[str, Any], **kwargs) -> None:
        """链开始执行时触发"""
        name = serialized.get('name', 'unknown') if serialized else 'unknown'
        print(f"🔗 链开始执行: {name}")
        print(f"   输入: {inputs}")
    
    def on_chain_end(self, outputs: Dict[str, Any], **kwargs) -> None:
        """链执行结束时触发"""
        print(f"✅ 链执行结束")
        print(f"   输出: {outputs}")
    
    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs) -> None:
        """LLM 开始执行时触发"""
        name = serialized.get('name', 'unknown') if serialized else 'unknown'
        print(f"🤖 LLM 开始执行: {name}")
        if prompts:
            print(f"   提示词: {prompts[0][:100]}...")
    
    def on_llm_end(self, response: Any, **kwargs) -> None:
        """LLM 执行结束时触发"""
        print(f"✅ LLM 执行结束")
        # 修复：正确访问响应内容
        if hasattr(response, 'generations') and response.generations:
            content = response.generations[0][0].text
            print(f"   响应: {content[:100]}...")
        elif hasattr(response, 'content'):
            print(f"   响应: {response.content[:100]}...")
        else:
            print(f"   响应: {str(response)[:100]}...")
    
    def on_llm_error(self, error: Exception, **kwargs) -> None:
        """LLM 执行出错时触发"""
        print(f"❌ LLM 执行出错: {error}")

# 2. 性能监控监听器 - 修复版
class PerformanceCallbackHandler(BaseCallbackHandler):
    """性能监控监听器 - 修复版"""
    
    def __init__(self):
        self.start_times = {}
        self.chain_times = {}
    
    def on_chain_start(self, serialized: Dict[str, Any], inputs: Dict[str, Any], **kwargs) -> None:
        """记录链开始时间"""
        chain_id = kwargs.get('run_id', id(serialized))
        self.start_times[chain_id] = time.time()
        name = serialized.get('name', 'unknown') if serialized else 'unknown'
        print(f"⏱️  链开始: {name}")
    
    def on_chain_end(self, outputs: Dict[str, Any], **kwargs) -> None:
        """计算链执行时间"""
        run_id = kwargs.get('run_id')
        if run_id and run_id in self.start_times:
            duration = time.time() - self.start_times[run_id]
            self.chain_times[run_id] = duration
            print(f"⏱️  链执行时间: {duration:.2f}秒")
    
    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs) -> None:
        """记录 LLM 开始时间"""
        llm_id = kwargs.get('run_id', id(serialized))
        self.start_times[llm_id] = time.time()
        name = serialized.get('name', 'unknown') if serialized else 'unknown'
        print(f"⏱️  LLM 开始: {name}")
    
    def on_llm_end(self, response: Any, **kwargs) -> None:
        """计算 LLM 执行时间"""
        run_id = kwargs.get('run_id')
        if run_id and run_id in self.start_times:
            duration = time.time() - self.start_times[run_id]
            print(f"⏱️  LLM 执行时间: {duration:.2f}秒")

# 3. 流式监听器 - 修复版
class StreamingCallbackHandler(BaseCallbackHandler):
    """流式监听器 - 修复版"""
    
    def __init__(self):
        self.token_count = 0
    
    def on_llm_new_token(self, token: str, **kwargs) -> None:
        """每个新 token 生成时触发"""
        self.token_count += 1
        print(token, end="", flush=True)
    
    def on_chain_stream(self, chunk: Any, **kwargs) -> None:
        """链流式输出时触发"""
        # 避免重复输出，只在非 token 事件时输出
        if not hasattr(chunk, 'content'):
            print(f" 链流式输出: {chunk}")

# 4. 调试监听器 - 修复版
class DebugCallbackHandler(BaseCallbackHandler):
    """调试监听器 - 修复版"""
    
    def on_chain_start(self, serialized: Dict[str, Any], inputs: Dict[str, Any], **kwargs) -> None:
        """详细的链开始信息"""
        print(f"🔍 调试 - 链开始:")
        if serialized:
            print(f"   序列化信息: {serialized}")
        else:
            print(f"   序列化信息: None")
        print(f"   输入: {inputs}")
        print(f"   其他参数: {kwargs}")
    
    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs) -> None:
        """详细的 LLM 开始信息"""
        print(f"🔍 调试 - LLM 开始:")
        if serialized:
            print(f"   序列化信息: {serialized}")
        else:
            print(f"   序列化信息: None")
        print(f"   提示词数量: {len(prompts)}")
        if prompts:
            print(f"   第一个提示词: {prompts[0][:200]}...")

def basic_event_listener_example():
    """基础事件监听示例"""
    
    print("=== 基础事件监听示例 ===")
    
    # 创建 LLM
    llm = ChatOpenAI(
        base_url="https://api.zhizengzeng.com/v1",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
        model="gpt-4o-mini",
        temperature=0
    )
    
    # 创建提示模板
    prompt = ChatPromptTemplate.from_template("请回答：{question}")
    
    # 创建链
    chain = prompt | llm | StrOutputParser()
    
    # 创建事件监听器
    callback_handler = BasicCallbackHandler()
    
    # 执行链（带事件监听）
    result = chain.invoke(
        {"question": "什么是人工智能？"},
        config={"callbacks": [callback_handler]}
    )
    
    print(f"\n最终结果: {result[:100]}...")

def performance_monitoring_example():
    """性能监控示例"""
    
    print("\n=== 性能监控示例 ===")
    
    # 创建 LLM
    llm = ChatOpenAI(
        base_url="https://api.zhizengzeng.com/v1",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
        model="gpt-4o-mini",
        temperature=0
    )
    
    # 创建简单的链
    prompt = ChatPromptTemplate.from_template("请解释：{topic}")
    chain = prompt | llm | StrOutputParser()
    
    # 创建性能监控监听器
    perf_handler = PerformanceCallbackHandler()
    
    # 执行链
    result = chain.invoke(
        {"topic": "机器学习"},
        config={"callbacks": [perf_handler]}
    )
    
    print(f"\n最终结果: {result[:100]}...")

def streaming_event_listener_example():
    """流式事件监听示例"""
    
    print("\n=== 流式事件监听示例 ===")
    
    # 创建 LLM
    llm = ChatOpenAI(
        base_url="https://api.zhizengzeng.com/v1",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
        model="gpt-4o-mini",
        temperature=0
    )
    
    # 创建提示模板
    prompt = ChatPromptTemplate.from_template("请详细解释：{topic}")
    
    # 创建链
    chain = prompt | llm | StrOutputParser()
    
    # 创建流式监听器
    stream_handler = StreamingCallbackHandler()
    
    print("开始流式处理...")
    
    # 流式执行
    for chunk in chain.stream(
        {"topic": "深度学习"},
        config={"callbacks": [stream_handler]}
    ):
        pass  # 监听器会处理输出
    
    print(f"\n\n流式处理完成，共生成 {stream_handler.token_count} 个 token")

def multiple_handlers_example():
    """多监听器示例"""
    
    print("\n=== 多监听器示例 ===")
    
    # 创建 LLM
    llm = ChatOpenAI(
        base_url="https://api.zhizengzeng.com/v1",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
        model="gpt-4o-mini",
        temperature=0
    )
    
    # 创建提示模板
    prompt = ChatPromptTemplate.from_template("请分析：{topic}")
    
    # 创建链
    chain = prompt | llm | StrOutputParser()
    
    # 创建多个监听器
    basic_handler = BasicCallbackHandler()
    perf_handler = PerformanceCallbackHandler()
    debug_handler = DebugCallbackHandler()
    
    # 执行链（带多个监听器）
    result = chain.invoke(
        {"topic": "区块链技术"},
        config={"callbacks": [basic_handler, perf_handler, debug_handler]}
    )
    
    print(f"\n最终结果: {result[:100]}...")

def async_event_listener_example():
    """异步事件监听示例"""
    
    print("\n=== 异步事件监听示例 ===")
    
    # 创建 LLM
    llm = ChatOpenAI(
        base_url="https://api.zhizengzeng.com/v1",
        api_key="sk-zk27b33f9750ab4d8b68c9d2482791ebe9c6670387cf12d2",
        model="gpt-4o-mini",
        temperature=0
    )
    
    # 创建提示模板
    prompt = ChatPromptTemplate.from_template("请回答：{question}")
    
    # 创建链
    chain = prompt | llm | StrOutputParser()
    
    # 创建事件监听器
    callback_handler = BasicCallbackHandler()
    
    async def async_run():
        """异步运行"""
        result = await chain.ainvoke(
            {"question": "什么是云计算？"},
            config={"callbacks": [callback_handler]}
        )
        print(f"\n异步执行结果: {result[:100]}...")
    
    # 运行异步函数
    asyncio.run(async_run())

def main():
    """主函数"""
    basic_event_listener_example()
    performance_monitoring_example()
    streaming_event_listener_example()
    multiple_handlers_example()
    async_event_listener_example()

if __name__ == "__main__":
    main() 